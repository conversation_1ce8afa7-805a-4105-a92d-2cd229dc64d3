{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=template&id=4569dbde&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753808450477}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"usercenter-container\">\n\n\n    <!-- 主要内容区域 -->\n    <div class=\"usercenter-main\">\n      <div class=\"container\">\n        <div class=\"usercenter-layout\">\n          <!-- 侧边栏 -->\n          <Sidebar\n            ref=\"sidebar\"\n            :current-page=\"currentPage\"\n            :user-info=\"userInfo\"\n            @menu-change=\"handleMenuChange\"\n            @action=\"handleSidebarAction\"\n          />\n\n          <!-- 内容区域 -->\n          <div class=\"usercenter-content\">\n            <!-- 概览页面 -->\n            <Overview\n              v-if=\"currentPage === 'overview'\"\n              :key=\"'overview'\"\n              :user-info=\"userInfo\"\n              @navigate=\"handleNavigate\"\n            />\n\n            <!-- 账户设置页面 -->\n            <Profile\n              v-else-if=\"currentPage === 'profile'\"\n              :key=\"'profile'\"\n              :user-info=\"userInfo\"\n              @navigate=\"handleNavigate\"\n              @avatar-updated=\"handleAvatarUpdated\"\n              @info-updated=\"handleInfoUpdated\"\n              @api-key-updated=\"handleApiKeyUpdated\"\n              @password-changed=\"handlePasswordChanged\"\n              @refresh-user-info=\"getUserInfo\"\n            />\n\n            <!-- 账户管理页面 -->\n            <Credits\n              v-else-if=\"currentPage === 'credits'\"\n              :key=\"'credits'\"\n              @navigate=\"handleNavigate\"\n            />\n\n            <!-- 订单记录页面 -->\n            <Orders\n              v-else-if=\"currentPage === 'orders'\"\n              :key=\"'orders'\"\n              @navigate=\"handleNavigate\"\n            />\n\n            <!-- 使用记录页面 -->\n            <Usage\n              v-else-if=\"currentPage === 'usage'\"\n              :key=\"'usage'\"\n              @navigate=\"handleNavigate\"\n            />\n\n            <!-- 🚫 临时注释掉会员服务和推荐奖励页面 -->\n            <!-- <Membership\n              v-else-if=\"currentPage === 'membership'\"\n              :key=\"'membership'\"\n              @navigate=\"handleNavigate\"\n            /> -->\n\n            <!-- <Referral\n              v-else-if=\"currentPage === 'referral'\"\n              :key=\"'referral'\"\n              @navigate=\"handleNavigate\"\n            /> -->\n\n            <!-- 系统通知页面 -->\n            <Notifications\n              v-else-if=\"currentPage === 'notifications'\"\n              :key=\"'notifications'\"\n              @navigate=\"handleNavigate\"\n              @notification-updated=\"handleNotificationUpdated\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 悬浮系统通知 -->\n  <FloatingNotifications\n    ref=\"floatingNotifications\"\n    @navigate-to-notifications=\"handleNavigateToNotifications\"\n  />\n</WebsitePage>\n", null]}