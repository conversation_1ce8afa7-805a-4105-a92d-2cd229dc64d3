{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753823262849}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport QuickRecharge from '@/components/QuickRecharge.vue'\nimport { createMembershipOrder, payOrder } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { getCurrentUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage,\n    QuickRecharge\n  },\n  data() {\n    return {\n      // 支付相关\n      paymentLoading: false,\n      showPaymentModal: false,\n      selectedPlan: null,\n      selectedPaymentMethod: 'alipay-qr',\n\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null,\n      checkingStatus: false,\n\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          originalPrice: '39',\n          discountText: '限时7.4折',\n          saveAmount: '10',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          originalPrice: '468',\n          discountText: '限时6.4折',\n          saveAmount: '170',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          originalPrice: '999',\n          discountText: '限时4.9折',\n          saveAmount: '510',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            { text: '解锁<strong class=\"highlight\">全部课程</strong>', disabled: false, exclusive: true },\n            { text: '插件<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '邀请<strong class=\"highlight\">奖励50%</strong>', disabled: false, exclusive: true },\n            { text: '调用工作流<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '复制<strong class=\"highlight\">所有工作流</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">解锁流媒体转换</strong>', disabled: false, exclusive: true },\n            { text: '部分<strong class=\"highlight\">插件免费</strong>', disabled: false, exclusive: true }\n          ],\n          buttonText: '立即购买',\n          featured: true\n        }\n      ]\n    }\n  },\n\n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n\n  mounted() {\n    console.log('Membership页面已挂载')\n    this.checkPaymentSuccessFromUrl()\n  },\n\n  methods: {\n    // 获取按钮文本（续费/升级/购买）\n    getButtonText(plan) {\n      const userRole = this.getUserRole() // 获取用户当前角色\n      const planRole = this.getPlanRole(plan.id) // 获取套餐对应角色\n\n      // 管理员显示\"立即购买\"（管理员不需要购买会员，但可以测试）\n      if (userRole === 'admin') {\n        return '立即购买'\n      }\n\n      // 相同等级显示\"续费会员\"\n      if (userRole === planRole) {\n        return '续费会员'\n      }\n      // 普通用户购买任何会员都显示\"立即购买\"\n      else if (userRole === 'user') {\n        return '立即购买'\n      }\n      // 会员用户购买更高等级显示\"升级会员\"\n      else if (this.isUpgrade(userRole, planRole)) {\n        return '升级会员'\n      }\n      // 其他情况显示\"立即购买\"\n      else {\n        return '立即购买'\n      }\n    },\n\n    // 获取套餐对应的角色\n    getPlanRole(planId) {\n      const roleMap = {\n        1: 'VIP',     // VIP月卡\n        2: 'VIP',     // VIP年卡\n        3: 'SVIP'     // SVIP年卡\n      }\n      return roleMap[planId] || 'user'\n    },\n\n    // 判断是否为升级\n    isUpgrade(currentRole, targetRole) {\n      const roleLevel = {\n        'user': 1,\n        'VIP': 2,\n        'SVIP': 3,\n        'admin': 999  // 管理员级别最高\n      }\n      return roleLevel[targetRole] > (roleLevel[currentRole] || 0)\n    },\n\n    // 是否显示升级提示\n    showUpgradeNotice(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      // 只有VIP升级到SVIP时显示特殊提示\n      return userRole === 'VIP' && planRole === 'SVIP'\n    },\n\n    // 获取升级提示文本\n    getUpgradeNoticeText(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      if (userRole === 'VIP' && planRole === 'SVIP') {\n        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~'\n      }\n\n      return ''\n    },\n\n    // 获取用户当前角色（统一角色获取逻辑）\n    getUserRole() {\n      try {\n        // 使用roleUtils获取角色\n        let role = getCurrentUserRole()\n\n        console.log('🔍 Membership: getUserRole - 从roleUtils获取角色:', role)\n\n        // 角色映射：将中文角色名转换为英文角色代码\n        const roleMap = {\n          // 中文角色名映射\n          '普通用户': 'user',\n          'VIP会员': 'VIP',\n          'SVIP会员': 'SVIP',\n          '管理员': 'admin',\n          // 英文角色代码（保持不变）\n          'user': 'user',\n          'VIP': 'VIP',\n          'SVIP': 'SVIP',\n          'admin': 'admin',\n          // 大小写兼容\n          'USER': 'user',\n          'vip': 'VIP',\n          'svip': 'SVIP',\n          'ADMIN': 'admin'\n        }\n\n        // 映射角色\n        const mappedRole = roleMap[role] || 'user'\n\n        console.log('🔍 Membership: getUserRole - 映射后的角色:', mappedRole)\n        return mappedRole\n\n      } catch (error) {\n        console.warn('🔍 Membership: 获取用户角色失败:', error)\n        return 'user'  // 出错时默认为普通用户\n      }\n    },\n\n    async handleSubscribe(plan) {\n      console.log('选择购买套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$message.warning('请先登录后再购买会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        // 准备会员订单数据（与充值订单结构保持一致）\n        const orderData = {\n          membershipLevel: plan.id,\n          duration: plan.name.includes('月') ? 1 : 12, // 根据套餐名称判断时长\n          amount: parseFloat(plan.price),\n          planName: plan.name,\n          paymentMethod: 'alipay' // 默认支付宝，与充值保持一致\n        }\n\n        console.log('🎯 创建会员订单:', orderData)\n        this.$message.loading('正在创建订单...', 0)\n\n        // 创建会员订单（使用与充值相同的交易记录系统）\n        const response = await createMembershipOrder(orderData)\n        this.$message.destroy()\n\n        if (response.success) {\n          const orderResult = response.result\n          console.log('🎯 会员订单创建成功:', orderResult)\n\n          // 显示支付选择弹窗\n          this.selectedPlan = plan\n          this.currentOrderId = orderResult.orderId\n          this.currentOrderAmount = orderResult.amount\n          this.showPaymentModal = true\n\n        } else {\n          this.$message.error(response.message || '创建订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('创建会员订单失败:', error)\n        this.$message.error('创建订单失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付\n    async handlePayment() {\n      if (!this.currentOrderId) {\n        this.$message.error('订单信息错误')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        if (this.selectedPaymentMethod === 'alipay-page') {\n          await this.handleAlipayPagePayment()\n        } else if (this.selectedPaymentMethod === 'alipay-qr') {\n          await this.handleAlipayQrPayment()\n        }\n\n        this.showPaymentModal = false\n      } catch (error) {\n        console.error('支付处理失败:', error)\n        this.$message.error('支付失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付宝网页支付（与充值功能完全一致）\n    async handleAlipayPagePayment() {\n      try {\n        console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝网页支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n\n    // 处理支付宝扫码支付（与充值功能完全一致）\n    async handleAlipayQrPayment() {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode)\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n\n    // 显示二维码弹窗\n    showQrCodeModal(qrCode) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`\n      this.showQrModal = true\n\n      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount)\n      console.log('🔍 二维码URL:', this.qrCodeUrl)\n\n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n\n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n\n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n\n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n\n    // 检查支付状态（与充值功能完全一致）\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        this.checkingStatus = true\n        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        console.log('🔍 支付状态查询响应:', response)\n\n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          console.log('🔍 会员购买支付成功！')\n          this.$message.success('支付成功！会员权益已生效')\n          this.closeQrModal()\n\n          // 留在当前membership页面，刷新用户状态\n          setTimeout(() => {\n            this.$message.success('会员购买成功！页面即将刷新以更新会员状态')\n            // 刷新当前页面以更新会员状态和按钮显示\n            window.location.reload()\n          }, 2000)\n        } else {\n          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    },\n\n    // 处理充值成功事件\n    handleRechargeSuccess() {\n      this.$message.success('充值成功！您可以继续选择套餐')\n      // 可以在这里添加其他逻辑，比如刷新用户信息等\n    },\n\n    // 检查URL中的支付成功参数\n    checkPaymentSuccessFromUrl() {\n      const urlParams = new URLSearchParams(window.location.search)\n      const paymentSuccess = urlParams.get('paymentSuccess')\n      const orderId = urlParams.get('orderId')\n\n      if (paymentSuccess === 'true' && orderId) {\n        console.log('🎉 检测到支付成功回调 - 订单号:', orderId)\n\n        if (orderId.startsWith('RECHARGE_')) {\n          // 充值成功\n          this.$message.success('充值成功！您可以继续选择套餐')\n        } else if (orderId.startsWith('MEMBERSHIP_')) {\n          // 会员购买成功\n          this.$message.success('会员购买成功！权益已生效')\n          // 延迟刷新页面以更新会员状态\n          setTimeout(() => {\n            window.location.reload()\n          }, 2000)\n        }\n\n        // 清除URL参数，避免重复提示\n        const newUrl = window.location.pathname\n        window.history.replaceState({}, document.title, newUrl)\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["Membership.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "Membership.vue", "sourceRoot": "src/views/website/membership", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"membership-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">订阅会员</h1>\n        <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n      </div>\n\n      <!-- 快速充值模块 -->\n      <section class=\"recharge-section\">\n        <div class=\"container\">\n          <QuickRecharge @recharge-success=\"handleRechargeSuccess\" />\n        </div>\n      </section>\n\n      <!-- 会员套餐区域 -->\n      <section class=\"plans-section\">\n        <div class=\"container\">\n          <div class=\"plans-grid\">\n            <div \n              v-for=\"plan in plans\" \n              :key=\"plan.id\"\n              class=\"plan-card\"\n              :class=\"{ 'featured': plan.featured }\"\n            >\n              <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n              <div class=\"plan-header\">\n                <h3 class=\"plan-name\">{{ plan.name }}</h3>\n                <div class=\"plan-price\">\n                  <!-- 原价显示 -->\n                  <div v-if=\"plan.originalPrice\" class=\"original-price\">\n                    <span class=\"original-price-text\">原价：¥{{ plan.originalPrice }}</span>\n                    <span class=\"discount-badge\">{{ plan.discountText }}</span>\n                  </div>\n                  <!-- 现价显示 -->\n                  <div class=\"current-price\">\n                    <span class=\"price-symbol\">¥</span>\n                    <span class=\"price-amount\">{{ plan.price }}</span>\n                    <span class=\"price-period\">/{{ plan.period }}</span>\n                  </div>\n                  <!-- 立省金额 -->\n                  <div v-if=\"plan.saveAmount\" class=\"save-amount\">\n                    立省¥{{ plan.saveAmount }}\n                  </div>\n                </div>\n                <p class=\"plan-description\">{{ plan.description }}</p>\n              </div>\n              \n              <div class=\"plan-features\">\n                <div\n                  v-for=\"feature in plan.features\"\n                  :key=\"feature.text || feature\"\n                  class=\"feature-item\"\n                  :class=\"{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }\"\n                >\n                  <a-icon\n                    :type=\"feature.disabled ? 'close' : 'check'\"\n                    :class=\"{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }\"\n                  />\n                  <span class=\"feature-text\" v-html=\"feature.text || feature\"></span>\n                  <span v-if=\"feature.exclusive\" class=\"exclusive-badge\">专属</span>\n                  <span v-if=\"feature.disabled\" class=\"disabled-text\">（SVIP专享）</span>\n                </div>\n              </div>\n              \n              <button\n                class=\"btn-subscribe\"\n                :class=\"{ 'featured': plan.featured }\"\n                @click=\"handleSubscribe(plan)\"\n              >\n                {{ getButtonText(plan) }}\n              </button>\n\n              <!-- VIP升级SVIP的特殊提示 -->\n              <div v-if=\"showUpgradeNotice(plan)\" class=\"upgrade-notice\">\n                <i class=\"icon-info\"></i>\n                <span>{{ getUpgradeNoticeText(plan) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n\n    <!-- 支付方式选择弹窗 -->\n    <a-modal\n      title=\"选择支付方式\"\n      :visible=\"showPaymentModal\"\n      @cancel=\"showPaymentModal = false\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"payment-modal-content\">\n        <div class=\"order-info\">\n          <h4>订单信息</h4>\n          <div class=\"order-details\">\n            <div class=\"order-item\">\n              <span>套餐名称：</span>\n              <span>{{ selectedPlan ? selectedPlan.name : '' }}</span>\n            </div>\n            <div class=\"order-item\">\n              <span>支付金额：</span>\n              <span class=\"amount\">¥{{ currentOrderAmount }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"payment-methods\">\n          <h4>支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n            <a-radio-button value=\"alipay-qr\">\n              <i class=\"anticon anticon-qrcode\"></i>\n              支付宝扫码\n            </a-radio-button>\n            <a-radio-button value=\"alipay-page\">\n              <i class=\"anticon anticon-alipay\"></i>\n              支付宝网页\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n\n        <div class=\"payment-actions\">\n          <a-button\n            type=\"primary\"\n            size=\"large\"\n            :loading=\"paymentLoading\"\n            @click=\"handlePayment\"\n            block\n          >\n            确认支付\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 支付二维码弹窗 -->\n    <a-modal\n      title=\"扫码支付\"\n      :visible=\"showQrModal\"\n      @cancel=\"closeQrModal\"\n      :footer=\"null\"\n      width=\"400px\"\n    >\n      <div class=\"qr-payment-content\">\n        <div class=\"qr-code-container\">\n          <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n            <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n          </div>\n          <div v-else class=\"qr-loading\">\n            <a-spin size=\"large\" />\n            <p>正在生成二维码...</p>\n          </div>\n        </div>\n        <div class=\"qr-info\">\n          <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n          <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n        </div>\n        <div class=\"qr-status\">\n          <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n            检查支付状态\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport QuickRecharge from '@/components/QuickRecharge.vue'\nimport { createMembershipOrder, payOrder } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { getCurrentUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage,\n    QuickRecharge\n  },\n  data() {\n    return {\n      // 支付相关\n      paymentLoading: false,\n      showPaymentModal: false,\n      selectedPlan: null,\n      selectedPaymentMethod: 'alipay-qr',\n\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null,\n      checkingStatus: false,\n\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          originalPrice: '39',\n          discountText: '限时7.4折',\n          saveAmount: '10',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          originalPrice: '468',\n          discountText: '限时6.4折',\n          saveAmount: '170',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          originalPrice: '999',\n          discountText: '限时4.9折',\n          saveAmount: '510',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            { text: '解锁<strong class=\"highlight\">全部课程</strong>', disabled: false, exclusive: true },\n            { text: '插件<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '邀请<strong class=\"highlight\">奖励50%</strong>', disabled: false, exclusive: true },\n            { text: '调用工作流<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '复制<strong class=\"highlight\">所有工作流</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">解锁流媒体转换</strong>', disabled: false, exclusive: true },\n            { text: '部分<strong class=\"highlight\">插件免费</strong>', disabled: false, exclusive: true }\n          ],\n          buttonText: '立即购买',\n          featured: true\n        }\n      ]\n    }\n  },\n\n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n\n  mounted() {\n    console.log('Membership页面已挂载')\n    this.checkPaymentSuccessFromUrl()\n  },\n\n  methods: {\n    // 获取按钮文本（续费/升级/购买）\n    getButtonText(plan) {\n      const userRole = this.getUserRole() // 获取用户当前角色\n      const planRole = this.getPlanRole(plan.id) // 获取套餐对应角色\n\n      // 管理员显示\"立即购买\"（管理员不需要购买会员，但可以测试）\n      if (userRole === 'admin') {\n        return '立即购买'\n      }\n\n      // 相同等级显示\"续费会员\"\n      if (userRole === planRole) {\n        return '续费会员'\n      }\n      // 普通用户购买任何会员都显示\"立即购买\"\n      else if (userRole === 'user') {\n        return '立即购买'\n      }\n      // 会员用户购买更高等级显示\"升级会员\"\n      else if (this.isUpgrade(userRole, planRole)) {\n        return '升级会员'\n      }\n      // 其他情况显示\"立即购买\"\n      else {\n        return '立即购买'\n      }\n    },\n\n    // 获取套餐对应的角色\n    getPlanRole(planId) {\n      const roleMap = {\n        1: 'VIP',     // VIP月卡\n        2: 'VIP',     // VIP年卡\n        3: 'SVIP'     // SVIP年卡\n      }\n      return roleMap[planId] || 'user'\n    },\n\n    // 判断是否为升级\n    isUpgrade(currentRole, targetRole) {\n      const roleLevel = {\n        'user': 1,\n        'VIP': 2,\n        'SVIP': 3,\n        'admin': 999  // 管理员级别最高\n      }\n      return roleLevel[targetRole] > (roleLevel[currentRole] || 0)\n    },\n\n    // 是否显示升级提示\n    showUpgradeNotice(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      // 只有VIP升级到SVIP时显示特殊提示\n      return userRole === 'VIP' && planRole === 'SVIP'\n    },\n\n    // 获取升级提示文本\n    getUpgradeNoticeText(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      if (userRole === 'VIP' && planRole === 'SVIP') {\n        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~'\n      }\n\n      return ''\n    },\n\n    // 获取用户当前角色（统一角色获取逻辑）\n    getUserRole() {\n      try {\n        // 使用roleUtils获取角色\n        let role = getCurrentUserRole()\n\n        console.log('🔍 Membership: getUserRole - 从roleUtils获取角色:', role)\n\n        // 角色映射：将中文角色名转换为英文角色代码\n        const roleMap = {\n          // 中文角色名映射\n          '普通用户': 'user',\n          'VIP会员': 'VIP',\n          'SVIP会员': 'SVIP',\n          '管理员': 'admin',\n          // 英文角色代码（保持不变）\n          'user': 'user',\n          'VIP': 'VIP',\n          'SVIP': 'SVIP',\n          'admin': 'admin',\n          // 大小写兼容\n          'USER': 'user',\n          'vip': 'VIP',\n          'svip': 'SVIP',\n          'ADMIN': 'admin'\n        }\n\n        // 映射角色\n        const mappedRole = roleMap[role] || 'user'\n\n        console.log('🔍 Membership: getUserRole - 映射后的角色:', mappedRole)\n        return mappedRole\n\n      } catch (error) {\n        console.warn('🔍 Membership: 获取用户角色失败:', error)\n        return 'user'  // 出错时默认为普通用户\n      }\n    },\n\n    async handleSubscribe(plan) {\n      console.log('选择购买套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$message.warning('请先登录后再购买会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        // 准备会员订单数据（与充值订单结构保持一致）\n        const orderData = {\n          membershipLevel: plan.id,\n          duration: plan.name.includes('月') ? 1 : 12, // 根据套餐名称判断时长\n          amount: parseFloat(plan.price),\n          planName: plan.name,\n          paymentMethod: 'alipay' // 默认支付宝，与充值保持一致\n        }\n\n        console.log('🎯 创建会员订单:', orderData)\n        this.$message.loading('正在创建订单...', 0)\n\n        // 创建会员订单（使用与充值相同的交易记录系统）\n        const response = await createMembershipOrder(orderData)\n        this.$message.destroy()\n\n        if (response.success) {\n          const orderResult = response.result\n          console.log('🎯 会员订单创建成功:', orderResult)\n\n          // 显示支付选择弹窗\n          this.selectedPlan = plan\n          this.currentOrderId = orderResult.orderId\n          this.currentOrderAmount = orderResult.amount\n          this.showPaymentModal = true\n\n        } else {\n          this.$message.error(response.message || '创建订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('创建会员订单失败:', error)\n        this.$message.error('创建订单失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付\n    async handlePayment() {\n      if (!this.currentOrderId) {\n        this.$message.error('订单信息错误')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        if (this.selectedPaymentMethod === 'alipay-page') {\n          await this.handleAlipayPagePayment()\n        } else if (this.selectedPaymentMethod === 'alipay-qr') {\n          await this.handleAlipayQrPayment()\n        }\n\n        this.showPaymentModal = false\n      } catch (error) {\n        console.error('支付处理失败:', error)\n        this.$message.error('支付失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付宝网页支付（与充值功能完全一致）\n    async handleAlipayPagePayment() {\n      try {\n        console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝网页支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n\n    // 处理支付宝扫码支付（与充值功能完全一致）\n    async handleAlipayQrPayment() {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode)\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n\n    // 显示二维码弹窗\n    showQrCodeModal(qrCode) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`\n      this.showQrModal = true\n\n      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount)\n      console.log('🔍 二维码URL:', this.qrCodeUrl)\n\n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n\n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n\n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n\n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n\n    // 检查支付状态（与充值功能完全一致）\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        this.checkingStatus = true\n        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        console.log('🔍 支付状态查询响应:', response)\n\n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          console.log('🔍 会员购买支付成功！')\n          this.$message.success('支付成功！会员权益已生效')\n          this.closeQrModal()\n\n          // 留在当前membership页面，刷新用户状态\n          setTimeout(() => {\n            this.$message.success('会员购买成功！页面即将刷新以更新会员状态')\n            // 刷新当前页面以更新会员状态和按钮显示\n            window.location.reload()\n          }, 2000)\n        } else {\n          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    },\n\n    // 处理充值成功事件\n    handleRechargeSuccess() {\n      this.$message.success('充值成功！您可以继续选择套餐')\n      // 可以在这里添加其他逻辑，比如刷新用户信息等\n    },\n\n    // 检查URL中的支付成功参数\n    checkPaymentSuccessFromUrl() {\n      const urlParams = new URLSearchParams(window.location.search)\n      const paymentSuccess = urlParams.get('paymentSuccess')\n      const orderId = urlParams.get('orderId')\n\n      if (paymentSuccess === 'true' && orderId) {\n        console.log('🎉 检测到支付成功回调 - 订单号:', orderId)\n\n        if (orderId.startsWith('RECHARGE_')) {\n          // 充值成功\n          this.$message.success('充值成功！您可以继续选择套餐')\n        } else if (orderId.startsWith('MEMBERSHIP_')) {\n          // 会员购买成功\n          this.$message.success('会员购买成功！权益已生效')\n          // 延迟刷新页面以更新会员状态\n          setTimeout(() => {\n            window.location.reload()\n          }, 2000)\n        }\n\n        // 清除URL参数，避免重复提示\n        const newUrl = window.location.pathname\n        window.history.replaceState({}, document.title, newUrl)\n      }\n    }\n  }\n}\n</script>\n\n<style>\n.membership-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 充值模块样式 */\n.recharge-section {\n  margin-bottom: 3rem;\n}\n\n.recharge-section .container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 会员套餐区域 */\n.plans-section {\n  padding: 1rem 0 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.plan-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.plan-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.plan-card.featured {\n  border-color: #3b82f6;\n  transform: scale(1.05);\n}\n\n.plan-badge {\n  position: absolute;\n  top: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1.5rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.plan-name {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.plan-price {\n  text-align: center;\n  margin-bottom: 1rem;\n}\n\n/* 原价显示 */\n.original-price {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.original-price-text {\n  font-size: 0.9rem;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);\n}\n\n/* 现价显示 */\n.current-price {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin: 0.5rem 0;\n}\n\n/* 立省金额 */\n.save-amount {\n  font-size: 0.9rem;\n  color: #27ae60;\n  font-weight: bold;\n  margin-top: 0.3rem;\n}\n\n.price-symbol {\n  font-size: 1.2rem;\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.price-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #3b82f6;\n  margin: 0 0.25rem;\n}\n\n.price-period {\n  font-size: 1rem;\n  color: #64748b;\n}\n\n.plan-description {\n  color: #64748b;\n  margin: 0;\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #1e293b;\n  transition: all 0.3s ease;\n  padding: 0.5rem 0;\n}\n\n.feature-item .anticon {\n  color: #10b981;\n  font-weight: bold;\n  flex-shrink: 0;\n}\n\n/* 禁用功能样式 */\n.feature-item.feature-disabled {\n  color: #94a3b8;\n  opacity: 0.6;\n}\n\n.feature-item.feature-disabled .anticon {\n  color: #ef4444;\n}\n\n.feature-item.feature-disabled .icon-disabled {\n  color: #ef4444;\n}\n\n.disabled-text {\n  font-size: 0.8rem;\n  color: #ef4444;\n  margin-left: auto;\n}\n\n/* 专属功能样式 */\n.feature-item.feature-exclusive {\n  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);\n  padding: 0.5rem;\n  border-radius: 8px;\n  border-left: 3px solid #0ea5e9;\n}\n\n.feature-item.feature-exclusive .anticon {\n  color: #0ea5e9;\n}\n\n.feature-item.feature-exclusive .icon-exclusive {\n  color: #0ea5e9;\n}\n\n.exclusive-badge {\n  background: linear-gradient(135deg, #0ea5e9, #0284c7);\n  color: white;\n  padding: 0.1rem 0.4rem;\n  border-radius: 8px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  margin-left: auto;\n  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);\n}\n\n.feature-text {\n  flex: 1;\n  font-size: 0.95rem;\n  line-height: 1.5;\n  font-weight: 500;\n  color: inherit;\n}\n\n/* 确保highlight样式不被覆盖 */\n.feature-text .highlight {\n  color: transparent !important;\n}\n\n/* 重点亮点样式 */\n.highlight {\n  color: #ff6b35 !important;\n  font-weight: bold !important;\n  font-size: 1em !important;\n  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;\n  -webkit-background-clip: text !important;\n  -webkit-text-fill-color: transparent !important;\n  background-clip: text !important;\n  display: inline !important;\n}\n\n/* 兼容性备用方案 */\n@supports not (-webkit-background-clip: text) {\n  .highlight {\n    color: #ff6b35 !important;\n    text-shadow: 0 2px 4px rgba(255, 107, 53, 0.4) !important;\n    -webkit-text-fill-color: initial !important;\n  }\n}\n\n.btn-subscribe {\n  width: 100%;\n  padding: 1rem;\n  background: transparent;\n  border: 2px solid #3b82f6;\n  color: #3b82f6;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 扫光特效 */\n.btn-subscribe::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.8s ease;\n  z-index: 1;\n}\n\n.btn-subscribe:hover::after {\n  left: 100%;\n}\n\n.btn-subscribe:hover {\n  background: #3b82f6;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.btn-subscribe.featured {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border: none;\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  animation: borderGlow 3s ease-in-out infinite;\n}\n\n/* 推荐套餐的斜向扫光特效 */\n.btn-subscribe.featured::after {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -150%;\n  width: 200%;\n  height: 200%;\n  background: linear-gradient(45deg,\n    transparent 30%,\n    rgba(240, 248, 255, 0.4) 45%,\n    rgba(255, 255, 255, 0.6) 50%,\n    rgba(240, 248, 255, 0.4) 55%,\n    transparent 70%\n  );\n  animation: diagonalSweep 3.5s ease-in-out infinite;\n  z-index: 1;\n  transform: rotate(-10deg);\n}\n\n/* 鼠标悬停时的扫光效果 */\n.btn-subscribe.featured:hover::after {\n  animation: hoverSweepIn 0.6s ease-out forwards;\n}\n\n/* 鼠标离开时先扫回来，然后延迟恢复自动动画 */\n.btn-subscribe.featured::after {\n  animation: diagonalSweep 3.5s ease-in-out infinite;\n}\n\n.btn-subscribe.featured:not(:hover)::after {\n  animation: hoverSweepOut 0.6s ease-in forwards, diagonalSweep 3.5s ease-in-out 2s infinite;\n}\n\n.btn-subscribe.featured:hover {\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), 0 0 15px rgba(139, 92, 246, 0.3);\n  transform: translateY(-2px);\n}\n\n/* 升级提示样式 */\n.upgrade-notice {\n  margin-top: 12px;\n  padding: 8px 12px;\n  background: rgba(255, 193, 7, 0.1);\n  border: 1px solid rgba(255, 193, 7, 0.3);\n  border-radius: 6px;\n  font-size: 12px;\n  color: #856404;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.upgrade-notice .icon-info {\n  width: 14px;\n  height: 14px;\n  background: #ffc107;\n  border-radius: 50%;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.upgrade-notice .icon-info::before {\n  content: 'i';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 10px;\n  font-weight: bold;\n  font-style: normal;\n}\n\n.upgrade-notice span {\n  line-height: 1.4;\n}\n\n/* 边框微光动画 */\n@keyframes borderGlow {\n  0%, 100% {\n    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);\n  }\n}\n\n/* 斜向来回扫光动画 */\n@keyframes diagonalSweep {\n  0% {\n    left: -150%;\n  }\n  25% {\n    left: 100%;\n  }\n  50% {\n    left: 100%;\n  }\n  75% {\n    left: -150%;\n  }\n  100% {\n    left: -150%;\n  }\n}\n\n/* 鼠标悬停时扫光进入动画 */\n@keyframes hoverSweepIn {\n  0% {\n    left: -150%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n\n/* 鼠标离开时扫光退出动画 */\n@keyframes hoverSweepOut {\n  0% {\n    left: 100%;\n  }\n  100% {\n    left: -150%;\n  }\n}\n\n/* 支付弹窗样式 */\n.payment-modal-content {\n  padding: 1rem 0;\n}\n\n.payment-modal-content h4 {\n  margin-bottom: 1rem;\n  color: #1f2937;\n  font-weight: 600;\n}\n\n.order-info {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #f9fafb;\n  border-radius: 8px;\n}\n\n.order-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.order-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.order-item .amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.payment-actions {\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n/* 二维码支付样式 */\n.qr-payment-content {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code img {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.qr-loading {\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.qr-info {\n  margin-bottom: 1.5rem;\n}\n\n.qr-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-bottom: 0.5rem;\n}\n\n.qr-tip {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card.featured {\n    transform: none;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  .order-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n}\n</style>\n"]}]}