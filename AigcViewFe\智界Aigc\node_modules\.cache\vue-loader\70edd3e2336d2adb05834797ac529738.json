{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753812959940}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WebsitePage from '@/components/website/WebsitePage.vue'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          originalPrice: '39',\n          discountText: '限时7.4折',\n          saveAmount: '10',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即订阅',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          originalPrice: '468',\n          discountText: '限时6.4折',\n          saveAmount: '170',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即订阅',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          originalPrice: '999',\n          discountText: '限时4.9折',\n          saveAmount: '510',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            { text: '解锁<strong class=\"highlight\">全部课程</strong>', disabled: false, exclusive: true },\n            { text: '插件<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '邀请<strong class=\"highlight\">奖励50%</strong>', disabled: false, exclusive: true },\n            { text: '调用工作流<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '复制<strong class=\"highlight\">所有工作流</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">解锁流媒体转换</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">插件免费</strong>', disabled: false, exclusive: true }\n          ],\n          buttonText: '立即订阅',\n          featured: true\n        }\n      ]\n    }\n  },\n  methods: {\n    handleSubscribe(plan) {\n      console.log('选择订阅套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get('ACCESS_TOKEN')\n      if (!token) {\n        this.$message.warning('请先登录后再订阅会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      // 显示订阅功能提示\n      this.$message.info(`您选择了 ${plan.name}，会员订阅功能即将上线，敬请期待！`)\n    }\n  }\n}\n", {"version": 3, "sources": ["Membership.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Membership.vue", "sourceRoot": "src/views/website/membership", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"membership-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">订阅会员</h1>\n        <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n      </div>\n\n      <!-- 会员套餐区域 -->\n      <section class=\"plans-section\">\n        <div class=\"container\">\n          <div class=\"plans-grid\">\n            <div \n              v-for=\"plan in plans\" \n              :key=\"plan.id\"\n              class=\"plan-card\"\n              :class=\"{ 'featured': plan.featured }\"\n            >\n              <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n              <div class=\"plan-header\">\n                <h3 class=\"plan-name\">{{ plan.name }}</h3>\n                <div class=\"plan-price\">\n                  <!-- 原价显示 -->\n                  <div v-if=\"plan.originalPrice\" class=\"original-price\">\n                    <span class=\"original-price-text\">原价：¥{{ plan.originalPrice }}</span>\n                    <span class=\"discount-badge\">{{ plan.discountText }}</span>\n                  </div>\n                  <!-- 现价显示 -->\n                  <div class=\"current-price\">\n                    <span class=\"price-symbol\">¥</span>\n                    <span class=\"price-amount\">{{ plan.price }}</span>\n                    <span class=\"price-period\">/{{ plan.period }}</span>\n                  </div>\n                  <!-- 立省金额 -->\n                  <div v-if=\"plan.saveAmount\" class=\"save-amount\">\n                    立省¥{{ plan.saveAmount }}\n                  </div>\n                </div>\n                <p class=\"plan-description\">{{ plan.description }}</p>\n              </div>\n              \n              <div class=\"plan-features\">\n                <div\n                  v-for=\"feature in plan.features\"\n                  :key=\"feature.text || feature\"\n                  class=\"feature-item\"\n                  :class=\"{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }\"\n                >\n                  <a-icon\n                    :type=\"feature.disabled ? 'close' : 'check'\"\n                    :class=\"{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }\"\n                  />\n                  <span class=\"feature-text\" v-html=\"feature.text || feature\"></span>\n                  <span v-if=\"feature.exclusive\" class=\"exclusive-badge\">专属</span>\n                  <span v-if=\"feature.disabled\" class=\"disabled-text\">（SVIP专享）</span>\n                </div>\n              </div>\n              \n              <button \n                class=\"btn-subscribe\"\n                :class=\"{ 'featured': plan.featured }\"\n                @click=\"handleSubscribe(plan)\"\n              >\n                {{ plan.buttonText }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          originalPrice: '39',\n          discountText: '限时7.4折',\n          saveAmount: '10',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即订阅',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          originalPrice: '468',\n          discountText: '限时6.4折',\n          saveAmount: '170',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即订阅',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          originalPrice: '999',\n          discountText: '限时4.9折',\n          saveAmount: '510',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            { text: '解锁<strong class=\"highlight\">全部课程</strong>', disabled: false, exclusive: true },\n            { text: '插件<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '邀请<strong class=\"highlight\">奖励50%</strong>', disabled: false, exclusive: true },\n            { text: '调用工作流<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '复制<strong class=\"highlight\">所有工作流</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">解锁流媒体转换</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">插件免费</strong>', disabled: false, exclusive: true }\n          ],\n          buttonText: '立即订阅',\n          featured: true\n        }\n      ]\n    }\n  },\n  methods: {\n    handleSubscribe(plan) {\n      console.log('选择订阅套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get('ACCESS_TOKEN')\n      if (!token) {\n        this.$message.warning('请先登录后再订阅会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      // 显示订阅功能提示\n      this.$message.info(`您选择了 ${plan.name}，会员订阅功能即将上线，敬请期待！`)\n    }\n  }\n}\n</script>\n\n<style>\n.membership-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 会员套餐区域 */\n.plans-section {\n  padding: 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.plan-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.plan-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.plan-card.featured {\n  border-color: #3b82f6;\n  transform: scale(1.05);\n}\n\n.plan-badge {\n  position: absolute;\n  top: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1.5rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.plan-name {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.plan-price {\n  text-align: center;\n  margin-bottom: 1rem;\n}\n\n/* 原价显示 */\n.original-price {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.original-price-text {\n  font-size: 0.9rem;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);\n}\n\n/* 现价显示 */\n.current-price {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin: 0.5rem 0;\n}\n\n/* 立省金额 */\n.save-amount {\n  font-size: 0.9rem;\n  color: #27ae60;\n  font-weight: bold;\n  margin-top: 0.3rem;\n}\n\n.price-symbol {\n  font-size: 1.2rem;\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.price-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #3b82f6;\n  margin: 0 0.25rem;\n}\n\n.price-period {\n  font-size: 1rem;\n  color: #64748b;\n}\n\n.plan-description {\n  color: #64748b;\n  margin: 0;\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #1e293b;\n  transition: all 0.3s ease;\n  padding: 0.5rem 0;\n}\n\n.feature-item .anticon {\n  color: #10b981;\n  font-weight: bold;\n  flex-shrink: 0;\n}\n\n/* 禁用功能样式 */\n.feature-item.feature-disabled {\n  color: #94a3b8;\n  opacity: 0.6;\n}\n\n.feature-item.feature-disabled .anticon {\n  color: #ef4444;\n}\n\n.feature-item.feature-disabled .icon-disabled {\n  color: #ef4444;\n}\n\n.disabled-text {\n  font-size: 0.8rem;\n  color: #ef4444;\n  margin-left: auto;\n}\n\n/* 专属功能样式 */\n.feature-item.feature-exclusive {\n  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);\n  padding: 0.5rem;\n  border-radius: 8px;\n  border-left: 3px solid #0ea5e9;\n}\n\n.feature-item.feature-exclusive .anticon {\n  color: #0ea5e9;\n}\n\n.feature-item.feature-exclusive .icon-exclusive {\n  color: #0ea5e9;\n}\n\n.exclusive-badge {\n  background: linear-gradient(135deg, #0ea5e9, #0284c7);\n  color: white;\n  padding: 0.1rem 0.4rem;\n  border-radius: 8px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  margin-left: auto;\n  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);\n}\n\n.feature-text {\n  flex: 1;\n  font-size: 0.95rem;\n  line-height: 1.5;\n  font-weight: 500;\n  color: inherit;\n}\n\n/* 确保highlight样式不被覆盖 */\n.feature-text .highlight {\n  color: transparent !important;\n}\n\n/* 重点亮点样式 */\n.highlight {\n  color: #ff6b35 !important;\n  font-weight: bold !important;\n  font-size: 1em !important;\n  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;\n  -webkit-background-clip: text !important;\n  -webkit-text-fill-color: transparent !important;\n  background-clip: text !important;\n  display: inline !important;\n}\n\n/* 兼容性备用方案 */\n@supports not (-webkit-background-clip: text) {\n  .highlight {\n    color: #ff6b35 !important;\n    text-shadow: 0 2px 4px rgba(255, 107, 53, 0.4) !important;\n    -webkit-text-fill-color: initial !important;\n  }\n}\n\n.btn-subscribe {\n  width: 100%;\n  padding: 1rem;\n  background: transparent;\n  border: 2px solid #3b82f6;\n  color: #3b82f6;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-subscribe:hover {\n  background: #3b82f6;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.btn-subscribe.featured {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border: none;\n}\n\n.btn-subscribe.featured:hover {\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card.featured {\n    transform: none;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n}\n</style>\n"]}]}