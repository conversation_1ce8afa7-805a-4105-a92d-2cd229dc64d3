{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue", "mtime": 1753835250707}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { mixinDevice } from '@/utils/mixin.js';\nimport { getSmsCaptcha } from '@/api/login';\nimport { getAction, postAction } from '@/api/manage';\nimport { checkOnlyUser } from '@/api/api';\nimport { getReferralCode } from '@/utils/referralUtils';\nvar levelNames = {\n  0: '低',\n  1: '低',\n  2: '中',\n  3: '强'\n};\nvar levelClass = {\n  0: 'error',\n  1: 'error',\n  2: 'warning',\n  3: 'success'\n};\nvar levelColor = {\n  0: '#ff0000',\n  1: '#ff0000',\n  2: '#ff7e05',\n  3: '#52c41a'\n};\nexport default {\n  name: \"Register\",\n  components: {},\n  mixins: [mixinDevice],\n  data: function data() {\n    return {\n      model: {},\n      validatorRules: {\n        username: [{\n          required: false\n        }, {\n          validator: this.checkUsername\n        }],\n        password: [{\n          required: false\n        }, {\n          validator: this.handlePasswordLevel\n        }],\n        password2: [{\n          required: false\n        }, {\n          validator: this.handlePasswordCheck\n        }],\n        mobile: [{\n          required: false\n        }, {\n          validator: this.handlePhoneCheck\n        }],\n        captcha: [{\n          required: false\n        }, {\n          validator: this.handleCaptchaCheck\n        }]\n      },\n      state: {\n        time: 60,\n        smsSendBtn: false,\n        passwordLevel: 0,\n        passwordLevelChecked: false,\n        percent: 10,\n        progressColor: '#FF0000'\n      },\n      registerBtn: false\n    };\n  },\n  computed: {\n    passwordLevelClass: function passwordLevelClass() {\n      return levelClass[this.state.passwordLevel];\n    },\n    passwordLevelName: function passwordLevelName() {\n      return levelNames[this.state.passwordLevel];\n    },\n    passwordLevelColor: function passwordLevelColor() {\n      return levelColor[this.state.passwordLevel];\n    }\n  },\n  methods: {\n    checkUsername: function checkUsername(rule, value, callback) {\n      if (!value) {\n        callback(new Error(\"请输入用户名\"));\n      } else {\n        var params = {\n          username: value\n        };\n        checkOnlyUser(params).then(function (res) {\n          if (res.success) {\n            callback();\n          } else {\n            callback(\"用户名已存在!\");\n          }\n        });\n      }\n    },\n    handleEmailCheck: function handleEmailCheck(rule, value, callback) {\n      var params = {\n        email: value\n      };\n      checkOnlyUser(params).then(function (res) {\n        if (res.success) {\n          callback();\n        } else {\n          callback(\"邮箱已存在!\");\n        }\n      });\n    },\n    handlePasswordLevel: function handlePasswordLevel(rule, value, callback) {\n      var level = 0;\n      var reg = /^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[~!@#$%^&*()_+`\\-={}:\";'<>?,./]).{8,}$/;\n\n      if (!reg.test(value)) {\n        callback(new Error('密码由8位数字、大小写字母和特殊符号组成!'));\n      } // 判断这个字符串中有没有数字\n\n\n      if (/[0-9]/.test(value)) {\n        level++;\n      } // 判断字符串中有没有字母\n\n\n      if (/[a-zA-Z]/.test(value)) {\n        level++;\n      } // 判断字符串中有没有特殊符号\n\n\n      if (/[^0-9a-zA-Z_]/.test(value)) {\n        level++;\n      }\n\n      this.state.passwordLevel = level;\n      this.state.percent = level * 30;\n\n      if (level >= 2) {\n        if (level >= 3) {\n          this.state.percent = 100;\n        }\n\n        callback();\n      } else {\n        if (level === 0) {\n          this.state.percent = 10;\n        }\n\n        callback(new Error('密码强度不够'));\n      }\n    },\n    handlePasswordCheck: function handlePasswordCheck(rule, value, callback) {\n      var password = this.model['password']; //console.log('value', value)\n\n      if (value === undefined) {\n        callback(new Error('请输入密码'));\n      }\n\n      if (value && password && value.trim() !== password.trim()) {\n        callback(new Error('两次密码不一致'));\n      }\n\n      callback();\n    },\n    handleCaptchaCheck: function handleCaptchaCheck(rule, value, callback) {\n      if (!value) {\n        callback(new Error(\"请输入验证码\"));\n      } else {\n        callback();\n      }\n    },\n    handlePhoneCheck: function handlePhoneCheck(rule, value, callback) {\n      var reg = /^1[3456789]\\d{9}$/;\n\n      if (!reg.test(value)) {\n        callback(new Error(\"请输入正确手机号\"));\n      } else {\n        var params = {\n          phone: value\n        };\n        checkOnlyUser(params).then(function (res) {\n          if (res.success) {\n            callback();\n          } else {\n            callback(\"手机号已存在!\");\n          }\n        });\n      }\n    },\n    handlePasswordInputClick: function handlePasswordInputClick() {\n      if (!this.isMobile()) {\n        this.state.passwordLevelChecked = true;\n        return;\n      }\n\n      this.state.passwordLevelChecked = false;\n    },\n    handleSubmit: function handleSubmit() {\n      var _this = this;\n\n      this.$refs['form'].validate(function (success) {\n        if (success == true) {\n          var values = _this.model; // 获取邀请人信息\n\n          var referralCode = getReferralCode();\n          var register = {\n            username: values.username,\n            password: values.password,\n            phone: values.mobile,\n            smscode: values.captcha,\n            inviteCode: referralCode // 添加邀请人信息\n\n          };\n          console.log('🔗 注册提交，邀请码:', referralCode);\n          postAction(\"/sys/user/register\", register).then(function (res) {\n            if (!res.success) {\n              _this.registerFailed(res.message);\n            } else {\n              _this.$router.push({\n                name: 'registerResult',\n                params: _objectSpread({}, values)\n              });\n            }\n          });\n        }\n      });\n    },\n    getCaptcha: function getCaptcha(e) {\n      var _this2 = this;\n\n      e.preventDefault();\n      var that = this;\n      this.$refs['form'].validateField(['mobile'], function (err) {\n        if (!err) {\n          _this2.state.smsSendBtn = true;\n          var interval = window.setInterval(function () {\n            if (that.state.time-- <= 0) {\n              that.state.time = 60;\n              that.state.smsSendBtn = false;\n              window.clearInterval(interval);\n            }\n          }, 1000);\n\n          var hide = _this2.$message.loading('验证码发送中..', 3);\n\n          var params = {\n            mobile: _this2.model.mobile,\n            smsmode: \"1\"\n          };\n          postAction(\"/sys/sms\", params).then(function (res) {\n            if (!res.success) {\n              _this2.registerFailed(res.message);\n\n              setTimeout(hide, 0);\n            }\n\n            setTimeout(hide, 500);\n          }).catch(function (err) {\n            setTimeout(hide, 1);\n            clearInterval(interval);\n            that.state.time = 60;\n            that.state.smsSendBtn = false;\n\n            _this2.requestFailed(err);\n          });\n        }\n      });\n    },\n    registerFailed: function registerFailed(message) {\n      this.$notification['error']({\n        message: \"注册失败\",\n        description: message,\n        duration: 2\n      });\n    },\n    requestFailed: function requestFailed(err) {\n      this.$notification['error']({\n        message: '错误',\n        description: ((err.response || {}).data || {}).message || \"请求出现错误，请稍后再试\",\n        duration: 4\n      });\n      this.registerBtn = false;\n    }\n  },\n  watch: {\n    'state.passwordLevel': function statePasswordLevel(val) {\n      console.log(val);\n    }\n  }\n};", null]}