(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~af8298de"],{"02fe":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"affiliate-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("邀请奖励")]),a("p",{staticClass:"simple-subtitle"},[t._v("邀请好友注册智界AIGC，获得丰厚奖励")]),a("div",{staticClass:"commission-badge"},[a("span",{staticClass:"badge-text"},[t._v("当前奖励比例："+t._s(t.currentCommissionRate)+"%")]),a("span",{staticClass:"badge-level"},[t._v(t._s(t.commissionLevelText))])])]),a("section",{staticClass:"affiliate-section"},[a("div",{staticClass:"container"},[a("div",{staticClass:"promotion-link-section"},[a("h2",{staticClass:"section-title"},[t._v("您的专属邀请链接")]),a("div",{staticClass:"link-main-container"},[a("div",{staticClass:"link-input-large"},[a("a-input",{attrs:{value:t.affiliateLink||"正在生成邀请链接...",readonly:"",loading:t.loading,size:"large",placeholder:"邀请链接生成中..."}})],1),a("div",{staticClass:"link-actions"},[a("a-button",{staticClass:"copy-btn",attrs:{type:"primary",size:"large",disabled:!t.affiliateLink||t.loading},on:{click:t.copyLink}},[a("a-icon",{attrs:{type:"copy"}}),t._v("\n                复制链接\n              ")],1),a("a-button",{staticClass:"qr-btn",attrs:{size:"large",loading:t.qrLoading},on:{click:t.generateQRCode}},[a("a-icon",{attrs:{type:"qrcode"}}),t._v("\n                邀请二维码\n              ")],1)],1)]),a("div",{staticClass:"link-tips"},[a("a-icon",{attrs:{type:"info-circle"}}),t._v("\n            分享此链接，您将获得好友付费的 "),a("strong",[t._v(t._s(t.currentCommissionRate)+"%")]),t._v(" 奖励\n          ")],1)]),a("div",{staticClass:"earnings-dashboard"},[a("h2",{staticClass:"section-title"},[t._v("收益概览")]),a("div",{staticClass:"earnings-grid"},[a("div",{staticClass:"earning-card primary"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"dollar"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v("¥"+t._s(t.formatNumber(t.totalEarnings)))]),a("div",{staticClass:"earning-label"},[t._v("累计收益")])])],1)]),a("div",{staticClass:"earning-card success"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"wallet"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v("¥"+t._s(t.formatNumber(t.availableEarnings)))]),a("div",{staticClass:"earning-label"},[t._v("可提现金额")])]),a("div",{staticClass:"card-action"},[a("a-button",{attrs:{type:"primary",size:"small",disabled:t.availableEarnings<=0||t.loading},on:{click:t.openWithdrawModal}},[t._v("\n                    立即提现\n                  ")])],1)],1)]),a("div",{staticClass:"earning-card info"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"team"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v(t._s(Math.floor(t.totalReferrals)))]),a("div",{staticClass:"earning-label"},[t._v("邀请注册人数")])])],1)]),a("div",{staticClass:"earning-card warning"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"crown"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v(t._s(Math.floor(t.memberReferrals)))]),a("div",{staticClass:"earning-label"},[t._v("转化人数")])])],1)])])]),a("div",{staticClass:"commission-progress"},[a("h2",{staticClass:"section-title"},[t._v("奖励等级进度")]),a("div",{staticClass:"progress-card"},[a("div",{staticClass:"level-timeline-horizontal"},t._l(t.commissionLevels,(function(e,i){return a("div",{key:i,staticClass:"level-step-horizontal",class:{current:e.isCurrent,completed:e.isCompleted,upcoming:e.isUpcoming}},[a("div",{staticClass:"step-circle-horizontal"},[e.isCompleted?a("a-icon",{attrs:{type:"check"}}):e.isCurrent?a("span",{staticClass:"current-dot"}):a("span",{staticClass:"step-number"},[t._v(t._s(i+1))])],1),a("div",{staticClass:"step-content-horizontal"},[a("div",{staticClass:"step-title"},[t._v(t._s(e.name))]),a("div",{staticClass:"step-rate"},[t._v(t._s(e.rate)+"%")]),a("div",{staticClass:"step-requirement"},[t._v(t._s(e.requirement)+"人")]),e.remaining>0?a("div",{staticClass:"step-remaining"},[t._v("\n                    还需"+t._s(e.remaining)+"个\n                  ")]):e.isCompleted?a("div",{staticClass:"step-completed"},[t._v("\n                    已达成\n                  ")]):t._e()]),i<t.commissionLevels.length-1?a("div",{staticClass:"step-line-horizontal"}):t._e()])})),0)])]),a("div",{staticClass:"commission-rules"},[a("h2",{staticClass:"section-title"},[t._v("奖励规则")]),a("div",{staticClass:"rules-table"},[a("div",{staticClass:"rule-row header"},[a("div",{staticClass:"rule-cell"},[t._v("用户等级")]),a("div",{staticClass:"rule-cell"},[t._v("邀请人数要求")]),a("div",{staticClass:"rule-cell"},[t._v("奖励比例")]),a("div",{staticClass:"rule-cell"},[t._v("说明")])]),a("a-spin",{attrs:{spinning:t.loading,size:"small"}},t._l(t.allLevelConfigs,(function(e){return a("div",{key:e.id,staticClass:"rule-row",class:{vip:"VIP"===e.role_code,svip:"SVIP"===e.role_code}},[a("div",{staticClass:"rule-cell"},[t._v(t._s(t.getRoleDisplayName(e.role_code)))]),a("div",{staticClass:"rule-cell"},[t._v(t._s(t.getRequirementText(e)))]),a("div",{staticClass:"rule-cell highlight"},[t._v(t._s(e.commission_rate)+"%")]),a("div",{staticClass:"rule-cell"},[t._v(t._s(e.level_name))])])})),0)],1)]),a("div",{staticClass:"referral-users"},[a("h2",{staticClass:"section-title"},[t._v("我的邀请用户")]),a("div",{staticClass:"users-table-container"},[a("a-table",{attrs:{columns:t.userColumns,"data-source":t.referralUsers,loading:t.usersLoading,pagination:t.usersPagination,size:"middle"},on:{change:t.handleUsersTableChange},scopedSlots:t._u([{key:"avatar",fn:function(e,i){return[a("a-avatar",{style:{backgroundColor:"#87d068"},attrs:{src:t.getAvatarUrl(i.avatar)}},[t._v("\n                  "+t._s(i.nickname?i.nickname.charAt(0):"U")+"\n                ")])]}},{key:"reward",fn:function(e){return[a("span",{staticClass:"reward-amount"},[t._v("¥"+t._s(e||"0.00"))])]}}])})],1)]),a("div",{staticClass:"withdraw-records"},[a("h2",{staticClass:"section-title"},[t._v("提现记录")]),a("div",{staticClass:"filter-section",staticStyle:{"margin-bottom":"16px",padding:"16px",background:"#fafafa","border-radius":"6px"}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:"提现金额"}},[a("a-input-group",{attrs:{compact:""}},[a("a-input-number",{staticStyle:{width:"50%"},attrs:{placeholder:"最小金额",min:0,precision:2},model:{value:t.withdrawFilter.minAmount,callback:function(e){t.$set(t.withdrawFilter,"minAmount",e)},expression:"withdrawFilter.minAmount"}}),a("a-input-number",{staticStyle:{width:"50%"},attrs:{placeholder:"最大金额",min:0,precision:2},model:{value:t.withdrawFilter.maxAmount,callback:function(e){t.$set(t.withdrawFilter,"maxAmount",e)},expression:"withdrawFilter.maxAmount"}})],1)],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:"申请时间"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",placeholder:"['开始日期', '结束日期']"},model:{value:t.withdrawFilter.dateRange,callback:function(e){t.$set(t.withdrawFilter,"dateRange",e)},expression:"withdrawFilter.dateRange"}})],1)],1),a("a-col",{attrs:{span:4}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择状态"},model:{value:t.withdrawFilter.status,callback:function(e){t.$set(t.withdrawFilter,"status",e)},expression:"withdrawFilter.status"}},[a("a-select-option",{attrs:{value:null}},[t._v("全部")]),a("a-select-option",{attrs:{value:1}},[t._v("待审核")]),a("a-select-option",{attrs:{value:2}},[t._v("已发放")]),a("a-select-option",{attrs:{value:3}},[t._v("审核拒绝")]),a("a-select-option",{attrs:{value:4}},[t._v("已取消")])],1)],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:"完成时间"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",placeholder:"['开始日期', '结束日期']"},model:{value:t.withdrawFilter.completeDateRange,callback:function(e){t.$set(t.withdrawFilter,"completeDateRange",e)},expression:"withdrawFilter.completeDateRange"}})],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:" "}},[a("a-button",{staticStyle:{"margin-right":"8px"},attrs:{type:"primary",loading:t.recordsLoading},on:{click:t.handleWithdrawFilter}},[t._v("\n                    搜索\n                  ")]),a("a-button",{on:{click:t.handleWithdrawReset}},[t._v("重置")])],1)],1)],1)],1),a("div",{staticClass:"records-table-container"},[a("a-table",{attrs:{columns:t.withdrawColumns,"data-source":t.withdrawRecords,loading:t.recordsLoading,pagination:t.withdrawPagination,size:"middle"},on:{change:t.handleWithdrawTableChange},scopedSlots:t._u([{key:"status",fn:function(e){return[a("a-tag",{attrs:{color:t.getStatusColor(e)}},[t._v("\n                  "+t._s(e)+"\n                ")])]}},{key:"amount",fn:function(e){return[a("span",{staticClass:"withdraw-amount"},[t._v("¥"+t._s(e))])]}},{key:"action",fn:function(e,i){return[1===i.rawStatus?a("a-button",{attrs:{type:"danger",size:"small",loading:t.cancelLoading},on:{click:function(e){return t.handleCancelWithdraw(i)}}},[t._v("\n                  取消提现\n                ")]):a("span",[t._v("-")])]}}])})],1)])])]),a("a-modal",{attrs:{title:"邀请二维码",footer:null,width:"400px",centered:""},model:{value:t.showQRModal,callback:function(e){t.showQRModal=e},expression:"showQRModal"}},[a("div",{staticClass:"qr-modal-content"},[t.qrCodeUrl?a("div",{staticClass:"qr-code-container"},[a("img",{staticClass:"qr-code-image",attrs:{src:t.qrCodeUrl,alt:"邀请二维码"}})]):t._e(),a("div",{staticClass:"qr-actions"},[t.qrCodeUrl?a("a-button",{attrs:{type:"primary",block:""},on:{click:t.downloadQRCode}},[a("a-icon",{attrs:{type:"download"}}),t._v("\n            下载二维码\n          ")],1):t._e()],1)])]),a("a-modal",{attrs:{title:"申请提现",footer:null,width:"500px",centered:""},model:{value:t.showWithdrawModal,callback:function(e){t.showWithdrawModal=e},expression:"showWithdrawModal"}},[a("div",{staticClass:"withdraw-modal-content"},[a("div",{staticClass:"withdraw-info"},[a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("可提现金额：")]),a("span",{staticClass:"info-value"},[t._v("¥"+t._s(t.formatNumber(t.availableEarnings)))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("最低提现金额：")]),a("span",{staticClass:"info-value"},[t._v("¥50.00")])])]),a("a-form",{attrs:{form:t.withdrawForm},on:{submit:t.handleWithdraw}},[a("a-form-item",{attrs:{label:"提现金额"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["amount",{rules:[{required:!0,message:"请输入提现金额"},{type:"number",min:50,message:"最低提现金额为50元"},{type:"number",max:t.availableEarnings,message:"提现金额不能超过可提现金额"}]}],expression:"['amount', {\n                rules: [\n                  { required: true, message: '请输入提现金额' },\n                  { type: 'number', min: 50, message: '最低提现金额为50元' },\n                  { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                ]\n              }]"}],staticStyle:{width:"100%"},attrs:{min:50,max:t.availableEarnings,precision:2,placeholder:"请输入提现金额"}},[a("template",{slot:"addonAfter"},[t._v("元")])],2)],1),a("a-form-item",{attrs:{label:"提现方式"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["method",{rules:[{required:!0,message:"请选择提现方式"}],initialValue:"alipay"}],expression:"['method', {\n                rules: [{ required: true, message: '请选择提现方式' }],\n                initialValue: 'alipay'\n              }]"}],attrs:{placeholder:"请选择提现方式",disabled:""}},[a("a-select-option",{attrs:{value:"alipay"}},[t._v("支付宝")])],1)],1),a("a-form-item",{attrs:{label:"支付宝手机号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["alipayAccount",{rules:[{required:!0,message:"请输入支付宝手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式"}]}],expression:"['alipayAccount', {\n                rules: [\n                  { required: true, message: '请输入支付宝手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式' }\n                ]\n              }]"}],attrs:{placeholder:"请输入支付宝手机号"}})],1),a("a-form-item",{attrs:{label:"收款人真实姓名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["realName",{rules:[{required:!0,message:"请输入收款人真实姓名"},{pattern:/^[\u4e00-\u9fa5]{2,4}$/,message:"请输入正确的中文姓名（2-4个汉字）"}]}],expression:"['realName', {\n                rules: [\n                  { required: true, message: '请输入收款人真实姓名' },\n                  { pattern: /^[\\u4e00-\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\n                ]\n              }]"}],attrs:{placeholder:"请输入收款人真实姓名"}})],1)],1),a("div",{staticClass:"withdraw-actions"},[a("a-button",{staticStyle:{"margin-right":"8px"},on:{click:function(e){t.showWithdrawModal=!1}}},[t._v("\n            取消\n          ")]),a("a-button",{attrs:{type:"primary",loading:t.withdrawLoading},on:{click:t.handleWithdraw}},[t._v("\n            申请提现\n          ")])],1)],1)])],1)])},s=[],r=a("a34a"),n=a.n(r),o=a("df7c"),c=a("77ea"),l=a("9fb0"),d=a("2b0e");function u(t,e,a,i,s,r,n){try{var o=t[r](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var r=t.apply(e,a);function n(t){u(r,i,s,n,o,"next",t)}function o(t){u(r,i,s,n,o,"throw",t)}n(void 0)}))}}var m={name:"Affiliate",components:{WebsitePage:o["default"]},data:function(){return{loading:!0,qrLoading:!1,totalEarnings:0,availableEarnings:0,totalReferrals:0,memberReferrals:0,affiliateLink:"",userRole:"user",currentCommissionRate:30,commissionLevelText:"新手邀请员",levelProgress:0,nextLevelRequirement:10,nextLevelText:"高级邀请员",nextLevelRate:40,progressColor:"#1890ff",commissionLevels:[],allLevelConfigs:[],showQRModal:!1,qrCodeUrl:"",qrPreGenerated:!1,showWithdrawModal:!1,withdrawLoading:!1,withdrawForm:this.$form.createForm(this),referralUsers:[],usersLoading:!1,usersPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")}},usersSort:{orderBy:"total_reward",order:"desc"},defaultAvatar:"/default-avatar.png",withdrawRecords:[],recordsLoading:!1,cancelLoading:!1,withdrawPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")}},withdrawSort:{orderBy:"apply_time",order:"desc"},withdrawFilter:{minAmount:null,maxAmount:null,status:null,dateRange:[],completeDateRange:[]},userInfo:null}},computed:{userColumns:function(){return[{title:"头像",dataIndex:"avatar",key:"avatar",scopedSlots:{customRender:"avatar"},width:80},{title:"用户昵称",dataIndex:"nickname",key:"nickname",sorter:!0},{title:"注册时间",dataIndex:"registerTime",key:"registerTime",sorter:!0},{title:"获得奖励",dataIndex:"reward",key:"reward",scopedSlots:{customRender:"reward"},sorter:!0}]},withdrawColumns:function(){return[{title:"提现金额",dataIndex:"amount",key:"amount",scopedSlots:{customRender:"amount"},sorter:!0},{title:"提现方式",dataIndex:"method",key:"method"},{title:"申请时间",dataIndex:"applyTime",key:"applyTime",sorter:!0},{title:"状态",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"},sorter:!0},{title:"完成时间",dataIndex:"completeTime",key:"completeTime",sorter:!0},{title:"操作",key:"action",width:100,scopedSlots:{customRender:"action"}}]}},mounted:function(){var t=h(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.checkLoginAndLoadData();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{getAvatarUrl:function(t){return t?t.startsWith("http://")||t.startsWith("https://")?t:this.getFileAccessHttpUrl(t)||this.defaultAvatar:this.defaultAvatar},getFileAccessHttpUrl:function(t){if(!t)return this.defaultAvatar;if(t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("uploads/"))return window.getFileAccessHttpUrl?window.getFileAccessHttpUrl(t):t;var e=this.$store.state.app.staticDomainURL;return e?"".concat(e,"/").concat(t):t},loadDefaultAvatar:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/sys/common/default-avatar-url");case 3:e=t.sent,e&&e.success&&e.result&&(this.defaultAvatar=e.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),checkLoginAndLoadData:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=d["default"].ls.get(l["a"]),e){t.next=4;break}return this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),t.abrupt("return");case 4:return t.prev=4,t.next=7,Promise.all([this.loadReferralData(),this.loadReferralLink(),this.loadUserRole(),this.loadLevelConfig(),this.loadReferralUsers(),this.loadWithdrawRecords(),this.loadDefaultAvatar()]);case 7:this.calculateCommissionLevel(),this.preGenerateQRCode(),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](4),this.$notification.error({message:"加载失败",description:"获取分销数据失败，请稍后重试",placement:"topRight"});case 15:return t.prev=15,this.loading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[4,11,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralData:function(){var t=h(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["o"])();case 3:e=t.sent,e.success&&(a=e.result,this.totalEarnings=a.total_reward_amount||0,this.availableEarnings=a.available_rewards||0,this.totalReferrals=a.total_referrals||0,this.memberReferrals=a.member_referrals||0),t.next=11;break;case 7:throw t.prev=7,t.t0=t["catch"](0),t.t0;case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralLink:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["g"])({});case 3:e=t.sent,e.success&&(this.affiliateLink=e.result||""),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.affiliateLink="".concat(window.location.origin,"?ref=loading...");case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadUserRole:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["w"])();case 3:e=t.sent,e.success&&(this.userRole=e.result.role_code||"user"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.userRole="user";case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadLevelConfig:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["k"])();case 3:e=t.sent,e.success&&(this.allLevelConfigs=e.result||[]),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.allLevelConfigs=[];case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),calculateCommissionLevel:function(){var t=this,e=this.memberReferrals,a=this.allLevelConfigs.filter((function(e){return e.role_code===t.userRole}));if(0!==a.length){for(var i=null,s=a.length-1;s>=0;s--)if(e>=a[s].min_referrals){i=a[s];break}i||(i=a[0]),this.currentCommissionRate=parseFloat(i.commission_rate),this.commissionLevelText=i.level_name;var r=a.find((function(t){return t.min_referrals>e}));r?(this.nextLevelRequirement=r.min_referrals,this.nextLevelText=r.level_name,this.nextLevelRate=parseFloat(r.commission_rate),this.levelProgress=e/r.min_referrals*100,this.progressColor="#1890ff"):(this.nextLevelRequirement=0,this.nextLevelText="已达最高等级",this.nextLevelRate=this.currentCommissionRate,this.levelProgress=100,this.progressColor="#722ed1"),this.commissionLevels=a.map((function(t,i){var s=e>=t.min_referrals,r=!1;if(!s)if(0===i)r=!0;else{var n=a[i-1].min_referrals;r=e>=n}var o=!s&&!r,c=0;return s||(c=t.min_referrals-e),{name:t.level_name,rate:parseFloat(t.commission_rate),requirement:t.min_referrals,isCompleted:s,isCurrent:r,isUpcoming:o,remaining:c>0?c:0}}))}},copyLink:function(){var t=this;this.affiliateLink?navigator.clipboard.writeText(this.affiliateLink).then((function(){t.$notification.success({message:"邀请链接已复制",description:"邀请链接已成功复制到剪贴板，快去分享给好友吧！",placement:"topRight",duration:3,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})})).catch((function(){t.$notification.error({message:"复制失败",description:"复制邀请链接失败，请手动复制",placement:"topRight"})})):this.$notification.warning({message:"邀请链接未生成",description:"邀请链接正在生成中，请稍后再试",placement:"topRight"})},preGenerateQRCode:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.affiliateLink&&!this.qrPreGenerated){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=6,this.$http.post("/api/usercenter/generateReferralQRCode",null,{params:{url:this.affiliateLink}});case 6:e=t.sent,e&&e.success&&(this.qrCodeUrl=e.result,this.qrPreGenerated=!0),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2);case 13:case"end":return t.stop()}}),t,this,[[2,10]])})));function e(){return t.apply(this,arguments)}return e}(),generateQRCode:function(){var t=h(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.affiliateLink){t.next=3;break}return this.$notification.warning({message:"邀请链接未生成",description:"请等待邀请链接生成完成后再生成二维码",placement:"topRight"}),t.abrupt("return");case 3:if(!this.qrPreGenerated||!this.qrCodeUrl){t.next=6;break}return this.showQRModal=!0,t.abrupt("return");case 6:return t.prev=6,this.qrLoading=!0,t.next=10,this.$http.post("/api/usercenter/generateReferralQRCode",null,{params:{url:this.affiliateLink}});case 10:if(e=t.sent,!e||!e.success){t.next=19;break}this.qrCodeUrl=e.result,this.qrPreGenerated=!0,this.showQRModal=!0,this.$notification.success({message:"二维码生成成功",description:"邀请二维码已生成并存储到CDN，可以下载保存",placement:"topRight"}),t.next=21;break;case 19:throw a=e&&e.message||"生成失败",new Error(a);case 21:t.next=27;break;case 23:t.prev=23,t.t0=t["catch"](6),this.$notification.error({message:"生成失败",description:t.t0.message||"二维码生成失败，请稍后重试",placement:"topRight"});case 27:return t.prev=27,this.qrLoading=!1,t.finish(27);case 30:case"end":return t.stop()}}),t,this,[[6,23,27,30]])})));function e(){return t.apply(this,arguments)}return e}(),downloadQRCode:function(){if(this.qrCodeUrl)try{var t=this.extractReferralCode(this.affiliateLink),e="https://www.aigcview.com/jeecg-boot",a="".concat(e,"/api/usercenter/downloadReferralQRCode?url=").concat(encodeURIComponent(this.qrCodeUrl),"&code=").concat(t,"&t=").concat(Date.now()),i=document.createElement("iframe");i.style.display="none",i.style.position="absolute",i.style.left="-9999px",i.src=a,document.body.appendChild(i),setTimeout((function(){i.parentNode&&document.body.removeChild(i)}),3e3),this.$notification.success({message:"下载开始",description:"邀请二维码_".concat(t,".png 正在下载"),placement:"topRight"})}catch(s){this.$notification.error({message:"下载失败",description:"二维码下载失败，请稍后重试",placement:"topRight"})}},extractReferralCode:function(t){if(!t)return"UNKNOWN";try{var e=new URL(t),a=e.searchParams.get("ref");return a||"UNKNOWN"}catch(i){return"UNKNOWN"}},openWithdrawModal:function(){this.availableEarnings<50?this.$notification.warning({message:"提现金额不足",description:"最低提现金额为50元，请继续邀请获得更多收益",placement:"topRight"}):this.showWithdrawModal=!0},handleWithdraw:function(){var t=h(n.a.mark((function t(){var e=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.withdrawForm.validateFields(function(){var t=h(n.a.mark((function t(a,i){var s;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=2;break}return t.abrupt("return");case 2:s=e.$createElement,e.$confirm({title:"确认提现申请",content:s("div",{style:{margin:"16px 0"}},[s("p",{style:{marginBottom:"8px"}},[s("strong","提现金额："),"¥".concat(i.amount)]),s("p",{style:{marginBottom:"8px"}},[s("strong","支付宝账号："),i.alipayAccount]),s("p",{style:{marginBottom:"8px"}},[s("strong","收款人姓名："),i.realName]),s("p",{style:{color:"#ff4d4f",marginTop:"12px",marginBottom:"0"}},[s("strong","注意："),"请再次核实一遍提现信息，请确认信息无误！"])]),okText:"确认提现",cancelText:"取消",centered:!0,width:400,onOk:function(){var t=h(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.submitWithdrawRequest(i);case 2:case"end":return t.stop()}}),t)})));function a(){return t.apply(this,arguments)}return a}()});case 4:case"end":return t.stop()}}),t)})));return function(e,a){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),submitWithdrawRequest:function(){var t=h(n.a.mark((function t(e){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.withdrawLoading=!0,t.prev=1,a={withdrawalAmount:e.amount,realName:e.realName,alipayAccount:e.alipayAccount},t.next=5,this.$http.post("/api/usercenter/applyWithdrawal",a);case 5:if(i=t.sent,!i.success){t.next=15;break}return this.withdrawLoading=!1,this.showWithdrawModal=!1,this.withdrawForm.resetFields(),this.$notification.success({message:"提现申请成功",description:"您的提现申请已提交，预计1-3个工作日到账",placement:"topRight"}),t.next=13,Promise.all([this.loadReferralData(),this.loadWithdrawRecords()]);case 13:t.next=17;break;case 15:this.withdrawLoading=!1,this.$notification.error({message:"提现申请失败",description:i.message||"申请失败，请重试",placement:"topRight"});case 17:t.next=24;break;case 19:t.prev=19,t.t0=t["catch"](1),this.withdrawLoading=!1,t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$notification.error({message:"提现申请失败",description:t.t0.response.data.message,placement:"topRight"}):t.t0.message?this.$notification.error({message:"提现申请失败",description:t.t0.message,placement:"topRight"}):this.$notification.error({message:"提现申请失败",description:"网络错误，请稍后重试",placement:"topRight"});case 24:case"end":return t.stop()}}),t,this,[[1,19]])})));function e(e){return t.apply(this,arguments)}return e}(),loadReferralUsers:function(){var t=h(n.a.mark((function t(){var e,a,i,s;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.usersLoading=!0,e={current:this.usersPagination&&this.usersPagination.current||1,size:this.usersPagination&&this.usersPagination.pageSize||10,orderBy:this.usersSort&&this.usersSort.orderBy||"total_reward",order:this.usersSort&&this.usersSort.order||"desc"},t.next=6,this.$http.get("/api/usercenter/referralList",{params:e});case 6:a=t.sent,a&&a.success?(i=a.result||{},s=i.records||[],this.usersPagination&&(this.usersPagination.total=i.total||0),this.referralUsers=s.map((function(t,e){return{key:t.id||e,nickname:t.referee_nickname||"用户***".concat(e+1),avatar:t.referee_avatar||"",registerTime:t.register_time||"",reward:t.total_reward||"0.00"}}))):(this.referralUsers=[],this.usersPagination&&(this.usersPagination.total=0)),t.next=15;break;case 10:t.prev=10,t.t0=t["catch"](0),this.referralUsers=[],t.t0.response&&401===t.t0.response.status&&this.$message.warning("登录已过期，请重新登录");case 15:return t.prev=15,this.usersLoading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[0,10,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),loadWithdrawRecords:function(){var t=h(n.a.mark((function t(){var e,a,i,s,r=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.recordsLoading=!0,e={current:this.withdrawPagination&&this.withdrawPagination.current||1,size:this.withdrawPagination&&this.withdrawPagination.pageSize||10,orderBy:this.withdrawSort&&this.withdrawSort.orderBy||"apply_time",order:this.withdrawSort&&this.withdrawSort.order||"desc"},this.withdrawFilter&&(null!==this.withdrawFilter.minAmount&&""!==this.withdrawFilter.minAmount&&(e.minAmount=this.withdrawFilter.minAmount),null!==this.withdrawFilter.maxAmount&&""!==this.withdrawFilter.maxAmount&&(e.maxAmount=this.withdrawFilter.maxAmount),null!==this.withdrawFilter.status&&(e.status=this.withdrawFilter.status),this.withdrawFilter.dateRange&&2===this.withdrawFilter.dateRange.length&&(e.startDate=this.withdrawFilter.dateRange[0].format?this.withdrawFilter.dateRange[0].format("YYYY-MM-DD"):this.withdrawFilter.dateRange[0],e.endDate=this.withdrawFilter.dateRange[1].format?this.withdrawFilter.dateRange[1].format("YYYY-MM-DD"):this.withdrawFilter.dateRange[1]),this.withdrawFilter.completeDateRange&&2===this.withdrawFilter.completeDateRange.length&&(e.completeStartDate=this.withdrawFilter.completeDateRange[0].format?this.withdrawFilter.completeDateRange[0].format("YYYY-MM-DD"):this.withdrawFilter.completeDateRange[0],e.completeEndDate=this.withdrawFilter.completeDateRange[1].format?this.withdrawFilter.completeDateRange[1].format("YYYY-MM-DD"):this.withdrawFilter.completeDateRange[1])),t.next=8,this.$http.get("/api/usercenter/withdrawalHistory",{params:e});case 8:a=t.sent,a&&a.success?(i=a.result||{},s=i.records||[],this.withdrawPagination&&(this.withdrawPagination.total=i.total||0),this.withdrawRecords=s.map((function(t,e){return{key:t.id||e,id:t.id,amount:t.withdrawal_amount||"0.00",method:t.withdrawalMethod||"支付宝",applyTime:t.apply_time||"",status:r.getWithdrawStatusText(t.status,t.review_remark),rawStatus:t.status,completeTime:t.review_time||"-"}}))):(this.withdrawRecords=[],this.withdrawPagination&&(this.withdrawPagination.total=0)),t.next=17;break;case 12:t.prev=12,t.t0=t["catch"](0),this.withdrawRecords=[],t.t0.response&&401===t.t0.response.status&&this.$message.warning("登录已过期，请重新登录");case 17:return t.prev=17,this.recordsLoading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[0,12,17,20]])})));function e(){return t.apply(this,arguments)}return e}(),getWithdrawStatusText:function(t,e){var a={0:"待审核",1:"待审核",2:"已完成",3:"已拒绝",4:"已取消"},i=a[t]||"未知状态";return 3===t&&e&&(i+="（".concat(e,"）")),i},getStatusColor:function(t){if(t.includes("已拒绝"))return"red";var e={"已完成":"green","处理中":"blue","待审核":"orange","已取消":"gray"};return e[t]||"default"},getSortOrder:function(t){return this.usersSort&&this.usersSort.orderBy===t?"asc"===this.usersSort.order?"ascend":"descend":null},handleUsersTableChange:function(t,e,a){if(this.usersPagination&&t&&(this.usersPagination.current=t.current||1,this.usersPagination.pageSize=t.pageSize||10),a&&a.field&&this.usersSort){var i={nickname:"nickname",registerTime:"register_time",reward:"total_reward"},s=i[a.field]||"total_reward";this.usersSort.orderBy===s?this.usersSort.order="asc"===this.usersSort.order?"desc":"asc":(this.usersSort.orderBy=s,this.usersSort.order="total_reward"===s?"desc":"asc"),this.usersPagination&&(this.usersPagination.current=1)}this.loadReferralUsers()},handleWithdrawTableChange:function(t,e,a){if(this.withdrawPagination&&t&&(this.withdrawPagination.current=t.current||1,this.withdrawPagination.pageSize=t.pageSize||10),a&&a.field&&this.withdrawSort){var i={amount:"withdrawal_amount",applyTime:"apply_time",status:"status",completeTime:"review_time"},s=i[a.field]||"apply_time";this.withdrawSort.orderBy===s?this.withdrawSort.order="asc"===this.withdrawSort.order?"desc":"asc":(this.withdrawSort.orderBy=s,this.withdrawSort.order="apply_time"===s||"withdrawal_amount"===s?"desc":"asc"),this.withdrawPagination&&(this.withdrawPagination.current=1)}this.loadWithdrawRecords()},handleWithdrawFilter:function(){this.withdrawPagination&&(this.withdrawPagination.current=1),this.loadWithdrawRecords()},handleWithdrawReset:function(){this.withdrawFilter={minAmount:null,maxAmount:null,status:null,dateRange:[],completeDateRange:[]},this.withdrawPagination&&(this.withdrawPagination.current=1),this.loadWithdrawRecords()},handleCancelWithdraw:function(t){var e=this,a=this.$createElement;this.$confirm({title:"确认取消提现",content:a("div",{style:{margin:"16px 0"}},[a("p",{style:{marginBottom:"8px"}},[a("strong","提现金额："),"¥".concat(t.amount)]),a("p",{style:{marginBottom:"8px"}},[a("strong","申请时间："),t.applyTime]),a("p",{style:{color:"#ff4d4f",marginTop:"12px",marginBottom:"0"}},[a("strong","注意："),"取消后金额将返还到可提现余额，此操作不可撤销！"])]),okText:"确认取消",okType:"danger",cancelText:"返回",centered:!0,width:400,onOk:function(){var a=h(n.a.mark((function a(){return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.confirmCancelWithdraw(t);case 2:case"end":return a.stop()}}),a)})));function i(){return a.apply(this,arguments)}return i}()})},confirmCancelWithdraw:function(){var t=h(n.a.mark((function t(e){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.cancelLoading=!0,t.prev=1,a={withdrawalId:e.id},t.next=5,this.$http.post("/api/usercenter/cancelWithdrawal",a);case 5:if(i=t.sent,!i.success){t.next=12;break}return this.$notification.success({message:"取消成功",description:"提现申请已取消，金额已返还到可提现余额",placement:"topRight"}),t.next=10,Promise.all([this.loadReferralData(),this.loadWithdrawRecords()]);case 10:t.next=13;break;case 12:this.$notification.error({message:"取消失败",description:i.message||"取消提现失败，请重试",placement:"topRight"});case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](1),t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$notification.error({message:"取消失败",description:t.t0.response.data.message,placement:"topRight"}):t.t0.message?this.$notification.error({message:"取消失败",description:t.t0.message,placement:"topRight"}):this.$notification.error({message:"取消失败",description:"网络错误，请稍后重试",placement:"topRight"});case 19:return t.prev=19,this.cancelLoading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[1,15,19,22]])})));function e(e){return t.apply(this,arguments)}return e}(),formatNumber:function(t){if(null===t||void 0===t)return"0";var e=parseFloat(t);return isNaN(e)?"0":t===this.totalEarnings?e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):e.toLocaleString("zh-CN")},getRoleDisplayName:function(t){switch(t){case"VIP":return"VIP用户";case"SVIP":return"SVIP用户";case"user":default:return"普通用户"}},getRequirementText:function(t){var e=t.min_referrals,a=t.role_code,i=this.allLevelConfigs.filter((function(t){return t.role_code===a})),s=i.findIndex((function(e){return e.id===t.id})),r=i[s+1];return"SVIP"===a?"无要求":r?0===e?"0-".concat(r.min_referrals-1,"人"):"".concat(e,"-").concat(r.min_referrals-1,"人"):"".concat(e,"人以上")}}},p=m,f=(a("5c696"),a("2877")),w=Object(f["a"])(p,i,s,!1,null,"e0dcc94e",null);e["default"]=w.exports},"04ff":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"jianying-draft-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("剪映小助手")]),a("p",{staticClass:"simple-subtitle"},[t._v("专业的剪映草稿管理工具")])]),a("div",{staticClass:"main-functions"},[a("div",{staticClass:"functions-container"},[a("div",{staticClass:"function-card download-card"},[a("div",{staticClass:"card-header"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"download"}})],1),a("h3",{staticClass:"card-title"},[t._v("下载小助手")]),a("p",{staticClass:"card-description"},[t._v("选择适合您操作系统的版本进行下载")])]),a("div",{staticClass:"download-options"},[a("div",{staticClass:"download-option",class:{downloading:t.isPlatformDownloading("windows"),recommended:"windows"===t.currentOS,disabled:t.isDownloadDisabled("windows")},on:{click:function(e){return t.handleDownload("windows")}}},[a("div",{staticClass:"option-icon"},[a("a-icon",{attrs:{type:"windows"}})],1),a("div",{staticClass:"option-content"},[a("h4",{staticClass:"option-title"},[t._v("\n                  Windows 版本\n                  "),"windows"===t.currentOS?a("span",{staticClass:"recommended-badge"},[t._v("推荐")]):t._e()]),a("p",{staticClass:"option-desc"},[t._v("适用于 Windows 8/10/11")]),t.isPlatformDownloading("windows")?a("p",{staticClass:"download-status"},[t._v("正在获取下载链接...")]):t._e(),t.getCooldownStatus("windows").inCooldown?a("p",{staticClass:"cooldown-status"},[t._v("\n                  请稍后再试 ("+t._s(t.getCooldownStatus("windows").remainingTime)+"s)\n                ")]):t._e()]),a("div",{staticClass:"option-arrow"},[a("a-icon",{attrs:{type:t.isPlatformDownloading("windows")?"loading":"download",spin:t.isPlatformDownloading("windows")}})],1)]),a("div",{staticClass:"download-option",class:{downloading:t.isPlatformDownloading("mac"),recommended:"mac"===t.currentOS,disabled:t.isDownloadDisabled("mac")},on:{click:function(e){return t.handleDownload("mac")}}},[a("div",{staticClass:"option-icon"},[a("a-icon",{attrs:{type:"apple"}})],1),a("div",{staticClass:"option-content"},[a("h4",{staticClass:"option-title"},[t._v("\n                  Mac 版本\n                  "),"mac"===t.currentOS?a("span",{staticClass:"recommended-badge"},[t._v("推荐")]):t._e()]),a("p",{staticClass:"option-desc"},[t._v("适用于 macOS 系统")]),t.isPlatformDownloading("mac")?a("p",{staticClass:"download-status"},[t._v("正在获取下载链接...")]):t._e(),t.getCooldownStatus("mac").inCooldown?a("p",{staticClass:"cooldown-status"},[t._v("\n                  请稍后再试 ("+t._s(t.getCooldownStatus("mac").remainingTime)+"s)\n                ")]):t._e()]),a("div",{staticClass:"option-arrow"},[a("a-icon",{attrs:{type:t.isPlatformDownloading("mac")?"loading":"download",spin:t.isPlatformDownloading("mac")}})],1)])]),a("div",{staticClass:"download-notice"},[a("div",{staticClass:"notice-item"},[a("a-icon",{attrs:{type:"info-circle"}}),a("span",[t._v("下载完成后，请按照安装向导完成安装")])],1)])]),a("div",{staticClass:"function-card draft-card"},[a("div",{staticClass:"card-header"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"file-text"}})],1),a("h3",{staticClass:"card-title"},[t._v("剪映草稿导出")]),a("p",{staticClass:"card-description"},[t._v("复制草稿链接到剪映小助手，一键下载并创建本地草稿文件"),a("br"),t._v("支持链接换行，批量处理多个草稿")])]),a("div",{staticClass:"draft-input-area"},[a("a-textarea",{staticClass:"draft-textarea",attrs:{placeholder:"请粘贴剪映草稿链接...",rows:6},model:{value:t.draftUrl,callback:function(e){t.draftUrl=e},expression:"draftUrl"}}),a("a-button",{staticClass:"copy-btn",attrs:{type:"primary",size:"large",disabled:!t.draftUrl},on:{click:t.handleCopyUrl}},[a("a-icon",{attrs:{type:"copy"}}),t._v("\n              复制草稿链接\n            ")],1)],1)])])]),a("div",{staticClass:"product-intro"},[a("div",{staticClass:"intro-container"},[a("div",{staticClass:"intro-header"},[a("h2",{staticClass:"intro-title"},[t._v("产品介绍")])]),a("div",{staticClass:"intro-content"},[a("div",{staticClass:"intro-card"},[a("div",{staticClass:"intro-icon"},[a("a-icon",{attrs:{type:"tool"}})],1),a("div",{staticClass:"intro-text"},[a("h3",[t._v("剪映小助手")]),a("p",[t._v("专为剪映用户打造的桌面工具，支持草稿文件的快速导入和管理。通过简单的链接分享，让您的创作更加便捷高效。支持Windows和Mac系统，本地处理保障数据安全。")])])])]),a("div",{staticClass:"features-grid"},t._l(t.features,(function(e){return a("div",{key:e.id,staticClass:"feature-item"},[a("div",{staticClass:"feature-icon"},[a("a-icon",{attrs:{type:e.icon}})],1),a("div",{staticClass:"feature-content"},[a("h4",{staticClass:"feature-title"},[t._v(t._s(e.title))]),a("p",{staticClass:"feature-description"},[t._v(t._s(e.description))])])])})),0)])])]),a("a-modal",{attrs:{title:"",footer:null,width:480,centered:!0,maskClosable:!1},model:{value:t.confirmModalVisible,callback:function(e){t.confirmModalVisible=e},expression:"confirmModalVisible"}},[a("div",{staticClass:"confirm-modal-content"},[a("div",{staticClass:"modal-icon"},[a("a-icon",{attrs:{type:"exclamation-circle"}})],1),a("div",{staticClass:"modal-title"},[t._v("系统不匹配提醒")]),a("div",{staticClass:"modal-message"},[t._v("\n        检测到您当前使用的是 "),a("strong",[t._v(t._s(t.confirmModalData.currentOS))]),t._v(" 系统，\n        但您选择下载 "),a("strong",[t._v(t._s(t.confirmModalData.targetOS))]),t._v(" 版本。\n        "),a("br"),a("br"),t._v("\n        确定要继续下载吗？\n      ")]),a("div",{staticClass:"modal-actions"},[a("a-button",{staticClass:"cancel-btn",on:{click:t.cancelDownload}},[t._v("\n          取消\n        ")]),a("a-button",{staticClass:"confirm-btn",attrs:{type:"primary"},on:{click:t.confirmDownload}},[t._v("\n          继续下载\n        ")])],1)])])],1)},s=[],r=a("a34a"),n=a.n(r),o=a("df7c");function c(t,e,a,i,s,r,n){try{var o=t[r](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function l(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var r=t.apply(e,a);function n(t){c(r,i,s,n,o,"next",t)}function o(t){c(r,i,s,n,o,"throw",t)}n(void 0)}))}}var d={name:"JianYingDraft",components:{WebsitePage:o["default"]},data:function(){return{draftUrl:"",downloadingPlatforms:{},currentOS:null,confirmModalVisible:!1,confirmModalData:{},downloadCooldown:{},downloadHistory:{},cooldownTimer:null,features:[{id:1,icon:"cloud-download",title:"草稿文件导入",description:"支持从云端下载剪映草稿文件到本地，快速同步您的创作项目"},{id:2,icon:"folder",title:"文件管理",description:"智能管理下载的草稿文件，自动分类整理，方便查找和使用"},{id:3,icon:"setting",title:"路径配置",description:"灵活设置剪映软件安装路径，确保草稿文件正确导入到剪映编辑器"},{id:4,icon:"safety",title:"本地安全",description:"所有操作在本地进行，保护您的创作内容安全，不会泄露到第三方"},{id:5,icon:"thunderbolt",title:"批量处理",description:"支持批量下载和导入多个草稿文件，大幅提升工作效率"},{id:6,icon:"global",title:"跨平台支持",description:"同时支持Windows和Mac系统，满足不同用户的使用需求"}]}},computed:{isDownloadDisabled:function(){var t=this;return function(e){if(t.downloadingPlatforms[e])return!0;var a=t.checkDownloadCooldown(e);return!!a.inCooldown}},getCooldownStatus:function(){var t=this;return function(e){return t.checkDownloadCooldown(e)}}},mounted:function(){this.checkDraftParam(),this.detectCurrentOS(),this.initializeFromLocalStorage(),this.startCooldownTimer()},beforeDestroy:function(){this.cooldownTimer&&clearInterval(this.cooldownTimer),this.cleanupExpiredData()},methods:{detectCurrentOS:function(){var t=navigator.userAgent.toLowerCase();t.includes("mac")?this.currentOS="mac":t.includes("win")?this.currentOS="windows":t.includes("linux")?this.currentOS="linux":this.currentOS="unknown"},checkDownloadCooldown:function(t){var e=Date.now(),a=45e3,i=this.downloadCooldown[t];if(i&&e-i<a){var s=Math.ceil((a-(e-i))/1e3);return{inCooldown:!0,remainingTime:s}}return{inCooldown:!1,remainingTime:0}},checkDownloadHistory:function(t){var e=this.downloadHistory[t];if(e){var a=Date.now()-e.timestamp,i=Math.floor(a/6e4);if(i<10)return{hasRecent:!0,minutesAgo:i,fileName:e.fileName}}return{hasRecent:!1,minutesAgo:0,fileName:""}},setDownloadCooldown:function(t){var e=Date.now();this.$set(this.downloadCooldown,t,e),this.saveToLocalStorage("download_cooldown",this.downloadCooldown)},recordDownloadHistory:function(t,e){var a={timestamp:Date.now(),fileName:e};this.$set(this.downloadHistory,t,a),this.saveToLocalStorage("download_history",this.downloadHistory)},startCooldownTimer:function(){var t=this;this.cooldownTimer=setInterval((function(){t.$forceUpdate(),Date.now()%1e4<1e3&&t.cleanupExpiredData()}),1e3)},cleanupExpiredData:function(){var t=this,e=Date.now(),a=45e3,i=864e5,s=!1;Object.keys(this.downloadCooldown).forEach((function(i){var r=t.downloadCooldown[i];r&&e-r>=a&&(t.$delete(t.downloadCooldown,i),s=!0)}));var r=!1;Object.keys(this.downloadHistory).forEach((function(a){var s=t.downloadHistory[a];s&&s.timestamp&&e-s.timestamp>=i&&(t.$delete(t.downloadHistory,a),r=!0)})),s&&this.saveToLocalStorage("download_cooldown",this.downloadCooldown),r&&this.saveToLocalStorage("download_history",this.downloadHistory)},saveToLocalStorage:function(t,e){try{localStorage.setItem("jianying_assistant_".concat(t),JSON.stringify(e))}catch(a){}},loadFromLocalStorage:function(t){try{var e=localStorage.getItem("jianying_assistant_".concat(t));return e?JSON.parse(e):null}catch(a){return null}},clearLocalStorage:function(t){try{localStorage.removeItem("jianying_assistant_".concat(t))}catch(e){}},clearAllDownloadData:function(){this.downloadCooldown={},this.downloadHistory={},this.downloadingPlatforms={},this.clearLocalStorage("download_cooldown"),this.clearLocalStorage("download_history"),this.$message.success("已清理所有下载数据")},debugDownloadStatus:function(){var t={downloadingPlatforms:this.downloadingPlatforms,downloadCooldown:this.downloadCooldown,downloadHistory:this.downloadHistory,localStorageCooldown:this.loadFromLocalStorage("download_cooldown"),localStorageHistory:this.loadFromLocalStorage("download_history")};return t},setPlatformDownloading:function(t,e){this.$set(this.downloadingPlatforms,t,e)},isPlatformDownloading:function(t){return this.downloadingPlatforms[t]||!1},initializeFromLocalStorage:function(){var t=this,e=this.loadFromLocalStorage("download_cooldown");if(e){var a=Date.now(),i=45e3;Object.keys(e).forEach((function(s){var r=e[s];r&&a-r<i&&t.$set(t.downloadCooldown,s,r)}))}var s=this.loadFromLocalStorage("download_history");if(s){var r=Date.now(),n=864e5;Object.keys(s).forEach((function(e){var a=s[e];a&&a.timestamp&&r-a.timestamp<n&&t.$set(t.downloadHistory,e,a)}))}},checkDraftParam:function(){var t=this.$route.query.draft;t&&(this.draftUrl=t)},handleDownload:function(){var t=l(n.a.mark((function t(e){var a,i,s,r,o,c;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.isPlatformDownloading(e)){t.next=2;break}return t.abrupt("return");case 2:if(a=this.checkDownloadCooldown(e),!a.inCooldown){t.next=7;break}return i="windows"===e?"Windows":"Mac",this.$message.warning("".concat(i," 版本请稍后再试，还需等待 ").concat(a.remainingTime," 秒")),t.abrupt("return");case 7:if(s=this.checkDownloadHistory(e),s.hasRecent&&(r="windows"===e?"Windows":"Mac",0===s.minutesAgo?this.$message.info("检测到您刚刚下载过 ".concat(r," 版本")):this.$message.info("检测到您 ".concat(s.minutesAgo," 分钟前下载过 ").concat(r," 版本"))),!this.currentOS||this.currentOS===e||"unknown"===this.currentOS){t.next=15;break}return o="mac"===this.currentOS?"Mac":"Windows",c="mac"===e?"Mac":"Windows",this.confirmModalVisible=!0,this.confirmModalData={currentOS:o,targetOS:c,platform:e},t.abrupt("return");case 15:return t.next=17,this.executeDownload(e);case 17:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),confirmDownload:function(){var t=l(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.confirmModalVisible=!1,e=this.confirmModalData.platform,t.next=4,this.executeDownload(e);case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),cancelDownload:function(){this.confirmModalVisible=!1,this.confirmModalData={}},executeDownload:function(){var t=l(n.a.mark((function t(e){var a,i,s,r,o,c;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.setPlatformDownloading(e,!0),this.setDownloadCooldown(e),a={windows:"剪映小助手-智界-Windows.zip",mac:"剪映小助手-智界-Mac.zip"},i=a[e],i){t.next=8;break}return this.$message.error("不支持的平台类型"),t.abrupt("return");case 8:return t.next=10,this.axios.get("".concat(this.API_BASE_URL,"/sys/common/desktop-app-download"),{params:{platform:e}});case 10:s=t.sent,s.success&&s.result?(r=document.createElement("a"),r.href=s.result,r.download=i,r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r),this.recordDownloadHistory(e,i),this.$message.success("开始下载 ".concat("windows"===e?"Windows":"Mac"," 版本"))):this.$message.error(s.message||"获取下载链接失败"),t.next=33;break;case 14:if(t.prev=14,t.t0=t["catch"](0),!t.t0.response){t.next=32;break}o=t.t0.response.status,c=t.t0.response.data&&t.t0.response.data.message||t.t0.response.statusText,t.t1=o,t.next=404===t.t1?23:403===t.t1?25:500===t.t1?27:29;break;case 23:return this.$message.error("安装包文件不存在，请联系管理员"),t.abrupt("break",30);case 25:return this.$message.error("没有权限访问该文件"),t.abrupt("break",30);case 27:return this.$message.error("服务器内部错误，请稍后重试"),t.abrupt("break",30);case 29:this.$message.error("下载失败: ".concat(c));case 30:t.next=33;break;case 32:"NETWORK_ERROR"===t.t0.code||t.t0.message.includes("Network Error")?this.$message.error("网络连接失败，请检查网络后重试"):"TIMEOUT"===t.t0.code?this.$message.error("请求超时，请稍后重试"):this.$message.error("下载失败，请稍后重试");case 33:return t.prev=33,this.setPlatformDownloading(e,!1),t.finish(33);case 36:case"end":return t.stop()}}),t,this,[[0,14,33,36]])})));function e(e){return t.apply(this,arguments)}return e}(),handleCopyUrl:function(){var t=this;this.draftUrl&&(navigator.clipboard?navigator.clipboard.writeText(this.draftUrl).then((function(){t.$message.success("草稿链接已复制，请粘贴到剪映小助手中")})).catch((function(){t.fallbackCopyText(t.draftUrl)})):this.fallbackCopyText(this.draftUrl))},fallbackCopyText:function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select();try{document.execCommand("copy"),this.$message.success("链接已复制到剪贴板")}catch(a){this.$message.error("复制失败，请手动复制")}document.body.removeChild(e)}}},u=d,h=(a("843e"),a("2877")),m=Object(h["a"])(u,i,s,!1,null,"66be1460",null);e["default"]=m.exports},"0fb8":function(t,e,a){},1885:function(t,e,a){},"1b68":function(t,e,a){},"1dae":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"main user-layout-register"},[t._m(0),a("a-form-model",{ref:"form",attrs:{model:t.model,rules:t.validatorRules}},[a("a-form-model-item",{attrs:{prop:"username"}},[a("a-input",{attrs:{size:"large",type:"text",autocomplete:"false",placeholder:"请输入用户名"},model:{value:t.model.username,callback:function(e){t.$set(t.model,"username",e)},expression:"model.username"}})],1),a("a-popover",{attrs:{placement:"rightTop",trigger:"click",visible:t.state.passwordLevelChecked}},[a("template",{slot:"content"},[a("div",{style:{width:"240px"}},[a("div",{class:["user-register",t.passwordLevelClass]},[t._v("强度："),a("span",[t._v(t._s(t.passwordLevelName))])]),a("a-progress",{attrs:{percent:t.state.percent,showInfo:!1,strokeColor:t.passwordLevelColor}}),a("div",{staticStyle:{"margin-top":"10px"}},[a("span",[t._v("请至少输入 8 个字符。请不要使用容易被猜到的密码。")])])],1)]),a("a-form-model-item",{attrs:{prop:"password"}},[a("a-input",{attrs:{size:"large",type:"password",autocomplete:"false",placeholder:"至少8位密码，区分大小写"},on:{click:t.handlePasswordInputClick},model:{value:t.model.password,callback:function(e){t.$set(t.model,"password",e)},expression:"model.password"}})],1)],2),a("a-form-model-item",{attrs:{prop:"password2"}},[a("a-input",{attrs:{size:"large",type:"password",autocomplete:"false",placeholder:"确认密码"},model:{value:t.model.password2,callback:function(e){t.$set(t.model,"password2",e)},expression:"model.password2"}})],1),a("a-form-model-item",{attrs:{prop:"mobile"}},[a("a-input",{attrs:{size:"large",placeholder:"11 位手机号"},model:{value:t.model.mobile,callback:function(e){t.$set(t.model,"mobile",e)},expression:"model.mobile"}},[a("a-select",{attrs:{slot:"addonBefore",size:"large",defaultValue:"+86"},slot:"addonBefore"},[a("a-select-option",{attrs:{value:"+86"}},[t._v("+86")]),a("a-select-option",{attrs:{value:"+87"}},[t._v("+87")])],1)],1)],1),a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:16}},[a("a-form-model-item",{attrs:{prop:"captcha"}},[a("a-input",{attrs:{size:"large",type:"text",placeholder:"验证码"},model:{value:t.model.captcha,callback:function(e){t.$set(t.model,"captcha",e)},expression:"model.captcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{staticClass:"getCaptcha",attrs:{size:"large",disabled:t.state.smsSendBtn},domProps:{textContent:t._s(t.state.smsSendBtn?t.state.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getCaptcha(e)}}})],1)],1),a("a-form-model-item",[a("a-button",{staticClass:"register-button",attrs:{size:"large",type:"primary",htmlType:"submit",loading:t.registerBtn,disabled:t.registerBtn},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[t._v("注册\n      ")]),a("router-link",{staticClass:"login",attrs:{to:{name:"login"}}},[t._v("使用已有账户登录")])],1)],1)],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",[a("span",[t._v("注册")])])}],r=a("ac0d"),n=(a("7ded"),a("0fea")),o=a("4ec3"),c=a("c4f2");function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function d(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?l(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var h={0:"低",1:"低",2:"中",3:"强"},m={0:"error",1:"error",2:"warning",3:"success"},p={0:"#ff0000",1:"#ff0000",2:"#ff7e05",3:"#52c41a"},f={name:"Register",components:{},mixins:[r["b"]],data:function(){return{model:{},validatorRules:{username:[{required:!1},{validator:this.checkUsername}],password:[{required:!1},{validator:this.handlePasswordLevel}],password2:[{required:!1},{validator:this.handlePasswordCheck}],mobile:[{required:!1},{validator:this.handlePhoneCheck}],captcha:[{required:!1},{validator:this.handleCaptchaCheck}]},state:{time:60,smsSendBtn:!1,passwordLevel:0,passwordLevelChecked:!1,percent:10,progressColor:"#FF0000"},registerBtn:!1}},computed:{passwordLevelClass:function(){return m[this.state.passwordLevel]},passwordLevelName:function(){return h[this.state.passwordLevel]},passwordLevelColor:function(){return p[this.state.passwordLevel]}},methods:{checkUsername:function(t,e,a){if(e){var i={username:e};Object(o["h"])(i).then((function(t){t.success?a():a("用户名已存在!")}))}else a(new Error("请输入用户名"))},handleEmailCheck:function(t,e,a){var i={email:e};Object(o["h"])(i).then((function(t){t.success?a():a("邮箱已存在!")}))},handlePasswordLevel:function(t,e,a){var i=0,s=/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/;s.test(e)||a(new Error("密码由8位数字、大小写字母和特殊符号组成!")),/[0-9]/.test(e)&&i++,/[a-zA-Z]/.test(e)&&i++,/[^0-9a-zA-Z_]/.test(e)&&i++,this.state.passwordLevel=i,this.state.percent=30*i,i>=2?(i>=3&&(this.state.percent=100),a()):(0===i&&(this.state.percent=10),a(new Error("密码强度不够")))},handlePasswordCheck:function(t,e,a){var i=this.model["password"];void 0===e&&a(new Error("请输入密码")),e&&i&&e.trim()!==i.trim()&&a(new Error("两次密码不一致")),a()},handleCaptchaCheck:function(t,e,a){e?a():a(new Error("请输入验证码"))},handlePhoneCheck:function(t,e,a){var i=/^1[3456789]\d{9}$/;if(i.test(e)){var s={phone:e};Object(o["h"])(s).then((function(t){t.success?a():a("手机号已存在!")}))}else a(new Error("请输入正确手机号"))},handlePasswordInputClick:function(){this.isMobile()?this.state.passwordLevelChecked=!1:this.state.passwordLevelChecked=!0},handleSubmit:function(){var t=this;this.$refs["form"].validate((function(e){if(1==e){var a=t.model,i=Object(c["a"])(),s={username:a.username,password:a.password,phone:a.mobile,smscode:a.captcha,inviteCode:i};Object(n["i"])("/sys/user/register",s).then((function(e){e.success?t.$router.push({name:"registerResult",params:d({},a)}):t.registerFailed(e.message)}))}}))},getCaptcha:function(t){var e=this;t.preventDefault();var a=this;this.$refs["form"].validateField(["mobile"],(function(t){if(!t){e.state.smsSendBtn=!0;var i=window.setInterval((function(){a.state.time--<=0&&(a.state.time=60,a.state.smsSendBtn=!1,window.clearInterval(i))}),1e3),s=e.$message.loading("验证码发送中..",3),r={mobile:e.model.mobile,smsmode:"1"};Object(n["i"])("/sys/sms",r).then((function(t){t.success||(e.registerFailed(t.message),setTimeout(s,0)),setTimeout(s,500)})).catch((function(t){setTimeout(s,1),clearInterval(i),a.state.time=60,a.state.smsSendBtn=!1,e.requestFailed(t)}))}}))},registerFailed:function(t){this.$notification["error"]({message:"注册失败",description:t,duration:2})},requestFailed:function(t){this.$notification["error"]({message:"错误",description:((t.response||{}).data||{}).message||"请求出现错误，请稍后再试",duration:4}),this.registerBtn=!1}},watch:{"state.passwordLevel":function(t){}}},w=f,g=(a("7afd"),a("ab6c"),a("2877")),v=Object(g["a"])(w,i,s,!1,null,"38b897e5",null);e["default"]=v.exports},2914:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"user-login-other"},[a("span",[t._v("其他登录方式")]),a("a",{attrs:{title:"github"},on:{click:function(e){return t.onThirdLogin("github")}}},[a("a-icon",{staticClass:"item-icon",attrs:{type:"github"}})],1),a("a",{attrs:{title:"企业微信"},on:{click:function(e){return t.onThirdLogin("wechat_enterprise")}}},[a("icon-font",{staticClass:"item-icon",attrs:{type:"icon-qiyeweixin3"}})],1),a("a",{attrs:{title:"钉钉"},on:{click:function(e){return t.onThirdLogin("dingtalk")}}},[a("a-icon",{staticClass:"item-icon",attrs:{type:"dingding"}})],1),a("a",{attrs:{title:"微信"},on:{click:function(e){return t.onThirdLogin("wechat_open")}}},[a("a-icon",{staticClass:"item-icon",attrs:{type:"wechat"}})],1)]),a("a-modal",{attrs:{title:"请输入密码",visible:t.thirdPasswordShow},on:{ok:t.thirdLoginCheckPassword,cancel:t.thirdLoginNoPassword}},[a("a-input-password",{attrs:{placeholder:"请输入密码"},model:{value:t.thirdLoginPassword,callback:function(e){t.thirdLoginPassword=e},expression:"thirdLoginPassword"}})],1),a("a-modal",{class:"ant-modal-confirm",attrs:{footer:null,closable:!1,visible:t.thirdConfirmShow}},[a("div",{staticClass:"ant-modal-confirm-body-wrapper"},[a("div",{staticClass:"ant-modal-confirm-body"},[a("a-icon",{staticStyle:{color:"#faad14"},attrs:{type:"question-circle"}}),a("span",{staticClass:"ant-modal-confirm-title"},[t._v("提示")]),a("div",{staticClass:"ant-modal-confirm-content"},[t._v("\n          已有同名账号存在,请确认是否绑定该账号？\n        ")])],1),a("div",{staticClass:"ant-modal-confirm-btns"},[a("a-button",{attrs:{loading:t.thirdCreateUserLoding},on:{click:t.thirdLoginUserCreate}},[t._v("创建新账号")]),a("a-button",{attrs:{type:"primary"},on:{click:t.thirdLoginUserBind}},[t._v("确认绑定")])],1)])]),a("a-modal",{class:"ant-modal-confirm",attrs:{visible:t.bindingPhoneModal}},[a("template",{slot:"footer"},[a("a-button",{key:"submit",attrs:{type:"primary"},on:{click:t.thirdHandleOk}},[t._v("\n        确定\n      ")])],1),a("div",{staticClass:"ant-modal-confirm-body-wrapper"},[a("a-form-model-item",[a("span",[t._v("绑定手机号")])]),a("a-form-model-item",[a("a-input",{attrs:{size:"large",type:"text",placeholder:"手机号"},model:{value:t.thirdPhone,callback:function(e){t.thirdPhone=e},expression:"thirdPhone"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mobile"},slot:"prefix"})],1)],1),a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:16}},[a("a-form-model-item",[a("a-input",{attrs:{size:"large",type:"text",placeholder:"请输入验证码"},model:{value:t.thirdCaptcha,callback:function(e){t.thirdCaptcha=e},expression:"thirdCaptcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{staticClass:"getCaptcha",attrs:{tabindex:"-1",disabled:t.thirdState.smsSendBtn},domProps:{textContent:t._s(t.thirdState.smsSendBtn?t.thirdState.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getThirdCaptcha(e)}}})],1)],1)],1)],2)],1)},s=[],r=a("2f62"),n=a("0fea"),o=a("ca00");function c(t){return c="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function d(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?l(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var h={data:function(){return{thirdLoginInfo:"",thirdPasswordShow:!1,thirdLoginPassword:"",thirdLoginUser:"",thirdConfirmShow:!1,thirdCreateUserLoding:!1,thirdLoginState:!1,bindingPhoneModal:!1,thirdPhone:"",thirdCaptcha:"",thirdState:{time:30,smsSendBtn:!1},thirdUserUuid:"",thirdType:"",url:{bindingThirdPhone:"/sys/thirdLogin/bindingThirdPhone"}}},created:function(){},methods:d(d({},Object(r["b"])(["ThirdLogin"])),{},{onThirdLogin:function(t){var e=window._CONFIG["domianURL"]+"/sys/thirdLogin/render/".concat(t);window.open(e,"login ".concat(t),"height=500, width=500, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no");var a=this;a.thirdType=t,a.thirdLoginInfo="",a.thirdLoginState=!1;var i=function(t){var e=t.data;if("string"===typeof e)if("登录失败"===e)a.$message.warning(e);else if(e.includes("绑定手机号")){a.bindingPhoneModal=!0;var i=e.split(",");a.thirdUserUuid=i[1]}else a.doThirdLogin(e);else"object"===c(e)?!0===e["isObj"]&&(a.thirdConfirmShow=!0,a.thirdLoginInfo=d({},e)):a.$message.warning("不识别的信息传递")};window.addEventListener("message",i,!1)},doThirdLogin:function(t){var e=this;if(!1===this.thirdLoginState){this.thirdLoginState=!0;var a={};a.thirdType=this.thirdType,a.token=t,this.ThirdLogin(a).then((function(t){t.success?e.loginSuccess():e.requestFailed(t)}))}},thirdLoginUserBind:function(){this.thirdLoginPassword="",this.thirdLoginUser=this.thirdLoginInfo.uuid,this.thirdConfirmShow=!1,this.thirdPasswordShow=!0},thirdLoginUserCreate:function(){var t=this;this.thirdCreateUserLoding=!0,this.thirdLoginInfo["suffix"]=parseInt(98*Math.random()+1),Object(n["i"])("/sys/third/user/create",this.thirdLoginInfo).then((function(e){if(e.success){var a=e.result;t.doThirdLogin(a),t.thirdConfirmShow=!1}else t.$message.warning(e.message)})).finally((function(){t.thirdCreateUserLoding=!1}))},thirdLoginCheckPassword:function(){var t=this,e=Object.assign({},this.thirdLoginInfo,{password:this.thirdLoginPassword});Object(n["i"])("/sys/third/user/checkPassword",e).then((function(e){e.success?(t.thirdLoginNoPassword(),t.doThirdLogin(e.result)):t.$message.warning(e.message)}))},thirdLoginNoPassword:function(){this.thirdPasswordShow=!1,this.thirdLoginPassword="",this.thirdLoginUser=""},getThirdCaptcha:function(){var t=this,e=this;if(this.thirdPhone){this.thirdState.smsSendBtn=!0;var a=window.setInterval((function(){e.thirdState.time--<=0&&(e.thirdState.time=30,e.thirdState.smsSendBtn=!1,window.clearInterval(a))}),1e3),i=this.$message.loading("验证码发送中..",0),s={};s.mobile=this.thirdPhone,s.smsmode="0",Object(n["i"])("/sys/sms",s).then((function(e){e.success||(setTimeout(i,0),t.cmsFailed(e.message)),setTimeout(i,500)})).catch((function(s){setTimeout(i,1),clearInterval(a),e.thirdState.time=30,e.thirdState.smsSendBtn=!1,t.requestFailed(s)}))}else e.cmsFailed("请输入手机号")},thirdHandleOk:function(){var t=this,e={};e.mobile=this.thirdPhone,e.captcha=this.thirdCaptcha,e.thirdUserUuid=this.thirdUserUuid,Object(n["i"])(this.url.bindingThirdPhone,e).then((function(e){e.success?(t.bindingPhoneModal=!1,t.doThirdLogin(e.result)):t.$message.warning(e.message)}))},loginSuccess:function(){this.$router.push({path:"/dashboard/analysis"}).catch((function(){})),this.$notification.success({message:"欢迎",description:"".concat(Object(o["o"])(),"，欢迎回来")})},cmsFailed:function(t){this.$notification["error"]({message:"登录失败",description:t,duration:4})},requestFailed:function(t){this.$notification["error"]({message:"登录失败",description:((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试",duration:4}),this.loginBtn=!1}})},m=a("0c63"),p=m["a"].createFromIconfontCN({scriptUrl:"/cdn/font-icon/font_2316098_umqusozousr.js"}),f={name:"thirdLogin",mixins:[h],components:{IconFont:p}},w=f,g=(a("8cea"),a("2877")),v=Object(g["a"])(w,i,s,!1,null,"0d925ab8",null);e["default"]=v.exports},"5c696":function(t,e,a){"use strict";var i=a("9544"),s=a.n(i);s.a},"7afd":function(t,e,a){"use strict";var i=a("1885"),s=a.n(i);s.a},"843e":function(t,e,a){"use strict";var i=a("0fb8"),s=a.n(i);s.a},"8cea":function(t,e,a){"use strict";var i=a("ea4e"),s=a.n(i);s.a},"94f4":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("result",{attrs:{isSuccess:!0,content:!1,title:t.email}},[a("template",{slot:"action"},[a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{size:"large"},on:{click:t.goHomeHandle}},[t._v("返回首页")])],1)],2)},s=[],r=a("9a3d"),n={name:"RegisterResult",components:{Result:r["default"]},data:function(){return{model:{}}},computed:{email:function(){var t=this.model?this.model.username||this.model.mobile:" XXX ",e="你的账户：".concat(t," 注册成功");return this.username=t,e}},created:function(){this.model=this.$route.params},methods:{goHomeHandle:function(){var t={};t.username=this.model.username,t.password=this.model.password,this.$router.push({name:"login",params:t})}}},o=n,c=a("2877"),l=Object(c["a"])(o,i,s,!1,null,"1a9021fb",null);e["default"]=l.exports},9544:function(t,e,a){},ab6c:function(t,e,a){"use strict";var i=a("1b68"),s=a.n(i);s.a},ea4e:function(t,e,a){}}]);