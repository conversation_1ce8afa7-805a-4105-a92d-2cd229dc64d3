{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753820476608}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getTransactionStats, createRechargeOrder, getUserProfile } from '@/api/usercenter';\nimport { getFileAccessHttpUrl } from '@/utils/util';\nexport default {\n  name: 'QuickRecharge',\n  data: function data() {\n    return {\n      loading: false,\n      rechargeLoading: false,\n      checkingStatus: false,\n      // 用户信息\n      userBalance: 0,\n      userNickname: '',\n      userAvatar: '',\n      defaultAvatar: '/default-avatar.png',\n      // 本地默认头像作为降级方案\n      // 充值弹窗\n      showRechargeModal: false,\n      // 充值选项\n      rechargeOptions: [{\n        amount: 50,\n        label: '体验套餐'\n      }, {\n        amount: 100,\n        label: '基础套餐'\n      }, {\n        amount: 300,\n        label: '进阶套餐'\n      }, {\n        amount: 500,\n        label: '专业套餐'\n      }, {\n        amount: 1000,\n        label: '企业套餐'\n      }],\n      selectedAmount: 0,\n      customAmount: null,\n      // 支付方式\n      selectedPaymentMethod: 'alipay-qr',\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null\n    };\n  },\n  computed: {\n    finalRechargeAmount: function finalRechargeAmount() {\n      return this.customAmount || this.selectedAmount || 0;\n    },\n    // 头像URL处理（与个人中心逻辑一致）\n    avatarUrl: function avatarUrl() {\n      var avatar = this.userAvatar;\n\n      if (!avatar) {\n        return this.defaultAvatar;\n      } // 如果是完整的URL，直接返回\n\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是相对路径，使用getFileAccessHttpUrl转换\n\n\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar;\n    }\n  },\n  mounted: function mounted() {\n    this.loadUserInfo();\n    this.loadDefaultAvatar();\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer);\n    }\n  },\n  methods: {\n    // 加载用户信息（包含余额）\n    loadUserInfo: function () {\n      var _loadUserInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var statsResponse, stats, profileResponse, profile;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.prev = 0;\n                this.loading = true; // 加载余额信息\n\n                _context.next = 4;\n                return getTransactionStats();\n\n              case 4:\n                statsResponse = _context.sent;\n\n                if (statsResponse.success) {\n                  stats = statsResponse.result || {};\n                  this.userBalance = stats.accountBalance || 0;\n                } // 加载用户基本信息\n\n\n                _context.next = 8;\n                return getUserProfile();\n\n              case 8:\n                profileResponse = _context.sent;\n\n                if (profileResponse.success) {\n                  profile = profileResponse.result || {};\n                  this.userNickname = profile.nickname || profile.realname || '';\n                  this.userAvatar = profile.avatar || '';\n                }\n\n                _context.next = 15;\n                break;\n\n              case 12:\n                _context.prev = 12;\n                _context.t0 = _context[\"catch\"](0);\n                console.error('加载用户信息失败:', _context.t0);\n\n              case 15:\n                _context.prev = 15;\n                this.loading = false;\n                return _context.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[0, 12, 15, 18]]);\n      }));\n\n      function loadUserInfo() {\n        return _loadUserInfo.apply(this, arguments);\n      }\n\n      return loadUserInfo;\n    }(),\n    // 加载TOS默认头像URL\n    loadDefaultAvatar: function () {\n      var _loadDefaultAvatar = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return this.$http.get('/sys/common/default-avatar-url');\n\n              case 3:\n                response = _context2.sent;\n\n                if (response && response.success && response.result) {\n                  this.defaultAvatar = response.result;\n                  console.log('🎯 QuickRecharge: 已加载TOS默认头像:', this.defaultAvatar);\n                }\n\n                _context2.next = 10;\n                break;\n\n              case 7:\n                _context2.prev = 7;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.warn('⚠️ QuickRecharge: 获取TOS默认头像失败，使用本地降级:', _context2.t0); // 保持本地默认头像作为降级方案\n\n              case 10:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 7]]);\n      }));\n\n      function loadDefaultAvatar() {\n        return _loadDefaultAvatar.apply(this, arguments);\n      }\n\n      return loadDefaultAvatar;\n    }(),\n    // 头像URL处理方法（与个人中心逻辑一致）\n    getFileAccessHttpUrl: function getFileAccessHttpUrl(avatar) {\n      if (!avatar) return ''; // 如果已经是完整URL，直接返回\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是TOS文件，使用全局方法\n\n\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar;\n      } // 本地文件，使用静态域名\n\n\n      return this.$store.state.app.staticDomainURL + '/' + avatar;\n    },\n    // 格式化余额显示\n    formatBalance: function formatBalance(balance) {\n      return parseFloat(balance || 0).toFixed(2);\n    },\n    // 选择充值金额\n    selectRechargeAmount: function selectRechargeAmount(amount) {\n      this.selectedAmount = amount;\n      this.customAmount = null;\n    },\n    // 处理充值\n    handleRecharge: function () {\n      var _handleRecharge = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var orderData, response, result;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                if (!(!this.finalRechargeAmount || this.finalRechargeAmount < 0.01)) {\n                  _context3.next = 3;\n                  break;\n                }\n\n                this.$message.warning('请选择或输入充值金额，最低0.01元');\n                return _context3.abrupt(\"return\");\n\n              case 3:\n                _context3.prev = 3;\n                this.rechargeLoading = true;\n                orderData = {\n                  amount: this.finalRechargeAmount,\n                  paymentMethod: this.selectedPaymentMethod\n                };\n                _context3.next = 8;\n                return createRechargeOrder(orderData);\n\n              case 8:\n                response = _context3.sent;\n\n                if (!response.success) {\n                  _context3.next = 22;\n                  break;\n                }\n\n                result = response.result;\n\n                if (!(this.selectedPaymentMethod === 'alipay-page')) {\n                  _context3.next = 16;\n                  break;\n                }\n\n                _context3.next = 14;\n                return this.handleAlipayPagePayment(result.orderId, result.amount);\n\n              case 14:\n                _context3.next = 19;\n                break;\n\n              case 16:\n                if (!(this.selectedPaymentMethod === 'alipay-qr')) {\n                  _context3.next = 19;\n                  break;\n                }\n\n                _context3.next = 19;\n                return this.handleAlipayQrPayment(result.orderId, result.amount);\n\n              case 19:\n                this.showRechargeModal = false;\n                _context3.next = 23;\n                break;\n\n              case 22:\n                this.$message.error(response.message || '创建充值订单失败');\n\n              case 23:\n                _context3.next = 29;\n                break;\n\n              case 25:\n                _context3.prev = 25;\n                _context3.t0 = _context3[\"catch\"](3);\n                console.error('创建充值订单失败:', _context3.t0);\n                this.$message.error('充值失败，请重试');\n\n              case 29:\n                _context3.prev = 29;\n                this.rechargeLoading = false;\n                return _context3.finish(29);\n\n              case 32:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[3, 25, 29, 32]]);\n      }));\n\n      function handleRecharge() {\n        return _handleRecharge.apply(this, arguments);\n      }\n\n      return handleRecharge;\n    }(),\n    // 处理支付宝网页支付\n    handleAlipayPagePayment: function () {\n      var _handleAlipayPagePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4(orderId, amount) {\n        var paymentData, payResponse, payForm, div, form;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount);\n                this.$message.loading('正在跳转到支付宝支付...', 0); // 调用支付宝支付接口\n\n                paymentData = {\n                  orderId: orderId,\n                  amount: amount,\n                  subject: '智界Aigc账户充值',\n                  body: \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)\n                };\n                console.log('🔍 发送支付请求数据:', paymentData);\n                _context4.next = 7;\n                return this.$http.post('/api/alipay/createOrder', paymentData);\n\n              case 7:\n                payResponse = _context4.sent;\n                console.log('🔍 支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context4.next = 24;\n                  break;\n                }\n\n                payForm = payResponse.result.payForm;\n                console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空');\n\n                if (payForm) {\n                  _context4.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付表单为空');\n                return _context4.abrupt(\"return\");\n\n              case 16:\n                // 创建表单并提交到支付宝\n                div = document.createElement('div');\n                div.innerHTML = payForm;\n                document.body.appendChild(div);\n                form = div.querySelector('form');\n\n                if (form) {\n                  console.log('🔍 找到支付表单，准备提交');\n                  form.submit();\n                } else {\n                  console.error('🔍 未找到支付表单');\n                  this.$message.error('支付表单创建失败');\n                } // 清理DOM\n\n\n                setTimeout(function () {\n                  if (document.body.contains(div)) {\n                    document.body.removeChild(div);\n                  }\n                }, 1000);\n                _context4.next = 26;\n                break;\n\n              case 24:\n                console.error('🔍 支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建支付订单失败');\n\n              case 26:\n                _context4.next = 33;\n                break;\n\n              case 28:\n                _context4.prev = 28;\n                _context4.t0 = _context4[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝支付失败:', _context4.t0);\n                this.$message.error('支付宝支付失败，请重试');\n\n              case 33:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 28]]);\n      }));\n\n      function handleAlipayPagePayment(_x, _x2) {\n        return _handleAlipayPagePayment.apply(this, arguments);\n      }\n\n      return handleAlipayPagePayment;\n    }(),\n    // 处理支付宝扫码支付\n    handleAlipayQrPayment: function () {\n      var _handleAlipayQrPayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(orderId, amount) {\n        var paymentData, payResponse, qrCode;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount);\n                this.$message.loading('正在生成支付二维码...', 0); // 调用支付宝扫码支付接口\n\n                paymentData = {\n                  orderId: orderId,\n                  amount: amount,\n                  subject: '智界Aigc账户充值',\n                  body: \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)\n                };\n                console.log('🔍 发送扫码支付请求数据:', paymentData);\n                _context5.next = 7;\n                return this.$http.post('/api/alipay/createQrOrder', paymentData);\n\n              case 7:\n                payResponse = _context5.sent;\n                console.log('🔍 扫码支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context5.next = 19;\n                  break;\n                }\n\n                qrCode = payResponse.result.qrCode;\n                console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空');\n\n                if (qrCode) {\n                  _context5.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付二维码生成失败');\n                return _context5.abrupt(\"return\");\n\n              case 16:\n                // 显示二维码支付弹窗\n                this.showQrCodeModal(qrCode, orderId, amount);\n                _context5.next = 21;\n                break;\n\n              case 19:\n                console.error('🔍 扫码支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建扫码支付订单失败');\n\n              case 21:\n                _context5.next = 28;\n                break;\n\n              case 23:\n                _context5.prev = 23;\n                _context5.t0 = _context5[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝扫码支付失败:', _context5.t0);\n                this.$message.error('支付宝扫码支付失败，请重试');\n\n              case 28:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 23]]);\n      }));\n\n      function handleAlipayQrPayment(_x3, _x4) {\n        return _handleAlipayQrPayment.apply(this, arguments);\n      }\n\n      return handleAlipayQrPayment;\n    }(),\n    // 显示二维码弹窗\n    showQrCodeModal: function showQrCodeModal(qrCode, orderId, amount) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = \"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=\".concat(encodeURIComponent(qrCode));\n      this.currentOrderId = orderId;\n      this.currentOrderAmount = amount;\n      this.showQrModal = true;\n      console.log('🔍 显示二维码弹窗 - 订单号:', orderId, '金额:', amount);\n      console.log('🔍 二维码URL:', this.qrCodeUrl); // 开始轮询支付状态\n\n      this.startPaymentStatusCheck();\n    },\n    // 关闭二维码弹窗\n    closeQrModal: function closeQrModal() {\n      this.showQrModal = false;\n      this.qrCodeUrl = '';\n      this.currentOrderId = '';\n      this.currentOrderAmount = 0;\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n        this.paymentCheckTimer = null;\n      }\n    },\n    // 开始支付状态检查\n    startPaymentStatusCheck: function startPaymentStatusCheck() {\n      var _this = this;\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n      }\n\n      this.paymentCheckTimer = setInterval(function () {\n        _this.checkPaymentStatus();\n      }, 3000); // 每3秒检查一次\n    },\n    // 检查支付状态\n    checkPaymentStatus: function () {\n      var _checkPaymentStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                if (this.currentOrderId) {\n                  _context6.next = 2;\n                  break;\n                }\n\n                return _context6.abrupt(\"return\");\n\n              case 2:\n                _context6.prev = 2;\n                this.checkingStatus = true;\n                console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId);\n                _context6.next = 7;\n                return this.$http.get(\"/api/alipay/queryOrder/\".concat(this.currentOrderId));\n\n              case 7:\n                response = _context6.sent;\n                console.log('🔍 支付状态查询响应:', response);\n\n                if (response.success && response.result.status === 'TRADE_SUCCESS') {\n                  console.log('🔍 支付成功！');\n                  this.$message.success('支付成功！');\n                  this.closeQrModal();\n                  this.loadUserInfo(); // 刷新用户信息和余额\n\n                  this.$emit('recharge-success'); // 通知父组件\n                } else {\n                  console.log('🔍 支付状态:', response.result && response.result.status || '未知');\n                }\n\n                _context6.next = 15;\n                break;\n\n              case 12:\n                _context6.prev = 12;\n                _context6.t0 = _context6[\"catch\"](2);\n                console.error('检查支付状态失败:', _context6.t0);\n\n              case 15:\n                _context6.prev = 15;\n                this.checkingStatus = false;\n                return _context6.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[2, 12, 15, 18]]);\n      }));\n\n      function checkPaymentStatus() {\n        return _checkPaymentStatus.apply(this, arguments);\n      }\n\n      return checkPaymentStatus;\n    }()\n  }\n};", {"version": 3, "sources": ["QuickRecharge.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6IA,SACA,mBADA,EAEA,mBAFA,EAGA,cAHA,QAIA,kBAJA;AAKA,SAAA,oBAAA,QAAA,cAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,eAAA,EAAA,KAFA;AAGA,MAAA,cAAA,EAAA,KAHA;AAKA;AACA,MAAA,WAAA,EAAA,CANA;AAOA,MAAA,YAAA,EAAA,EAPA;AAQA,MAAA,UAAA,EAAA,EARA;AASA,MAAA,aAAA,EAAA,qBATA;AASA;AAEA;AACA,MAAA,iBAAA,EAAA,KAZA;AAcA;AACA,MAAA,eAAA,EAAA,CACA;AAAA,QAAA,MAAA,EAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,MAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,CAfA;AAsBA,MAAA,cAAA,EAAA,CAtBA;AAuBA,MAAA,YAAA,EAAA,IAvBA;AAyBA;AACA,MAAA,qBAAA,EAAA,WA1BA;AA4BA;AACA,MAAA,WAAA,EAAA,KA7BA;AA8BA,MAAA,SAAA,EAAA,EA9BA;AA+BA,MAAA,cAAA,EAAA,EA/BA;AAgCA,MAAA,kBAAA,EAAA,CAhCA;AAiCA,MAAA,iBAAA,EAAA;AAjCA,KAAA;AAmCA,GAtCA;AAwCA,EAAA,QAAA,EAAA;AACA,IAAA,mBADA,iCACA;AACA,aAAA,KAAA,YAAA,IAAA,KAAA,cAAA,IAAA,CAAA;AACA,KAHA;AAKA;AACA,IAAA,SANA,uBAMA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;;AACA,UAAA,CAAA,MAAA,EAAA;AACA,eAAA,KAAA,aAAA;AACA,OAJA,CAMA;;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,KAAA,MAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA;AACA,OATA,CAWA;;;AACA,aAAA,KAAA,oBAAA,CAAA,MAAA,KAAA,KAAA,aAAA;AACA;AAnBA,GAxCA;AA8DA,EAAA,OA9DA,qBA8DA;AACA,SAAA,YAAA;AACA,SAAA,iBAAA;AACA,GAjEA;AAmEA,EAAA,aAnEA,2BAmEA;AACA,QAAA,KAAA,iBAAA,EAAA;AACA,MAAA,aAAA,CAAA,KAAA,iBAAA,CAAA;AACA;AACA,GAvEA;AAyEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,YAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,qBAAA,OAAA,GAAA,IAAA,CAJA,CAMA;;AANA;AAAA,uBAOA,mBAAA,EAPA;;AAAA;AAOA,gBAAA,aAPA;;AAQA,oBAAA,aAAA,CAAA,OAAA,EAAA;AACA,kBAAA,KADA,GACA,aAAA,CAAA,MAAA,IAAA,EADA;AAEA,uBAAA,WAAA,GAAA,KAAA,CAAA,cAAA,IAAA,CAAA;AACA,iBAXA,CAaA;;;AAbA;AAAA,uBAcA,cAAA,EAdA;;AAAA;AAcA,gBAAA,eAdA;;AAeA,oBAAA,eAAA,CAAA,OAAA,EAAA;AACA,kBAAA,OADA,GACA,eAAA,CAAA,MAAA,IAAA,EADA;AAEA,uBAAA,YAAA,GAAA,OAAA,CAAA,QAAA,IAAA,OAAA,CAAA,QAAA,IAAA,EAAA;AACA,uBAAA,UAAA,GAAA,OAAA,CAAA,MAAA,IAAA,EAAA;AACA;;AAnBA;AAAA;;AAAA;AAAA;AAAA;AAqBA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;;AArBA;AAAA;AAuBA,qBAAA,OAAA,GAAA,KAAA;AAvBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA2BA;AACA,IAAA,iBA5BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBA8BA,KAAA,KAAA,CAAA,GAAA,CAAA,gCAAA,CA9BA;;AAAA;AA8BA,gBAAA,QA9BA;;AA+BA,oBAAA,QAAA,IAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,MAAA,EAAA;AACA,uBAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,KAAA,aAAA;AACA;;AAlCA;AAAA;;AAAA;AAAA;AAAA;AAoCA,gBAAA,OAAA,CAAA,IAAA,CAAA,uCAAA,gBApCA,CAqCA;;AArCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAyCA;AACA,IAAA,oBA1CA,gCA0CA,MA1CA,EA0CA;AACA,UAAA,CAAA,MAAA,EAAA,OAAA,EAAA,CADA,CAGA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,KAAA,MAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA;AACA,OANA,CAQA;;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,oBAAA,GAAA,MAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAA;AACA,OAXA,CAaA;;;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,eAAA,GAAA,GAAA,GAAA,MAAA;AACA,KAzDA;AA2DA;AACA,IAAA,aA5DA,yBA4DA,OA5DA,EA4DA;AACA,aAAA,UAAA,CAAA,OAAA,IAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,KA9DA;AAgEA;AACA,IAAA,oBAjEA,gCAiEA,MAjEA,EAiEA;AACA,WAAA,cAAA,GAAA,MAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KApEA;AAsEA;AACA,IAAA,cAvEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAwEA,CAAA,KAAA,mBAAA,IAAA,KAAA,mBAAA,GAAA,IAxEA;AAAA;AAAA;AAAA;;AAyEA,qBAAA,QAAA,CAAA,OAAA,CAAA,oBAAA;AAzEA;;AAAA;AAAA;AA8EA,qBAAA,eAAA,GAAA,IAAA;AAEA,gBAAA,SAhFA,GAgFA;AACA,kBAAA,MAAA,EAAA,KAAA,mBADA;AAEA,kBAAA,aAAA,EAAA,KAAA;AAFA,iBAhFA;AAAA;AAAA,uBAqFA,mBAAA,CAAA,SAAA,CArFA;;AAAA;AAqFA,gBAAA,QArFA;;AAAA,qBAsFA,QAAA,CAAA,OAtFA;AAAA;AAAA;AAAA;;AAuFA,gBAAA,MAvFA,GAuFA,QAAA,CAAA,MAvFA;;AAAA,sBAyFA,KAAA,qBAAA,KAAA,aAzFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBA0FA,KAAA,uBAAA,CAAA,MAAA,CAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CA1FA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBA2FA,KAAA,qBAAA,KAAA,WA3FA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBA4FA,KAAA,qBAAA,CAAA,MAAA,CAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CA5FA;;AAAA;AA+FA,qBAAA,iBAAA,GAAA,KAAA;AA/FA;AAAA;;AAAA;AAiGA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,UAAA;;AAjGA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAoGA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AArGA;AAAA;AAuGA,qBAAA,eAAA,GAAA,KAAA;AAvGA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA2GA;AACA,IAAA,uBA5GA;AAAA,gHA4GA,OA5GA,EA4GA,MA5GA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8GA,gBAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,eAAA,EAAA,CAAA,EA/GA,CAiHA;;AACA,gBAAA,WAlHA,GAkHA;AACA,kBAAA,OAAA,EAAA,OADA;AAEA,kBAAA,MAAA,EAAA,MAFA;AAGA,kBAAA,OAAA,EAAA,YAHA;AAIA,kBAAA,IAAA,8CAAA,MAAA;AAJA,iBAlHA;AAyHA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,WAAA;AAzHA;AAAA,uBA0HA,KAAA,KAAA,CAAA,IAAA,CAAA,yBAAA,EAAA,WAAA,CA1HA;;AAAA;AA0HA,gBAAA,WA1HA;AA2HA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,WAAA;AACA,qBAAA,QAAA,CAAA,OAAA;;AA5HA,qBA8HA,WAAA,CAAA,OA9HA;AAAA;AAAA;AAAA;;AA+HA,gBAAA,OA/HA,GA+HA,WAAA,CAAA,MAAA,CAAA,OA/HA;AAgIA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,OAAA,GAAA,KAAA,GAAA,IAAA;;AAhIA,oBAkIA,OAlIA;AAAA;AAAA;AAAA;;AAmIA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AAnIA;;AAAA;AAuIA;AACA,gBAAA,GAxIA,GAwIA,QAAA,CAAA,aAAA,CAAA,KAAA,CAxIA;AAyIA,gBAAA,GAAA,CAAA,SAAA,GAAA,OAAA;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA;AAEA,gBAAA,IA5IA,GA4IA,GAAA,CAAA,aAAA,CAAA,MAAA,CA5IA;;AA6IA,oBAAA,IAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;AACA,kBAAA,IAAA,CAAA,MAAA;AACA,iBAHA,MAGA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,iBAnJA,CAqJA;;;AACA,gBAAA,UAAA,CAAA,YAAA;AACA,sBAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA;AACA;AACA,iBAJA,EAIA,IAJA,CAAA;AAtJA;AAAA;;AAAA;AA6JA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,WAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,IAAA,UAAA;;AA9JA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAkKA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,aAAA;;AApKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwKA;AACA,IAAA,qBAzKA;AAAA,8GAyKA,OAzKA,EAyKA,MAzKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2KA,gBAAA,OAAA,CAAA,GAAA,CAAA,uBAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,cAAA,EAAA,CAAA,EA5KA,CA8KA;;AACA,gBAAA,WA/KA,GA+KA;AACA,kBAAA,OAAA,EAAA,OADA;AAEA,kBAAA,MAAA,EAAA,MAFA;AAGA,kBAAA,OAAA,EAAA,YAHA;AAIA,kBAAA,IAAA,8CAAA,MAAA;AAJA,iBA/KA;AAsLA,gBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,WAAA;AAtLA;AAAA,uBAuLA,KAAA,KAAA,CAAA,IAAA,CAAA,2BAAA,EAAA,WAAA,CAvLA;;AAAA;AAuLA,gBAAA,WAvLA;AAwLA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,WAAA;AACA,qBAAA,QAAA,CAAA,OAAA;;AAzLA,qBA2LA,WAAA,CAAA,OA3LA;AAAA;AAAA;AAAA;;AA4LA,gBAAA,MA5LA,GA4LA,WAAA,CAAA,MAAA,CAAA,MA5LA;AA6LA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA;;AA7LA,oBA+LA,MA/LA;AAAA;AAAA;AAAA;;AAgMA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AAhMA;;AAAA;AAoMA;AACA,qBAAA,eAAA,CAAA,MAAA,EAAA,OAAA,EAAA,MAAA;AArMA;AAAA;;AAAA;AAwMA,gBAAA,OAAA,CAAA,KAAA,CAAA,cAAA,EAAA,WAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,IAAA,YAAA;;AAzMA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA6MA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA;;AA/MA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmNA;AACA,IAAA,eApNA,2BAoNA,MApNA,EAoNA,OApNA,EAoNA,MApNA,EAoNA;AACA;AACA,WAAA,SAAA,2EAAA,kBAAA,CAAA,MAAA,CAAA;AACA,WAAA,cAAA,GAAA,OAAA;AACA,WAAA,kBAAA,GAAA,MAAA;AACA,WAAA,WAAA,GAAA,IAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,mBAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,SAAA,EARA,CAUA;;AACA,WAAA,uBAAA;AACA,KAhOA;AAkOA;AACA,IAAA,YAnOA,0BAmOA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,cAAA,GAAA,EAAA;AACA,WAAA,kBAAA,GAAA,CAAA;;AAEA,UAAA,KAAA,iBAAA,EAAA;AACA,QAAA,aAAA,CAAA,KAAA,iBAAA,CAAA;AACA,aAAA,iBAAA,GAAA,IAAA;AACA;AACA,KA7OA;AA+OA;AACA,IAAA,uBAhPA,qCAgPA;AAAA;;AACA,UAAA,KAAA,iBAAA,EAAA;AACA,QAAA,aAAA,CAAA,KAAA,iBAAA,CAAA;AACA;;AAEA,WAAA,iBAAA,GAAA,WAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,kBAAA;AACA,OAFA,EAEA,IAFA,CAAA,CALA,CAOA;AACA,KAxPA;AA0PA;AACA,IAAA,kBA3PA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBA4PA,KAAA,cA5PA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AA+PA,qBAAA,cAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,KAAA,cAAA;AAhQA;AAAA,uBAiQA,KAAA,KAAA,CAAA,GAAA,kCAAA,KAAA,cAAA,EAjQA;;AAAA;AAiQA,gBAAA,QAjQA;AAkQA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,QAAA;;AAEA,oBAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,MAAA,CAAA,MAAA,KAAA,eAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,UAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,OAAA;AACA,uBAAA,YAAA;AACA,uBAAA,YAAA,GAJA,CAIA;;AACA,uBAAA,KAAA,CAAA,kBAAA,EALA,CAKA;AACA,iBANA,MAMA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,MAAA,CAAA,MAAA,IAAA,IAAA;AACA;;AA5QA;AAAA;;AAAA;AAAA;AAAA;AA8QA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;;AA9QA;AAAA;AAgRA,qBAAA,cAAA,GAAA,KAAA;AAhRA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAzEA,CAAA", "sourcesContent": ["<template>\n  <div class=\"quick-recharge-card\">\n    <!-- 余额显示区域 -->\n    <div class=\"balance-section\">\n      <div class=\"user-info\">\n        <div class=\"user-avatar\">\n          <a-avatar\n            :size=\"60\"\n            :src=\"avatarUrl\"\n            icon=\"user\"\n            :style=\"{ backgroundColor: '#87d068' }\"\n          >\n            {{ userNickname ? userNickname.charAt(0) : 'U' }}\n          </a-avatar>\n        </div>\n        <div class=\"user-details\">\n          <div class=\"user-name\">{{ userNickname || '智界用户' }}</div>\n          <div class=\"balance-info\">\n            <span class=\"balance-label\">账户余额</span>\n            <span class=\"balance-amount\">¥{{ formatBalance(userBalance) }}</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"balance-actions\">\n        <button class=\"recharge-btn\" @click=\"showRechargeModal = true\">\n          <i class=\"anticon anticon-plus-circle\"></i>\n          快速充值\n        </button>\n      </div>\n    </div>\n    \n    <!-- 充值弹窗 -->\n    <a-modal\n      title=\"账户充值\"\n      :visible=\"showRechargeModal\"\n      @cancel=\"showRechargeModal = false\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"recharge-modal-content\">\n        <!-- 充值选项 -->\n        <div class=\"recharge-options\">\n          <h4>选择充值金额</h4>\n          <div class=\"options-grid\">\n            <div \n              v-for=\"option in rechargeOptions\" \n              :key=\"option.amount\"\n              class=\"recharge-option\"\n              :class=\"{ selected: selectedAmount === option.amount }\"\n              @click=\"selectRechargeAmount(option.amount)\"\n            >\n              <div class=\"option-amount\">¥{{ option.amount }}</div>\n              <div class=\"option-label\">{{ option.label }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 自定义金额 -->\n        <div class=\"custom-amount\">\n          <h4>自定义金额</h4>\n          <div class=\"custom-input\">\n            <a-input-number\n              v-model=\"customAmount\"\n              :min=\"0.01\"\n              :max=\"10000\"\n              :step=\"0.01\"\n              placeholder=\"最低0.01元\"\n              size=\"large\"\n              style=\"width: 200px\"\n            />\n            <span class=\"currency\">元</span>\n          </div>\n        </div>\n        \n        <!-- 支付方式 -->\n        <div class=\"payment-methods\">\n          <h4>支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n            <a-radio-button value=\"alipay-qr\">\n              <i class=\"anticon anticon-qrcode\"></i>\n              支付宝扫码\n            </a-radio-button>\n            <a-radio-button value=\"alipay-page\">\n              <i class=\"anticon anticon-alipay\"></i>\n              支付宝网页\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n        \n        <!-- 充值按钮 -->\n        <div class=\"recharge-actions\">\n          <div class=\"amount-summary\">\n            <span>充值金额：</span>\n            <span class=\"final-amount\">¥{{ finalRechargeAmount }}</span>\n          </div>\n          <a-button \n            type=\"primary\" \n            size=\"large\"\n            :loading=\"rechargeLoading\"\n            @click=\"handleRecharge\"\n            :disabled=\"!finalRechargeAmount || finalRechargeAmount < 0.01\"\n          >\n            确认充值\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n    \n    <!-- 支付二维码弹窗 -->\n    <a-modal\n      title=\"扫码支付\"\n      :visible=\"showQrModal\"\n      @cancel=\"closeQrModal\"\n      :footer=\"null\"\n      width=\"400px\"\n    >\n      <div class=\"qr-payment-content\">\n        <div class=\"qr-code-container\">\n          <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n            <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n          </div>\n          <div v-else class=\"qr-loading\">\n            <a-spin size=\"large\" />\n            <p>正在生成二维码...</p>\n          </div>\n        </div>\n        <div class=\"qr-info\">\n          <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n          <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n        </div>\n        <div class=\"qr-status\">\n          <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n            检查支付状态\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport {\n  getTransactionStats,\n  createRechargeOrder,\n  getUserProfile\n} from '@/api/usercenter'\nimport { getFileAccessHttpUrl } from '@/utils/util'\n\nexport default {\n  name: 'QuickRecharge',\n  data() {\n    return {\n      loading: false,\n      rechargeLoading: false,\n      checkingStatus: false,\n      \n      // 用户信息\n      userBalance: 0,\n      userNickname: '',\n      userAvatar: '',\n      defaultAvatar: '/default-avatar.png', // 本地默认头像作为降级方案\n      \n      // 充值弹窗\n      showRechargeModal: false,\n      \n      // 充值选项\n      rechargeOptions: [\n        { amount: 50, label: '体验套餐' },\n        { amount: 100, label: '基础套餐' },\n        { amount: 300, label: '进阶套餐' },\n        { amount: 500, label: '专业套餐' },\n        { amount: 1000, label: '企业套餐' }\n      ],\n      selectedAmount: 0,\n      customAmount: null,\n      \n      // 支付方式\n      selectedPaymentMethod: 'alipay-qr',\n      \n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null\n    }\n  },\n  \n  computed: {\n    finalRechargeAmount() {\n      return this.customAmount || this.selectedAmount || 0\n    },\n\n    // 头像URL处理（与个人中心逻辑一致）\n    avatarUrl() {\n      const avatar = this.userAvatar\n      if (!avatar) {\n        return this.defaultAvatar\n      }\n\n      // 如果是完整的URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是相对路径，使用getFileAccessHttpUrl转换\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar\n    }\n  },\n  \n  mounted() {\n    this.loadUserInfo()\n    this.loadDefaultAvatar()\n  },\n  \n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n  \n  methods: {\n    // 加载用户信息（包含余额）\n    async loadUserInfo() {\n      try {\n        this.loading = true\n\n        // 加载余额信息\n        const statsResponse = await getTransactionStats()\n        if (statsResponse.success) {\n          const stats = statsResponse.result || {}\n          this.userBalance = stats.accountBalance || 0\n        }\n\n        // 加载用户基本信息\n        const profileResponse = await getUserProfile()\n        if (profileResponse.success) {\n          const profile = profileResponse.result || {}\n          this.userNickname = profile.nickname || profile.realname || ''\n          this.userAvatar = profile.avatar || ''\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载TOS默认头像URL\n    async loadDefaultAvatar() {\n      try {\n        const response = await this.$http.get('/sys/common/default-avatar-url')\n        if (response && response.success && response.result) {\n          this.defaultAvatar = response.result\n          console.log('🎯 QuickRecharge: 已加载TOS默认头像:', this.defaultAvatar)\n        }\n      } catch (error) {\n        console.warn('⚠️ QuickRecharge: 获取TOS默认头像失败，使用本地降级:', error)\n        // 保持本地默认头像作为降级方案\n      }\n    },\n\n    // 头像URL处理方法（与个人中心逻辑一致）\n    getFileAccessHttpUrl(avatar) {\n      if (!avatar) return ''\n\n      // 如果已经是完整URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是TOS文件，使用全局方法\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar\n      }\n\n      // 本地文件，使用静态域名\n      return this.$store.state.app.staticDomainURL + '/' + avatar\n    },\n    \n    // 格式化余额显示\n    formatBalance(balance) {\n      return parseFloat(balance || 0).toFixed(2)\n    },\n    \n    // 选择充值金额\n    selectRechargeAmount(amount) {\n      this.selectedAmount = amount\n      this.customAmount = null\n    },\n    \n    // 处理充值\n    async handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元')\n        return\n      }\n\n      try {\n        this.rechargeLoading = true\n\n        const orderData = {\n          amount: this.finalRechargeAmount,\n          paymentMethod: this.selectedPaymentMethod\n        }\n\n        const response = await createRechargeOrder(orderData)\n        if (response.success) {\n          const result = response.result\n\n          if (this.selectedPaymentMethod === 'alipay-page') {\n            await this.handleAlipayPagePayment(result.orderId, result.amount)\n          } else if (this.selectedPaymentMethod === 'alipay-qr') {\n            await this.handleAlipayQrPayment(result.orderId, result.amount)\n          }\n\n          this.showRechargeModal = false\n        } else {\n          this.$message.error(response.message || '创建充值订单失败')\n        }\n      } catch (error) {\n        console.error('创建充值订单失败:', error)\n        this.$message.error('充值失败，请重试')\n      } finally {\n        this.rechargeLoading = false\n      }\n    },\n    \n    // 处理支付宝网页支付\n    async handleAlipayPagePayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        // 调用支付宝支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n    \n    // 处理支付宝扫码支付\n    async handleAlipayQrPayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        // 调用支付宝扫码支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode, orderId, amount)\n\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n    \n    // 显示二维码弹窗\n    showQrCodeModal(qrCode, orderId, amount) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`\n      this.currentOrderId = orderId\n      this.currentOrderAmount = amount\n      this.showQrModal = true\n\n      console.log('🔍 显示二维码弹窗 - 订单号:', orderId, '金额:', amount)\n      console.log('🔍 二维码URL:', this.qrCodeUrl)\n\n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n    \n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n      this.currentOrderId = ''\n      this.currentOrderAmount = 0\n      \n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n    \n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n      \n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n    \n    // 检查支付状态\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        this.checkingStatus = true\n        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        console.log('🔍 支付状态查询响应:', response)\n\n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          console.log('🔍 支付成功！')\n          this.$message.success('支付成功！')\n          this.closeQrModal()\n          this.loadUserInfo() // 刷新用户信息和余额\n          this.$emit('recharge-success') // 通知父组件\n        } else {\n          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.quick-recharge-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 16px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.quick-recharge-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.balance-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-avatar {\n  position: relative;\n}\n\n.user-avatar .ant-avatar {\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.user-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.user-name {\n  color: white;\n  font-size: 1.1rem;\n  font-weight: 600;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.balance-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.balance-label {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.85rem;\n  margin-bottom: 0.1rem;\n}\n\n.balance-amount {\n  color: white;\n  font-size: 1.6rem;\n  font-weight: bold;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.balance-actions {\n  position: relative;\n  z-index: 1;\n}\n\n.recharge-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n}\n\n.recharge-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n.recharge-btn i {\n  font-size: 1.1rem;\n}\n\n/* 充值弹窗样式 */\n.recharge-modal-content {\n  padding: 1rem 0;\n}\n\n.recharge-modal-content h4 {\n  margin-bottom: 1rem;\n  color: #1f2937;\n  font-weight: 600;\n}\n\n.options-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.recharge-option {\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 1rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f9fafb;\n}\n\n.recharge-option:hover {\n  border-color: #3b82f6;\n  background: #eff6ff;\n}\n\n.recharge-option.selected {\n  border-color: #3b82f6;\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n}\n\n.option-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  margin-bottom: 0.25rem;\n}\n\n.option-label {\n  font-size: 0.8rem;\n  opacity: 0.8;\n}\n\n.custom-amount {\n  margin-bottom: 2rem;\n}\n\n.custom-input {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.currency {\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.recharge-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.amount-summary {\n  font-size: 1rem;\n  color: #374151;\n}\n\n.final-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-left: 0.5rem;\n}\n\n/* 二维码支付样式 */\n.qr-payment-content {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code img {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.qr-loading {\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.qr-info {\n  margin-bottom: 1.5rem;\n}\n\n.qr-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-bottom: 0.5rem;\n}\n\n.qr-tip {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .quick-recharge-card {\n    padding: 1.5rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .balance-section {\n    flex-direction: column;\n    gap: 1.5rem;\n    text-align: center;\n  }\n\n  .user-info {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .user-details {\n    align-items: center;\n  }\n\n  .balance-amount {\n    font-size: 1.4rem;\n  }\n\n  .options-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .recharge-actions {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/components"}]}