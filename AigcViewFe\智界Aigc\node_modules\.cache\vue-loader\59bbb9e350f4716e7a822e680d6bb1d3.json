{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=style&index=0&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753827334840}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.membership-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 充值模块样式 */\n.recharge-section {\n  margin-bottom: 3rem;\n}\n\n.recharge-section .container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 会员套餐区域 */\n.plans-section {\n  padding: 1rem 0 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.plan-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.plan-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.plan-card.featured {\n  border-color: #3b82f6;\n  transform: scale(1.05);\n}\n\n.plan-badge {\n  position: absolute;\n  top: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1.5rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.plan-name {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.plan-price {\n  text-align: center;\n  margin-bottom: 1rem;\n}\n\n/* 原价显示 */\n.original-price {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.original-price-text {\n  font-size: 0.9rem;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);\n}\n\n/* 现价显示 */\n.current-price {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin: 0.5rem 0;\n}\n\n/* 立省金额 */\n.save-amount {\n  font-size: 0.9rem;\n  color: #27ae60;\n  font-weight: bold;\n  margin-top: 0.3rem;\n}\n\n.price-symbol {\n  font-size: 1.2rem;\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.price-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #3b82f6;\n  margin: 0 0.25rem;\n}\n\n.price-period {\n  font-size: 1rem;\n  color: #64748b;\n}\n\n.plan-description {\n  color: #64748b;\n  margin: 0;\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #1e293b;\n  transition: all 0.3s ease;\n  padding: 0.5rem 0;\n}\n\n.feature-item .anticon {\n  color: #10b981;\n  font-weight: bold;\n  flex-shrink: 0;\n}\n\n/* 禁用功能样式 */\n.feature-item.feature-disabled {\n  color: #94a3b8;\n  opacity: 0.6;\n}\n\n.feature-item.feature-disabled .anticon {\n  color: #ef4444;\n}\n\n.feature-item.feature-disabled .icon-disabled {\n  color: #ef4444;\n}\n\n.disabled-text {\n  font-size: 0.8rem;\n  color: #ef4444;\n  margin-left: auto;\n}\n\n/* 专属功能样式 */\n.feature-item.feature-exclusive {\n  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);\n  padding: 0.5rem;\n  border-radius: 8px;\n  border-left: 3px solid #0ea5e9;\n}\n\n.feature-item.feature-exclusive .anticon {\n  color: #0ea5e9;\n}\n\n.feature-item.feature-exclusive .icon-exclusive {\n  color: #0ea5e9;\n}\n\n.exclusive-badge {\n  background: linear-gradient(135deg, #0ea5e9, #0284c7);\n  color: white;\n  padding: 0.1rem 0.4rem;\n  border-radius: 8px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  margin-left: auto;\n  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);\n}\n\n.feature-text {\n  flex: 1;\n  font-size: 0.95rem;\n  line-height: 1.5;\n  font-weight: 500;\n  color: inherit;\n}\n\n/* 确保highlight样式不被覆盖 */\n.feature-text .highlight {\n  color: transparent !important;\n}\n\n/* 重点亮点样式 */\n.highlight {\n  color: #ff6b35 !important;\n  font-weight: bold !important;\n  font-size: 1em !important;\n  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;\n  -webkit-background-clip: text !important;\n  -webkit-text-fill-color: transparent !important;\n  background-clip: text !important;\n  display: inline !important;\n}\n\n/* 兼容性备用方案 */\n@supports not (-webkit-background-clip: text) {\n  .highlight {\n    color: #ff6b35 !important;\n    text-shadow: 0 2px 4px rgba(255, 107, 53, 0.4) !important;\n    -webkit-text-fill-color: initial !important;\n  }\n}\n\n.btn-subscribe {\n  width: 100%;\n  padding: 1rem;\n  background: transparent;\n  border: 2px solid #3b82f6;\n  color: #3b82f6;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 扫光特效 */\n.btn-subscribe::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.8s ease;\n  z-index: 1;\n}\n\n.btn-subscribe:hover::after {\n  left: 100%;\n}\n\n.btn-subscribe:hover {\n  background: #3b82f6;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.btn-subscribe.featured {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border: none;\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  animation: borderGlow 3s ease-in-out infinite;\n}\n\n/* 推荐套餐的斜向扫光特效 */\n.btn-subscribe.featured::after {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -150%;\n  width: 200%;\n  height: 200%;\n  background: linear-gradient(45deg,\n    transparent 30%,\n    rgba(240, 248, 255, 0.4) 45%,\n    rgba(255, 255, 255, 0.6) 50%,\n    rgba(240, 248, 255, 0.4) 55%,\n    transparent 70%\n  );\n  animation: diagonalSweep 3.5s ease-in-out infinite;\n  z-index: 1;\n  transform: rotate(-10deg);\n}\n\n/* 鼠标悬停时的扫光效果 */\n.btn-subscribe.featured:hover::after {\n  animation: hoverSweepIn 0.6s ease-out forwards;\n}\n\n/* 鼠标离开时先扫回来，然后延迟恢复自动动画 */\n.btn-subscribe.featured::after {\n  animation: diagonalSweep 3.5s ease-in-out infinite;\n}\n\n.btn-subscribe.featured:not(:hover)::after {\n  animation: hoverSweepOut 0.6s ease-in forwards, diagonalSweep 3.5s ease-in-out 2s infinite;\n}\n\n.btn-subscribe.featured:hover {\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), 0 0 15px rgba(139, 92, 246, 0.3);\n  transform: translateY(-2px);\n}\n\n/* 升级提示样式 */\n.upgrade-notice {\n  margin-top: 12px;\n  padding: 8px 12px;\n  background: rgba(255, 193, 7, 0.1);\n  border: 1px solid rgba(255, 193, 7, 0.3);\n  border-radius: 6px;\n  font-size: 12px;\n  color: #856404;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.upgrade-notice .icon-info {\n  width: 14px;\n  height: 14px;\n  background: #ffc107;\n  border-radius: 50%;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.upgrade-notice .icon-info::before {\n  content: 'i';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 10px;\n  font-weight: bold;\n  font-style: normal;\n}\n\n.upgrade-notice span {\n  line-height: 1.4;\n}\n\n/* 边框微光动画 */\n@keyframes borderGlow {\n  0%, 100% {\n    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);\n  }\n}\n\n/* 斜向来回扫光动画 */\n@keyframes diagonalSweep {\n  0% {\n    left: -150%;\n  }\n  25% {\n    left: 100%;\n  }\n  50% {\n    left: 100%;\n  }\n  75% {\n    left: -150%;\n  }\n  100% {\n    left: -150%;\n  }\n}\n\n/* 鼠标悬停时扫光进入动画 */\n@keyframes hoverSweepIn {\n  0% {\n    left: -150%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n\n/* 鼠标离开时扫光退出动画 */\n@keyframes hoverSweepOut {\n  0% {\n    left: 100%;\n  }\n  100% {\n    left: -150%;\n  }\n}\n\n/* 支付弹窗样式 */\n.payment-modal-content {\n  padding: 1rem 0;\n}\n\n.payment-modal-content h4 {\n  margin-bottom: 1rem;\n  color: #1f2937;\n  font-weight: 600;\n}\n\n.order-info {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #f9fafb;\n  border-radius: 8px;\n}\n\n.order-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.order-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.order-item .amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.payment-actions {\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n/* 二维码支付样式 */\n.qr-payment-content {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code img {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.qr-loading {\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.qr-info {\n  margin-bottom: 1.5rem;\n}\n\n.qr-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-bottom: 0.5rem;\n}\n\n.qr-tip {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card.featured {\n    transform: none;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  .order-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n}\n", null]}