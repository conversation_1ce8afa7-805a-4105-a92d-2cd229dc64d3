(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~606734d1"],{"28ad":function(e,t,a){},"2ddc":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("WebsitePage",[a("div",{staticClass:"tutorials-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[e._v("教程中心")]),a("p",{staticClass:"simple-subtitle"},[e._v("详细的使用教程和操作指南，快速上手智界AIGC")])]),a("section",{staticClass:"tutorials-section"},[a("div",{staticClass:"container"},[a("div",{staticClass:"tutorials-grid"},e._l(e.tutorials,(function(t){return a("div",{key:t.id,staticClass:"tutorial-card"},[a("div",{staticClass:"tutorial-icon"},[a("a-icon",{attrs:{type:t.icon}})],1),a("h3",{staticClass:"tutorial-title"},[e._v(e._s(t.title))]),a("p",{staticClass:"tutorial-description"},[e._v(e._s(t.description))]),a("button",{staticClass:"btn-tutorial",on:{click:function(a){return e.handleViewTutorial(t)}}},[e._v("\n              开始学习\n            ")])])})),0)])])])])},i=[],r=a("df7c"),s={name:"Tutorials",components:{WebsitePage:r["default"]},data:function(){return{tutorials:[{id:1,title:"快速入门指南",description:"5分钟了解智界AIGC的基本功能和使用方法",icon:"rocket"},{id:2,title:"小红书内容生成",description:"学习如何使用AI生成爆款小红书图文和视频内容",icon:"edit"},{id:3,title:"自动发布设置",description:"配置多平台自动发布，提升内容传播效率",icon:"share-alt"},{id:4,title:"剪映小助手使用",description:"掌握智能视频剪辑技巧，制作专业级视频内容",icon:"scissor"}]}},methods:{handleViewTutorial:function(e){this.$message.info("开始学习：".concat(e.title))}}},o=s,c=(a("61a9"),a("2877")),u=Object(c["a"])(o,n,i,!1,null,"e4596550",null);t["default"]=u.exports},"2e7d":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("WebsitePage",[a("div",{staticClass:"usercenter-container"},[a("div",{staticClass:"usercenter-main"},[a("div",{staticClass:"container"},[a("div",{staticClass:"usercenter-layout"},[a("Sidebar",{ref:"sidebar",attrs:{"current-page":e.currentPage,"user-info":e.userInfo},on:{"menu-change":e.handleMenuChange,action:e.handleSidebarAction}}),a("div",{staticClass:"usercenter-content"},["overview"===e.currentPage?a("Overview",{key:"overview",attrs:{"user-info":e.userInfo},on:{navigate:e.handleNavigate}}):"profile"===e.currentPage?a("Profile",{key:"profile",attrs:{"user-info":e.userInfo},on:{navigate:e.handleNavigate,"avatar-updated":e.handleAvatarUpdated,"info-updated":e.handleInfoUpdated,"api-key-updated":e.handleApiKeyUpdated,"password-changed":e.handlePasswordChanged,"refresh-user-info":e.getUserInfo}}):"credits"===e.currentPage?a("Credits",{key:"credits",on:{navigate:e.handleNavigate}}):"orders"===e.currentPage?a("Orders",{key:"orders",on:{navigate:e.handleNavigate}}):"usage"===e.currentPage?a("Usage",{key:"usage",on:{navigate:e.handleNavigate}}):"notifications"===e.currentPage?a("Notifications",{key:"notifications",on:{navigate:e.handleNavigate,"notification-updated":e.handleNotificationUpdated}}):e._e()],1)],1)])])]),a("FloatingNotifications",{ref:"floatingNotifications",on:{"navigate-to-notifications":e.handleNavigateToNotifications}})],1)},i=[],r=a("a34a"),s=a.n(r),o=a("df7c"),c=a("6205"),u=a("8a06"),l=a("7aa6"),d=a("c6c6"),f=a("b3db"),h=a("7ea6"),p=a("6993"),g=a("15f34"),v=a("77ea"),m=a("9fb0"),b=a("302c"),w=a("2b0e");function C(e,t,a,n,i,r,s){try{var o=e[r](s),c=o.value}catch(u){return void a(u)}o.done?t(c):Promise.resolve(c).then(n,i)}function k(e){return function(){var t=this,a=arguments;return new Promise((function(n,i){var r=e.apply(t,a);function s(e){C(r,n,i,s,o,"next",e)}function o(e){C(r,n,i,s,o,"throw",e)}s(void 0)}))}}var $={name:"UserCenter",components:{WebsitePage:o["default"],Sidebar:c["default"],FloatingNotifications:u["default"],Overview:l["default"],Profile:d["default"],Credits:f["default"],Orders:h["default"],Usage:p["default"],Notifications:g["default"]},data:function(){return{loading:!0,currentPage:"overview",userInfo:{nickname:"",email:"",avatar:"",phone:"",accountBalance:0,currentRole:"普通用户",totalConsumption:0,totalRecharge:0,memberExpireTime:null,apiKey:"",createTime:null,passwordChanged:0},pageMap:{overview:"概览",profile:"账户设置",credits:"账户管理",orders:"订单记录",usage:"使用记录",notifications:"系统通知"}}},computed:{currentPageTitle:function(){return this.pageMap[this.currentPage]||""}},mounted:function(){var e=k(s.a.mark((function e(){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.checkLoginStatus()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,this.loadUserInfo();case 4:this.initAnimations();case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{checkLoginStatus:function(){var e=w["default"].ls.get(m["a"]);return!!e||(this.$message.warning("请先登录"),this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),!1)},loadUserInfo:function(){var e=k(s.a.mark((function e(){var t,a,n;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.loading=!0,e.next=4,Object(v["s"])();case 4:if(t=e.sent,!t.success){e.next=20;break}a=t.result||t.data||{},n={nickname:a.nickname||"",email:a.email||"",avatar:a.avatar||"",phone:a.phone||"",accountBalance:a.account_balance||0,totalConsumption:a.total_consumption||0,totalRecharge:a.total_recharge||0,currentRole:a.current_role||"普通用户",apiKey:a.api_key||"",createTime:a.user_create_time||null,username:a.username||"",realname:a.realname||"",passwordChanged:a.password_changed||0},Object.assign(this.userInfo,n),this.$forceUpdate(),e.next=29;break;case 20:if(!(401===t.code||t.message&&t.message.includes("Token")||t.message&&t.message.includes("登录"))){e.next=28;break}return this.$message.warning("登录已过期，请重新登录"),this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),e.abrupt("return");case 28:this.$message.error("获取用户信息失败: ".concat(t.message||"未知错误"));case 29:e.next=39;break;case 31:if(e.prev=31,e.t0=e["catch"](0),!e.t0.response||401!==e.t0.response.status){e.next=38;break}return this.$message.warning("登录已过期，请重新登录"),this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),e.abrupt("return");case 38:this.$message.error("加载用户信息失败，请刷新重试");case 39:return e.prev=39,this.loading=!1,e.finish(39);case 42:case"end":return e.stop()}}),e,this,[[0,31,39,42]])})));function t(){return e.apply(this,arguments)}return t}(),getUserInfo:function(){var e=k(s.a.mark((function e(){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=3,this.loadUserInfo();case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),initAnimations:function(){var e=this;this.$nextTick((function(){e.$animationManager&&b["a"].init(e.$animationManager)}))},handleMenuChange:function(e){if(this.currentPage!==e){var t=".page-".concat(this.currentPage),a=".page-".concat(e);this.$animationManager&&b["a"].switchPage(t,a),this.currentPage=e,this.$router.replace({path:"/usercenter",query:{page:e}})}},handleNavigate:function(e){this.handleMenuChange(e)},handleSidebarAction:function(e){switch(e){case"recharge":this.handleMenuChange("credits");break;default:}},handleNavigateToNotifications:function(){this.handleMenuChange("notifications")},handleNotificationUpdated:function(){this.$refs.floatingNotifications&&this.$refs.floatingNotifications.loadNotifications(),this.$refs.sidebar&&this.$refs.sidebar.loadUnreadNotificationCount()},handleAvatarUpdated:function(e){this.userInfo.avatar=e,this.$forceUpdate()},handleInfoUpdated:function(e){Object.assign(this.userInfo,e),this.$forceUpdate()},handleApiKeyUpdated:function(e){var t=window.pageYOffset||document.documentElement.scrollTop;this.$set(this.userInfo,"apiKey",e),this.$nextTick((function(){window.scrollTo(0,t)}))},handlePasswordChanged:function(){this.userInfo&&(this.userInfo.passwordChanged=1)}},beforeRouteEnter:function(e,t,a){a((function(t){var a=e.query.page;a&&t.pageMap[a]&&(t.currentPage=a)}))},beforeRouteUpdate:function(e,t,a){var n=e.query.page;n&&this.pageMap[n]&&(this.currentPage=n),a()}},y=$,_=(a("3a1e"),a("2877")),P=Object(_["a"])(y,n,i,!1,null,"311f9019",null);t["default"]=P.exports},"3a1e":function(e,t,a){"use strict";var n=a("28ad"),i=a.n(n);i.a},"3ee9":function(e,t,a){},"61a9":function(e,t,a){"use strict";var n=a("3ee9"),i=a.n(n);i.a},6737:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"2rem","text-align":"center"}},[a("h1",[e._v("路由测试页面")]),a("p",[e._v("如果您能看到这个页面，说明路由配置是正常的")]),a("p",[e._v("当前路由: "+e._s(e.$route.path))]),a("p",[e._v("当前时间: "+e._s((new Date).toLocaleString()))]),a("div",{staticStyle:{"margin-top":"2rem"}},[a("button",{staticStyle:{margin:"0.5rem",padding:"0.5rem 1rem"},on:{click:e.goHome}},[e._v("\n      跳转到首页 (/home)\n    ")]),a("button",{staticStyle:{margin:"0.5rem",padding:"0.5rem 1rem"},on:{click:e.goRoot}},[e._v("\n      跳转到根路径 (/)\n    ")])])])},i=[],r={name:"RouteTest",methods:{goHome:function(){this.$router.push("/home")},goRoot:function(){this.$router.push("/")}}},s=r,o=a("2877"),c=Object(o["a"])(s,n,i,!1,null,null,null);t["default"]=c.exports}}]);