{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753827534417}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getTransactionStats, createRechargeOrder, getUserProfile, getUserRole } from '@/api/usercenter';\nimport { getFileAccessHttpUrl } from '@/utils/util';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'QuickRecharge',\n  data: function data() {\n    return {\n      loading: false,\n      rechargeLoading: false,\n      checkingStatus: false,\n      // 用户信息\n      userBalance: 0,\n      userNickname: '',\n      userAvatar: '',\n      userMemberExpireTime: null,\n      // 会员到期时间\n      userRole: 'user',\n      // 用户角色（实时获取）\n      defaultAvatar: '/default-avatar.png',\n      // 本地默认头像作为降级方案\n      // 充值弹窗\n      showRechargeModal: false,\n      // 充值选项\n      rechargeOptions: [{\n        amount: 50,\n        label: '体验套餐'\n      }, {\n        amount: 100,\n        label: '基础套餐'\n      }, {\n        amount: 300,\n        label: '进阶套餐'\n      }, {\n        amount: 500,\n        label: '专业套餐'\n      }, {\n        amount: 1000,\n        label: '企业套餐'\n      }],\n      selectedAmount: 0,\n      customAmount: null,\n      // 支付方式\n      selectedPaymentMethod: 'alipay-qr',\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null\n    };\n  },\n  computed: {\n    finalRechargeAmount: function finalRechargeAmount() {\n      return this.customAmount || this.selectedAmount || 0;\n    },\n    // 头像URL处理（与个人中心逻辑一致）\n    avatarUrl: function avatarUrl() {\n      var avatar = this.userAvatar;\n\n      if (!avatar) {\n        return this.defaultAvatar;\n      } // 如果是完整的URL，直接返回\n\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是相对路径，使用getFileAccessHttpUrl转换\n\n\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar;\n    }\n  },\n  mounted: function mounted() {\n    // 检查登录状态，只有登录后才加载用户信息\n    if (this.checkLoginStatus()) {\n      this.loadUserInfo();\n      this.loadDefaultAvatar();\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer);\n    }\n  },\n  methods: {\n    // 检查登录状态\n    checkLoginStatus: function checkLoginStatus() {\n      var token = Vue.ls.get(ACCESS_TOKEN);\n\n      if (!token) {\n        console.log('🔍 QuickRecharge: 未登录，不加载用户信息');\n        return false;\n      }\n\n      return true;\n    },\n    // 加载用户信息（包含余额）\n    loadUserInfo: function () {\n      var _loadUserInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var statsResponse, stats, profileResponse, profile, roleResponse;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.prev = 0;\n                this.loading = true; // 加载余额信息\n\n                _context.next = 4;\n                return getTransactionStats();\n\n              case 4:\n                statsResponse = _context.sent;\n\n                if (statsResponse.success) {\n                  stats = statsResponse.result || {};\n                  this.userBalance = stats.accountBalance || 0;\n                } // 加载用户基本信息\n\n\n                _context.next = 8;\n                return getUserProfile();\n\n              case 8:\n                profileResponse = _context.sent;\n\n                if (profileResponse.success) {\n                  profile = profileResponse.result || {};\n                  this.userNickname = profile.nickname || profile.realname || '';\n                  this.userAvatar = profile.avatar || '';\n                  this.userMemberExpireTime = profile.member_expire_time || null;\n                } // 实时获取用户角色（和affiliate页面一样）\n\n\n                _context.next = 12;\n                return getUserRole();\n\n              case 12:\n                roleResponse = _context.sent;\n\n                if (roleResponse.success) {\n                  this.userRole = roleResponse.result.role_code || 'user';\n                  console.log('🔍 QuickRecharge: 实时获取角色成功:', this.userRole);\n                } else {\n                  this.userRole = 'user';\n                  console.warn('🔍 QuickRecharge: 获取角色失败，使用默认值');\n                }\n\n                _context.next = 19;\n                break;\n\n              case 16:\n                _context.prev = 16;\n                _context.t0 = _context[\"catch\"](0);\n                console.error('加载用户信息失败:', _context.t0);\n\n              case 19:\n                _context.prev = 19;\n                this.loading = false;\n                return _context.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[0, 16, 19, 22]]);\n      }));\n\n      function loadUserInfo() {\n        return _loadUserInfo.apply(this, arguments);\n      }\n\n      return loadUserInfo;\n    }(),\n    // 加载TOS默认头像URL\n    loadDefaultAvatar: function () {\n      var _loadDefaultAvatar = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return this.$http.get('/sys/common/default-avatar-url');\n\n              case 3:\n                response = _context2.sent;\n\n                if (response && response.success && response.result) {\n                  this.defaultAvatar = response.result;\n                  console.log('🎯 QuickRecharge: 已加载TOS默认头像:', this.defaultAvatar);\n                }\n\n                _context2.next = 10;\n                break;\n\n              case 7:\n                _context2.prev = 7;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.warn('⚠️ QuickRecharge: 获取TOS默认头像失败，使用本地降级:', _context2.t0); // 保持本地默认头像作为降级方案\n\n              case 10:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 7]]);\n      }));\n\n      function loadDefaultAvatar() {\n        return _loadDefaultAvatar.apply(this, arguments);\n      }\n\n      return loadDefaultAvatar;\n    }(),\n    // 头像URL处理方法（与个人中心逻辑一致）\n    getFileAccessHttpUrl: function getFileAccessHttpUrl(avatar) {\n      if (!avatar) return ''; // 如果已经是完整URL，直接返回\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是TOS文件，使用全局方法\n\n\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar;\n      } // 本地文件，使用静态域名\n\n\n      return this.$store.state.app.staticDomainURL + '/' + avatar;\n    },\n    // 格式化余额显示\n    formatBalance: function formatBalance(balance) {\n      return parseFloat(balance || 0).toFixed(2);\n    },\n    // 选择充值金额\n    selectRechargeAmount: function selectRechargeAmount(amount) {\n      this.selectedAmount = amount;\n      this.customAmount = null;\n    },\n    // 处理充值\n    handleRecharge: function () {\n      var _handleRecharge = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var orderData, response, result;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                if (!(!this.finalRechargeAmount || this.finalRechargeAmount < 0.01)) {\n                  _context3.next = 3;\n                  break;\n                }\n\n                this.$message.warning('请选择或输入充值金额，最低0.01元');\n                return _context3.abrupt(\"return\");\n\n              case 3:\n                _context3.prev = 3;\n                this.rechargeLoading = true;\n                orderData = {\n                  amount: this.finalRechargeAmount,\n                  paymentMethod: this.selectedPaymentMethod\n                };\n                _context3.next = 8;\n                return createRechargeOrder(orderData);\n\n              case 8:\n                response = _context3.sent;\n\n                if (!response.success) {\n                  _context3.next = 22;\n                  break;\n                }\n\n                result = response.result;\n\n                if (!(this.selectedPaymentMethod === 'alipay-page')) {\n                  _context3.next = 16;\n                  break;\n                }\n\n                _context3.next = 14;\n                return this.handleAlipayPagePayment(result.orderId, result.amount);\n\n              case 14:\n                _context3.next = 19;\n                break;\n\n              case 16:\n                if (!(this.selectedPaymentMethod === 'alipay-qr')) {\n                  _context3.next = 19;\n                  break;\n                }\n\n                _context3.next = 19;\n                return this.handleAlipayQrPayment(result.orderId, result.amount);\n\n              case 19:\n                this.showRechargeModal = false;\n                _context3.next = 23;\n                break;\n\n              case 22:\n                this.$message.error(response.message || '创建充值订单失败');\n\n              case 23:\n                _context3.next = 29;\n                break;\n\n              case 25:\n                _context3.prev = 25;\n                _context3.t0 = _context3[\"catch\"](3);\n                console.error('创建充值订单失败:', _context3.t0);\n                this.$message.error('充值失败，请重试');\n\n              case 29:\n                _context3.prev = 29;\n                this.rechargeLoading = false;\n                return _context3.finish(29);\n\n              case 32:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[3, 25, 29, 32]]);\n      }));\n\n      function handleRecharge() {\n        return _handleRecharge.apply(this, arguments);\n      }\n\n      return handleRecharge;\n    }(),\n    // 获取用户角色显示名称（使用实时获取的角色）\n    getUserRoleDisplayName: function getUserRoleDisplayName() {\n      try {\n        // 使用实时获取的角色，而不是localStorage缓存\n        var role = this.userRole; // 角色显示名称映射\n\n        var roleDisplayMap = {\n          // 中文角色名（直接显示）\n          '普通用户': '普通用户',\n          'VIP会员': 'VIP会员',\n          'SVIP会员': 'SVIP会员',\n          '管理员': '管理员',\n          // 英文角色代码（转换为中文）\n          'user': '普通用户',\n          'VIP': 'VIP会员',\n          'SVIP': 'SVIP会员',\n          'admin': '管理员',\n          // 大小写兼容\n          'USER': '普通用户',\n          'vip': 'VIP会员',\n          'svip': 'SVIP会员',\n          'ADMIN': '管理员'\n        };\n        return roleDisplayMap[role] || '普通用户';\n      } catch (error) {\n        console.warn('获取用户角色失败:', error);\n        return '普通用户';\n      }\n    },\n    // 获取会员到期信息\n    getMemberExpireInfo: function getMemberExpireInfo() {\n      try {\n        // 使用实时获取的角色\n        var role = this.userRole; // 只有VIP和SVIP显示到期时间\n\n        if (!role || !['VIP', 'SVIP', 'VIP会员', 'SVIP会员'].includes(role)) {\n          return null;\n        }\n\n        if (!this.userMemberExpireTime) {\n          return null;\n        }\n\n        var expireDate = new Date(this.userMemberExpireTime);\n        var now = new Date(); // 检查是否已过期\n\n        if (expireDate <= now) {\n          return '已过期';\n        } // 计算剩余天数\n\n\n        var diffTime = expireDate.getTime() - now.getTime();\n        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n        if (diffDays <= 7) {\n          return \"\".concat(diffDays, \"\\u5929\\u540E\\u5230\\u671F\");\n        } else {\n          // 格式化到期日期\n          var year = expireDate.getFullYear();\n          var month = String(expireDate.getMonth() + 1).padStart(2, '0');\n          var day = String(expireDate.getDate()).padStart(2, '0');\n          return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"\\u5230\\u671F\");\n        }\n      } catch (error) {\n        console.warn('获取会员到期信息失败:', error);\n        return null;\n      }\n    },\n    // 处理支付宝网页支付\n    handleAlipayPagePayment: function () {\n      var _handleAlipayPagePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4(orderId, amount) {\n        var paymentData, payResponse, payForm, div, form;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount);\n                this.$message.loading('正在跳转到支付宝支付...', 0); // 调用支付宝支付接口\n\n                paymentData = {\n                  orderId: orderId,\n                  amount: amount,\n                  subject: '智界Aigc账户充值',\n                  body: \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)\n                };\n                console.log('🔍 发送支付请求数据:', paymentData);\n                _context4.next = 7;\n                return this.$http.post('/api/alipay/createOrder', paymentData);\n\n              case 7:\n                payResponse = _context4.sent;\n                console.log('🔍 支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context4.next = 24;\n                  break;\n                }\n\n                payForm = payResponse.result.payForm;\n                console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空');\n\n                if (payForm) {\n                  _context4.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付表单为空');\n                return _context4.abrupt(\"return\");\n\n              case 16:\n                // 创建表单并提交到支付宝\n                div = document.createElement('div');\n                div.innerHTML = payForm;\n                document.body.appendChild(div);\n                form = div.querySelector('form');\n\n                if (form) {\n                  console.log('🔍 找到支付表单，准备提交');\n                  form.submit();\n                } else {\n                  console.error('🔍 未找到支付表单');\n                  this.$message.error('支付表单创建失败');\n                } // 清理DOM\n\n\n                setTimeout(function () {\n                  if (document.body.contains(div)) {\n                    document.body.removeChild(div);\n                  }\n                }, 1000);\n                _context4.next = 26;\n                break;\n\n              case 24:\n                console.error('🔍 支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建支付订单失败');\n\n              case 26:\n                _context4.next = 33;\n                break;\n\n              case 28:\n                _context4.prev = 28;\n                _context4.t0 = _context4[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝支付失败:', _context4.t0);\n                this.$message.error('支付宝支付失败，请重试');\n\n              case 33:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 28]]);\n      }));\n\n      function handleAlipayPagePayment(_x, _x2) {\n        return _handleAlipayPagePayment.apply(this, arguments);\n      }\n\n      return handleAlipayPagePayment;\n    }(),\n    // 处理支付宝扫码支付\n    handleAlipayQrPayment: function () {\n      var _handleAlipayQrPayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(orderId, amount) {\n        var paymentData, payResponse, qrCode;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount);\n                this.$message.loading('正在生成支付二维码...', 0); // 调用支付宝扫码支付接口\n\n                paymentData = {\n                  orderId: orderId,\n                  amount: amount,\n                  subject: '智界Aigc账户充值',\n                  body: \"\\u5145\\u503C\\u91D1\\u989D\\uFF1A\\xA5\".concat(amount)\n                };\n                console.log('🔍 发送扫码支付请求数据:', paymentData);\n                _context5.next = 7;\n                return this.$http.post('/api/alipay/createQrOrder', paymentData);\n\n              case 7:\n                payResponse = _context5.sent;\n                console.log('🔍 扫码支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context5.next = 19;\n                  break;\n                }\n\n                qrCode = payResponse.result.qrCode;\n                console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空');\n\n                if (qrCode) {\n                  _context5.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付二维码生成失败');\n                return _context5.abrupt(\"return\");\n\n              case 16:\n                // 显示二维码支付弹窗\n                this.showQrCodeModal(qrCode, orderId, amount);\n                _context5.next = 21;\n                break;\n\n              case 19:\n                console.error('🔍 扫码支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建扫码支付订单失败');\n\n              case 21:\n                _context5.next = 28;\n                break;\n\n              case 23:\n                _context5.prev = 23;\n                _context5.t0 = _context5[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝扫码支付失败:', _context5.t0);\n                this.$message.error('支付宝扫码支付失败，请重试');\n\n              case 28:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 23]]);\n      }));\n\n      function handleAlipayQrPayment(_x3, _x4) {\n        return _handleAlipayQrPayment.apply(this, arguments);\n      }\n\n      return handleAlipayQrPayment;\n    }(),\n    // 显示二维码弹窗\n    showQrCodeModal: function showQrCodeModal(qrCode, orderId, amount) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = \"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=\".concat(encodeURIComponent(qrCode));\n      this.currentOrderId = orderId;\n      this.currentOrderAmount = amount;\n      this.showQrModal = true;\n      console.log('🔍 显示二维码弹窗 - 订单号:', orderId, '金额:', amount);\n      console.log('🔍 二维码URL:', this.qrCodeUrl); // 开始轮询支付状态\n\n      this.startPaymentStatusCheck();\n    },\n    // 关闭二维码弹窗\n    closeQrModal: function closeQrModal() {\n      this.showQrModal = false;\n      this.qrCodeUrl = '';\n      this.currentOrderId = '';\n      this.currentOrderAmount = 0;\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n        this.paymentCheckTimer = null;\n      }\n    },\n    // 开始支付状态检查\n    startPaymentStatusCheck: function startPaymentStatusCheck() {\n      var _this = this;\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n      }\n\n      this.paymentCheckTimer = setInterval(function () {\n        _this.checkPaymentStatus();\n      }, 3000); // 每3秒检查一次\n    },\n    // 检查支付状态\n    checkPaymentStatus: function () {\n      var _checkPaymentStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                if (this.currentOrderId) {\n                  _context6.next = 2;\n                  break;\n                }\n\n                return _context6.abrupt(\"return\");\n\n              case 2:\n                _context6.prev = 2;\n                this.checkingStatus = true;\n                console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId);\n                _context6.next = 7;\n                return this.$http.get(\"/api/alipay/queryOrder/\".concat(this.currentOrderId));\n\n              case 7:\n                response = _context6.sent;\n                console.log('🔍 支付状态查询响应:', response);\n\n                if (response.success && response.result.status === 'TRADE_SUCCESS') {\n                  console.log('🔍 支付成功！');\n                  this.$message.success('支付成功！');\n                  this.closeQrModal();\n                  this.loadUserInfo(); // 刷新用户信息和余额\n\n                  this.$emit('recharge-success'); // 通知父组件\n                } else {\n                  console.log('🔍 支付状态:', response.result && response.result.status || '未知');\n                }\n\n                _context6.next = 15;\n                break;\n\n              case 12:\n                _context6.prev = 12;\n                _context6.t0 = _context6[\"catch\"](2);\n                console.error('检查支付状态失败:', _context6.t0);\n\n              case 15:\n                _context6.prev = 15;\n                this.checkingStatus = false;\n                return _context6.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[2, 12, 15, 18]]);\n      }));\n\n      function checkPaymentStatus() {\n        return _checkPaymentStatus.apply(this, arguments);\n      }\n\n      return checkPaymentStatus;\n    }()\n  }\n};", null]}