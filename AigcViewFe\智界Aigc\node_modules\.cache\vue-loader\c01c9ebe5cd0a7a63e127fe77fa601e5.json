{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753814840953}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { \n  getTransactionStats, \n  createRechargeOrder \n} from '@/api/usercenter'\n\nexport default {\n  name: 'QuickRecharge',\n  data() {\n    return {\n      loading: false,\n      rechargeLoading: false,\n      checkingStatus: false,\n      \n      // 用户余额\n      userBalance: 0,\n      \n      // 充值弹窗\n      showRechargeModal: false,\n      \n      // 充值选项\n      rechargeOptions: [\n        { amount: 50, label: '体验套餐' },\n        { amount: 100, label: '基础套餐' },\n        { amount: 300, label: '进阶套餐' },\n        { amount: 500, label: '专业套餐' },\n        { amount: 1000, label: '企业套餐' }\n      ],\n      selectedAmount: 0,\n      customAmount: null,\n      \n      // 支付方式\n      selectedPaymentMethod: 'alipay-qr',\n      \n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null\n    }\n  },\n  \n  computed: {\n    finalRechargeAmount() {\n      return this.customAmount || this.selectedAmount || 0\n    }\n  },\n  \n  mounted() {\n    this.loadUserBalance()\n  },\n  \n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n  \n  methods: {\n    // 加载用户余额\n    async loadUserBalance() {\n      try {\n        this.loading = true\n        const response = await getTransactionStats()\n        if (response.success) {\n          const stats = response.result || {}\n          this.userBalance = stats.accountBalance || 0\n        }\n      } catch (error) {\n        console.error('加载余额失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 格式化余额显示\n    formatBalance(balance) {\n      return parseFloat(balance || 0).toFixed(2)\n    },\n    \n    // 选择充值金额\n    selectRechargeAmount(amount) {\n      this.selectedAmount = amount\n      this.customAmount = null\n    },\n    \n    // 处理充值\n    async handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元')\n        return\n      }\n\n      try {\n        this.rechargeLoading = true\n\n        const orderData = {\n          amount: this.finalRechargeAmount,\n          paymentMethod: this.selectedPaymentMethod\n        }\n\n        const response = await createRechargeOrder(orderData)\n        if (response.success) {\n          const result = response.result\n\n          if (this.selectedPaymentMethod === 'alipay-page') {\n            await this.handleAlipayPagePayment(result.orderId, result.amount)\n          } else if (this.selectedPaymentMethod === 'alipay-qr') {\n            await this.handleAlipayQrPayment(result.orderId, result.amount)\n          }\n\n          this.showRechargeModal = false\n        } else {\n          this.$message.error(response.message || '创建充值订单失败')\n        }\n      } catch (error) {\n        console.error('创建充值订单失败:', error)\n        this.$message.error('充值失败，请重试')\n      } finally {\n        this.rechargeLoading = false\n      }\n    },\n    \n    // 处理支付宝网页支付\n    async handleAlipayPagePayment(orderId, amount) {\n      try {\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const paymentUrl = payResponse.result.paymentUrl\n          if (paymentUrl) {\n            window.open(paymentUrl, '_blank')\n            this.$message.success('支付页面已打开，请完成支付')\n          } else {\n            this.$message.error('支付链接生成失败')\n          }\n        } else {\n          this.$message.error(payResponse.message || '支付失败')\n        }\n      } catch (error) {\n        console.error('支付宝支付失败:', error)\n        this.$message.error('支付失败，请重试')\n      }\n    },\n    \n    // 处理支付宝扫码支付\n    async handleAlipayQrPayment(orderId, amount) {\n      try {\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          if (qrCode) {\n            this.showQrCodeModal(qrCode, orderId, amount)\n          } else {\n            this.$message.error('支付二维码生成失败')\n          }\n        } else {\n          this.$message.error(payResponse.message || '支付失败')\n        }\n      } catch (error) {\n        console.error('扫码支付失败:', error)\n        this.$message.error('支付失败，请重试')\n      }\n    },\n    \n    // 显示二维码弹窗\n    showQrCodeModal(qrCode, orderId, amount) {\n      this.qrCodeUrl = qrCode\n      this.currentOrderId = orderId\n      this.currentOrderAmount = amount\n      this.showQrModal = true\n      \n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n    \n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n      this.currentOrderId = ''\n      this.currentOrderAmount = 0\n      \n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n    \n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n      \n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n    \n    // 检查支付状态\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n      \n      try {\n        this.checkingStatus = true\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        \n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          this.$message.success('支付成功！')\n          this.closeQrModal()\n          this.loadUserBalance() // 刷新余额\n          this.$emit('recharge-success') // 通知父组件\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["QuickRecharge.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "QuickRecharge.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"quick-recharge-card\">\n    <!-- 余额显示区域 -->\n    <div class=\"balance-section\">\n      <div class=\"balance-info\">\n        <div class=\"balance-icon\">\n          <i class=\"anticon anticon-wallet\"></i>\n        </div>\n        <div class=\"balance-details\">\n          <span class=\"balance-label\">账户余额</span>\n          <span class=\"balance-amount\">¥{{ formatBalance(userBalance) }}</span>\n        </div>\n      </div>\n      <div class=\"balance-actions\">\n        <button class=\"recharge-btn\" @click=\"showRechargeModal = true\">\n          <i class=\"anticon anticon-plus-circle\"></i>\n          快速充值\n        </button>\n      </div>\n    </div>\n    \n    <!-- 充值弹窗 -->\n    <a-modal\n      title=\"账户充值\"\n      :visible=\"showRechargeModal\"\n      @cancel=\"showRechargeModal = false\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"recharge-modal-content\">\n        <!-- 充值选项 -->\n        <div class=\"recharge-options\">\n          <h4>选择充值金额</h4>\n          <div class=\"options-grid\">\n            <div \n              v-for=\"option in rechargeOptions\" \n              :key=\"option.amount\"\n              class=\"recharge-option\"\n              :class=\"{ selected: selectedAmount === option.amount }\"\n              @click=\"selectRechargeAmount(option.amount)\"\n            >\n              <div class=\"option-amount\">¥{{ option.amount }}</div>\n              <div class=\"option-label\">{{ option.label }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 自定义金额 -->\n        <div class=\"custom-amount\">\n          <h4>自定义金额</h4>\n          <div class=\"custom-input\">\n            <a-input-number\n              v-model=\"customAmount\"\n              :min=\"0.01\"\n              :max=\"10000\"\n              :step=\"0.01\"\n              placeholder=\"最低0.01元\"\n              size=\"large\"\n              style=\"width: 200px\"\n            />\n            <span class=\"currency\">元</span>\n          </div>\n        </div>\n        \n        <!-- 支付方式 -->\n        <div class=\"payment-methods\">\n          <h4>支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n            <a-radio-button value=\"alipay-qr\">\n              <i class=\"anticon anticon-qrcode\"></i>\n              支付宝扫码\n            </a-radio-button>\n            <a-radio-button value=\"alipay-page\">\n              <i class=\"anticon anticon-alipay\"></i>\n              支付宝网页\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n        \n        <!-- 充值按钮 -->\n        <div class=\"recharge-actions\">\n          <div class=\"amount-summary\">\n            <span>充值金额：</span>\n            <span class=\"final-amount\">¥{{ finalRechargeAmount }}</span>\n          </div>\n          <a-button \n            type=\"primary\" \n            size=\"large\"\n            :loading=\"rechargeLoading\"\n            @click=\"handleRecharge\"\n            :disabled=\"!finalRechargeAmount || finalRechargeAmount < 0.01\"\n          >\n            确认充值\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n    \n    <!-- 支付二维码弹窗 -->\n    <a-modal\n      title=\"扫码支付\"\n      :visible=\"showQrModal\"\n      @cancel=\"closeQrModal\"\n      :footer=\"null\"\n      width=\"400px\"\n    >\n      <div class=\"qr-payment-content\">\n        <div class=\"qr-code-container\">\n          <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n            <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n          </div>\n          <div v-else class=\"qr-loading\">\n            <a-spin size=\"large\" />\n            <p>正在生成二维码...</p>\n          </div>\n        </div>\n        <div class=\"qr-info\">\n          <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n          <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n        </div>\n        <div class=\"qr-status\">\n          <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n            检查支付状态\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport { \n  getTransactionStats, \n  createRechargeOrder \n} from '@/api/usercenter'\n\nexport default {\n  name: 'QuickRecharge',\n  data() {\n    return {\n      loading: false,\n      rechargeLoading: false,\n      checkingStatus: false,\n      \n      // 用户余额\n      userBalance: 0,\n      \n      // 充值弹窗\n      showRechargeModal: false,\n      \n      // 充值选项\n      rechargeOptions: [\n        { amount: 50, label: '体验套餐' },\n        { amount: 100, label: '基础套餐' },\n        { amount: 300, label: '进阶套餐' },\n        { amount: 500, label: '专业套餐' },\n        { amount: 1000, label: '企业套餐' }\n      ],\n      selectedAmount: 0,\n      customAmount: null,\n      \n      // 支付方式\n      selectedPaymentMethod: 'alipay-qr',\n      \n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null\n    }\n  },\n  \n  computed: {\n    finalRechargeAmount() {\n      return this.customAmount || this.selectedAmount || 0\n    }\n  },\n  \n  mounted() {\n    this.loadUserBalance()\n  },\n  \n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n  \n  methods: {\n    // 加载用户余额\n    async loadUserBalance() {\n      try {\n        this.loading = true\n        const response = await getTransactionStats()\n        if (response.success) {\n          const stats = response.result || {}\n          this.userBalance = stats.accountBalance || 0\n        }\n      } catch (error) {\n        console.error('加载余额失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 格式化余额显示\n    formatBalance(balance) {\n      return parseFloat(balance || 0).toFixed(2)\n    },\n    \n    // 选择充值金额\n    selectRechargeAmount(amount) {\n      this.selectedAmount = amount\n      this.customAmount = null\n    },\n    \n    // 处理充值\n    async handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元')\n        return\n      }\n\n      try {\n        this.rechargeLoading = true\n\n        const orderData = {\n          amount: this.finalRechargeAmount,\n          paymentMethod: this.selectedPaymentMethod\n        }\n\n        const response = await createRechargeOrder(orderData)\n        if (response.success) {\n          const result = response.result\n\n          if (this.selectedPaymentMethod === 'alipay-page') {\n            await this.handleAlipayPagePayment(result.orderId, result.amount)\n          } else if (this.selectedPaymentMethod === 'alipay-qr') {\n            await this.handleAlipayQrPayment(result.orderId, result.amount)\n          }\n\n          this.showRechargeModal = false\n        } else {\n          this.$message.error(response.message || '创建充值订单失败')\n        }\n      } catch (error) {\n        console.error('创建充值订单失败:', error)\n        this.$message.error('充值失败，请重试')\n      } finally {\n        this.rechargeLoading = false\n      }\n    },\n    \n    // 处理支付宝网页支付\n    async handleAlipayPagePayment(orderId, amount) {\n      try {\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const paymentUrl = payResponse.result.paymentUrl\n          if (paymentUrl) {\n            window.open(paymentUrl, '_blank')\n            this.$message.success('支付页面已打开，请完成支付')\n          } else {\n            this.$message.error('支付链接生成失败')\n          }\n        } else {\n          this.$message.error(payResponse.message || '支付失败')\n        }\n      } catch (error) {\n        console.error('支付宝支付失败:', error)\n        this.$message.error('支付失败，请重试')\n      }\n    },\n    \n    // 处理支付宝扫码支付\n    async handleAlipayQrPayment(orderId, amount) {\n      try {\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          if (qrCode) {\n            this.showQrCodeModal(qrCode, orderId, amount)\n          } else {\n            this.$message.error('支付二维码生成失败')\n          }\n        } else {\n          this.$message.error(payResponse.message || '支付失败')\n        }\n      } catch (error) {\n        console.error('扫码支付失败:', error)\n        this.$message.error('支付失败，请重试')\n      }\n    },\n    \n    // 显示二维码弹窗\n    showQrCodeModal(qrCode, orderId, amount) {\n      this.qrCodeUrl = qrCode\n      this.currentOrderId = orderId\n      this.currentOrderAmount = amount\n      this.showQrModal = true\n      \n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n    \n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n      this.currentOrderId = ''\n      this.currentOrderAmount = 0\n      \n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n    \n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n      \n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n    \n    // 检查支付状态\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n      \n      try {\n        this.checkingStatus = true\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        \n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          this.$message.success('支付成功！')\n          this.closeQrModal()\n          this.loadUserBalance() // 刷新余额\n          this.$emit('recharge-success') // 通知父组件\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    }\n  }\n}\n</script>\n"]}]}