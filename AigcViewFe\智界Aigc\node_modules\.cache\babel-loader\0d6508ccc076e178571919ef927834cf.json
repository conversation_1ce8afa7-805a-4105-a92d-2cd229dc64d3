{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753837377830}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { login, getCaptcha, phoneLogin, emailLogin } from '@/api/login';\nimport { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt';\nimport { getAction } from '@/api/manage';\nimport { gsap } from 'gsap';\nimport WebsiteHeader from '@/components/website/WebsiteHeader.vue';\nimport LogoImage from '@/components/common/LogoImage.vue';\nimport { ACCESS_TOKEN, USER_NAME, USER_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types';\nimport { checkUsername, sendSmsCode, sendEmailCode, register, generateWechatQrCode } from '@/api/register';\nimport Vue from 'vue';\nimport { handleLoginConflict } from '@/utils/loginConflictHandler';\nimport { getReferralCode } from '@/utils/referralUtils';\nexport default {\n  name: 'WebsiteLogin',\n  components: {\n    WebsiteHeader: WebsiteHeader,\n    LogoImage: LogoImage\n  },\n  data: function data() {\n    return {\n      // 登录相关\n      form: this.$form.createForm(this),\n      phoneLoginForm: this.$form.createForm(this),\n      emailLoginForm: this.$form.createForm(this),\n      loginLoading: false,\n      phoneLoginLoading: false,\n      emailLoginLoading: false,\n      rememberMe: false,\n      randCodeImage: '',\n      currdatetime: new Date().getTime(),\n      encryptedString: '',\n      // 登录方式切换\n      loginType: 'phone',\n      // password, phone, email, wechat - 默认手机号登录\n      // 验证码相关\n      smsCodeSending: false,\n      smsCountdown: 0,\n      emailCodeSending: false,\n      emailCountdown: 0,\n      // 邀请码（静默处理）\n      inviteCodeFromUrl: '',\n      // 微信登录\n      wechatLoginQrCode: '',\n      features: [{\n        icon: 'robot',\n        title: 'AI智能创作',\n        description: '强大的AI算法，助您快速生成高质量内容'\n      }, {\n        icon: 'thunderbolt',\n        title: '极速响应',\n        description: '毫秒级响应速度，让创作灵感不再等待'\n      }, {\n        icon: 'safety-certificate',\n        title: '安全可靠',\n        description: '企业级安全保障，保护您的创作成果'\n      }, {\n        icon: 'global',\n        title: '全球服务',\n        description: '覆盖全球的CDN网络，随时随地畅享服务'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.getEncrypte();\n    this.handleChangeCheckCode();\n    this.initAnimations();\n    this.checkInviteCode();\n  },\n  methods: {\n    // 获取密码加密规则\n    getEncrypte: function getEncrypte() {\n      var _this = this;\n\n      getEncryptedString().then(function (data) {\n        _this.encryptedString = data;\n      });\n    },\n    // 刷新验证码\n    handleChangeCheckCode: function handleChangeCheckCode() {\n      var _this2 = this;\n\n      this.currdatetime = new Date().getTime();\n      getAction(\"/sys/randomImage/\".concat(this.currdatetime)).then(function (res) {\n        if (res.success) {\n          _this2.randCodeImage = res.result;\n        } else {\n          _this2.$message.error(res.message);\n        }\n      }).catch(function () {\n        _this2.$message.error('验证码加载失败');\n      });\n    },\n    handleSubmit: function handleSubmit(e) {\n      var _this3 = this;\n\n      e.preventDefault();\n      this.form.validateFields(function (err, values) {\n        if (!err) {\n          _this3.loginLoading = true;\n          console.log('官网登录信息:', values); // 使用真实的登录API\n\n          var user = encryption(values.username, _this3.encryptedString.key, _this3.encryptedString.iv);\n          var pwd = encryption(values.password, _this3.encryptedString.key, _this3.encryptedString.iv);\n          var loginParams = {\n            username: user,\n            password: pwd,\n            captcha: values.inputCode,\n            checkKey: _this3.currdatetime,\n            remember_me: _this3.rememberMe,\n            loginType: 'website' // 标识为官网用户登录\n\n          };\n          console.log(\"官网登录参数\", loginParams);\n          login(loginParams).then( /*#__PURE__*/function () {\n            var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(res) {\n              var result, userInfo, roleRes, userRole, departId, redirectPath, _redirectPath, _redirectPath2;\n\n              return _regeneratorRuntime.wrap(function _callee$(_context) {\n                while (1) {\n                  switch (_context.prev = _context.next) {\n                    case 0:\n                      _this3.loginLoading = false;\n                      console.log(\"🔍 登录响应:\", res);\n                      console.log(\"🔍 响应code:\", res.code, \"类型:\", _typeof(res.code));\n\n                      if (!(res.code === 200 || res.code === '200')) {\n                        _context.next = 25;\n                        break;\n                      }\n\n                      _this3.$notification.success({\n                        message: '登录成功',\n                        description: '欢迎回来！正在跳转到个人中心...',\n                        placement: 'topRight',\n                        duration: 3,\n                        style: {\n                          width: '350px',\n                          marginTop: '101px',\n                          borderRadius: '8px',\n                          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                        }\n                      }); // ✅ 存储登录信息\n\n\n                      result = res.result;\n                      userInfo = result.userInfo;\n                      Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // ✅ 获取用户角色信息\n\n                      _context.prev = 11;\n                      _context.next = 14;\n                      return getAction(\"/sys/user/getCurrentUserDeparts\");\n\n                    case 14:\n                      roleRes = _context.sent;\n\n                      if (roleRes.success) {\n                        userRole = roleRes.result.role;\n                        departId = roleRes.result.departId; // 存储角色信息\n\n                        localStorage.setItem('userRole', userRole || '');\n                        localStorage.setItem('departId', departId || ''); // 优先处理重定向参数\n\n                        redirectPath = _this3.$route.query.redirect;\n                        console.log('🔍 登录成功，检查重定向参数:', redirectPath);\n\n                        if (redirectPath) {\n                          // 有重定向参数，直接跳转到目标页面\n                          console.log('🔄 有重定向参数，跳转到:', redirectPath);\n\n                          _this3.$router.push(redirectPath);\n                        } else {\n                          // 没有重定向参数，根据角色决定跳转\n                          if (_this3.isAdminRole(userRole)) {\n                            // 管理员用户，跳转到后台\n                            console.log('🔄 管理员用户，跳转到后台管理');\n\n                            _this3.$router.push('/dashboard/analysis');\n                          } else {\n                            // 普通用户，跳转到个人中心\n                            console.log('🔄 普通用户，跳转到个人中心');\n\n                            _this3.$router.push('/usercenter');\n                          }\n                        }\n                      } else {\n                        // 获取角色失败，检查重定向参数\n                        _redirectPath = _this3.$route.query.redirect;\n\n                        if (_redirectPath) {\n                          _this3.$router.push(_redirectPath);\n                        } else {\n                          _this3.$router.push('/usercenter');\n                        }\n                      }\n\n                      _context.next = 23;\n                      break;\n\n                    case 18:\n                      _context.prev = 18;\n                      _context.t0 = _context[\"catch\"](11);\n                      console.error('获取角色信息失败:', _context.t0); // 出错时也检查重定向参数\n\n                      _redirectPath2 = _this3.$route.query.redirect;\n\n                      if (_redirectPath2) {\n                        _this3.$router.push(_redirectPath2);\n                      } else {\n                        _this3.$router.push('/usercenter');\n                      }\n\n                    case 23:\n                      _context.next = 27;\n                      break;\n\n                    case 25:\n                      _this3.$notification.error({\n                        message: '登录失败',\n                        description: res.message || '用户名或密码错误，请检查后重试',\n                        placement: 'topRight',\n                        duration: 4,\n                        style: {\n                          width: '380px',\n                          marginTop: '101px',\n                          borderRadius: '8px',\n                          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                        }\n                      });\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                    case 27:\n                    case \"end\":\n                      return _context.stop();\n                  }\n                }\n              }, _callee, null, [[11, 18]]);\n            }));\n\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }()).catch( /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3(err) {\n              var conflictInfo, forceLoginFn, forceLoginResponse, result, userInfo, redirectPath;\n              return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n                while (1) {\n                  switch (_context3.prev = _context3.next) {\n                    case 0:\n                      _this3.loginLoading = false; // 检查是否是登录冲突错误\n\n                      if (!(err.response && err.response.data && err.response.data.code === 4002)) {\n                        _context3.next = 36;\n                        break;\n                      }\n\n                      console.log('检测到用户名密码登录冲突，显示确认弹窗');\n                      conflictInfo = err.response.data.result; // 创建强制登录函数\n\n                      forceLoginFn = /*#__PURE__*/function () {\n                        var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n                          var forceLoginParams;\n                          return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                            while (1) {\n                              switch (_context2.prev = _context2.next) {\n                                case 0:\n                                  forceLoginParams = _objectSpread(_objectSpread({}, loginParams), {}, {\n                                    loginType: 'force' // 修改登录类型为强制登录\n\n                                  });\n                                  console.log('用户名密码强制登录数据:', forceLoginParams);\n                                  _context2.next = 4;\n                                  return login(forceLoginParams);\n\n                                case 4:\n                                  return _context2.abrupt(\"return\", _context2.sent);\n\n                                case 5:\n                                case \"end\":\n                                  return _context2.stop();\n                              }\n                            }\n                          }, _callee2);\n                        }));\n\n                        return function forceLoginFn() {\n                          return _ref3.apply(this, arguments);\n                        };\n                      }();\n\n                      _context3.prev = 5;\n                      _context3.next = 8;\n                      return handleLoginConflict(conflictInfo, forceLoginFn);\n\n                    case 8:\n                      forceLoginResponse = _context3.sent;\n\n                      if (!(forceLoginResponse && (forceLoginResponse.code === 200 || forceLoginResponse.code === '200'))) {\n                        _context3.next = 21;\n                        break;\n                      }\n\n                      // 强制登录成功，执行登录成功的逻辑\n                      _this3.$notification.success({\n                        message: '登录成功',\n                        description: '欢迎回来！正在跳转到个人中心...',\n                        placement: 'topRight',\n                        duration: 3\n                      }); // 存储登录信息\n\n\n                      result = forceLoginResponse.result;\n                      userInfo = result.userInfo;\n                      Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000);\n                      Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // 跳转逻辑\n\n                      redirectPath = _this3.$route.query.redirect;\n\n                      if (redirectPath) {\n                        _this3.$router.push(redirectPath);\n                      } else {\n                        _this3.$router.push('/usercenter');\n                      }\n\n                      _context3.next = 22;\n                      break;\n\n                    case 21:\n                      throw new Error(forceLoginResponse && forceLoginResponse.message || '强制登录失败');\n\n                    case 22:\n                      _context3.next = 34;\n                      break;\n\n                    case 24:\n                      _context3.prev = 24;\n                      _context3.t0 = _context3[\"catch\"](5);\n\n                      if (!(_context3.t0.message === 'USER_CANCELLED')) {\n                        _context3.next = 32;\n                        break;\n                      }\n\n                      // 用户取消登录\n                      console.log('用户取消用户名密码强制登录');\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                      return _context3.abrupt(\"return\");\n\n                    case 32:\n                      _this3.$notification.error({\n                        message: '登录失败',\n                        description: _context3.t0.message || '强制登录失败',\n                        placement: 'topRight',\n                        duration: 4\n                      });\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                    case 34:\n                      _context3.next = 38;\n                      break;\n\n                    case 36:\n                      // 其他错误，显示原有的错误处理\n                      _this3.$notification.error({\n                        message: '登录失败',\n                        description: err.message || '网络连接异常，请检查网络后重试',\n                        placement: 'topRight',\n                        duration: 4,\n                        style: {\n                          width: '380px',\n                          marginTop: '101px',\n                          borderRadius: '8px',\n                          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                        }\n                      });\n\n                      _this3.handleChangeCheckCode(); // 刷新验证码\n\n\n                    case 38:\n                    case \"end\":\n                      return _context3.stop();\n                  }\n                }\n              }, _callee3, null, [[5, 24]]);\n            }));\n\n            return function (_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }());\n        }\n      });\n    },\n    handleForgotPassword: function handleForgotPassword() {\n      this.$notification.info({\n        message: '忘记密码',\n        description: '忘记密码功能正在开发中，敬请期待...',\n        placement: 'topRight',\n        duration: 3,\n        style: {\n          width: '350px',\n          marginTop: '101px',\n          borderRadius: '8px',\n          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n        }\n      }); // TODO: 跳转到忘记密码页面\n    },\n    handleSocialLogin: function handleSocialLogin(type) {\n      var typeMap = {\n        wechat: '微信',\n        qq: 'QQ',\n        alipay: '支付宝'\n      };\n      this.$message.info(\"\".concat(typeMap[type], \"\\u767B\\u5F55\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\")); // TODO: 实现第三方登录\n    },\n    // 检查URL中的邀请码（静默处理）\n    checkInviteCode: function checkInviteCode() {\n      // 优先从sessionStorage获取邀请码\n      var sessionReferralCode = getReferralCode(); // 支持两种参数格式：ref（推广链接）和 invite（邀请码）\n\n      var refCode = this.$route.query.ref;\n      var inviteCode = this.$route.query.invite;\n      var urlInviteCode = refCode || inviteCode; // 优先使用sessionStorage中的邀请码，如果没有再使用URL中的\n\n      var finalInviteCode = sessionReferralCode || urlInviteCode;\n\n      if (finalInviteCode) {\n        this.inviteCodeFromUrl = finalInviteCode; // 静默记录邀请码，不显示给用户\n\n        console.log('🔗 Login.vue检测到邀请码:', finalInviteCode, '来源:', sessionReferralCode ? 'sessionStorage' : refCode ? 'ref参数' : 'invite参数');\n      }\n    },\n    // 登录方式切换\n    switchLoginType: function switchLoginType(type) {\n      this.loginType = type; // 重置验证码倒计时\n\n      this.smsCountdown = 0;\n      this.emailCountdown = 0;\n\n      if (type === 'wechat') {\n        this.generateWechatLoginQrCode();\n      }\n    },\n    // 手机号登录（自动注册）\n    handlePhoneLogin: function handlePhoneLogin(e) {\n      var _this4 = this;\n\n      e.preventDefault();\n      this.phoneLoginForm.validateFields( /*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4(err, values) {\n          var checkResponse;\n          return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n            while (1) {\n              switch (_context4.prev = _context4.next) {\n                case 0:\n                  if (err) {\n                    _context4.next = 19;\n                    break;\n                  }\n\n                  _this4.phoneLoginLoading = true;\n                  _context4.prev = 2;\n                  _context4.next = 5;\n                  return checkUsername(values.phone, 'phone');\n\n                case 5:\n                  checkResponse = _context4.sent;\n\n                  if (!checkResponse.success) {\n                    _context4.next = 11;\n                    break;\n                  }\n\n                  _context4.next = 9;\n                  return _this4.autoRegisterAndLogin('phone', values);\n\n                case 9:\n                  _context4.next = 13;\n                  break;\n\n                case 11:\n                  _context4.next = 13;\n                  return _this4.loginWithSmsCode(values);\n\n                case 13:\n                  _context4.next = 19;\n                  break;\n\n                case 15:\n                  _context4.prev = 15;\n                  _context4.t0 = _context4[\"catch\"](2);\n                  _this4.phoneLoginLoading = false;\n\n                  _this4.$notification.error({\n                    message: '登录失败',\n                    description: _context4.t0.message || '登录过程中发生错误',\n                    placement: 'topRight',\n                    duration: 4\n                  });\n\n                case 19:\n                case \"end\":\n                  return _context4.stop();\n              }\n            }\n          }, _callee4, null, [[2, 15]]);\n        }));\n\n        return function (_x3, _x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 邮箱登录（自动注册）\n    handleEmailLogin: function handleEmailLogin(e) {\n      var _this5 = this;\n\n      e.preventDefault();\n      this.emailLoginForm.validateFields( /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(err, values) {\n          var checkResponse;\n          return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n            while (1) {\n              switch (_context5.prev = _context5.next) {\n                case 0:\n                  if (err) {\n                    _context5.next = 19;\n                    break;\n                  }\n\n                  _this5.emailLoginLoading = true;\n                  _context5.prev = 2;\n                  _context5.next = 5;\n                  return checkUsername(values.email, 'email');\n\n                case 5:\n                  checkResponse = _context5.sent;\n\n                  if (!checkResponse.success) {\n                    _context5.next = 11;\n                    break;\n                  }\n\n                  _context5.next = 9;\n                  return _this5.autoRegisterAndLogin('email', values);\n\n                case 9:\n                  _context5.next = 13;\n                  break;\n\n                case 11:\n                  _context5.next = 13;\n                  return _this5.loginWithEmailCode(values);\n\n                case 13:\n                  _context5.next = 19;\n                  break;\n\n                case 15:\n                  _context5.prev = 15;\n                  _context5.t0 = _context5[\"catch\"](2);\n                  _this5.emailLoginLoading = false;\n\n                  _this5.$notification.error({\n                    message: '登录失败',\n                    description: _context5.t0.message || '登录过程中发生错误',\n                    placement: 'topRight',\n                    duration: 4\n                  });\n\n                case 19:\n                case \"end\":\n                  return _context5.stop();\n              }\n            }\n          }, _callee5, null, [[2, 15]]);\n        }));\n\n        return function (_x5, _x6) {\n          return _ref5.apply(this, arguments);\n        };\n      }());\n    },\n    // 自动注册并登录（无密码账户）\n    autoRegisterAndLogin: function () {\n      var _autoRegisterAndLogin = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(type, values) {\n        var _registerData, randomPassword, registerData, registerResponse;\n\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                // 为无密码账户生成符合要求的随机密码\n                randomPassword = this.generateSecurePassword(); // 构建注册数据\n\n                registerData = (_registerData = {\n                  type: type\n                }, _defineProperty(_registerData, type, values[type]), _defineProperty(_registerData, \"verifyCode\", values[type === 'phone' ? 'smsCode' : 'emailCode']), _defineProperty(_registerData, \"password\", randomPassword), _defineProperty(_registerData, \"confirmPassword\", randomPassword), _defineProperty(_registerData, \"inviteCode\", this.inviteCodeFromUrl), _defineProperty(_registerData, \"inviteSource\", this.inviteCodeFromUrl ? 'link' : null), _registerData);\n                console.log('🔗 自动注册数据:', {\n                  type: registerData.type,\n                  email: registerData.email,\n                  inviteCode: registerData.inviteCode,\n                  inviteSource: registerData.inviteSource,\n                  hasInviteCode: !!registerData.inviteCode\n                }); // 调用注册接口\n\n                _context6.next = 6;\n                return register(registerData);\n\n              case 6:\n                registerResponse = _context6.sent;\n\n                if (!registerResponse.success) {\n                  _context6.next = 13;\n                  break;\n                }\n\n                // 注册成功，现在需要自动登录获取token\n                console.log('注册成功，用户ID:', registerResponse.result); // 使用生成的密码进行自动登录\n\n                _context6.next = 11;\n                return this.performAutoLogin(type, values, randomPassword);\n\n              case 11:\n                _context6.next = 14;\n                break;\n\n              case 13:\n                throw new Error(registerResponse.message || '注册失败');\n\n              case 14:\n                _context6.next = 19;\n                break;\n\n              case 16:\n                _context6.prev = 16;\n                _context6.t0 = _context6[\"catch\"](0);\n                throw _context6.t0;\n\n              case 19:\n                _context6.prev = 19;\n                this.phoneLoginLoading = false;\n                this.emailLoginLoading = false;\n                return _context6.finish(19);\n\n              case 23:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 16, 19, 23]]);\n      }));\n\n      function autoRegisterAndLogin(_x7, _x8) {\n        return _autoRegisterAndLogin.apply(this, arguments);\n      }\n\n      return autoRegisterAndLogin;\n    }(),\n    // 使用短信验证码登录\n    loginWithSmsCode: function () {\n      var _loginWithSmsCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8(values) {\n        var loginData, loginResponse, conflictInfo, forceLoginFn, forceLoginResponse;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n                // 构建登录数据\n                loginData = {\n                  mobile: values.phone,\n                  captcha: values.smsCode,\n                  loginType: 'website' // 标识为官网用户登录\n\n                };\n                console.log('短信验证码登录:', loginData); // 调用短信验证码登录接口\n\n                _context8.next = 5;\n                return phoneLogin(loginData);\n\n              case 5:\n                loginResponse = _context8.sent;\n\n                if (!loginResponse.success) {\n                  _context8.next = 11;\n                  break;\n                }\n\n                _context8.next = 9;\n                return this.handleLoginSuccess(loginResponse.result);\n\n              case 9:\n                _context8.next = 38;\n                break;\n\n              case 11:\n                if (!(loginResponse.code === 4002)) {\n                  _context8.next = 37;\n                  break;\n                }\n\n                console.log('检测到手机号登录冲突，显示确认弹窗');\n                conflictInfo = loginResponse.result; // 创建强制登录函数\n\n                forceLoginFn = /*#__PURE__*/function () {\n                  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n                    var forceLoginData;\n                    return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n                      while (1) {\n                        switch (_context7.prev = _context7.next) {\n                          case 0:\n                            forceLoginData = _objectSpread(_objectSpread({}, loginData), {}, {\n                              loginType: 'force' // 修改登录类型为强制登录\n\n                            });\n                            console.log('手机号强制登录数据:', forceLoginData);\n                            _context7.next = 4;\n                            return phoneLogin(forceLoginData);\n\n                          case 4:\n                            return _context7.abrupt(\"return\", _context7.sent);\n\n                          case 5:\n                          case \"end\":\n                            return _context7.stop();\n                        }\n                      }\n                    }, _callee7);\n                  }));\n\n                  return function forceLoginFn() {\n                    return _ref6.apply(this, arguments);\n                  };\n                }();\n\n                _context8.prev = 15;\n                _context8.next = 18;\n                return handleLoginConflict(conflictInfo, forceLoginFn);\n\n              case 18:\n                forceLoginResponse = _context8.sent;\n\n                if (!(forceLoginResponse && forceLoginResponse.success)) {\n                  _context8.next = 24;\n                  break;\n                }\n\n                _context8.next = 22;\n                return this.handleLoginSuccess(forceLoginResponse.result);\n\n              case 22:\n                _context8.next = 25;\n                break;\n\n              case 24:\n                throw new Error(forceLoginResponse && forceLoginResponse.message || '强制登录失败');\n\n              case 25:\n                _context8.next = 35;\n                break;\n\n              case 27:\n                _context8.prev = 27;\n                _context8.t0 = _context8[\"catch\"](15);\n\n                if (!(_context8.t0.message === 'USER_CANCELLED')) {\n                  _context8.next = 34;\n                  break;\n                }\n\n                // 用户取消登录\n                console.log('用户取消手机号强制登录');\n                return _context8.abrupt(\"return\");\n\n              case 34:\n                throw _context8.t0;\n\n              case 35:\n                _context8.next = 38;\n                break;\n\n              case 37:\n                throw new Error(loginResponse.message || '登录失败');\n\n              case 38:\n                _context8.next = 43;\n                break;\n\n              case 40:\n                _context8.prev = 40;\n                _context8.t1 = _context8[\"catch\"](0);\n                throw _context8.t1;\n\n              case 43:\n                _context8.prev = 43;\n                this.phoneLoginLoading = false;\n                return _context8.finish(43);\n\n              case 46:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[0, 40, 43, 46], [15, 27]]);\n      }));\n\n      function loginWithSmsCode(_x9) {\n        return _loginWithSmsCode.apply(this, arguments);\n      }\n\n      return loginWithSmsCode;\n    }(),\n    // 使用邮箱验证码登录\n    loginWithEmailCode: function () {\n      var _loginWithEmailCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10(values) {\n        var loginData, loginResponse, conflictInfo, forceLoginFn, forceLoginResponse;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                _context10.prev = 0;\n                // 构建登录数据\n                loginData = {\n                  email: values.email,\n                  emailCode: values.emailCode,\n                  loginType: 'website' // 标识为官网用户登录\n\n                };\n                console.log('邮箱验证码登录:', loginData); // 调用邮箱验证码登录接口\n\n                _context10.next = 5;\n                return emailLogin(loginData);\n\n              case 5:\n                loginResponse = _context10.sent;\n\n                if (!loginResponse.success) {\n                  _context10.next = 11;\n                  break;\n                }\n\n                _context10.next = 9;\n                return this.handleLoginSuccess(loginResponse.result);\n\n              case 9:\n                _context10.next = 38;\n                break;\n\n              case 11:\n                if (!(loginResponse.code === 4002)) {\n                  _context10.next = 37;\n                  break;\n                }\n\n                console.log('检测到邮箱登录冲突，显示确认弹窗');\n                conflictInfo = loginResponse.result; // 创建强制登录函数\n\n                forceLoginFn = /*#__PURE__*/function () {\n                  var _ref7 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n                    var forceLoginData;\n                    return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n                      while (1) {\n                        switch (_context9.prev = _context9.next) {\n                          case 0:\n                            forceLoginData = _objectSpread(_objectSpread({}, loginData), {}, {\n                              loginType: 'force' // 修改登录类型为强制登录\n\n                            });\n                            console.log('邮箱强制登录数据:', forceLoginData);\n                            _context9.next = 4;\n                            return emailLogin(forceLoginData);\n\n                          case 4:\n                            return _context9.abrupt(\"return\", _context9.sent);\n\n                          case 5:\n                          case \"end\":\n                            return _context9.stop();\n                        }\n                      }\n                    }, _callee9);\n                  }));\n\n                  return function forceLoginFn() {\n                    return _ref7.apply(this, arguments);\n                  };\n                }();\n\n                _context10.prev = 15;\n                _context10.next = 18;\n                return handleLoginConflict(conflictInfo, forceLoginFn);\n\n              case 18:\n                forceLoginResponse = _context10.sent;\n\n                if (!(forceLoginResponse && forceLoginResponse.success)) {\n                  _context10.next = 24;\n                  break;\n                }\n\n                _context10.next = 22;\n                return this.handleLoginSuccess(forceLoginResponse.result);\n\n              case 22:\n                _context10.next = 25;\n                break;\n\n              case 24:\n                throw new Error(forceLoginResponse && forceLoginResponse.message || '强制登录失败');\n\n              case 25:\n                _context10.next = 35;\n                break;\n\n              case 27:\n                _context10.prev = 27;\n                _context10.t0 = _context10[\"catch\"](15);\n\n                if (!(_context10.t0.message === 'USER_CANCELLED')) {\n                  _context10.next = 34;\n                  break;\n                }\n\n                // 用户取消登录\n                console.log('用户取消邮箱强制登录');\n                return _context10.abrupt(\"return\");\n\n              case 34:\n                throw _context10.t0;\n\n              case 35:\n                _context10.next = 38;\n                break;\n\n              case 37:\n                throw new Error(loginResponse.message || '登录失败');\n\n              case 38:\n                _context10.next = 43;\n                break;\n\n              case 40:\n                _context10.prev = 40;\n                _context10.t1 = _context10[\"catch\"](0);\n                throw _context10.t1;\n\n              case 43:\n                _context10.prev = 43;\n                this.emailLoginLoading = false;\n                return _context10.finish(43);\n\n              case 46:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[0, 40, 43, 46], [15, 27]]);\n      }));\n\n      function loginWithEmailCode(_x10) {\n        return _loginWithEmailCode.apply(this, arguments);\n      }\n\n      return loginWithEmailCode;\n    }(),\n    // 处理登录成功\n    handleLoginSuccess: function () {\n      var _handleLoginSuccess = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11(result) {\n        var redirect;\n        return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n          while (1) {\n            switch (_context11.prev = _context11.next) {\n              case 0:\n                _context11.prev = 0;\n                // 存储token和用户信息\n                Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_NAME, result.userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_INFO, result.userInfo, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // 显示登录成功消息\n\n                this.$notification.success({\n                  message: '登录成功',\n                  description: \"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\".concat(result.userInfo.realname || result.userInfo.username, \"\\uFF01\"),\n                  placement: 'topRight',\n                  duration: 3\n                }); // 跳转到目标页面\n\n                redirect = this.$route.query.redirect || '/';\n                this.$router.push(redirect);\n                _context11.next = 14;\n                break;\n\n              case 10:\n                _context11.prev = 10;\n                _context11.t0 = _context11[\"catch\"](0);\n                console.error('处理登录成功失败:', _context11.t0);\n                throw new Error('登录后处理失败');\n\n              case 14:\n              case \"end\":\n                return _context11.stop();\n            }\n          }\n        }, _callee11, this, [[0, 10]]);\n      }));\n\n      function handleLoginSuccess(_x11) {\n        return _handleLoginSuccess.apply(this, arguments);\n      }\n\n      return handleLoginSuccess;\n    }(),\n    // 发送登录短信验证码\n    sendLoginSmsCode: function () {\n      var _sendLoginSmsCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {\n        var phone, response;\n        return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n          while (1) {\n            switch (_context12.prev = _context12.next) {\n              case 0:\n                phone = this.phoneLoginForm.getFieldValue('phone');\n\n                if (phone) {\n                  _context12.next = 4;\n                  break;\n                }\n\n                this.$message.error('请先输入手机号');\n                return _context12.abrupt(\"return\");\n\n              case 4:\n                if (/^1[3-9]\\d{9}$/.test(phone)) {\n                  _context12.next = 7;\n                  break;\n                }\n\n                this.$message.error('手机号格式不正确');\n                return _context12.abrupt(\"return\");\n\n              case 7:\n                this.smsCodeSending = true;\n                _context12.prev = 8;\n                _context12.next = 11;\n                return sendSmsCode(phone, 'register');\n\n              case 11:\n                response = _context12.sent;\n\n                if (response.success) {\n                  this.$message.success('验证码发送成功，请查收短信');\n                  this.startSmsCountdown();\n                } else {\n                  this.$message.error(response.message || '验证码发送失败');\n                }\n\n                _context12.next = 19;\n                break;\n\n              case 15:\n                _context12.prev = 15;\n                _context12.t0 = _context12[\"catch\"](8);\n                console.error('发送短信验证码失败:', _context12.t0);\n                this.$message.error('验证码发送失败，请稍后重试');\n\n              case 19:\n                _context12.prev = 19;\n                this.smsCodeSending = false;\n                return _context12.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context12.stop();\n            }\n          }\n        }, _callee12, this, [[8, 15, 19, 22]]);\n      }));\n\n      function sendLoginSmsCode() {\n        return _sendLoginSmsCode.apply(this, arguments);\n      }\n\n      return sendLoginSmsCode;\n    }(),\n    // 发送登录邮箱验证码\n    sendLoginEmailCode: function () {\n      var _sendLoginEmailCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee13() {\n        var email, response;\n        return _regeneratorRuntime.wrap(function _callee13$(_context13) {\n          while (1) {\n            switch (_context13.prev = _context13.next) {\n              case 0:\n                email = this.emailLoginForm.getFieldValue('email');\n\n                if (email) {\n                  _context13.next = 4;\n                  break;\n                }\n\n                this.$message.error('请先输入邮箱');\n                return _context13.abrupt(\"return\");\n\n              case 4:\n                if (/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n                  _context13.next = 7;\n                  break;\n                }\n\n                this.$message.error('邮箱格式不正确');\n                return _context13.abrupt(\"return\");\n\n              case 7:\n                this.emailCodeSending = true;\n                _context13.prev = 8;\n                _context13.next = 11;\n                return sendEmailCode(email, 'register');\n\n              case 11:\n                response = _context13.sent;\n\n                if (response.success) {\n                  this.$message.success('验证码发送成功，请查收邮件');\n                  this.startEmailCountdown();\n                } else {\n                  this.$message.error(response.message || '验证码发送失败');\n                }\n\n                _context13.next = 19;\n                break;\n\n              case 15:\n                _context13.prev = 15;\n                _context13.t0 = _context13[\"catch\"](8);\n                console.error('发送邮箱验证码失败:', _context13.t0);\n                this.$message.error('验证码发送失败，请稍后重试');\n\n              case 19:\n                _context13.prev = 19;\n                this.emailCodeSending = false;\n                return _context13.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context13.stop();\n            }\n          }\n        }, _callee13, this, [[8, 15, 19, 22]]);\n      }));\n\n      function sendLoginEmailCode() {\n        return _sendLoginEmailCode.apply(this, arguments);\n      }\n\n      return sendLoginEmailCode;\n    }(),\n    // 短信验证码倒计时\n    startSmsCountdown: function startSmsCountdown() {\n      var _this6 = this;\n\n      this.smsCountdown = 60;\n      var timer = setInterval(function () {\n        _this6.smsCountdown--;\n\n        if (_this6.smsCountdown <= 0) {\n          clearInterval(timer);\n        }\n      }, 1000);\n    },\n    // 邮箱验证码倒计时\n    startEmailCountdown: function startEmailCountdown() {\n      var _this7 = this;\n\n      this.emailCountdown = 60;\n      var timer = setInterval(function () {\n        _this7.emailCountdown--;\n\n        if (_this7.emailCountdown <= 0) {\n          clearInterval(timer);\n        }\n      }, 1000);\n    },\n    // 生成微信登录二维码\n    generateWechatLoginQrCode: function () {\n      var _generateWechatLoginQrCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee14() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee14$(_context14) {\n          while (1) {\n            switch (_context14.prev = _context14.next) {\n              case 0:\n                _context14.prev = 0;\n                _context14.next = 3;\n                return generateWechatQrCode('login', this.inviteCodeFromUrl);\n\n              case 3:\n                response = _context14.sent;\n\n                if (response.success) {\n                  this.wechatLoginQrCode = response.result.qrCodeUrl;\n                } else {\n                  this.$message.error('生成微信二维码失败');\n                }\n\n                _context14.next = 11;\n                break;\n\n              case 7:\n                _context14.prev = 7;\n                _context14.t0 = _context14[\"catch\"](0);\n                console.error('生成微信二维码失败:', _context14.t0);\n                this.$message.error('生成微信二维码失败');\n\n              case 11:\n              case \"end\":\n                return _context14.stop();\n            }\n          }\n        }, _callee14, this, [[0, 7]]);\n      }));\n\n      function generateWechatLoginQrCode() {\n        return _generateWechatLoginQrCode.apply(this, arguments);\n      }\n\n      return generateWechatLoginQrCode;\n    }(),\n    // 生成符合要求的安全密码（至少8位，包含字母和数字）\n    generateSecurePassword: function generateSecurePassword() {\n      var letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';\n      var numbers = '0123456789';\n      var allChars = letters + numbers;\n      var password = ''; // 确保至少包含一个字母和一个数字\n\n      password += letters.charAt(Math.floor(Math.random() * letters.length));\n      password += numbers.charAt(Math.floor(Math.random() * numbers.length)); // 生成剩余的6位字符\n\n      for (var i = 0; i < 10; i++) {\n        password += allChars.charAt(Math.floor(Math.random() * allChars.length));\n      } // 打乱字符顺序\n\n\n      return password.split('').sort(function () {\n        return Math.random() - 0.5;\n      }).join('');\n    },\n    // 注册成功后自动登录\n    performAutoLogin: function () {\n      var _performAutoLogin = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee15(type, values, password) {\n        var _this8 = this;\n\n        var username, user, pwd, loginParams, loginResponse, result, userInfo;\n        return _regeneratorRuntime.wrap(function _callee15$(_context15) {\n          while (1) {\n            switch (_context15.prev = _context15.next) {\n              case 0:\n                _context15.prev = 0;\n                // 先获取验证码图片\n                this.handleChangeCheckCode(); // 构建登录参数 - 完全按照正常登录的格式\n\n                username = values[type]; // phone 或 email\n\n                user = encryption(username, this.encryptedString.key, this.encryptedString.iv);\n                pwd = encryption(password, this.encryptedString.key, this.encryptedString.iv);\n                loginParams = {\n                  username: user,\n                  password: pwd,\n                  captcha: 'AUTO_LOGIN_2025',\n                  // 使用特殊验证码绕过验证\n                  checkKey: this.currdatetime,\n                  remember_me: true,\n                  loginType: 'website'\n                };\n                console.log('自动登录参数:', {\n                  username: username,\n                  loginType: 'auto',\n                  checkKey: this.currdatetime\n                });\n                _context15.next = 9;\n                return login(loginParams);\n\n              case 9:\n                loginResponse = _context15.sent;\n\n                if (!(loginResponse.code === 200 || loginResponse.code === '200')) {\n                  _context15.next = 21;\n                  break;\n                }\n\n                // 登录成功提示\n                this.$notification.success({\n                  message: '欢迎加入智界AIGC！',\n                  description: \"\\u60A8\\u5DF2\\u6210\\u529F\\u6CE8\\u518C\\u5E76\\u767B\\u5F55\\uFF0C\\u8D26\\u6237\\u5DF2\\u521B\\u5EFA\\u4E3A\\u65E0\\u5BC6\\u7801\\u6A21\\u5F0F\\uFF0C\\u4ECA\\u540E\\u53EF\\u76F4\\u63A5\\u4F7F\\u7528\".concat(type === 'phone' ? '手机号' : '邮箱', \"\\u9A8C\\u8BC1\\u7801\\u767B\\u5F55\\uFF01\"),\n                  placement: 'topRight',\n                  duration: 6\n                }); // 存储登录信息\n\n                result = loginResponse.result;\n                userInfo = result.userInfo;\n                Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000);\n                Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000); // 延迟跳转\n\n                setTimeout(function () {\n                  _this8.$router.push('/usercenter');\n                }, 1500);\n                _context15.next = 22;\n                break;\n\n              case 21:\n                throw new Error(loginResponse.message || '自动登录失败');\n\n              case 22:\n                _context15.next = 28;\n                break;\n\n              case 24:\n                _context15.prev = 24;\n                _context15.t0 = _context15[\"catch\"](0);\n                console.error('自动登录失败:', _context15.t0);\n                this.$notification.error({\n                  message: '注册成功，但自动登录失败',\n                  description: '请手动使用验证码登录',\n                  placement: 'topRight',\n                  duration: 4\n                });\n\n              case 28:\n              case \"end\":\n                return _context15.stop();\n            }\n          }\n        }, _callee15, this, [[0, 24]]);\n      }));\n\n      function performAutoLogin(_x12, _x13, _x14) {\n        return _performAutoLogin.apply(this, arguments);\n      }\n\n      return performAutoLogin;\n    }(),\n    // 初始化页面动画\n    initAnimations: function initAnimations() {\n      // ✅ 创建主时间线，确保动画流畅连贯\n      var tl = gsap.timeline(); // ✅ 左侧信息区域动画 - 从初始状态开始\n\n      tl.to(this.$refs.loginInfo, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }); // ✅ 右侧登录表单动画 - 与左侧稍微重叠\n\n      tl.to(this.$refs.loginContainer, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }, \"-=0.6\"); // 提前0.6秒开始，创造重叠效果\n      // ✅ 特性列表依次出现 - 更流畅的时序\n\n      tl.to(\".feature-item\", {\n        duration: 0.5,\n        y: 0,\n        opacity: 1,\n        stagger: 0.08,\n        // 减少间隔，更流畅\n        ease: \"power2.out\"\n      }, \"-=0.4\"); // 与右侧动画重叠\n    }\n  }\n};", null]}