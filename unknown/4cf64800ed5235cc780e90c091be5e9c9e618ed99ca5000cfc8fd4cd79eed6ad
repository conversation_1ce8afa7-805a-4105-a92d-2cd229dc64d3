<template>
  <div class="website-login">
    <!-- 动态背景 -->
    <div class="login-background">
      <div class="bg-animated-grid"></div>
      <div class="bg-floating-elements"></div>
      <div class="bg-gradient-overlay"></div>
    </div>

    <!-- 复用官网页头组件 -->
    <WebsiteHeader />

    <!-- 主要内容区域 -->
    <div class="login-main">
      <!-- 左侧信息展示 -->
      <div class="login-info" ref="loginInfo">
        <div class="info-content">
          <div class="brand-showcase">
            <div class="brand-logo-large">
              <LogoImage
                size="large"
                :hover="false"
                container-class="login-logo-container"
                image-class="login-logo-image"
                fallback-class="login-logo-fallback"
              />
              <h1 class="brand-title">智界AIGC</h1>
            </div>
            <p class="brand-slogan">AI驱动的内容生成平台</p>
          </div>

          <div class="feature-highlights">
            <div class="feature-item" v-for="(feature, index) in features" :key="index">
              <div class="feature-icon">
                <a-icon :type="feature.icon" />
              </div>
              <div class="feature-text">
                <h3>{{ feature.title }}</h3>
                <p>{{ feature.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-container" ref="loginContainer">
        <div class="login-card">
          <!-- 登录头部 -->
          <div class="login-header">
            <h2 class="login-title">欢迎使用智界AIGC</h2>
            <p class="login-subtitle">{{ inviteCodeFromUrl ? '您正在通过邀请链接登录' : '选择您的登录方式，开启AI创作之旅' }}</p>
          </div>

          <!-- 登录方式切换Tab -->
          <div class="auth-tabs">
            <div class="tab-buttons">
              <button
                :class="['tab-btn', { active: loginType === 'phone' }]"
                @click="switchLoginType('phone')"
              >
                <a-icon type="mobile" />
                <span class="tab-text">手机号</span>
              </button>
              <button
                :class="['tab-btn', { active: loginType === 'email' }]"
                @click="switchLoginType('email')"
              >
                <a-icon type="mail" />
                <span class="tab-text">邮箱</span>
              </button>
              <!-- 🔐 微信登录（暂时隐藏，待后续配置） -->
              <!-- <button
                :class="['tab-btn', { active: loginType === 'wechat' }]"
                @click="switchLoginType('wechat')"
              >
                <a-icon type="wechat" />
                <span class="tab-text">微信</span>
              </button> -->
              <button
                :class="['tab-btn', { active: loginType === 'password' }]"
                @click="switchLoginType('password')"
              >
                <a-icon type="lock" />
                <span class="tab-text">密码登录</span>
              </button>
            </div>
          </div>

          <!-- 登录表单 -->
          <div class="login-form">
            <!-- 密码登录 -->
            <div v-if="loginType === 'password'" class="login-content">
              <a-form :form="form" @submit="handleSubmit" class="account-login-form">
              <!-- 用户名输入 -->
              <div class="input-group">
                <a-form-item>
                  <a-input
                    v-decorator="['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]"
                    size="large"
                    placeholder="用户名或邮箱"
                    class="clean-input"
                  >
                    <a-icon slot="prefix" type="user" />
                  </a-input>
                </a-form-item>
              </div>

              <!-- 密码输入 -->
              <div class="input-group">
                <a-form-item>
                  <a-input-password
                    v-decorator="['password', { rules: [{ required: true, message: '请输入密码' }] }]"
                    size="large"
                    placeholder="密码"
                    class="clean-input"
                  >
                    <a-icon slot="prefix" type="lock" />
                  </a-input-password>
                </a-form-item>
              </div>

              <!-- 验证码 -->
              <div class="input-group">
                <a-form-item>
                  <div class="captcha-row">
                    <a-input
                      v-decorator="['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]"
                      size="large"
                      placeholder="验证码"
                      class="clean-input captcha-input"
                    >
                      <a-icon slot="prefix" type="safety-certificate" />
                    </a-input>
                    <div class="captcha-image-container" @click="handleChangeCheckCode">
                      <img
                        :src="randCodeImage"
                        class="captcha-image"
                        alt="验证码"
                      />
                      <div class="captcha-refresh-overlay">
                        <a-icon type="reload" />
                      </div>
                    </div>
                  </div>
                </a-form-item>
              </div>

              <!-- 登录选项 -->
              <div class="login-options">
                <a-checkbox v-model="rememberMe" class="remember-me">
                  记住我
                </a-checkbox>
                <a class="forgot-link" @click="handleForgotPassword">
                  忘记密码？
                </a>
              </div>

              <!-- 登录按钮 -->
              <a-form-item class="login-button-item">
                <a-button
                  type="primary"
                  html-type="submit"
                  size="large"
                  :loading="loginLoading"
                  class="login-submit-button"
                  block
                >
                  <span v-if="!loginLoading">登录</span>
                  <span v-else>登录中...</span>
                </a-button>
              </a-form-item>
              </a-form>
            </div>

            <!-- 手机号登录 -->
            <div v-if="loginType === 'phone'" class="login-content">
              <a-form :form="phoneLoginForm" @submit="handlePhoneLogin" class="phone-login-form">
                <!-- 手机号输入 -->
                <div class="input-group">
                  <a-form-item>
                    <a-input
                      v-decorator="['phone', { rules: [
                        { required: true, message: '请输入手机号' },
                        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
                      ] }]"
                      size="large"
                      placeholder="请输入手机号"
                      class="clean-input"
                    >
                      <a-icon slot="prefix" type="mobile" />
                    </a-input>
                  </a-form-item>
                </div>

                <!-- 短信验证码 -->
                <div class="input-group">
                  <a-form-item>
                    <div class="verify-code-row">
                      <a-input
                        v-decorator="['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]"
                        size="large"
                        placeholder="请输入短信验证码"
                        class="clean-input verify-code-input"
                      >
                        <a-icon slot="prefix" type="safety-certificate" />
                      </a-input>
                      <a-button
                        :disabled="smsCodeSending || smsCountdown > 0"
                        @click="sendLoginSmsCode"
                        class="send-code-btn"
                        size="large"
                      >
                        {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '发送验证码' }}
                      </a-button>
                    </div>
                  </a-form-item>
                </div>

                <!-- 登录按钮 -->
                <a-form-item class="login-button-item">
                  <a-button
                    type="primary"
                    html-type="submit"
                    size="large"
                    :loading="phoneLoginLoading"
                    class="login-submit-button"
                    block
                  >
                    <span v-if="!phoneLoginLoading">登录</span>
                    <span v-else>登录中...</span>
                  </a-button>
                </a-form-item>

                <!-- 提示信息 -->
                <div class="phone-login-tip">
                  <a-alert
                    message="手机号登录说明"
                    description="首次使用手机号登录将自动为您创建账户，无需设置密码"
                    type="info"
                    show-icon
                  />
                </div>
              </a-form>
            </div>

            <!-- 邮箱登录 -->
            <div v-if="loginType === 'email'" class="login-content">
              <a-form :form="emailLoginForm" @submit="handleEmailLogin" class="email-login-form">
                <!-- 邮箱输入 -->
                <div class="input-group">
                  <a-form-item>
                    <a-input
                      v-decorator="['email', { rules: [
                        { required: true, message: '请输入邮箱' },
                        { type: 'email', message: '邮箱格式不正确' }
                      ] }]"
                      size="large"
                      placeholder="请输入邮箱"
                      class="clean-input"
                    >
                      <a-icon slot="prefix" type="mail" />
                    </a-input>
                  </a-form-item>
                </div>

                <!-- 邮箱验证码 -->
                <div class="input-group">
                  <a-form-item>
                    <div class="verify-code-row">
                      <a-input
                        v-decorator="['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]"
                        size="large"
                        placeholder="请输入邮箱验证码"
                        class="clean-input verify-code-input"
                      >
                        <a-icon slot="prefix" type="safety-certificate" />
                      </a-input>
                      <a-button
                        :disabled="emailCodeSending || emailCountdown > 0"
                        @click="sendLoginEmailCode"
                        class="send-code-btn"
                        size="large"
                      >
                        {{ emailCountdown > 0 ? `${emailCountdown}s后重发` : '发送验证码' }}
                      </a-button>
                    </div>
                  </a-form-item>
                </div>

                <!-- 登录按钮 -->
                <a-form-item class="login-button-item">
                  <a-button
                    type="primary"
                    html-type="submit"
                    size="large"
                    :loading="emailLoginLoading"
                    class="login-submit-button"
                    block
                  >
                    <span v-if="!emailLoginLoading">登录</span>
                    <span v-else>登录中...</span>
                  </a-button>
                </a-form-item>

                <!-- 提示信息 -->
                <div class="email-login-tip">
                  <a-alert
                    message="邮箱登录说明"
                    description="首次使用邮箱登录将自动为您创建账户，无需设置密码"
                    type="info"
                    show-icon
                  />
                </div>
              </a-form>
            </div>

            <!-- 微信登录 -->
            <div v-if="loginType === 'wechat'" class="login-content">
              <div class="wechat-login-container">
                <div class="wechat-qr-section">
                  <div class="qr-code-container">
                    <img :src="wechatLoginQrCode" alt="微信登录二维码" class="qr-code-image" v-if="wechatLoginQrCode" />
                    <div class="qr-loading" v-else>
                      <a-spin size="large" />
                      <p>正在生成二维码...</p>
                    </div>
                  </div>
                  <div class="qr-instructions">
                    <h4>使用微信扫码登录</h4>
                    <p>1. 打开微信扫一扫</p>
                    <p>2. 扫描上方二维码</p>
                    <p>3. 确认登录</p>
                    <p v-if="inviteCodeFromUrl" class="invite-tip">* 您正在通过邀请链接登录</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { login, getCaptcha, phoneLogin, emailLogin } from '@/api/login'
import { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'
import { getAction } from '@/api/manage'
import { gsap } from 'gsap'
import WebsiteHeader from '@/components/website/WebsiteHeader.vue'
import LogoImage from '@/components/common/LogoImage.vue'
import { ACCESS_TOKEN, USER_NAME, USER_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'
import {
  checkUsername,
  sendSmsCode,
  sendEmailCode,
  register,
  generateWechatQrCode
} from '@/api/register'
import Vue from 'vue'
import { handleLoginConflict } from '@/utils/loginConflictHandler'
import { getReferralCode } from '@/utils/referralUtils'

export default {
  name: 'WebsiteLogin',
  components: {
    WebsiteHeader,
    LogoImage
  },
  data() {
    return {
      // 登录相关
      form: this.$form.createForm(this),
      phoneLoginForm: this.$form.createForm(this),
      emailLoginForm: this.$form.createForm(this),
      loginLoading: false,
      phoneLoginLoading: false,
      emailLoginLoading: false,
      rememberMe: false,
      randCodeImage: '',
      currdatetime: new Date().getTime(),
      encryptedString: '',

      // 登录方式切换
      loginType: 'phone', // password, phone, email, wechat - 默认手机号登录

      // 验证码相关
      smsCodeSending: false,
      smsCountdown: 0,
      emailCodeSending: false,
      emailCountdown: 0,

      // 邀请码（静默处理）
      inviteCodeFromUrl: '',

      // 微信登录
      wechatLoginQrCode: '',

      features: [
        {
          icon: 'robot',
          title: 'AI智能创作',
          description: '强大的AI算法，助您快速生成高质量内容'
        },
        {
          icon: 'thunderbolt',
          title: '极速响应',
          description: '毫秒级响应速度，让创作灵感不再等待'
        },
        {
          icon: 'safety-certificate',
          title: '安全可靠',
          description: '企业级安全保障，保护您的创作成果'
        },
        {
          icon: 'global',
          title: '全球服务',
          description: '覆盖全球的CDN网络，随时随地畅享服务'
        }
      ]
    }
  },
  mounted() {
    this.getEncrypte()
    this.handleChangeCheckCode()
    this.initAnimations()
    this.checkInviteCode()
  },
  methods: {
    // 获取密码加密规则
    getEncrypte() {
      getEncryptedString().then((data) => {
        this.encryptedString = data
      })
    },

    // 刷新验证码
    handleChangeCheckCode() {
      this.currdatetime = new Date().getTime()
      getAction(`/sys/randomImage/${this.currdatetime}`).then(res => {
        if (res.success) {
          this.randCodeImage = res.result
        } else {
          this.$message.error(res.message)
        }
      }).catch(() => {
        this.$message.error('验证码加载失败')
      })
    },

    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          this.loginLoading = true
          console.log('官网登录信息:', values)

          // 使用真实的登录API
          let user = encryption(values.username, this.encryptedString.key, this.encryptedString.iv)
          let pwd = encryption(values.password, this.encryptedString.key, this.encryptedString.iv)
          let loginParams = {
            username: user,
            password: pwd,
            captcha: values.inputCode,
            checkKey: this.currdatetime,
            remember_me: this.rememberMe,
            loginType: 'website' // 标识为官网用户登录
          }

          console.log("官网登录参数", loginParams)
          login(loginParams).then(async (res) => {
            this.loginLoading = false
            console.log("🔍 登录响应:", res)
            console.log("🔍 响应code:", res.code, "类型:", typeof res.code)
            if (res.code === 200 || res.code === '200') {
              this.$notification.success({
                message: '登录成功',
                description: '欢迎回来！正在跳转到个人中心...',
                placement: 'topRight',
                duration: 3,
                style: {
                  width: '350px',
                  marginTop: '101px',
                  borderRadius: '8px',
                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
                }
              })

              // ✅ 存储登录信息
              const result = res.result
              const userInfo = result.userInfo
              Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
              Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)
              Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
              Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)

              // ✅ 获取用户角色信息
              try {
                const roleRes = await getAction("/sys/user/getCurrentUserDeparts")
                if (roleRes.success) {
                  const userRole = roleRes.result.role
                  const departId = roleRes.result.departId

                  // 存储角色信息
                  localStorage.setItem('userRole', userRole || '')
                  localStorage.setItem('departId', departId || '')

                  // 优先处理重定向参数
                  const redirectPath = this.$route.query.redirect
                  console.log('🔍 登录成功，检查重定向参数:', redirectPath)

                  if (redirectPath) {
                    // 有重定向参数，直接跳转到目标页面
                    console.log('🔄 有重定向参数，跳转到:', redirectPath)
                    this.$router.push(redirectPath)
                  } else {
                    // 没有重定向参数，根据角色决定跳转
                    if (this.isAdminRole(userRole)) {
                      // 管理员用户，跳转到后台
                      console.log('🔄 管理员用户，跳转到后台管理')
                      this.$router.push('/dashboard/analysis')
                    } else {
                      // 普通用户，跳转到个人中心
                      console.log('🔄 普通用户，跳转到个人中心')
                      this.$router.push('/usercenter')
                    }
                  }
                } else {
                  // 获取角色失败，检查重定向参数
                  const redirectPath = this.$route.query.redirect
                  if (redirectPath) {
                    this.$router.push(redirectPath)
                  } else {
                    this.$router.push('/usercenter')
                  }
                }
              } catch (error) {
                console.error('获取角色信息失败:', error)
                // 出错时也检查重定向参数
                const redirectPath = this.$route.query.redirect
                if (redirectPath) {
                  this.$router.push(redirectPath)
                } else {
                  this.$router.push('/usercenter')
                }
              }
            } else {
              this.$notification.error({
                message: '登录失败',
                description: res.message || '用户名或密码错误，请检查后重试',
                placement: 'topRight',
                duration: 4,
                style: {
                  width: '380px',
                  marginTop: '101px',
                  borderRadius: '8px',
                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
                }
              })
              this.handleChangeCheckCode() // 刷新验证码
            }
          }).catch(async (err) => {
            this.loginLoading = false

            // 检查是否是登录冲突错误
            if (err.response && err.response.data && err.response.data.code === 4002) {
              console.log('检测到用户名密码登录冲突，显示确认弹窗')
              const conflictInfo = err.response.data.result

              // 创建强制登录函数
              const forceLoginFn = async () => {
                const forceLoginParams = {
                  ...loginParams,
                  loginType: 'force' // 修改登录类型为强制登录
                }
                console.log('用户名密码强制登录数据:', forceLoginParams)
                return await login(forceLoginParams)
              }

              try {
                // 显示登录冲突确认弹窗
                const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)

                if (forceLoginResponse && (forceLoginResponse.code === 200 || forceLoginResponse.code === '200')) {
                  // 强制登录成功，执行登录成功的逻辑
                  this.$notification.success({
                    message: '登录成功',
                    description: '欢迎回来！正在跳转到个人中心...',
                    placement: 'topRight',
                    duration: 3
                  })

                  // 存储登录信息
                  const result = forceLoginResponse.result
                  const userInfo = result.userInfo
                  Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
                  Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)
                  Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
                  Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)

                  // 跳转逻辑
                  const redirectPath = this.$route.query.redirect
                  if (redirectPath) {
                    this.$router.push(redirectPath)
                  } else {
                    this.$router.push('/usercenter')
                  }
                } else {
                  throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')
                }
              } catch (conflictError) {
                if (conflictError.message === 'USER_CANCELLED') {
                  // 用户取消登录
                  console.log('用户取消用户名密码强制登录')
                  this.handleChangeCheckCode() // 刷新验证码
                  return
                } else {
                  this.$notification.error({
                    message: '登录失败',
                    description: conflictError.message || '强制登录失败',
                    placement: 'topRight',
                    duration: 4
                  })
                  this.handleChangeCheckCode() // 刷新验证码
                }
              }
            } else {
              // 其他错误，显示原有的错误处理
              this.$notification.error({
                message: '登录失败',
                description: err.message || '网络连接异常，请检查网络后重试',
                placement: 'topRight',
                duration: 4,
                style: {
                  width: '380px',
                  marginTop: '101px',
                  borderRadius: '8px',
                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
                }
              })
              this.handleChangeCheckCode() // 刷新验证码
            }
          })
        }
      })
    },

    handleForgotPassword() {
      this.$notification.info({
        message: '忘记密码',
        description: '忘记密码功能正在开发中，敬请期待...',
        placement: 'topRight',
        duration: 3,
        style: {
          width: '350px',
          marginTop: '101px',
          borderRadius: '8px',
          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
        }
      })
      // TODO: 跳转到忘记密码页面
    },

    handleSocialLogin(type) {
      const typeMap = {
        wechat: '微信',
        qq: 'QQ',
        alipay: '支付宝'
      }
      this.$message.info(`${typeMap[type]}登录功能开发中...`)
      // TODO: 实现第三方登录
    },



    // 检查URL中的邀请码（静默处理）
    checkInviteCode() {
      // 优先从sessionStorage获取邀请码
      const sessionReferralCode = getReferralCode()

      // 支持两种参数格式：ref（推广链接）和 invite（邀请码）
      const refCode = this.$route.query.ref
      const inviteCode = this.$route.query.invite
      const urlInviteCode = refCode || inviteCode

      // 优先使用sessionStorage中的邀请码，如果没有再使用URL中的
      const finalInviteCode = sessionReferralCode || urlInviteCode

      if (finalInviteCode) {
        this.inviteCodeFromUrl = finalInviteCode
        // 静默记录邀请码，不显示给用户
        console.log('🔗 Login.vue检测到邀请码:', finalInviteCode, '来源:', sessionReferralCode ? 'sessionStorage' : (refCode ? 'ref参数' : 'invite参数'))
      }
    },

    // 登录方式切换
    switchLoginType(type) {
      this.loginType = type

      // 重置验证码倒计时
      this.smsCountdown = 0
      this.emailCountdown = 0

      if (type === 'wechat') {
        this.generateWechatLoginQrCode()
      }
    },

    // 手机号登录（自动注册）
    handlePhoneLogin(e) {
      e.preventDefault()
      this.phoneLoginForm.validateFields(async (err, values) => {
        if (!err) {
          this.phoneLoginLoading = true

          try {
            // 先检查手机号是否已注册
            const checkResponse = await checkUsername(values.phone, 'phone')

            if (checkResponse.success) {
              // 手机号未注册，自动注册（无密码账户）
              await this.autoRegisterAndLogin('phone', values)
            } else {
              // 手机号已注册，直接登录
              await this.loginWithSmsCode(values)
            }
          } catch (error) {
            this.phoneLoginLoading = false
            this.$notification.error({
              message: '登录失败',
              description: error.message || '登录过程中发生错误',
              placement: 'topRight',
              duration: 4
            })
          }
        }
      })
    },

    // 邮箱登录（自动注册）
    handleEmailLogin(e) {
      e.preventDefault()
      this.emailLoginForm.validateFields(async (err, values) => {
        if (!err) {
          this.emailLoginLoading = true

          try {
            // 先检查邮箱是否已注册
            const checkResponse = await checkUsername(values.email, 'email')

            if (checkResponse.success) {
              // 邮箱未注册，自动注册（无密码账户）
              await this.autoRegisterAndLogin('email', values)
            } else {
              // 邮箱已注册，直接登录
              await this.loginWithEmailCode(values)
            }
          } catch (error) {
            this.emailLoginLoading = false
            this.$notification.error({
              message: '登录失败',
              description: error.message || '登录过程中发生错误',
              placement: 'topRight',
              duration: 4
            })
          }
        }
      })
    },

    // 自动注册并登录（无密码账户）
    async autoRegisterAndLogin(type, values) {
      try {
        // 为无密码账户生成符合要求的随机密码
        const randomPassword = this.generateSecurePassword()

        // 构建注册数据
        const registerData = {
          type: type,
          [type]: values[type], // phone 或 email
          verifyCode: values[type === 'phone' ? 'smsCode' : 'emailCode'],
          // 生成随机密码（用户不需要知道）
          password: randomPassword,
          confirmPassword: randomPassword,
          inviteCode: this.inviteCodeFromUrl, // 静默携带邀请码
          inviteSource: this.inviteCodeFromUrl ? 'link' : null
        }

        console.log('🔗 自动注册数据:', {
          type: registerData.type,
          email: registerData.email,
          inviteCode: registerData.inviteCode,
          inviteSource: registerData.inviteSource,
          hasInviteCode: !!registerData.inviteCode
        })

        // 调用注册接口
        const registerResponse = await register(registerData)

        if (registerResponse.success) {
          // 注册成功，现在需要自动登录获取token
          console.log('注册成功，用户ID:', registerResponse.result)

          // 使用生成的密码进行自动登录
          await this.performAutoLogin(type, values, randomPassword)
        } else {
          throw new Error(registerResponse.message || '注册失败')
        }
      } catch (error) {
        throw error
      } finally {
        this.phoneLoginLoading = false
        this.emailLoginLoading = false
      }
    },

    // 使用短信验证码登录
    async loginWithSmsCode(values) {
      try {
        // 构建登录数据
        const loginData = {
          mobile: values.phone,
          captcha: values.smsCode,
          loginType: 'website' // 标识为官网用户登录
        }

        console.log('短信验证码登录:', loginData)

        // 调用短信验证码登录接口
        const loginResponse = await phoneLogin(loginData)

        if (loginResponse.success) {
          // 登录成功，处理token和用户信息
          await this.handleLoginSuccess(loginResponse.result)
        } else {
          // 检查是否是登录冲突错误
          if (loginResponse.code === 4002) {
            console.log('检测到手机号登录冲突，显示确认弹窗')
            const conflictInfo = loginResponse.result

            // 创建强制登录函数
            const forceLoginFn = async () => {
              const forceLoginData = {
                ...loginData,
                loginType: 'force' // 修改登录类型为强制登录
              }
              console.log('手机号强制登录数据:', forceLoginData)
              return await phoneLogin(forceLoginData)
            }

            try {
              // 显示登录冲突确认弹窗
              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)

              if (forceLoginResponse && forceLoginResponse.success) {
                // 强制登录成功
                await this.handleLoginSuccess(forceLoginResponse.result)
              } else {
                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')
              }
            } catch (conflictError) {
              if (conflictError.message === 'USER_CANCELLED') {
                // 用户取消登录
                console.log('用户取消手机号强制登录')
                return
              } else {
                throw conflictError
              }
            }
          } else {
            throw new Error(loginResponse.message || '登录失败')
          }
        }
      } catch (error) {
        throw error
      } finally {
        this.phoneLoginLoading = false
      }
    },

    // 使用邮箱验证码登录
    async loginWithEmailCode(values) {
      try {
        // 构建登录数据
        const loginData = {
          email: values.email,
          emailCode: values.emailCode,
          loginType: 'website' // 标识为官网用户登录
        }

        console.log('邮箱验证码登录:', loginData)

        // 调用邮箱验证码登录接口
        const loginResponse = await emailLogin(loginData)

        if (loginResponse.success) {
          // 登录成功，处理token和用户信息
          await this.handleLoginSuccess(loginResponse.result)
        } else {
          // 检查是否是登录冲突错误
          if (loginResponse.code === 4002) {
            console.log('检测到邮箱登录冲突，显示确认弹窗')
            const conflictInfo = loginResponse.result

            // 创建强制登录函数
            const forceLoginFn = async () => {
              const forceLoginData = {
                ...loginData,
                loginType: 'force' // 修改登录类型为强制登录
              }
              console.log('邮箱强制登录数据:', forceLoginData)
              return await emailLogin(forceLoginData)
            }

            try {
              // 显示登录冲突确认弹窗
              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)

              if (forceLoginResponse && forceLoginResponse.success) {
                // 强制登录成功
                await this.handleLoginSuccess(forceLoginResponse.result)
              } else {
                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')
              }
            } catch (conflictError) {
              if (conflictError.message === 'USER_CANCELLED') {
                // 用户取消登录
                console.log('用户取消邮箱强制登录')
                return
              } else {
                throw conflictError
              }
            }
          } else {
            throw new Error(loginResponse.message || '登录失败')
          }
        }
      } catch (error) {
        throw error
      } finally {
        this.emailLoginLoading = false
      }
    },

    // 处理登录成功
    async handleLoginSuccess(result) {
      try {
        // 存储token和用户信息
        Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
        Vue.ls.set(USER_NAME, result.userInfo.username, 7 * 24 * 60 * 60 * 1000)
        Vue.ls.set(USER_INFO, result.userInfo, 7 * 24 * 60 * 60 * 1000)
        Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)

        // 显示登录成功消息
        this.$notification.success({
          message: '登录成功',
          description: `欢迎回来，${result.userInfo.realname || result.userInfo.username}！`,
          placement: 'topRight',
          duration: 3
        })

        // 跳转到目标页面
        const redirect = this.$route.query.redirect || '/'
        this.$router.push(redirect)
      } catch (error) {
        console.error('处理登录成功失败:', error)
        throw new Error('登录后处理失败')
      }
    },

    // 发送登录短信验证码
    async sendLoginSmsCode() {
      const phone = this.phoneLoginForm.getFieldValue('phone')
      if (!phone) {
        this.$message.error('请先输入手机号')
        return
      }

      if (!/^1[3-9]\d{9}$/.test(phone)) {
        this.$message.error('手机号格式不正确')
        return
      }

      this.smsCodeSending = true
      try {
        const response = await sendSmsCode(phone, 'register')

        if (response.success) {
          this.$message.success('验证码发送成功，请查收短信')
          this.startSmsCountdown()
        } else {
          this.$message.error(response.message || '验证码发送失败')
        }
      } catch (error) {
        console.error('发送短信验证码失败:', error)
        this.$message.error('验证码发送失败，请稍后重试')
      } finally {
        this.smsCodeSending = false
      }
    },

    // 发送登录邮箱验证码
    async sendLoginEmailCode() {
      const email = this.emailLoginForm.getFieldValue('email')
      if (!email) {
        this.$message.error('请先输入邮箱')
        return
      }

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        this.$message.error('邮箱格式不正确')
        return
      }

      this.emailCodeSending = true
      try {
        const response = await sendEmailCode(email, 'register')

        if (response.success) {
          this.$message.success('验证码发送成功，请查收邮件')
          this.startEmailCountdown()
        } else {
          this.$message.error(response.message || '验证码发送失败')
        }
      } catch (error) {
        console.error('发送邮箱验证码失败:', error)
        this.$message.error('验证码发送失败，请稍后重试')
      } finally {
        this.emailCodeSending = false
      }
    },

    // 短信验证码倒计时
    startSmsCountdown() {
      this.smsCountdown = 60
      const timer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    },

    // 邮箱验证码倒计时
    startEmailCountdown() {
      this.emailCountdown = 60
      const timer = setInterval(() => {
        this.emailCountdown--
        if (this.emailCountdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    },

    // 生成微信登录二维码
    async generateWechatLoginQrCode() {
      try {
        const response = await generateWechatQrCode('login', this.inviteCodeFromUrl)
        if (response.success) {
          this.wechatLoginQrCode = response.result.qrCodeUrl
        } else {
          this.$message.error('生成微信二维码失败')
        }
      } catch (error) {
        console.error('生成微信二维码失败:', error)
        this.$message.error('生成微信二维码失败')
      }
    },

    // 生成符合要求的安全密码（至少8位，包含字母和数字）
    generateSecurePassword() {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
      const numbers = '0123456789'
      const allChars = letters + numbers

      let password = ''

      // 确保至少包含一个字母和一个数字
      password += letters.charAt(Math.floor(Math.random() * letters.length))
      password += numbers.charAt(Math.floor(Math.random() * numbers.length))

      // 生成剩余的6位字符
      for (let i = 0; i < 10; i++) {
        password += allChars.charAt(Math.floor(Math.random() * allChars.length))
      }

      // 打乱字符顺序
      return password.split('').sort(() => Math.random() - 0.5).join('')
    },

    // 注册成功后自动登录
    async performAutoLogin(type, values, password) {
      try {
        // 先获取验证码图片
        this.handleChangeCheckCode()

        // 构建登录参数 - 完全按照正常登录的格式
        const username = values[type] // phone 或 email
        const user = encryption(username, this.encryptedString.key, this.encryptedString.iv)
        const pwd = encryption(password, this.encryptedString.key, this.encryptedString.iv)

        const loginParams = {
          username: user,
          password: pwd,
          captcha: 'AUTO_LOGIN_2025', // 使用特殊验证码绕过验证
          checkKey: this.currdatetime,
          remember_me: true,
          loginType: 'website'
        }

        console.log('自动登录参数:', { username, loginType: 'auto', checkKey: this.currdatetime })

        const loginResponse = await login(loginParams)

        if (loginResponse.code === 200 || loginResponse.code === '200') {
          // 登录成功提示
          this.$notification.success({
            message: '欢迎加入智界AIGC！',
            description: `您已成功注册并登录，账户已创建为无密码模式，今后可直接使用${type === 'phone' ? '手机号' : '邮箱'}验证码登录！`,
            placement: 'topRight',
            duration: 6
          })

          // 存储登录信息
          const result = loginResponse.result
          const userInfo = result.userInfo
          Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
          Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)
          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
          Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)

          // 延迟跳转
          setTimeout(() => {
            this.$router.push('/usercenter')
          }, 1500)
        } else {
          throw new Error(loginResponse.message || '自动登录失败')
        }
      } catch (error) {
        console.error('自动登录失败:', error)
        this.$notification.error({
          message: '注册成功，但自动登录失败',
          description: '请手动使用验证码登录',
          placement: 'topRight',
          duration: 4
        })
      }
    },









    // 初始化页面动画
    initAnimations() {
      // ✅ 创建主时间线，确保动画流畅连贯
      const tl = gsap.timeline()

      // ✅ 左侧信息区域动画 - 从初始状态开始
      tl.to(this.$refs.loginInfo, {
        duration: 0.8,
        x: 0,
        opacity: 1,
        ease: "power3.out"
      })

      // ✅ 右侧登录表单动画 - 与左侧稍微重叠
      tl.to(this.$refs.loginContainer, {
        duration: 0.8,
        x: 0,
        opacity: 1,
        ease: "power3.out"
      }, "-=0.6") // 提前0.6秒开始，创造重叠效果

      // ✅ 特性列表依次出现 - 更流畅的时序
      tl.to(".feature-item", {
        duration: 0.5,
        y: 0,
        opacity: 1,
        stagger: 0.08, // 减少间隔，更流畅
        ease: "power2.out"
      }, "-=0.4") // 与右侧动画重叠
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.website-login {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 动态背景 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.bg-animated-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

.bg-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 85% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 45% 55%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
  animation: float 12s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(90deg); }
}

.bg-gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(59, 130, 246, 0.05) 25%,
    rgba(139, 92, 246, 0.05) 50%,
    rgba(16, 185, 129, 0.05) 75%,
    rgba(255, 255, 255, 0.2) 100%
  );
}



/* 主要内容区域 */
.login-main {
  display: flex;
  min-height: 100vh;
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 8rem 2rem 2rem; /* ✅ 增加顶部间距到8rem，给login-info更多距离页头的空间 */
}

/* 左侧信息展示 */
.login-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem; /* ✅ 增加内边距，让内容与容器边缘有更多距离 */
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  margin: 1rem 1rem 2rem; /* ✅ 增加顶部margin，与页头保持更好的距离 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  /* ✅ 初始状态设置为不可见，避免闪烁 */
  opacity: 0;
  transform: translateX(-50px);
}

.info-content {
  max-width: 600px;
  color: #374151;
}

.brand-showcase {
  text-align: center;
  margin-bottom: 4rem;
}

.brand-logo-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Login页面Logo容器样式 */
.login-logo-container {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);
  animation: logoFloat 3s ease-in-out infinite;
}

.login-logo-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  border-radius: 20px;
}

/* Login页面Fallback样式 */
.login-logo-fallback {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.5rem;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.brand-title {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.brand-slogan {
  font-size: 1.2rem;
  color: #6b7280;
  margin: 0;
}

/* 特性展示 */
.feature-highlights {
  display: grid;
  gap: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  /* ✅ 初始状态设置为不可见，避免闪烁 */
  opacity: 0;
  transform: translateY(30px);
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateX(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.feature-text h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.feature-text p {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 右侧登录容器 */
.login-container {
  flex: 1;
  max-width: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 0 1rem 2rem; /* 移除顶部margin，只保留底部和左右 */
  /* ✅ 初始状态设置为不可见，避免闪烁 */
  opacity: 0;
  transform: translateX(50px);
}

.login-card {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 3rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-title {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 1rem 0;
}

.login-subtitle {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* 登录方式切换Tab */
.auth-tabs {
  margin-bottom: 2rem;
}

.tab-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  background: #f8fafc;
  padding: 0.25rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.tab-btn {
  padding: 0.75rem 0.5rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
  color: #64748b;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 0;
  flex: 1;
}

.tab-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.tab-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.tab-btn:hover {
  color: #3b82f6;
}

.tab-btn .anticon {
  font-size: 1rem;
}

/* 登录表单 */
.login-form {
  margin-top: 0;
}

.login-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 快速登录顶部 */
.quick-login-top {
  margin-bottom: 2rem;
}

.quick-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  text-align: center;
}

.social-buttons-horizontal {
  display: flex;
  gap: 1rem;
}

.social-btn-large {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: #ffffff;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.social-btn-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.social-btn-large.wechat:hover {
  border-color: #07c160;
  color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.social-btn-large.qq:hover {
  border-color: #12b7f5;
  color: #12b7f5;
  background: rgba(18, 183, 245, 0.05);
}

.social-btn-large .anticon {
  font-size: 1.2rem;
}

/* 分割线 */
.divider-with-text {
  text-align: center;
  margin: 2rem 0;
  position: relative;
}

.divider-with-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
}

.divider-with-text span {
  background: #ffffff;
  padding: 0 1.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 输入组 */
.input-group {
  margin-bottom: 1.5rem;
}

.clean-input {
  border-radius: 12px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
}

.clean-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.clean-input:hover {
  border-color: #9ca3af !important;
}

/* 验证码行 */
.captcha-row {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image-container {
  position: relative;
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.captcha-image-container:hover {
  transform: scale(1.02);
}

.captcha-image {
  width: 120px;
  height: 48px;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  display: block;
}

.captcha-refresh-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.captcha-image-container:hover .captcha-refresh-overlay {
  opacity: 1;
}

/* 验证码行样式 */
.verify-code-row {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.verify-code-input {
  flex: 1;
}

.send-code-btn {
  border-radius: 8px;
  white-space: nowrap;
  min-width: 120px;
}

.send-code-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 微信登录样式 */
.wechat-login-container {
  text-align: center;
  padding: 2rem 0;
}

.wechat-qr-section {
  margin-bottom: 2rem;
}

.qr-code-container {
  margin-bottom: 1.5rem;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
}

.qr-loading {
  padding: 3rem;
  color: #64748b;
}

.qr-loading p {
  margin-top: 1rem;
  margin-bottom: 0;
}

.qr-instructions h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-weight: 600;
}

.qr-instructions p {
  margin: 0.5rem 0;
  color: #64748b;
  font-size: 0.9rem;
}

.invite-tip {
  color: #3b82f6 !important;
  font-weight: 500;
}

/* 提示信息样式 */
.phone-login-tip,
.email-login-tip {
  margin-top: 1rem;
}

.phone-login-tip .ant-alert,
.email-login-tip .ant-alert {
  border-radius: 8px;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0;
}

.remember-me {
  color: #6b7280;
  font-size: 0.9rem;
}

.forgot-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.forgot-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* 登录按钮 */
.login-button-item {
  margin-bottom: 0;
}

.login-submit-button {
  height: 52px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-submit-button:hover::before {
  left: 100%;
}

.login-submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

/* 注册部分 */
.register-section {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.register-text {
  color: #6b7280;
  font-size: 0.9rem;
  margin-right: 0.5rem;
}

.register-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.register-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* 登录/注册切换Tab */
.auth-tabs {
  margin-bottom: 2rem;
}

.tab-buttons {
  display: flex;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
  color: #374151;
  background: rgba(255, 255, 255, 0.5);
}

/* 注册表单 */
.register-form {
  margin-top: 0;
}

/* 注册方式切换 */
.register-type-tabs {
  margin-bottom: 2rem;
}

.type-tab-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.type-tab-btn {
  flex: 1;
  min-width: 120px;
  padding: 12px 16px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.type-tab-btn.active {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.type-tab-btn:hover:not(.active) {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
  color: #374151;
}

/* 注册内容区域 */
.register-content {
  margin-top: 1.5rem;
}

.register-form-content {
  margin: 0;
}

/* 验证码输入行 */
.verify-code-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.verify-code-input {
  flex: 1;
}

.send-code-btn {
  flex-shrink: 0;
  min-width: 120px;
  border-radius: 8px;
  border: 2px solid #3b82f6;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.send-code-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.send-code-btn:disabled {
  background: #e5e7eb;
  border-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 用户协议 */
.agreement-section {
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 12px;
}

.agreement-checkbox {
  color: #374151;
  font-size: 0.9rem;
  line-height: 1.6;
}

.agreement-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.agreement-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* 注册按钮 */
.submit-section {
  margin: 2rem 0 1.5rem;
}

.register-submit-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.register-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.register-submit-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
}

.login-text {
  color: #6b7280;
  font-size: 0.9rem;
  margin-right: 0.5rem;
}

.login-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.login-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* 微信注册 */
.wechat-register-container {
  text-align: center;
  padding: 2rem 0;
}

.wechat-qr-section {
  margin-bottom: 2rem;
}

.qr-code-container {
  display: inline-block;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
}

.qr-loading {
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.qr-instructions h4 {
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.qr-instructions p {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0.5rem 0;
  line-height: 1.5;
}

.invite-info {
  margin: 1.5rem 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .login-main {
    max-width: 1200px;
    padding: 0 1.5rem;
  }
}

@media (max-width: 1024px) {
  .login-main {
    flex-direction: column;
    max-width: 800px;
    padding: 6rem 1rem 2rem; /* 平板端保持顶部padding */
  }

  .login-info {
    margin: 0 0 1rem; /* 减少margin */
    padding: 2rem;
  }

  .login-container {
    margin: 0 0 1rem; /* 减少margin */
    max-width: none;
  }

  .brand-title {
    font-size: 2.5rem;
  }

  .feature-highlights {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .tab-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {

  .login-main {
    padding: 5rem 0.5rem 2rem; /* 移动端减少顶部padding */
  }

  .login-info {
    margin: 0 0 1rem; /* 移动端减少margin */
    padding: 1.5rem;
  }

  .login-container {
    margin: 0 0 1rem; /* 移动端减少margin */
    padding: 1rem;
  }

  .login-card {
    padding: 2rem 1.5rem;
  }

  .brand-title {
    font-size: 2rem;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .feature-highlights {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-item {
    padding: 1rem;
  }

  .feature-item:hover {
    transform: translateY(-2px);
  }

  .social-buttons-horizontal {
    flex-direction: column;
    gap: 0.75rem;
  }

  .captcha-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .captcha-image-container {
    align-self: stretch;
  }

  .captcha-image {
    width: 100%;
  }

  .tab-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .tab-btn {
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  .verify-code-row {
    flex-direction: column;
    gap: 1rem;
  }

  .send-code-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {

  .login-card {
    padding: 1.5rem 1rem;
    border-radius: 16px;
  }

  .brand-title {
    font-size: 1.8rem;
  }

  .login-title {
    font-size: 1.3rem;
  }

  .input-group {
    margin-bottom: 1rem;
  }

  .login-submit-button {
    height: 48px;
    font-size: 1rem;
  }

  .social-btn-large {
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  .social-btn-large .anticon {
    font-size: 1rem;
  }

  .tab-buttons {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }

  .tab-btn {
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
  }

  .qr-code-image {
    width: 150px;
    height: 150px;
  }
}
</style>
