package org.jeecg.modules.api.controller;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import java.net.URLEncoder;
import java.io.InputStream;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.userrecord.service.IAicgUserRecordService;
import org.jeecg.modules.api.service.IAigcApiService;
import org.jeecg.modules.api.service.impl.AigcApiServiceImpl;
import org.jeecg.modules.demo.apiusage.service.IAicgUserApiUsageService;
import org.jeecg.modules.api.dto.PluginVerifyResult;
import org.jeecg.modules.api.mapper.AicgApiLogMapper;
import org.jeecg.modules.api.mapper.AicgOnlineUsersMapper;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.jeecg.modules.api.util.QRCodeUtil;
import org.jeecg.modules.api.util.SecurityUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.aspect.annotation.AutoLog;
import javax.annotation.security.PermitAll;

/**
 * @Description: 智界Aigc对外API接口
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Api(tags = "智界Aigc对外API接口")
@RestController
@RequestMapping("/api/aigc")
@Slf4j
public class AigcApiController {

    @Autowired
    private IAigcApiService aigcApiService;



    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private IAicgUserApiUsageService aicgUserApiUsageService;

    @Autowired
    private IAicgUserRecordService userRecordService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Autowired
    private AicgApiLogMapper apiLogMapper;

    @Autowired
    private org.jeecg.modules.demo.apiusage.service.IAicgUserApiUsageService apiUsageService;

    @Value("${aigc.base.url:https://www.aigcview.com}")
    private String baseUrl;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AicgOnlineUsersMapper onlineUsersMapper;

    @Value("${aigc.api.file-storage.html-path:C:/aigcview/html/}")
    private String htmlStoragePath;

    @Value("${aigc.api.file-storage.qrcode-path:C:/aigcview/qrcode/}")
    private String qrcodeStoragePath;
    
    @Value("${aigc.base.url:}")
    private String configuredBaseUrl;

    /**
     * 动态获取基础URL（支持外网访问）
     */
    private String getBaseUrl(HttpServletRequest request) {
        // 1. 如果配置了固定的外网地址，优先使用
        if (!oConvertUtils.isEmpty(configuredBaseUrl)) {
            return configuredBaseUrl;
        }

        // 2. 动态构建外网访问地址
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        String contextPath = request.getContextPath();

        // 处理代理和负载均衡的情况
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        String forwardedHost = request.getHeader("X-Forwarded-Host");
        String forwardedPort = request.getHeader("X-Forwarded-Port");

        if (!oConvertUtils.isEmpty(forwardedProto)) {
            scheme = forwardedProto;
        }
        if (!oConvertUtils.isEmpty(forwardedHost)) {
            serverName = forwardedHost;
        }
        if (!oConvertUtils.isEmpty(forwardedPort)) {
            serverPort = Integer.parseInt(forwardedPort);
        }

        StringBuilder baseUrl = new StringBuilder();
        baseUrl.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if (("http".equals(scheme) && serverPort != 80) ||
            ("https".equals(scheme) && serverPort != 443)) {
            baseUrl.append(":").append(serverPort);
        }

        if (!oConvertUtils.isEmpty(contextPath)) {
            baseUrl.append(contextPath);
        }

        return baseUrl.toString();
    }

    /**
     * API-Key验证接口
     * @param apiKey API密钥
     * @param pluginKey 插件唯一标识（可选）
     * @return 验证结果
     */
    @ApiOperation(value = "API-Key验证", notes = "验证API密钥是否有效，支持插件调用验证")
    @PostMapping("/verify-apikey")
    public Result<?> verifyApiKey(@ApiParam("API密钥") @RequestParam String apiKey,
                                 @ApiParam("插件唯一标识") @RequestParam(required = false) String pluginKey) {
        long requestStartTime = System.currentTimeMillis(); // 记录请求开始时间
        try {
            log.info("API-Key验证请求: {}, 插件标识: {}", apiKey, pluginKey);

            if (oConvertUtils.isEmpty(apiKey)) {
                return Result.error("API-Key不能为空");
            }

            // 验证API-Key格式
            if (!apiKey.startsWith("ak_") || apiKey.length() != 35) {
                return Result.error("API-Key格式错误");
            }

            // 查询API-Key是否存在且有效
            AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
            if (userProfile == null) {
                log.warn("无效的API-Key: {}", apiKey);
                return Result.error("API-Key无效");
            }

            // 检查用户状态
            if (userProfile.getStatus() != 1) {
                log.warn("用户状态异常，API-Key: {}, 状态: {}", apiKey, userProfile.getStatus());
                return Result.error("用户状态异常，API-Key已被禁用");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("valid", true);
            result.put("userId", userProfile.getUserId());
            result.put("nickname", userProfile.getNickname());

            result.put("balance", userProfile.getAccountBalance());

            // 如果提供了插件标识，进行插件调用验证和扣费
            if (!oConvertUtils.isEmpty(pluginKey)) {
                PluginVerifyResult pluginResult = aigcApiService.verifyPluginAndDeduct(userProfile.getUserId(), pluginKey);

                if (!pluginResult.isSuccess()) {
                    log.warn("插件验证失败: {}", pluginResult.getErrorMessage());
                    return Result.error(pluginResult.getErrorMessage());
                }

                // 添加插件相关信息到返回结果
                result.put("pluginVerified", true);
                result.put("pluginName", pluginResult.getPluginName());
                result.put("deductedAmount", pluginResult.getNeedAmount());
                result.put("balanceAfter", pluginResult.getBalanceAfter());
                result.put("pluginId", pluginResult.getPluginId());

                log.info("API-Key和插件验证成功: {}, 插件: {}, 扣费: {}", apiKey, pluginKey, pluginResult.getNeedAmount());
            } else {
                result.put("pluginVerified", false);
                log.info("API-Key验证成功（无插件验证）: {}", apiKey);
            }

            // 记录API使用记录到aicg_user_api_usage表（仅当有插件验证时记录）
            if (!oConvertUtils.isEmpty(pluginKey)) {
                try {
                    long endTime = System.currentTimeMillis();
                    int responseTime = (int) (endTime - requestStartTime);

                    aicgUserApiUsageService.recordUsage(
                        userProfile.getUserId(),           // 用户ID
                        apiKey,                           // API密钥
                        "/api/aigc/verify-apikey",        // API接口
                        "POST",                           // 请求方法
                        "pluginKey: " + pluginKey,        // 请求参数概要
                        200,                              // 响应状态
                        responseTime,                     // 响应时间
                        null,                             // Token使用量（验证接口不涉及）
                        result.get("deductedAmount") != null ? (BigDecimal) result.get("deductedAmount") : BigDecimal.ZERO, // 扣费金额
                        "127.0.0.1",                      // IP地址（验证接口通常内部调用）
                        "Plugin Verification",            // 用户代理
                        null                              // 错误信息
                    );
                    log.info("API使用记录已保存 - 用户: {}, 接口: /api/aigc/verify-apikey", userProfile.getUserId());
                } catch (Exception e) {
                    log.error("记录API使用失败: {}", e.getMessage(), e);
                    // 不影响主流程
                }
            }

            return Result.OK("验证成功", result);

        } catch (Exception e) {
            log.error("API-Key验证异常: {}", e.getMessage(), e);
            return Result.error("API-Key验证失败");
        }
    }

    /**
     * HTML文件生成接口（内部专用）
     * ⚠️ 注意：此接口仅供内部使用，不对外暴露
     * @param request HTTP请求
     * @return 生成结果
     */
    @ApiOperation(value = "HTML文件生成（内部专用）", notes = "内部接口：将HTML代码生成文件并返回访问地址和二维码，不对外暴露")
    @PostMapping("/generate-html")
    public Result<?> generateHtml(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        try {
            // 1. 获取请求参数
            String apiKey = (String) params.get("apiKey");
            String htmlContent = (String) params.get("htmlContent");
            String timestamp = (String) params.get("timestamp");
            String signature = (String) params.get("signature");
            String filename = (String) params.get("filename");
            
            log.info("HTML生成请求 - API-Key: {}, 文件名: {}", apiKey, filename);
            
            // 2. 参数验证
            if (oConvertUtils.isEmpty(apiKey) || oConvertUtils.isEmpty(htmlContent) || 
                oConvertUtils.isEmpty(timestamp) || oConvertUtils.isEmpty(signature)) {
                return Result.error("必要参数不能为空");
            }
            
            // 3. API-Key验证
            AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
            if (userProfile == null || userProfile.getStatus() != 1) {
                log.warn("无效的API-Key或用户状态异常: {}", apiKey);
                return Result.error("API-Key无效或已被禁用");
            }
            
            // 4. 时间戳验证（防重放攻击）
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - requestTime) > 300000) { // 5分钟有效期
                return Result.error("请求已过期，请重新发送");
            }
            
            // 5. 签名验证
            String expectedSignature = SecurityUtil.generateSignature(apiKey, htmlContent, timestamp);
            if (!signature.equals(expectedSignature)) {
                log.warn("签名验证失败 - API-Key: {}", apiKey);
                return Result.error("签名验证失败");
            }
            
            // 6. HTML内容安全检查已移除（内部接口，不对外暴露）
            log.debug("跳过HTML安全检查 - 内部接口，内容可信");
            
            // 7. 频率限制检查已移除，不再限制请求频率
            
            // 8. 生成HTML文件
            String htmlFileName = generateHtmlFile(htmlContent, filename);
            String requestBaseUrl = request != null ? getBaseUrl(request) : this.baseUrl;
            String htmlUrl = requestBaseUrl + "/jeecg-boot/api/aigc/html/" + htmlFileName;

            // 9. 生成二维码
            String qrcodeFileName = generateQRCode(htmlUrl);
            String qrcodeUrl = requestBaseUrl + "/jeecg-boot/api/aigc/qrcode/" + qrcodeFileName;
            
            // 10. 记录操作日志
            aigcApiService.recordApiUsage(userProfile.getUserId(), "generate-html", htmlFileName);

            // 11. 记录API使用记录到aicg_user_api_usage表
            try {
                long endTime = System.currentTimeMillis();
                long startTime = Long.parseLong(timestamp); // 使用请求时间戳作为开始时间
                int responseTime = (int) (endTime - startTime);

                aicgUserApiUsageService.recordUsage(
                    userProfile.getUserId(),           // 用户ID
                    apiKey,                           // API密钥
                    "/api/aigc/generate-html",        // API接口
                    "POST",                           // 请求方法
                    "htmlContent length: " + htmlContent.length(), // 请求参数概要
                    200,                              // 响应状态
                    responseTime,                     // 响应时间
                    null,                             // Token使用量（HTML生成不涉及）
                    BigDecimal.ZERO,                  // 费用（免费接口）
                    getClientIpAddress(request),      // IP地址
                    request != null ? request.getHeader("User-Agent") : "Internal-Call",  // 用户代理
                    null                              // 错误信息
                );
                log.info("API使用记录已保存 - 用户: {}, 接口: /api/aigc/generate-html", userProfile.getUserId());
            } catch (Exception e) {
                log.error("记录API使用失败: {}", e.getMessage(), e);
                // 不影响主流程
            }

            Map<String, Object> result = new HashMap<>();
            result.put("htmlUrl", htmlUrl);
            result.put("qrcodeUrl", qrcodeUrl);
            result.put("filename", htmlFileName);
            result.put("generateTime", new Date());
            
            log.info("HTML文件生成成功 - 用户: {}, 文件: {}", userProfile.getUserId(), htmlFileName);
            return Result.OK("HTML文件生成成功", result);
            
        } catch (Exception e) {
            log.error("HTML文件生成异常: {}", e.getMessage(), e);
            return Result.error("HTML文件生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成HTML文件
     */
    private String generateHtmlFile(String htmlContent, String filename) throws IOException {
        // 创建存储目录
        File storageDir = new File(htmlStoragePath);
        if (!storageDir.exists()) {
            storageDir.mkdirs();
        }
        
        // 生成文件名
        String fileName = oConvertUtils.isEmpty(filename) ? 
            "aigc_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".html" :
            filename.endsWith(".html") ? filename : filename + ".html";
        
        // 写入文件
        File htmlFile = new File(storageDir, fileName);
        try (java.io.OutputStreamWriter writer = new java.io.OutputStreamWriter(
                new java.io.FileOutputStream(htmlFile), "UTF-8")) {
            writer.write(htmlContent);
        }
        
        return fileName;
    }
    
    /**
     * 生成二维码
     */
    private String generateQRCode(String content) throws Exception {
        // 创建存储目录
        File storageDir = new File(qrcodeStoragePath);
        if (!storageDir.exists()) {
            storageDir.mkdirs();
        }
        
        // 生成二维码文件名
        String qrcodeFileName = "qr_" + System.currentTimeMillis() + "_" + 
            UUID.randomUUID().toString().substring(0, 8) + ".png";
        
        // 生成二维码
        File qrcodeFile = new File(storageDir, qrcodeFileName);
        QRCodeUtil.generateQRCode(content, 300, 300, qrcodeFile.getAbsolutePath());
        
        return qrcodeFileName;
    }
    
    /**
     * 获取HTML文件
     */
    @GetMapping("/html/{filename}")
    public void getHtmlFile(@PathVariable String filename, HttpServletRequest request, 
                           javax.servlet.http.HttpServletResponse response) throws IOException {
        File htmlFile = new File(htmlStoragePath, filename);
        if (!htmlFile.exists()) {
            response.setStatus(404);
            return;
        }
        
        response.setContentType("text/html;charset=UTF-8");
        response.setHeader("Cache-Control", "public, max-age=3600");
        
        byte[] content = Files.readAllBytes(htmlFile.toPath());
        response.getOutputStream().write(content);
    }
    
    /**
     * 获取二维码文件
     */
    @GetMapping("/qrcode/{filename}")
    public void getQRCodeFile(@PathVariable String filename, HttpServletRequest request,
                             javax.servlet.http.HttpServletResponse response) throws IOException {
        File qrcodeFile = new File(qrcodeStoragePath, filename);
        if (!qrcodeFile.exists()) {
            response.setStatus(404);
            return;
        }

        response.setContentType("image/png");
        response.setHeader("Cache-Control", "public, max-age=86400");

        byte[] content = Files.readAllBytes(qrcodeFile.toPath());
        response.getOutputStream().write(content);
    }

    /**
     * 获取API使用统计（管理员接口）
     * @param userId 用户ID
     * @return 使用统计
     */
    @ApiOperation(value = "获取API使用统计", notes = "管理员查看用户API使用情况")
    @GetMapping("/admin/usage-stats/{userId}")
    public Result<?> getUsageStats(@PathVariable String userId) {
        try {
            // 这里应该添加管理员权限验证

            Map<String, Object> stats = new HashMap<>();

            // 获取用户信息
            AicgUserProfile userProfile = aigcApiService.getUserByApiKey(null); // 需要重构
            if (userProfile == null) {
                return Result.error("用户不存在");
            }

            // 获取频率限制配置
            Map<String, Object> rateLimits = new HashMap<>();
            rateLimits.put("perMinute", 100);  // 这里应该根据用户等级动态获取
            rateLimits.put("perHour", 5000);
            rateLimits.put("perDay", 50000);
            stats.put("rateLimits", rateLimits);

            // 获取当前使用情况（从缓存中获取）
            Map<String, Object> currentUsage = new HashMap<>();
            currentUsage.put("thisMinute", 0);
            currentUsage.put("thisHour", 0);
            currentUsage.put("today", 0);
            stats.put("currentUsage", currentUsage);

            // 获取历史统计
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000L); // 7天前
            Map<String, Object> historyStats = aigcApiService.getApiUsageStats(userId, startDate, endDate);
            stats.put("history", historyStats);

            return Result.OK("获取成功", stats);

        } catch (Exception e) {
            log.error("获取API使用统计异常: {}", e.getMessage(), e);
            return Result.error("获取统计失败");
        }
    }

    /**
     * 重置用户频率限制（管理员接口）
     * @param userId 用户ID
     * @return 重置结果
     */
    @ApiOperation(value = "重置用户频率限制", notes = "管理员重置用户的频率限制计数")
    @PostMapping("/admin/reset-rate-limit/{userId}")
    public Result<?> resetRateLimit(@PathVariable String userId) {
        try {
            // 这里应该添加管理员权限验证

            // 清除用户的频率限制缓存
            // aigcApiService.resetUserRateLimit(userId);

            log.info("管理员重置用户 {} 的频率限制", userId);
            return Result.OK("重置成功");

        } catch (Exception e) {
            log.error("重置频率限制异常: {}", e.getMessage(), e);
            return Result.error("重置失败");
        }
    }





    /**
     * 获取仪表板数据
     * @return 仪表板数据
     */
    @ApiOperation(value = "获取仪表板数据", notes = "获取用户仪表板所需的所有数据")
    @GetMapping("/dashboard-data")
    public Result<?> getDashboardData() {
        try {
            Map<String, Object> dashboardData = new HashMap<>();

            // 获取当前登录用户信息
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            String userId = loginUser.getId();
            String username = loginUser.getUsername();

            // 获取用户角色
            List<String> roles = sysUserService.getRole(username);
            String userRole = (roles != null && !roles.isEmpty()) ? roles.get(0) : "user";
            boolean isAdmin = "admin".equalsIgnoreCase(userRole);

            // 1. 获取用户扩展信息
            Map<String, Object> userInfo = getUserExtendedInfo(userId);
            dashboardData.put("userInfo", userInfo);

            // 2. 获取API统计数据 - 根据角色返回不同数据
            Map<String, Object> apiStats;
            if (isAdmin) {
                // admin角色获取系统累计数据
                apiStats = getSystemApiStatistics();
            } else {
                // 其他角色获取个人数据
                apiStats = getApiStatistics(userId);
            }
            dashboardData.put("apiStats", apiStats);

            // 3. 频率限制已移除，不再提供限制信息

            // 4. 获取实时统计 - 根据角色返回不同数据
            Map<String, Object> realTimeStats;
            if (isAdmin) {
                // admin角色获取系统实时统计
                realTimeStats = getSystemRealTimeStatistics();
            } else {
                // 其他角色获取个人实时统计
                realTimeStats = getRealTimeStatistics(userId);
            }
            dashboardData.put("realTimeStats", realTimeStats);

            // 5. 获取最近调用记录 - 根据角色返回不同数据
            List<Map<String, Object>> recentCalls;
            if (isAdmin) {
                // admin角色获取系统最近调用记录
                recentCalls = getSystemRecentApiCalls(10);
            } else {
                // 其他角色获取个人最近调用记录
                recentCalls = getRecentApiCalls(userId, 10);
            }
            dashboardData.put("recentCalls", recentCalls);

            // 6. 获取图表数据 - 根据角色返回不同数据
            Map<String, Object> chartData;
            if (isAdmin) {
                // admin角色获取系统图表数据
                chartData = getSystemChartData();
            } else {
                // 其他角色获取个人图表数据
                chartData = getChartData(userId);
            }
            dashboardData.put("chartData", chartData);

            // 添加角色信息到返回数据中
            dashboardData.put("isAdmin", isAdmin);
            dashboardData.put("userRole", userRole);

            return Result.OK("获取成功", dashboardData);

        } catch (Exception e) {
            log.error("获取仪表板数据异常: {}", e.getMessage(), e);
            return Result.error("获取仪表板数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户扩展信息
     */
    private Map<String, Object> getUserExtendedInfo(String userId) {
        Map<String, Object> userInfo = new HashMap<>();
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

            // 查询用户扩展信息
            QueryWrapper<AicgUserProfile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            AicgUserProfile userProfile = userProfileService.getOne(queryWrapper);

            // 获取用户角色并映射到会员等级
            int memberLevel = getMemberLevelByUserRole(loginUser.getUsername());

            // 🔥 检查是否为admin用户
            boolean isAdminUser = "admin".equalsIgnoreCase(loginUser.getUsername());

            if (userProfile != null) {
                userInfo.put("nickname", userProfile.getNickname());
                userInfo.put("memberLevel", memberLevel); // 使用角色映射的会员等级
                userInfo.put("accountBalance", userProfile.getAccountBalance());
                userInfo.put("avatar", ""); // 暂时设为空，avatar字段可能不存在

                // 🔥 根据用户角色计算totalConsumption
                BigDecimal totalConsumption;
                if (isAdminUser) {
                    // admin用户：计算系统总收入（基于实际收款：充值+会员购买）
                    totalConsumption = calculateSystemTotalIncome(userProfile.getUserId());
                    log.info("admin用户查看系统总收入: {}", totalConsumption);
                } else {
                    // 普通用户：返回自己的消费金额
                    totalConsumption = userProfile.getTotalConsumption();
                }
                userInfo.put("totalConsumption", totalConsumption);

                userInfo.put("totalRecharge", userProfile.getTotalRecharge());
                userInfo.put("apiKey", userProfile.getApiKey());
            } else {
                // 如果没有扩展信息，返回默认值
                userInfo.put("nickname", loginUser.getRealname() != null ? loginUser.getRealname() : "智界用户");
                userInfo.put("memberLevel", memberLevel); // 使用角色映射的会员等级
                userInfo.put("accountBalance", BigDecimal.ZERO);
                userInfo.put("avatar", "");

                // 🔥 根据用户角色计算totalConsumption
                BigDecimal totalConsumption;
                if (isAdminUser) {
                    // admin用户：计算系统总收入（基于实际收款：充值+会员购买）
                    totalConsumption = calculateSystemTotalIncome(userId); // 使用admin用户ID
                    log.info("admin用户查看系统总收入（无扩展信息）: {}", totalConsumption);
                } else {
                    // 普通用户：默认为0
                    totalConsumption = BigDecimal.ZERO;
                }
                userInfo.put("totalConsumption", totalConsumption);

                userInfo.put("totalRecharge", BigDecimal.ZERO);
                userInfo.put("apiKey", "");
            }
        } catch (Exception e) {
            log.error("获取用户扩展信息异常: {}", e.getMessage(), e);
        }
        return userInfo;
    }

    /**
     * 计算系统总收入（基于实际收款：充值+会员购买）
     * @param adminUserId admin用户ID（排除admin的充值和会员购买）
     * @return 系统总收入
     */
    private BigDecimal calculateSystemTotalIncome(String adminUserId) {
        try {
            // 查询所有已完成的充值和会员购买订单（排除admin用户）
            String sql = "SELECT IFNULL(SUM(amount), 0) as total_income " +
                        "FROM aicg_user_transaction " +
                        "WHERE order_status = 3 " +  // 已完成
                        "AND order_type IN ('recharge', 'membership') " +
                        "AND user_id != ?";  // 排除admin用户

            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, adminUserId);

            BigDecimal systemTotalIncome = BigDecimal.ZERO;
            if (result != null && !result.isEmpty()) {
                Object totalIncome = result.get(0).get("total_income");
                if (totalIncome != null) {
                    systemTotalIncome = new BigDecimal(totalIncome.toString());
                }
            }

            log.info("计算系统总收入 - 基于实际收款（充值+会员）: {}", systemTotalIncome);

            return systemTotalIncome.max(BigDecimal.ZERO); // 确保不为负数

        } catch (Exception e) {
            log.error("计算系统总收入异常: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据用户角色映射会员等级
     * @param username 用户名
     * @return 会员等级 (1-普通用户, 2-VIP用户, 3-SVIP用户)
     */
    private int getMemberLevelByUserRole(String username) {
        try {
            // 获取用户角色
            List<String> roles = sysUserService.getRole(username);
            if (roles == null || roles.isEmpty()) {
                return 1; // 默认普通用户
            }

            String roleCode = roles.get(0); // 取第一个角色

            // 角色映射到会员等级
            switch (roleCode.toLowerCase()) {
                case "svip":
                case "super_vip":
                case "admin":
                    return 3; // SVIP用户
                case "vip":
                case "premium":
                    return 2; // VIP用户
                case "user":
                case "normal":
                default:
                    return 1; // 普通用户
            }
        } catch (Exception e) {
            log.error("获取用户角色映射会员等级异常: {}", e.getMessage(), e);
            return 1; // 异常时返回普通用户
        }
    }

    /**
     * 获取API统计数据（真实数据）
     */
    private Map<String, Object> getApiStatistics(String userId) {
        Map<String, Object> apiStats = new HashMap<>();
        try {
            // 获取今日统计
            Map<String, Object> todayStats = apiLogMapper.getUserTodayStats(userId);
            int todayCalls = 0;
            int todaySuccess = 0;
            if (todayStats != null && todayStats.get("total_calls") != null) {
                todayCalls = ((Number) todayStats.get("total_calls")).intValue();
                todaySuccess = todayStats.get("success_calls") != null ?
                    ((Number) todayStats.get("success_calls")).intValue() : 0;
            }

            // 获取昨日统计（用于计算增长率）
            Map<String, Object> yesterdayStats = apiLogMapper.getUserYesterdayStats(userId);
            int yesterdayCalls = 0;
            if (yesterdayStats != null && yesterdayStats.get("total_calls") != null) {
                yesterdayCalls = ((Number) yesterdayStats.get("total_calls")).intValue();
            }

            // 获取本月统计
            Map<String, Object> monthStats = apiLogMapper.getUserMonthStats(userId);
            int monthCalls = 0;
            if (monthStats != null && monthStats.get("total_calls") != null) {
                monthCalls = ((Number) monthStats.get("total_calls")).intValue();
            }

            // 获取上月统计（用于计算增长率）
            Map<String, Object> lastMonthStats = apiLogMapper.getUserLastMonthStats(userId);
            int lastMonthCalls = 0;
            if (lastMonthStats != null && lastMonthStats.get("total_calls") != null) {
                lastMonthCalls = ((Number) lastMonthStats.get("total_calls")).intValue();
            }

            // 计算增长率
            double todayGrowth = yesterdayCalls > 0 ?
                ((double)(todayCalls - yesterdayCalls) / yesterdayCalls * 100) : 0;
            double monthGrowth = lastMonthCalls > 0 ?
                ((double)(monthCalls - lastMonthCalls) / lastMonthCalls * 100) : 0;

            // 计算真实成功率
            double successRate = todayCalls > 0 ?
                ((double) todaySuccess / todayCalls * 100) : 100.0;

            // 计算成功率增长（与昨日对比）
            int yesterdaySuccess = 0;
            if (yesterdayStats != null && yesterdayStats.get("success_calls") != null) {
                yesterdaySuccess = ((Number) yesterdayStats.get("success_calls")).intValue();
            }
            double yesterdaySuccessRate = yesterdayCalls > 0 ?
                ((double) yesterdaySuccess / yesterdayCalls * 100) : 100.0;
            double successRateGrowth = successRate - yesterdaySuccessRate;

            apiStats.put("todayCalls", todayCalls);
            apiStats.put("todayGrowth", Math.round(todayGrowth * 100.0) / 100.0);
            apiStats.put("monthCalls", monthCalls);
            apiStats.put("monthGrowth", Math.round(monthGrowth * 100.0) / 100.0);
            apiStats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            apiStats.put("successRateGrowth", Math.round(successRateGrowth * 100.0) / 100.0);

        } catch (Exception e) {
            log.error("获取API统计数据异常: {}", e.getMessage(), e);
            // 返回默认值
            apiStats.put("todayCalls", 0);
            apiStats.put("todayGrowth", 0.0);
            apiStats.put("monthCalls", 0);
            apiStats.put("monthGrowth", 0.0);
            apiStats.put("successRate", 100.0);
            apiStats.put("successRateGrowth", 0.0);
        }
        return apiStats;
    }



    /**
     * 获取实时统计（真实数据）
     */
    private Map<String, Object> getRealTimeStatistics(String userId) {
        Map<String, Object> realTimeStats = new HashMap<>();
        try {
            // 获取真实在线用户数
            int onlineUsers = onlineUsersMapper.getCurrentOnlineUsersCount();
            realTimeStats.put("onlineUsers", onlineUsers);

            // 获取真实今日活跃用户数
            int todayActiveUsers = onlineUsersMapper.getTodayActiveUsersCount();
            realTimeStats.put("todayActiveUsers", todayActiveUsers);

            // 用户在线状态
            realTimeStats.put("isOnline", true);
            realTimeStats.put("lastActiveTime", new Date());

            // 更新用户活跃时间
            onlineUsersMapper.updateLastActiveTime(userId);

        } catch (Exception e) {
            log.error("获取实时统计异常: {}", e.getMessage(), e);
            realTimeStats.put("onlineUsers", 0);
            realTimeStats.put("todayActiveUsers", 0);
            realTimeStats.put("isOnline", false);
            realTimeStats.put("lastActiveTime", new Date());
        }
        return realTimeStats;
    }

    /**
     * 获取最近API调用记录（真实数据）
     */
    private List<Map<String, Object>> getRecentApiCalls(String userId, int limit) {
        List<Map<String, Object>> recentCalls = new ArrayList<>();
        try {
            // 从API日志表查询真实的调用记录
            List<Map<String, Object>> apiLogs = apiLogMapper.getUserRecentCalls(userId, limit);

            for (Map<String, Object> log : apiLogs) {
                Map<String, Object> call = new HashMap<>();
                call.put("id", log.get("id"));
                call.put("time", log.get("request_time"));
                call.put("apiType", log.get("api_type"));
                call.put("success", log.get("success"));
                call.put("amount", log.get("cost_points"));
                call.put("description", "API调用: " + log.get("api_type"));
                recentCalls.add(call);
            }

        } catch (Exception e) {
            log.error("获取最近API调用记录异常: {}", e.getMessage(), e);
        }
        return recentCalls;
    }

    /**
     * 获取图表数据（真实数据）
     */
    private Map<String, Object> getChartData(String userId) {
        Map<String, Object> chartData = new HashMap<>();
        try {
            // 趋势图数据 - 从API日志获取真实数据
            Map<String, Object> trendData = new HashMap<>();
            List<Map<String, Object>> trendLogs = apiLogMapper.getUserTrendData(userId);

            List<String> timeLabels = new ArrayList<>();
            List<Integer> callCounts = new ArrayList<>();
            List<Integer> successCounts = new ArrayList<>();
            List<Integer> errorCounts = new ArrayList<>();

            // 填充24小时数据，没有数据的时间点补0
            for (int i = 23; i >= 0; i--) {
                LocalDateTime time = LocalDateTime.now().minusHours(i);
                String timeLabel = time.format(DateTimeFormatter.ofPattern("HH:mm"));
                timeLabels.add(timeLabel);

                // 查找对应时间的数据
                boolean found = false;
                for (Map<String, Object> log : trendLogs) {
                    String logTimeLabel = (String) log.get("time_label");
                    if (timeLabel.equals(logTimeLabel)) {
                        callCounts.add(((Number) log.get("call_count")).intValue());
                        successCounts.add(((Number) log.get("success_count")).intValue());
                        errorCounts.add(((Number) log.get("error_count")).intValue());
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    callCounts.add(0);
                    successCounts.add(0);
                    errorCounts.add(0);
                }
            }

            trendData.put("timeLabels", timeLabels);
            trendData.put("callCounts", callCounts);
            trendData.put("successCounts", successCounts);
            trendData.put("errorCounts", errorCounts);
            chartData.put("trendData", trendData);

            // API分布数据 - 从API日志获取真实分布
            Map<String, Object> distributionData = new HashMap<>();
            List<Map<String, Object>> apiDistribution = apiLogMapper.getUserApiDistribution(userId);
            distributionData.put("data", apiDistribution);
            chartData.put("distributionData", distributionData);

            // 错误统计数据 - 从API日志获取真实错误统计
            Map<String, Object> errorStatsData = new HashMap<>();
            List<Map<String, Object>> errorLogs = apiLogMapper.getUserErrorStats(userId);

            List<String> categories = new ArrayList<>();
            List<Integer> error4xx = new ArrayList<>();
            List<Integer> error5xx = new ArrayList<>();
            List<Integer> timeoutErrors = new ArrayList<>();

            // 填充7天数据
            for (Map<String, Object> errorLog : errorLogs) {
                categories.add((String) errorLog.get("day_name"));
                error4xx.add(((Number) errorLog.getOrDefault("error_4xx", 0)).intValue());
                error5xx.add(((Number) errorLog.getOrDefault("error_5xx", 0)).intValue());
                timeoutErrors.add(((Number) errorLog.getOrDefault("timeout_errors", 0)).intValue());
            }

            errorStatsData.put("categories", categories);
            errorStatsData.put("error4xx", error4xx);
            errorStatsData.put("error5xx", error5xx);
            errorStatsData.put("timeoutErrors", timeoutErrors);
            chartData.put("errorStatsData", errorStatsData);

        } catch (Exception e) {
            log.error("获取图表数据异常: {}", e.getMessage(), e);
        }
        return chartData;
    }



    /**
     * 获取系统API统计数据（admin角色专用，真实数据）
     */
    private Map<String, Object> getSystemApiStatistics() {
        Map<String, Object> apiStats = new HashMap<>();
        try {
            // 获取系统今日统计
            Map<String, Object> todayStats = apiLogMapper.getSystemTodayStats();
            int todayCalls = 0;
            int todaySuccess = 0;
            if (todayStats != null && todayStats.get("total_calls") != null) {
                todayCalls = ((Number) todayStats.get("total_calls")).intValue();
                todaySuccess = todayStats.get("success_calls") != null ?
                    ((Number) todayStats.get("success_calls")).intValue() : 0;
            }

            // 获取系统昨日统计（用于计算增长率）
            Map<String, Object> yesterdayStats = apiLogMapper.getSystemYesterdayStats();
            int yesterdayCalls = 0;
            if (yesterdayStats != null && yesterdayStats.get("total_calls") != null) {
                yesterdayCalls = ((Number) yesterdayStats.get("total_calls")).intValue();
            }

            // 获取系统本月统计
            Map<String, Object> monthStats = apiLogMapper.getSystemMonthStats();
            int monthCalls = 0;
            if (monthStats != null && monthStats.get("total_calls") != null) {
                monthCalls = ((Number) monthStats.get("total_calls")).intValue();
            }

            // 获取系统上月统计（用于计算增长率）
            Map<String, Object> lastMonthStats = apiLogMapper.getSystemLastMonthStats();
            int lastMonthCalls = 0;
            if (lastMonthStats != null && lastMonthStats.get("total_calls") != null) {
                lastMonthCalls = ((Number) lastMonthStats.get("total_calls")).intValue();
            }

            // 计算增长率
            double todayGrowth = yesterdayCalls > 0 ?
                ((double)(todayCalls - yesterdayCalls) / yesterdayCalls * 100) : 0;
            double monthGrowth = lastMonthCalls > 0 ?
                ((double)(monthCalls - lastMonthCalls) / lastMonthCalls * 100) : 0;

            // 计算真实成功率
            double successRate = todayCalls > 0 ?
                ((double) todaySuccess / todayCalls * 100) : 100.0;

            // 计算成功率增长（与昨日对比）
            int yesterdaySuccess = 0;
            if (yesterdayStats != null && yesterdayStats.get("success_calls") != null) {
                yesterdaySuccess = ((Number) yesterdayStats.get("success_calls")).intValue();
            }
            double yesterdaySuccessRate = yesterdayCalls > 0 ?
                ((double) yesterdaySuccess / yesterdayCalls * 100) : 100.0;
            double successRateGrowth = successRate - yesterdaySuccessRate;

            apiStats.put("todayCalls", todayCalls);
            apiStats.put("todayGrowth", Math.round(todayGrowth * 100.0) / 100.0);
            apiStats.put("monthCalls", monthCalls);
            apiStats.put("monthGrowth", Math.round(monthGrowth * 100.0) / 100.0);
            apiStats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            apiStats.put("successRateGrowth", Math.round(successRateGrowth * 100.0) / 100.0);

        } catch (Exception e) {
            log.error("获取系统API统计数据异常: {}", e.getMessage(), e);
            // 返回默认值
            apiStats.put("todayCalls", 0);
            apiStats.put("todayGrowth", 0.0);
            apiStats.put("monthCalls", 0);
            apiStats.put("monthGrowth", 0.0);
            apiStats.put("successRate", 100.0);
            apiStats.put("successRateGrowth", 0.0);
        }
        return apiStats;
    }

    /**
     * 获取系统实时统计（admin角色专用，真实数据）
     */
    private Map<String, Object> getSystemRealTimeStatistics() {
        Map<String, Object> realTimeStats = new HashMap<>();
        try {
            // 获取系统真实在线用户数
            int onlineUsers = onlineUsersMapper.getCurrentOnlineUsersCount();
            realTimeStats.put("onlineUsers", onlineUsers);

            // 获取系统真实今日活跃用户数
            int todayActiveUsers = onlineUsersMapper.getTodayActiveUsersCount();
            realTimeStats.put("todayActiveUsers", todayActiveUsers);

            // 系统状态
            realTimeStats.put("isOnline", true);
            realTimeStats.put("lastActiveTime", new Date());

            // 清理离线用户
            onlineUsersMapper.cleanOfflineUsers();

        } catch (Exception e) {
            log.error("获取系统实时统计异常: {}", e.getMessage(), e);
            realTimeStats.put("onlineUsers", 0);
            realTimeStats.put("todayActiveUsers", 0);
            realTimeStats.put("isOnline", false);
            realTimeStats.put("lastActiveTime", new Date());
        }
        return realTimeStats;
    }

    /**
     * 获取系统最近API调用记录（admin角色专用，真实数据）
     */
    private List<Map<String, Object>> getSystemRecentApiCalls(int limit) {
        List<Map<String, Object>> recentCalls = new ArrayList<>();
        try {
            // 从API日志表查询系统最近的调用记录
            List<Map<String, Object>> apiLogs = apiLogMapper.getSystemRecentCalls(limit);

            for (Map<String, Object> log : apiLogs) {
                Map<String, Object> call = new HashMap<>();
                call.put("id", log.get("id"));
                call.put("time", log.get("request_time"));
                call.put("apiType", log.get("api_type"));
                call.put("success", log.get("success"));
                call.put("amount", log.get("cost_points"));
                call.put("description", "API调用: " + log.get("api_type"));
                call.put("userId", log.get("user_id")); // admin可以看到用户ID
                recentCalls.add(call);
            }

        } catch (Exception e) {
            log.error("获取系统最近API调用记录异常: {}", e.getMessage(), e);
        }
        return recentCalls;
    }

    /**
     * 获取系统图表数据（admin角色专用，真实数据）
     */
    private Map<String, Object> getSystemChartData() {
        Map<String, Object> chartData = new HashMap<>();
        try {
            // 趋势图数据 - 从API日志获取系统真实数据
            Map<String, Object> trendData = new HashMap<>();
            List<Map<String, Object>> trendLogs = apiLogMapper.getSystemTrendData();

            List<String> timeLabels = new ArrayList<>();
            List<Integer> callCounts = new ArrayList<>();
            List<Integer> successCounts = new ArrayList<>();
            List<Integer> errorCounts = new ArrayList<>();

            // 填充24小时数据，没有数据的时间点补0
            for (int i = 23; i >= 0; i--) {
                LocalDateTime time = LocalDateTime.now().minusHours(i);
                String timeLabel = time.format(DateTimeFormatter.ofPattern("HH:mm"));
                timeLabels.add(timeLabel);

                // 查找对应时间的数据
                boolean found = false;
                for (Map<String, Object> log : trendLogs) {
                    String logTimeLabel = (String) log.get("time_label");
                    if (timeLabel.equals(logTimeLabel)) {
                        callCounts.add(((Number) log.get("call_count")).intValue());
                        successCounts.add(((Number) log.get("success_count")).intValue());
                        errorCounts.add(((Number) log.get("error_count")).intValue());
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    callCounts.add(0);
                    successCounts.add(0);
                    errorCounts.add(0);
                }
            }

            trendData.put("timeLabels", timeLabels);
            trendData.put("callCounts", callCounts);
            trendData.put("successCounts", successCounts);
            trendData.put("errorCounts", errorCounts);
            chartData.put("trendData", trendData);

            // API分布数据 - 从API日志获取系统真实分布
            Map<String, Object> distributionData = new HashMap<>();
            List<Map<String, Object>> apiDistribution = apiLogMapper.getSystemApiDistribution();
            distributionData.put("data", apiDistribution);
            chartData.put("distributionData", distributionData);

            // 错误统计数据 - 从API日志获取系统真实错误统计
            Map<String, Object> errorStatsData = new HashMap<>();
            List<Map<String, Object>> errorLogs = apiLogMapper.getSystemErrorStats();

            List<String> categories = new ArrayList<>();
            List<Integer> error4xx = new ArrayList<>();
            List<Integer> error5xx = new ArrayList<>();
            List<Integer> timeoutErrors = new ArrayList<>();

            // 填充7天数据
            for (Map<String, Object> errorLog : errorLogs) {
                categories.add((String) errorLog.get("day_name"));
                error4xx.add(((Number) errorLog.getOrDefault("error_4xx", 0)).intValue());
                error5xx.add(((Number) errorLog.getOrDefault("error_5xx", 0)).intValue());
                timeoutErrors.add(((Number) errorLog.getOrDefault("timeout_errors", 0)).intValue());
            }

            errorStatsData.put("categories", categories);
            errorStatsData.put("error4xx", error4xx);
            errorStatsData.put("error5xx", error5xx);
            errorStatsData.put("timeoutErrors", timeoutErrors);
            chartData.put("errorStatsData", errorStatsData);

        } catch (Exception e) {
            log.error("获取系统图表数据异常: {}", e.getMessage(), e);
        }
        return chartData;
    }

    // ==================== 小红书分享功能接口 ====================

    /**
     * 扣子插件：小红书内容生成和分享页面（兼容接口）
     *
     * 注意：此接口保持向后兼容，内部调用新的统一逻辑
     *
     * @param request HTTP请求
     * @param params 请求参数，包含apiKey和pluginKey等
     * @return 生成结果，包含HTML页面链接和二维码
     */
    @ApiOperation(value = "小红书分享页面生成", notes = "扣子插件专用：通过pluginKey验证并生成小红书内容分享页面（兼容接口）")
    @PostMapping("/coze/xiaohongshu/generate-share-page")
    public Result<?> generateXiaohongshuSharePage(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        try {
            log.info("扣子插件调用 - 小红书分享页面生成（兼容接口，内部调用新逻辑）");

            // 直接调用新的统一接口，保持完全兼容
            return publishToXiaohongshu(params, request);

        } catch (Exception e) {
            log.error("扣子插件兼容接口调用失败: {}", e.getMessage(), e);
            return Result.error("系统异常，请稍后重试：" + e.getMessage());
        }

    }

    /**
     * 更新分享状态
     */
    @ApiOperation(value = "更新分享状态", notes = "更新页面的分享状态，防止重复分享")
    @PostMapping("/coze/xiaohongshu/update-share-status")
    public Result<?> updateShareStatus(@RequestBody Map<String, Object> params) {
        try {
            String pageId = (String) params.get("pageId");
            String platform = (String) params.get("platform");
            Long timestamp = (Long) params.get("timestamp");

            if (oConvertUtils.isEmpty(pageId) || oConvertUtils.isEmpty(platform)) {
                return Result.error("参数不能为空：pageId、platform");
            }

            // 记录分享状态到数据库（可选功能，不影响主流程）
            try {
                recordShareStatus(pageId, platform, timestamp);
            } catch (Exception e) {
                log.warn("记录分享状态失败，但不影响主功能: {}", e.getMessage());
            }

            log.info("分享状态更新 - 页面ID: {}, 平台: {}, 时间: {}", pageId, platform, new Date(timestamp));

            return Result.OK("分享状态更新成功");

        } catch (Exception e) {
            log.error("分享状态更新失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    // ==================== 小红书分享功能辅助方法 ====================

    /**
     * 调用现有的API验证接口
     */
    private Map<String, Object> callVerifyApiKey(String apiKey, String pluginKey) {
        try {
            // 直接调用本类的verifyApiKey方法
            Result<?> result = verifyApiKey(apiKey, pluginKey);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("result", result.getResult());

            return response;
        } catch (Exception e) {
            log.error("调用API验证接口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "API验证失败：" + e.getMessage());
            return response;
        }
    }

    /**
     * 一步到位：API验证 + 预扣费（原子操作，解决并发问题）
     */
    private Map<String, Object> callVerifyApiKeyAndPreDeduct(String apiKey, String pluginKey) {
        try {
            // 1. API-Key验证
            AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
            if (userProfile == null || userProfile.getStatus() != 1) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "API-Key无效或已被禁用");
                return response;
            }

            // 2. 查询插件信息
            AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
            if (plugin == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "插件不存在");
                return response;
            }

            String userId = userProfile.getUserId();
            BigDecimal pluginCost = plugin.getNeednum();

            // 边界情况检查
            if (pluginCost == null || pluginCost.compareTo(BigDecimal.ZERO) < 0) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "插件价格配置错误");
                return response;
            }

            // 如果插件免费，直接返回成功（不需要预扣费）
            if (pluginCost.compareTo(BigDecimal.ZERO) == 0) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", userId);
                userInfo.put("availableBalance", userProfileService.getAvailableBalance(userId));
                userInfo.put("pluginCost", BigDecimal.ZERO);
                userInfo.put("pluginName", plugin.getPlubname());
                userInfo.put("pluginKey", pluginKey);

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "免费插件验证成功");
                response.put("result", userInfo);
                return response;
            }

            // 3. 🔥 关键：直接进行预扣费（原子操作，自动检查余额并冻结）
            boolean preDeductSuccess = userProfileService.preDeductBalance(userId, pluginCost);
            if (!preDeductSuccess) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "余额不足或系统繁忙，请稍后重试");
                return response;
            }

            // 4. 预扣费成功，返回用户信息
            BigDecimal currentBalance = userProfileService.getAvailableBalance(userId);

            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", userId);
            userInfo.put("availableBalance", currentBalance);
            userInfo.put("pluginCost", pluginCost);
            userInfo.put("pluginName", plugin.getPlubname());
            userInfo.put("pluginKey", pluginKey);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "验证和预扣费成功");
            response.put("result", userInfo);

            return response;
        } catch (Exception e) {
            log.error("API验证和预扣费失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "验证失败：" + e.getMessage());
            return response;
        }
    }

    /**
     * 调用现有的HTML生成接口
     */
    private Map<String, Object> callGenerateHtml(String apiKey, String htmlContent, String filename) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("apiKey", apiKey);
            params.put("htmlContent", htmlContent);
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
            params.put("filename", filename);

            // 生成签名
            String timestamp = (String) params.get("timestamp");
            String signature = SecurityUtil.generateSignature(apiKey, htmlContent, timestamp);
            params.put("signature", signature);

            // 直接调用本类的generateHtml方法，传入null作为request参数
            // 在generateHtml方法中会处理request为null的情况
            Result<?> result = generateHtml(null, params);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("result", result.getResult());

            return response;
        } catch (Exception e) {
            log.error("调用HTML生成接口失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "HTML生成失败：" + e.getMessage());
            return response;
        }
    }

    /**
     * 构建用户分享内容
     */
    private Map<String, Object> buildUserShareContent(String shareType, String title, String content,
                                                     List<String> images, String video, List<String> tags, String location) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 处理标题
            if (title != null && !title.trim().isEmpty()) {
                result.put("title", title.trim());
            } else {
                result.put("title", "小红书分享");
            }

            // 处理内容
            if (content != null && !content.trim().isEmpty()) {
                result.put("content", content.trim());
            } else {
                result.put("content", "精彩内容分享");
            }

            // 处理图片
            if (images != null && !images.isEmpty()) {
                // 过滤空的图片URL
                List<String> validImages = images.stream()
                    .filter(img -> img != null && !img.trim().isEmpty())
                    .collect(Collectors.toList());
                result.put("images", validImages);
            } else {
                result.put("images", new ArrayList<>());
            }

            // 处理视频
            if (video != null && !video.trim().isEmpty()) {
                result.put("video", video.trim());
            }

            // 处理标签
            if (tags != null && !tags.isEmpty()) {
                // 过滤空的标签
                List<String> validTags = tags.stream()
                    .filter(tag -> tag != null && !tag.trim().isEmpty())
                    .collect(Collectors.toList());
                result.put("tags", validTags);
            } else {
                result.put("tags", Arrays.asList("分享", "生活"));
            }

            // 处理位置
            if (location != null && !location.trim().isEmpty()) {
                result.put("location", location.trim());
            }

            // 处理分享类型
            result.put("shareType", shareType != null ? shareType : "图文笔记");

            // 添加一些默认字段
            result.put("emojis", Arrays.asList("✨", "💕", "🌟", "👍", "🔥"));
            result.put("wordCount", content != null ? content.length() : 0);
            result.put("isPremium", false);

            return result;
        } catch (Exception e) {
            log.error("构建用户分享内容失败", e);
            throw new RuntimeException("内容构建失败：" + e.getMessage());
        }
    }

    /**
     * 生成小红书内容（AI生成，保留备用）
     */
    private Map<String, Object> generateXiaohongshuContent(String keywords, String contentType,
                                                          String style, String length, String targetAudience,
                                                          Map<String, Object> userInfo) {
        try {
            // 生成标题
            String title = generateXhsTitle(keywords, contentType);

            // 生成正文内容
            String content = generateXhsContent(keywords, contentType, style, length);

            // 生成标签
            List<String> tags = generateXhsTags(keywords, contentType);

            // 生成表情符号
            List<String> emojis = Arrays.asList("✨", "💕", "🌟", "👍", "🔥", "💖", "🎉", "😍");

            Map<String, Object> result = new HashMap<>();
            result.put("title", title);
            result.put("content", content);
            result.put("tags", tags);
            result.put("emojis", emojis);
            result.put("wordCount", content.length());
            result.put("isPremium", ((Number) userInfo.get("memberLevel")).intValue() >= 2);

            return result;
        } catch (Exception e) {
            log.error("生成小红书内容失败", e);
            throw new RuntimeException("内容生成失败：" + e.getMessage());
        }
    }

    /**
     * 生成小红书标题
     */
    private String generateXhsTitle(String keywords, String contentType) {
        String[] titleTemplates = {
            "🌟{keywords}超实用攻略！必看干货分享✨",
            "💕{keywords}种草清单！姐妹们冲鸭🔥",
            "✨{keywords}神仙好物推荐！真的太好用了💖",
            "🎉{keywords}全攻略！小白也能轻松上手👍",
            "💖{keywords}宝藏分享！不看后悔系列🌟"
        };

        String template = titleTemplates[(int) (Math.random() * titleTemplates.length)];
        return template.replace("{keywords}", keywords);
    }

    /**
     * 生成小红书正文内容
     */
    private String generateXhsContent(String keywords, String contentType, String style, String length) {
        StringBuilder content = new StringBuilder();

        // 开头
        content.append("姐妹们！今天要分享关于").append(keywords).append("的超实用内容！\n\n");
        content.append("经过我的亲身体验，发现了这些超棒的方法：\n\n");

        // 主要内容
        content.append("1️⃣ 第一个重点：").append(keywords).append("的基础知识\n");
        content.append("这个真的很重要，一定要掌握好基础！\n\n");

        content.append("2️⃣ 第二个要点：实用技巧分享\n");
        content.append("这些小技巧真的太实用了，学会了事半功倍！\n\n");

        content.append("3️⃣ 第三个技巧：进阶方法\n");
        content.append("想要更进一步的姐妹可以试试这个方法！\n\n");

        // 根据长度调整内容
        if ("long".equals(length)) {
            content.append("4️⃣ 第四个秘诀：高级技巧\n");
            content.append("这个是我压箱底的秘诀，今天分享给大家！\n\n");
            content.append("5️⃣ 最后的建议：持续学习\n");
            content.append("记住，").append(keywords).append("需要持续学习和实践哦！\n\n");
        }

        // 结尾
        content.append("真的太好用了！强烈推荐给大家～\n\n");
        content.append("有什么问题欢迎评论区交流哦！\n");
        content.append("记得点赞收藏支持一下💕");

        return content.toString();
    }

    /**
     * 生成小红书标签
     */
    private List<String> generateXhsTags(String keywords, String contentType) {
        List<String> tags = new ArrayList<>();
        tags.add(keywords);
        tags.add(contentType);
        tags.add("干货分享");
        tags.add("实用攻略");
        tags.add("必看");
        tags.add("种草");
        tags.add("推荐");

        return tags;
    }

    /**
     * 计算图片滑块宽度
     */
    private double calculateImageSlideWidth(Map<String, Object> contentResult) {
        try {
            List<String> images = (List<String>) contentResult.get("images");
            if (images == null || images.isEmpty()) {
                return 100.0; // 如果没有图片，默认100%
            }

            // 计算有效图片数量
            int validImageCount = 0;
            for (String imageUrl : images) {
                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    validImageCount++;
                }
            }

            if (validImageCount == 0) {
                return 100.0; // 如果没有有效图片，默认100%
            }

            // 轮播图每个slide应该占满容器宽度，通过transform切换
            return 100.0;
        } catch (Exception e) {
            log.error("计算图片滑块宽度失败", e);
            return 100.0; // 出错时默认100%
        }
    }

    /**
     * 构建小红书HTML页面内容（返回HTML和二维码URL）
     */
    private Map<String, Object> buildXiaohongshuHtmlPageWithQrCode(Map<String, Object> contentResult, String pageUrl, String baseUrl) {
        try {
            // 注意：不再在页面生成时生成签名，改为点击分享时实时获取

            // 生成页面二维码
            String qrCodeUrl = generatePageQrCode(pageUrl, baseUrl);

            // 使用内嵌模板（已包含触摸滑动功能）
            String htmlTemplate = getXiaohongshuHtmlTemplate(contentResult);

            // 安全处理用户输入
            String title = escapeHtml((String) contentResult.get("title"));
            String content = escapeHtml((String) contentResult.get("content"));
            String titleJs = escapeJavaScript((String) contentResult.get("title"));
            String contentJs = escapeJavaScript((String) contentResult.get("content"));

            // 确保JavaScript变量不为空
            if (titleJs == null || titleJs.trim().isEmpty()) {
                titleJs = "小红书内容";
            }
            if (contentJs == null || contentJs.trim().isEmpty()) {
                contentJs = "精彩内容分享";
            }

            // 构建标签HTML（安全转义）
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) contentResult.get("tags");
            String tagsHtml = "";
            if (tags != null && !tags.isEmpty()) {
                tagsHtml = tags.stream()
                    .map(tag -> "<span class=\"tag\">#" + escapeHtml(tag) + "</span>")
                    .collect(Collectors.joining("\n                "));
            }

            // 构建图片HTML
            @SuppressWarnings("unchecked")
            List<String> images = (List<String>) contentResult.get("images");

            String imagesHtml = buildImagesHtml(images);

            // 生成圆点指示器HTML
            String dotsHtml = buildDotsHtml(images);

            // 生成页面ID
            String pageId = "page_" + System.currentTimeMillis();

            // 安全替换模板变量
            String htmlContent = htmlTemplate;

            // 基本内容替换（使用旧模板占位符）
            htmlContent = htmlContent.replace("{{title}}", title != null ? title : "");
            htmlContent = htmlContent.replace("{{content}}", content != null ? content : "");
            htmlContent = htmlContent.replace("{{tags}}", tagsHtml != null ? tagsHtml : "");
            htmlContent = htmlContent.replace("{{images}}", imagesHtml != null ? imagesHtml : "");
            htmlContent = htmlContent.replace("{{dots}}", dotsHtml != null ? dotsHtml : "");

            // JavaScript变量替换（确保安全）
            htmlContent = htmlContent.replace("{{titleJs}}", titleJs != null ? titleJs : "");
            htmlContent = htmlContent.replace("{{contentJs}}", contentJs != null ? contentJs : "");
            htmlContent = htmlContent.replace("{{isShared}}", "false");
            htmlContent = htmlContent.replace("{{pageId}}", pageId != null ? pageId : "");

            // 构建图片数组的JavaScript代码
            String imagesJs = "[]";
            if (images != null && !images.isEmpty()) {
                imagesJs = "[" + images.stream()
                    .map(img -> "\"" + escapeJavaScript(img) + "\"")
                    .collect(Collectors.joining(", ")) + "]";
            }
            htmlContent = htmlContent.replace("{{imagesJs}}", imagesJs);
            htmlContent = htmlContent.replace("{{qrCodeUrl}}", qrCodeUrl != null ? qrCodeUrl : "");

            // 注意：不再在页面生成时嵌入签名，改为点击分享时实时获取





            Map<String, Object> result = new HashMap<>();
            result.put("htmlContent", htmlContent);
            result.put("qrCodeUrl", qrCodeUrl);

            return result;
        } catch (Exception e) {
            log.error("构建HTML页面失败", e);
            throw new RuntimeException("HTML页面构建失败：" + e.getMessage());
        }
    }

    /**
     * 构建图片HTML
     */
    private String buildImagesHtml(List<String> images) {
        log.info("=== buildImagesHtml 开始 ===");
        log.info("输入图片列表: {}", images);

        if (images == null || images.isEmpty()) {
            log.info("图片列表为空，返回占位符");
            // 没有图片时显示占位符
            return "<div class=\"image-slide\">" +
                   "<div class=\"image-placeholder\">" +
                   "📷<br>暂无图片" +
                   "</div>" +
                   "</div>";
        }

        StringBuilder imagesHtml = new StringBuilder();
        int validImageCount = 0;
        for (int i = 0; i < images.size(); i++) {
            String imageUrl = images.get(i);
            log.info("处理图片[{}]: '{}'", i, imageUrl);

            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                validImageCount++;
                String safeImageUrl = escapeHtml(imageUrl.trim());
                log.info("添加有效图片[{}]: '{}'", validImageCount, safeImageUrl);

                // 修复onerror属性的转义问题，添加图片加载后的宽高比检测
                imagesHtml.append("<div class=\"image-slide\">")
                         .append("<img src=\"").append(safeImageUrl).append("\" ")
                         .append("alt=\"分享图片\" ")
                         .append("onload=\"console.log('图片加载完成:', this.naturalWidth, 'x', this.naturalHeight);\" ")
                         .append("onerror=\"this.style.display='none';this.parentElement.innerHTML='<div class=&quot;image-placeholder&quot;>📷<br>图片加载失败</div>'\">")
                         .append("</div>");
            } else {
                log.info("跳过无效图片[{}]: '{}'", i, imageUrl);
            }
        }

        log.info("有效图片数量: {}", validImageCount);

        // 如果所有图片URL都是空的，返回占位符
        if (imagesHtml.length() == 0) {
            log.info("所有图片都无效，返回占位符");
            return "<div class=\"image-slide\">" +
                   "<div class=\"image-placeholder\">" +
                   "📷<br>暂无图片" +
                   "</div>" +
                   "</div>";
        }

        String result = imagesHtml.toString();
        log.info("=== buildImagesHtml 完成 ===");
        log.info("生成的HTML长度: {}", result.length());
        log.info("生成的HTML内容: {}", result);
        return result;
    }

    /**
     * 生成圆点指示器HTML
     */
    private String buildDotsHtml(List<String> images) {
        if (images == null || images.isEmpty()) {
            return "";
        }

        StringBuilder dotsHtml = new StringBuilder();
        for (int i = 0; i < images.size(); i++) {
            dotsHtml.append("<div class=\"carousel-dot")
                   .append(i == 0 ? " active" : "")
                   .append("\" onclick=\"goToImage(")
                   .append(i)
                   .append(")\"></div>");
        }

        return dotsHtml.toString();
    }

    /**
     * 构建小红书HTML页面内容（兼容旧方法）
     */
    private String buildXiaohongshuHtmlPage(Map<String, Object> contentResult, String pageUrl, String baseUrl) {
        Map<String, Object> result = buildXiaohongshuHtmlPageWithQrCode(contentResult, pageUrl, baseUrl);
        return (String) result.get("htmlContent");
    }

    /**
     * 小红书官方签名工具方法
     */
    private String buildXhsSignature(String appKey, String nonce, String timeStamp, String secret) {
        try {
            Map<String, String> params = new java.util.TreeMap<>();
            params.put("appKey", appKey);     // 🔥 官方文档：appKey
            params.put("nonce", nonce);       // 🔥 官方文档：nonce
            params.put("timeStamp", timeStamp); // 🔥 官方文档：timeStamp
            return generateXhsSignature(secret, params);
        } catch (Exception e) {
            log.error("生成小红书签名失败", e);
            throw new RuntimeException("签名生成失败: " + e.getMessage());
        }
    }

    /**
     * 小红书官方签名算法（按照官方代码实现）
     */
    private String generateXhsSignature(String secretKey, Map<String, String> params) {
        // Step 1: Sort parameters by key (TreeMap已经自动排序)
        Map<String, String> sortedParams = new java.util.TreeMap<>(params);

        // Step 2: Concatenate sorted parameters
        StringBuilder paramsString = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (paramsString.length() > 0) {
                paramsString.append("&");
            }
            paramsString.append(entry.getKey()).append("=").append(entry.getValue());
        }

        // Step 3: Create the final string to be signed (parameters + secret)
        String finalString = paramsString.toString() + secretKey;

        log.info("参数字符串: {}", paramsString.toString());
        log.info("Secret Key: {}", secretKey);
        log.info("待签名字符串: {}", finalString);

        // Step 4: Calculate signature using SHA-256
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(finalString.getBytes(StandardCharsets.UTF_8));

            // Convert the byte array to hexadecimal string
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            String signature = sb.toString();
            log.info("生成的签名: {}", signature);
            return signature;
        } catch (Exception e) {
            throw new RuntimeException("SHA-256加密失败", e);
        }
    }

    /**
     * 获取小红书access_token
     */
    private String getXhsAccessToken(String appKey, String appSecret, String tokenUrl) {
        try {
            log.info("正在获取小红书access_token...");

            // 生成第一次签名所需的参数
            String nonce = UUID.randomUUID().toString().replace("-", "").substring(0, 32);
            String timeStamp = String.valueOf(System.currentTimeMillis());

            // 第一次签名：使用appSecret
            String signature = buildXhsSignature(appKey, nonce, timeStamp, appSecret);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("app_key", appKey);
            requestBody.put("nonce", nonce);
            requestBody.put("timestamp", Long.parseLong(timeStamp));
            requestBody.put("signature", signature);

            log.info("Token请求参数: {}", requestBody);

            // 发送HTTP请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "COZE-Plugin/1.0");

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, entity, Map.class);

            log.info("Token响应状态: {}", response.getStatusCode());
            log.info("Token响应内容: {}", response.getBody());

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                String accessToken = (String) responseBody.get("access_token");
                if (accessToken == null && responseBody.get("data") != null) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    accessToken = (String) data.get("access_token");
                }

                if (accessToken != null) {
                    log.info("Access Token获取成功");
                    return accessToken;
                } else {
                    throw new RuntimeException("响应中未找到access_token字段");
                }
            } else {
                throw new RuntimeException("Token请求失败: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("获取access_token失败", e);
            throw new RuntimeException("获取access_token失败: " + e.getMessage());
        }
    }

    /**
     * 🚀 小红书分享页面生成接口（统一逻辑）
     * 使用新的模板和正确的分享逻辑，支持扣子插件和直接调用
     */
    @ApiOperation(value = "小红书分享页面生成", notes = "生成小红书分享页面，支持移动端直接分享")
    @PostMapping("/xiaohongshu/publish")
    public Result<?> publishToXiaohongshu(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        long requestStartTime = System.currentTimeMillis(); // 记录请求开始时间
        try {
            // 1. 获取请求参数 - 兼容COZE插件字段
            String apiKey = (String) params.get("apiKey");
            String pluginKey = (String) params.get("pluginKey");
            String shareType = (String) params.get("shareType");
            String title = (String) params.get("title");
            String content = (String) params.get("content");
            @SuppressWarnings("unchecked")
            List<String> images = (List<String>) params.get("images");
            String video = (String) params.get("video");
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) params.get("tags");
            String location = (String) params.get("location");

            log.info("小红书分享请求 - API-Key: {}, 插件: {}, 标题: {}", apiKey, pluginKey, title);

            // 2. 参数验证 - 只验证必要字段
            if (oConvertUtils.isEmpty(apiKey) || oConvertUtils.isEmpty(pluginKey)) {
                return Result.error("API密钥和插件标识不能为空");
            }

            // 3. 🔥 使用预扣费逻辑（原子操作，解决并发问题）
            Map<String, Object> preDeductResult = callVerifyApiKeyAndPreDeduct(apiKey, pluginKey);
            if (!(Boolean) preDeductResult.get("success")) {
                return Result.error((String) preDeductResult.get("message"));
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> userInfo = (Map<String, Object>) preDeductResult.get("result");
            String userId = (String) userInfo.get("userId");
            BigDecimal pluginCost = (BigDecimal) userInfo.get("pluginCost");

            log.info("预扣费验证成功，用户ID: {}, 冻结金额: {}", userId, pluginCost);

            try {
                // 4. 🔥 转存外部图片到我们的服务器
                log.info("🖼️ 开始处理图片，原始图片数量: {}", images != null ? images.size() : 0);
                if (images != null) {
                    for (int i = 0; i < images.size(); i++) {
                        log.info("🖼️ 原始图片[{}]: {}", i, images.get(i));
                    }
                }

                List<String> localImages = convertExternalImagesToLocal(images);

                log.info("🖼️ 图片处理完成，转存后图片数量: {}", localImages != null ? localImages.size() : 0);
                if (localImages != null) {
                    for (int i = 0; i < localImages.size(); i++) {
                        log.info("🖼️ 转存后图片[{}]: {}", i, localImages.get(i));
                    }
                }

                // 5. 构建用户分享内容（使用转存后的图片）
                Map<String, Object> contentResult = buildUserShareContent(shareType, title, content, localImages, video, tags, location);

                // 5. 生成页面ID
                String pageId = String.valueOf(System.currentTimeMillis());

                // 6. 生成小红书签名
                Map<String, Object> signatureResult = generateXhsShareSignature();

                // 7. 使用新模板生成HTML内容（🔥 使用转存后的图片）
                String htmlContent = generateXiaohongshuHtml(title, content, localImages, pageId, signatureResult);

                // 8. 保存HTML文件
                String htmlFileName = "xiaohongshu_" + pageId + "_" + generateRandomString(8) + ".html";
                String savedFileName = generateHtmlFile(htmlContent, htmlFileName);
                String requestBaseUrl = request != null ? getBaseUrl(request) : this.baseUrl;
                String previewUrl = requestBaseUrl + "/jeecg-boot/api/aigc/html/" + savedFileName;

                // 9. 生成二维码
                String qrcodeFileName = generateQRCode(previewUrl);
                String qrcodeUrl = requestBaseUrl + "/jeecg-boot/api/aigc/qrcode/" + qrcodeFileName;

                // 10. 所有业务逻辑成功，确认扣费（免费插件跳过）
                if (pluginCost.compareTo(BigDecimal.ZERO) > 0) {
                    boolean confirmSuccess = userProfileService.confirmDeductBalance(userId, pluginCost);
                    if (!confirmSuccess) {
                        throw new RuntimeException("确认扣费失败，冻结余额不足");
                    }
                    log.info("确认扣费成功，用户ID: {}, 扣费金额: {}", userId, pluginCost);
                } else {
                    log.info("免费插件，跳过扣费，用户ID: {}", userId);
                }

                // 11. 构建返回结果（兼容扣子插件格式）
                Map<String, Object> result = new HashMap<>();
                result.put("content", contentResult);
                result.put("pageUrl", previewUrl);
                result.put("qrCodeUrl", qrcodeUrl);
                result.put("pageId", pageId);
                result.put("expireTime", LocalDateTime.now().plusHours(24).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

                // 获取用户当前余额
                BigDecimal currentBalance = userProfileService.getAvailableBalance(userId);

                Map<String, Object> usage = new HashMap<>();
                usage.put("cost", pluginCost);
                usage.put("remainingBalance", currentBalance);
                usage.put("pluginName", (String) userInfo.get("pluginName"));
                result.put("usage", usage);

                result.put("tips", "PC端点击分享按钮会显示二维码，请用手机微信扫码后分享。移动端可直接分享。每个内容只能分享一次。");

                // 12. 🔥 生成交易记录（扣费已在第10步完成）
                try {
                    log.info("=== 开始生成交易记录 ===");
                    String pluginName = (String) userInfo.get("pluginName");

                    // 🔥 手动生成交易记录（因为confirmDeductBalance不会自动生成）
                    generateTransactionRecord(userId, pluginKey, pluginName, pluginCost);

                } catch (Exception e) {
                    log.error("生成交易记录失败", e);
                }

                // 13. 记录API使用情况（标准流程）
                try {
                    log.info("=== 开始记录API使用情况 ===");
                    log.info("userId: {}, apiKey: {}, pluginKey: {}, pluginName: {}, cost: {}",
                            userId, apiKey, pluginKey, userInfo.get("pluginName"), pluginCost);

                    recordApiUsage(request, userId, apiKey,
                                 pluginKey, (String) userInfo.get("pluginName"),
                                 200, requestStartTime, null, pluginCost, null);

                    // 🔥 更新API使用记录的page_id
                    updateApiUsagePageId(userId, pluginKey, pageId);

                    log.info("=== API使用情况记录完成 ===");
                } catch (Exception e) {
                    log.error("记录API使用情况失败，但不影响主功能", e);
                }

                // 14. 🔥 更新插件统计数据（插件调用次数、收益等）
                try {
                    log.info("=== 开始更新插件统计数据 ===");

                    // 查询插件信息
                    AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
                    if (plugin != null) {
                        // 调用数据联动更新
                        ((AigcApiServiceImpl) aigcApiService).performDataLinkage(userId, plugin, pluginCost);
                        log.info("✅ 插件统计数据更新成功 - 插件: {}, 用户: {}, 金额: {}",
                                plugin.getPlubname(), userId, pluginCost);
                    } else {
                        log.warn("⚠️ 未找到插件信息，跳过统计更新: {}", pluginKey);
                    }

                } catch (Exception e) {
                    log.error("❌ 更新插件统计数据失败，但不影响主功能", e);
                }

                log.info("小红书分享页面生成成功 - 页面ID: {}, URL: {}", pageId, previewUrl);
                return Result.OK("小红书分享页面创建成功！您可以通过以下链接访问分享页面", result);

            } catch (Exception e) {
                // 业务执行失败，退还冻结余额（免费插件跳过）
                log.error("小红书分享业务执行失败: {}", e.getMessage(), e);

                if (pluginCost.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("开始退还冻结余额，用户ID: {}, 金额: {}", userId, pluginCost);

                    // 尝试退还冻结余额，最多重试3次（立即重试，避免阻塞线程）
                    boolean refundSuccess = false;
                    for (int i = 0; i < 3; i++) {
                        refundSuccess = userProfileService.refundFrozenBalance(userId, pluginCost);
                        if (refundSuccess) {
                            log.info("冻结余额退还成功，用户ID: {}, 退还金额: {}, 重试次数: {}", userId, pluginCost, i + 1);
                            break;
                        } else {
                            log.warn("冻结余额退还失败，第{}次重试，用户ID: {}, 金额: {}", i + 1, userId, pluginCost);
                            // 立即重试，不等待（数据库操作失败通常是瞬时的）
                        }
                    }

                    if (!refundSuccess) {
                        // 🚨 严重错误：记录到错误日志，需要人工处理
                        log.error("🚨 严重错误：冻结余额退还最终失败！用户ID: {}, 金额: {}, 需要人工处理！", userId, pluginCost);
                        // TODO: 可以考虑发送告警通知或写入特殊的错误表
                    }
                } else {
                    log.info("免费插件业务失败，无需退还冻结余额，用户ID: {}", userId);
                }

                return Result.error("业务执行失败：" + e.getMessage());
            }

        } catch (Exception e) {
            log.error("小红书分享页面生成失败: {}", e.getMessage(), e);
            return Result.error("系统异常，请稍后重试：" + e.getMessage());
        }
    }

    /**
     * 🎨 生成小红书HTML内容
     * 使用新的模板文件
     */
    private String generateXiaohongshuHtml(String title, String content, List<String> images, String pageId, Map<String, Object> signatureResult) {
        try {
            // 读取模板文件
            String templatePath = "templates/xiaohongshu-share-template.html";
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(templatePath);
            if (inputStream == null) {
                throw new RuntimeException("模板文件未找到: " + templatePath);
            }

            // Java 8兼容的读取方式
            StringBuilder templateBuilder = new StringBuilder();
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    templateBuilder.append(line).append("\n");
                }
            }
            String template = templateBuilder.toString();

            // 处理图片数据
            if (images == null) {
                images = new ArrayList<>();
            }

            // 生成图片HTML
            StringBuilder imagesHtml = new StringBuilder();
            for (int i = 0; i < images.size(); i++) {
                String imageUrl = images.get(i);
                imagesHtml.append("<div class=\"image-slide")
                         .append(i == 0 ? " active" : "")
                         .append("\"><img src=\"")
                         .append(imageUrl)
                         .append("\" alt=\"图片")
                         .append(i + 1)
                         .append("\" /></div>");
            }



            // 🔥 查询真实的分享状态（关键优化）
            Map<String, Object> attemptInfo = checkShareAttempts(pageId);
            boolean isShared = checkIfPageShared(pageId);
            int currentAttempts = (Integer) attemptInfo.get("currentAttempts");
            int maxAttempts = (Integer) attemptInfo.get("maxAttempts");

            log.info("🔍 生成HTML时查询分享状态 - 页面ID: {}, 已分享: {}, 尝试次数: {}/{}",
                pageId, isShared, currentAttempts, maxAttempts);

            // 转换为JSON格式
            ObjectMapper objectMapper = new ObjectMapper();
            String titleJson = objectMapper.writeValueAsString(title);
            String contentJson = objectMapper.writeValueAsString(content);
            String imagesJson = objectMapper.writeValueAsString(images);

            // 替换模板变量（🔥 包含真实分享状态）
            String html = template
                .replace("{{TITLE}}", escapeHtml(title))
                .replace("{{CONTENT}}", escapeHtml(content))
                .replace("{{IMAGES_HTML}}", imagesHtml.toString())
                .replace("{{CAROUSEL_CONTROLS}}", "")
                .replace("{{CAROUSEL_DOTS}}", "")
                .replace("{{IMAGE_CONTAINER_STYLE}}", images.isEmpty() ? "style=\"display:none;\"" : "")
                .replace("{{IMAGE_COUNT}}", String.valueOf(images.size()))
                .replace("{{PAGE_ID}}", pageId)
                .replace("{{TITLE_JSON}}", titleJson)
                .replace("{{CONTENT_JSON}}", contentJson)
                .replace("{{IMAGES_JSON}}", imagesJson)
                .replace("{{TOTAL_IMAGES}}", String.valueOf(images.size()))
                // 🔥 新增：真实分享状态变量
                .replace("{{CURRENT_ATTEMPTS}}", String.valueOf(currentAttempts))
                .replace("{{MAX_ATTEMPTS}}", String.valueOf(maxAttempts))
                .replace("{{IS_SHARED}}", String.valueOf(isShared));

            return html;

        } catch (Exception e) {
            log.error("生成小红书HTML失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成小红书HTML失败: " + e.getMessage());
        }
    }

    /**
     * 🎲 生成随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder result = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            result.append(chars.charAt(random.nextInt(chars.length())));
        }
        return result.toString();
    }

    /**
     * 获取动态小红书分享签名（每次调用都生成新的nonce和timestamp）
     * 匿名访问，用于分享页面
     */
    @AutoLog(value = "获取小红书分享签名")
    @PostMapping("/xiaohongshu/get-fresh-signature")
    @PermitAll
    public Result<?> getFreshXhsSignature(@RequestBody Map<String, Object> params) {
        try {
            String pageId = (String) params.get("pageId");
            log.info("收到小红书签名请求，pageId: {}", pageId);

            // 🔥 检查是否已经分享过
            boolean isShared = checkIfPageShared(pageId);
            if (isShared) {
                log.warn("⚠️ 页面已分享，拒绝重复分享: {}", pageId);
                return Result.error(403, "该笔记已被分享，无法重复分享");
            }

            // 🔥 检查分享尝试次数
            Map<String, Object> attemptCheck = checkShareAttempts(pageId);
            boolean canAttempt = (Boolean) attemptCheck.get("canAttempt");
            int currentAttempts = (Integer) attemptCheck.get("currentAttempts");
            int maxAttempts = (Integer) attemptCheck.get("maxAttempts");

            if (!canAttempt) {
                log.warn("⚠️ 页面分享次数已达上限: {}, 当前次数: {}/{}", pageId, currentAttempts, maxAttempts);
                return Result.error(429, String.format("分享次数已达上限（%d/%d次），请联系客服", currentAttempts, maxAttempts));
            }

            // 🔥 增加尝试次数
            incrementShareAttempts(pageId);
            int newAttemptCount = currentAttempts + 1;
            int remainingAttempts = maxAttempts - newAttemptCount;

            log.info("🔄 页面 {} 第{}次分享尝试，剩余{}次", pageId, newAttemptCount, remainingAttempts);

            Map<String, Object> signature = generateXhsShareSignature();
            Map<String, Object> data = (Map<String, Object>) signature.get("data");
            log.info("生成新的小红书分享签名: nonce={}, timestamp={}",
                data.get("nonce"), data.get("timestamp"));

            // 🔥 注意：这里不再立即标记为已分享，等前端确认成功后再标记
            // markPageAsShared(pageId);

            // 🔥 返回签名数据和尝试次数信息
            Map<String, Object> result = new HashMap<>();
            result.putAll(data);
            result.put("currentAttempts", newAttemptCount);
            result.put("remainingAttempts", remainingAttempts);
            result.put("maxAttempts", maxAttempts);

            return Result.OK(result);
        } catch (Exception e) {
            log.error("生成小红书分享签名失败", e);
            return Result.error("获取分享签名失败: " + e.getMessage());
        }
    }

    /**
     * 🔍 检查分享状态API
     */
    @GetMapping("/xiaohongshu/check-share-status/{pageId}")
    @PermitAll
    public Result<?> checkShareStatus(@PathVariable String pageId) {
        try {
            boolean isShared = checkIfPageShared(pageId);
            Map<String, Object> attemptInfo = checkShareAttempts(pageId);

            Map<String, Object> result = new HashMap<>();
            result.put("pageId", pageId);
            result.put("isShared", isShared);
            result.put("currentAttempts", attemptInfo.get("currentAttempts"));
            result.put("maxAttempts", attemptInfo.get("maxAttempts"));
            result.put("canAttempt", attemptInfo.get("canAttempt"));
            result.put("message", isShared ? "该笔记已被分享" : "该笔记未分享");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 检查分享状态失败", e);
            return Result.error("检查状态失败: " + e.getMessage());
        }
    }

    /**
     * 🔥 确认分享成功API
     */
    @PostMapping("/xiaohongshu/confirm-share-success")
    @PermitAll
    public Result<?> confirmShareSuccess(@RequestBody Map<String, Object> params) {
        try {
            String pageId = (String) params.get("pageId");
            log.info("收到分享成功确认，pageId: {}", pageId);

            if (oConvertUtils.isEmpty(pageId)) {
                return Result.error("页面ID不能为空");
            }

            // 检查是否已经分享过
            boolean isShared = checkIfPageShared(pageId);
            if (isShared) {
                return Result.error("该页面已经分享过了");
            }

            // 标记为已分享
            markPageAsShared(pageId);

            Map<String, Object> result = new HashMap<>();
            result.put("pageId", pageId);
            result.put("message", "分享成功确认完成");
            result.put("timestamp", System.currentTimeMillis());

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 确认分享成功失败", e);
            return Result.error("确认分享成功失败: " + e.getMessage());
        }
    }

    /**
     * 🔥 检查页面是否已分享
     */
    private boolean checkIfPageShared(String pageId) {
        try {
            if (oConvertUtils.isEmpty(pageId)) {
                return false;
            }

            // 查询数据库中是否有该页面的分享记录
            String sql = "SELECT share_status FROM aicg_user_api_usage WHERE page_id = ? AND plugin_key = 'xiaohongshufabu' LIMIT 1";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, pageId);

            if (results.isEmpty()) {
                return false;
            }

            Object shareStatus = results.get(0).get("share_status");
            return shareStatus != null && Integer.parseInt(shareStatus.toString()) == 1;

        } catch (Exception e) {
            log.error("❌ 检查分享状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 🔥 生成交易记录（学习 consume 方法的插入逻辑）
     */
    private void generateTransactionRecord(String userId, String pluginKey, String pluginName, BigDecimal amount) {
        try {
            log.info("🔥 开始生成交易记录 - 用户ID: {}, 插件: {}, 金额: {}", userId, pluginKey, amount);

            // 获取用户当前余额信息
            AicgUserProfile userProfile = userProfileService.getByUserId(userId);
            if (userProfile == null) {
                log.error("❌ 生成交易记录失败：用户不存在 - 用户ID: {}", userId);
                return;
            }
            log.info("✅ 用户信息获取成功 - 当前余额: {}", userProfile.getAccountBalance());

            // 查询插件ID
            String pluginId = null;
            try {
                AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
                if (plugin != null) {
                    pluginId = plugin.getId();
                }
            } catch (Exception e) {
                log.warn("查询插件ID失败: {}", e.getMessage());
            }

            // 🔥 完全按照 consume 方法的逻辑插入交易记录
            String transactionId = UUID.randomUUID().toString().replace("-", "");
            String orderType = "plugin"; // 插件类型
            int orderStatus = 3; // 已完成

            // 生成订单号（按照 consume 方法的格式）
            String orderPrefix = "PLG";
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String orderSuffix = transactionId.substring(transactionId.length() - 8).toUpperCase();
            String relatedOrderId = orderPrefix + dateStr + "_" + orderSuffix;

            String insertSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                "description, related_order_id, transaction_time, create_by, create_time, " +
                "order_status, order_type, plugin_id, plugin_key, plugin_name" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            jdbcTemplate.update(insertSql,
                transactionId,
                userId,
                1, // 消费类型
                amount,
                userProfile.getAccountBalance().add(amount), // 扣费前余额
                userProfile.getAccountBalance(), // 扣费后余额
                "调用插件: " + pluginName,
                relatedOrderId,
                new Date(),
                "api_system",
                new Date(),
                orderStatus,
                orderType,
                pluginId,
                pluginKey,
                pluginName
            );

            log.info("✅ 交易记录生成成功 - 用户ID: {}, 插件: {}, 金额: {}, 订单号: {}",
                    userId, pluginName, amount, relatedOrderId);

        } catch (Exception e) {
            log.error("❌ 生成交易记录失败 - 用户ID: {}, 插件: {}, 金额: {}, 错误: {}",
                    userId, pluginKey, amount, e.getMessage(), e);
        }
    }

    /**
     * 🔥 更新API使用记录的page_id
     */
    private void updateApiUsagePageId(String userId, String pluginKey, String pageId) {
        try {
            if (oConvertUtils.isEmpty(pageId) || oConvertUtils.isEmpty(userId) || oConvertUtils.isEmpty(pluginKey)) {
                return;
            }

            // 更新最近的API使用记录，添加page_id
            String sql = "UPDATE aicg_user_api_usage SET page_id = ? WHERE user_id = ? AND plugin_key = ? AND page_id IS NULL ORDER BY call_time DESC LIMIT 1";
            int updatedRows = jdbcTemplate.update(sql, pageId, userId, pluginKey);

            if (updatedRows > 0) {
                log.info("✅ 成功更新API使用记录的page_id: {} -> {}", userId, pageId);
            } else {
                log.warn("⚠️ 未找到对应的API使用记录: userId={}, pluginKey={}", userId, pluginKey);
            }

        } catch (Exception e) {
            log.error("❌ 更新API使用记录page_id失败: {}", e.getMessage());
        }
    }

    /**
     * 🔥 检查分享尝试次数
     */
    private Map<String, Object> checkShareAttempts(String pageId) {
        Map<String, Object> result = new HashMap<>();

        try {
            if (oConvertUtils.isEmpty(pageId)) {
                result.put("canAttempt", false);
                result.put("currentAttempts", 0);
                result.put("maxAttempts", 3);
                return result;
            }

            // 查询现有记录
            String sql = "SELECT share_attempts, max_share_attempts FROM aicg_user_api_usage WHERE page_id = ? AND plugin_key = 'xiaohongshufabu' LIMIT 1";
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sql, pageId);

            int currentAttempts = 0;
            int maxAttempts = 3; // 默认最大3次

            if (!records.isEmpty()) {
                Map<String, Object> record = records.get(0);
                Object attemptsObj = record.get("share_attempts");
                Object maxAttemptsObj = record.get("max_share_attempts");

                currentAttempts = attemptsObj != null ? Integer.parseInt(attemptsObj.toString()) : 0;
                maxAttempts = maxAttemptsObj != null ? Integer.parseInt(maxAttemptsObj.toString()) : 3;
            }

            boolean canAttempt = currentAttempts < maxAttempts;

            result.put("canAttempt", canAttempt);
            result.put("currentAttempts", currentAttempts);
            result.put("maxAttempts", maxAttempts);

            log.info("🔍 页面 {} 分享次数检查: {}/{}, 可继续: {}", pageId, currentAttempts, maxAttempts, canAttempt);

            return result;

        } catch (Exception e) {
            log.error("❌ 检查分享次数失败: {}", e.getMessage());
            result.put("canAttempt", false);
            result.put("currentAttempts", 0);
            result.put("maxAttempts", 3);
            return result;
        }
    }

    /**
     * 🔥 增加分享尝试次数（修复版 - 兼容无唯一索引）
     */
    private void incrementShareAttempts(String pageId) {
        try {
            if (oConvertUtils.isEmpty(pageId)) {
                log.warn("⚠️ pageId为空，无法增加分享尝试次数");
                return;
            }

            log.info("🔄 开始增加分享尝试次数，pageId: {}", pageId);

            // 🔥 先查询是否存在记录
            String selectSql = "SELECT id, share_attempts, max_share_attempts FROM aicg_user_api_usage WHERE page_id = ? AND plugin_key = 'xiaohongshufabu' LIMIT 1";
            List<Map<String, Object>> existingRecords = jdbcTemplate.queryForList(selectSql, pageId);

            if (!existingRecords.isEmpty()) {
                // 🔥 更新现有记录
                Map<String, Object> record = existingRecords.get(0);
                int currentAttempts = Integer.parseInt(record.get("share_attempts").toString());
                int newAttempts = currentAttempts + 1;

                String updateSql = "UPDATE aicg_user_api_usage SET share_attempts = ?, last_attempt_time = NOW() WHERE page_id = ? AND plugin_key = 'xiaohongshufabu'";
                int updated = jdbcTemplate.update(updateSql, newAttempts, pageId);
                log.info("✅ 更新分享尝试次数成功，pageId: {}, 从{}次更新到{}次，影响行数: {}", pageId, currentAttempts, newAttempts, updated);
            } else {
                // 🔥 插入新记录
                String insertSql = "INSERT INTO aicg_user_api_usage (page_id, plugin_key, share_attempts, max_share_attempts, last_attempt_time) VALUES (?, 'xiaohongshufabu', 1, 3, NOW())";
                int inserted = jdbcTemplate.update(insertSql, pageId);
                log.info("✅ 插入分享尝试记录成功，pageId: {}, 初始次数: 1, 影响行数: {}", pageId, inserted);
            }

            // 🔥 验证操作结果
            String verifySql = "SELECT share_attempts, max_share_attempts FROM aicg_user_api_usage WHERE page_id = ? AND plugin_key = 'xiaohongshufabu' LIMIT 1";
            List<Map<String, Object>> verifyRecords = jdbcTemplate.queryForList(verifySql, pageId);

            if (!verifyRecords.isEmpty()) {
                Map<String, Object> record = verifyRecords.get(0);
                int currentAttempts = Integer.parseInt(record.get("share_attempts").toString());
                int maxAttempts = Integer.parseInt(record.get("max_share_attempts").toString());
                log.info("🔍 验证结果 - pageId: {}, 当前尝试次数: {}, 最大次数: {}", pageId, currentAttempts, maxAttempts);
            } else {
                log.error("❌ 验证失败：找不到记录，pageId: {}", pageId);
            }

        } catch (Exception e) {
            log.error("❌ 增加分享尝试次数失败，pageId: {}, 错误: {}", pageId, e.getMessage(), e);
        }
    }

    /**
     * 🔥 标记页面为已分享
     */
    private void markPageAsShared(String pageId) {
        try {
            if (oConvertUtils.isEmpty(pageId)) {
                return;
            }

            // 更新数据库记录
            String sql = "UPDATE aicg_user_api_usage SET share_status = 1, share_time = NOW(), share_platform = 'xiaohongshu' WHERE page_id = ? AND plugin_key = 'xiaohongshufabu'";
            int updatedRows = jdbcTemplate.update(sql, pageId);

            if (updatedRows > 0) {
                log.info("✅ 成功标记页面为已分享: {}", pageId);
            } else {
                log.warn("⚠️ 未找到对应的页面记录: {}", pageId);
            }

        } catch (Exception e) {
            log.error("❌ 标记分享状态失败: {}", e.getMessage());
        }
    }

    /**
     * 生成小红书分享签名（完全按照xiaohongshu-signature.js的逻辑实现）
     */
    private Map<String, Object> generateXhsShareSignature() {
        try {
            // 使用和JS版本完全一样的配置
            String appKey = "red.s0LZLFZmuDLp4WzZ";
            String appSecret = "a1795fb9f59178367d6485fe2105267f";
            String tokenUrl = "https://edith.xiaohongshu.com/api/sns/v1/ext/access/token";

            log.info("🔐 开始生成小红书官方签名...");

            // 第一步：获取access_token（按照JS版本的逻辑）
            String accessToken = getXhsAccessTokenLikeJS(appKey, appSecret, tokenUrl);
            log.info("✅ Access Token获取成功");

            // 第二步：生成JS签名（按照JS版本的逻辑）
            String nonce = generateNonceLikeJS(32);
            String timeStamp = String.valueOf(System.currentTimeMillis());

            // 使用access_token作为secret生成签名（和JS版本一样）
            String signature = generateXHSSignatureLikeJS(appKey, nonce, timeStamp, accessToken);

            log.info("🔐 签名生成详情: appKey={}, nonce={}, timestamp={}, signature={}...",
                appKey, nonce, timeStamp, signature.substring(0, 16));

            Map<String, Object> signatureData = new HashMap<>();
            signatureData.put("appKey", appKey);        // 前端SDK：appKey
            signatureData.put("nonce", nonce);          // 前端SDK：nonce
            signatureData.put("timestamp", timeStamp);  // 前端SDK：timestamp（全小写）
            signatureData.put("signature", signature);  // 前端SDK：signature

            // 返回和JS版本一样的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("data", signatureData);

            return result;
        } catch (Exception e) {
            log.error("❌ 签名生成失败:", e);
            throw new RuntimeException("签名生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成小红书分享页面（使用和xiaohongshu-h5-template.html一模一样的模板）
     */
    @GetMapping("/xiaohongshu/share-page/{pageId}")
    @PermitAll
    public void generateXhsSharePage(@PathVariable String pageId, HttpServletResponse response) {
        try {
            // 获取页面数据
            Map<String, Object> pageData = getPageDataById(pageId);
            if (pageData == null) {
                response.setStatus(404);
                response.getWriter().write("页面不存在");
                return;
            }

            // 生成HTML内容
            String htmlContent = generateXhsHtmlTemplate(pageData);

            // 设置响应头
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");

            // 输出HTML
            response.getWriter().write(htmlContent);

        } catch (Exception e) {
            log.error("生成小红书分享页面失败", e);
            try {
                response.setStatus(500);
                response.getWriter().write("页面生成失败");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 获取页面数据
     */
    private Map<String, Object> getPageDataById(String pageId) {
        // 这里应该从数据库获取页面数据，暂时返回测试数据
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("title", "测试标题");
        pageData.put("content", "测试内容");
        pageData.put("images", Arrays.asList("https://example.com/image1.jpg"));
        return pageData;
    }

    /**
     * 生成和xiaohongshu-h5-template.html一模一样的HTML模板
     */
    private String generateXhsHtmlTemplate(Map<String, Object> pageData) {
        // 完全复制xiaohongshu-h5-template.html的内容
        return loadXiaohongshuTemplateContent(pageData);
    }

    /**
     * 按照JS版本逻辑生成随机字符串
     */
    private String generateNonceLikeJS(int length) {
        // 按照JS版本：crypto.randomBytes(length).toString('hex').substring(0, length)
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        String chars = "0123456789abcdef";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 按照JS版本逻辑生成签名
     */
    private String generateXHSSignatureLikeJS(String appKey, String nonce, String timestamp, String secret) {
        try {
            log.info("🔐 生成签名参数: appKey={}, nonce={}, timestamp={}, secret={}...",
                appKey, nonce, timestamp, secret.substring(0, Math.min(10, secret.length())));

            // 🔥 按照官方文档：使用正确的参数名称
            Map<String, String> params = new HashMap<>();
            params.put("appKey", appKey);    // 官方文档：appKey
            params.put("nonce", nonce);      // 官方文档：nonce
            params.put("timeStamp", timestamp); // 官方文档：timeStamp（驼峰）

            // 对所有待签名参数使用 URL 键值对的格式拼接成字符串
            String paramString = params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

            // 🔥 按照官方文档：直接拼接secret到参数字符串末尾（不用&）
            String dataToSign = paramString + secret;

            log.info("🔐 待签名字符串: {}", dataToSign);

            // 对 string1 作 SHA-256 加密
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(dataToSign.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            String signature = sb.toString();
            log.info("🔐 生成的签名: {}", signature);

            return signature;
        } catch (Exception e) {
            log.error("❌ 签名生成失败", e);
            throw new RuntimeException("签名生成失败: " + e.getMessage());
        }
    }

    /**
     * 按照JS版本逻辑获取access_token
     */
    private String getXhsAccessTokenLikeJS(String appKey, String appSecret, String tokenUrl) {
        try {
            log.info("🔑 正在获取小红书access_token...");
            log.info("🔑 使用Token URL: {}", tokenUrl);
            log.info("🔑 使用appKey: {}", appKey);

            // 生成第一次签名所需的参数
            String nonce = generateNonceLikeJS(32);
            String timestamp = String.valueOf(System.currentTimeMillis());

            // 第一次签名：使用appSecret
            String signature = generateXHSSignatureLikeJS(appKey, nonce, timestamp, appSecret);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("app_key", appKey);
            requestBody.put("nonce", nonce);
            requestBody.put("timestamp", timestamp);  // 🔧 修复：保持字符串格式，与签名生成保持一致
            requestBody.put("signature", signature);

            log.info("🔑 Token请求参数: {}", requestBody);

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "COZE-Plugin/1.0");

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, entity, Map.class);

            log.info("🔑 响应状态: {}", response.getStatusCode());
            log.info("🔑 Token响应: {}", response.getBody());

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();

                // 按照JS版本的逻辑获取access_token
                String accessToken = (String) responseBody.get("access_token");
                if (accessToken == null && responseBody.get("data") != null) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    accessToken = (String) data.get("access_token");
                }

                if (accessToken == null) {
                    throw new RuntimeException("响应中未找到access_token字段");
                }

                log.info("✅ Access Token获取成功: {}...", accessToken.substring(0, Math.min(20, accessToken.length())));
                return accessToken;
            } else {
                throw new RuntimeException("Token请求失败: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("❌ Access Token获取失败", e);
            throw new RuntimeException("Access Token获取失败: " + e.getMessage());
        }
    }

    /**
     * 加载小红书HTML模板内容（完全复制xiaohongshu-h5-template.html）
     */
    private String loadXiaohongshuTemplateContent(Map<String, Object> pageData) {
        // 这里应该完全复制xiaohongshu-h5-template.html的内容
        // 暂时返回简化版本，稍后完善
        return "<!DOCTYPE html><html><head><title>小红书分享</title></head><body><h1>小红书分享页面</h1></body></html>";
    }

    /**
     * 生成页面二维码
     */
    private String generatePageQrCode(String pageUrl, String baseUrl) {
        try {
            // 生成二维码文件名
            String qrCodeFileName = "page_qr_" + System.currentTimeMillis() + ".png";

            // 确保二维码存储目录存在
            File qrcodeDir = new File(qrcodeStoragePath);
            if (!qrcodeDir.exists()) {
                qrcodeDir.mkdirs();
            }

            // 生成二维码文件路径
            String qrCodeFilePath = qrcodeStoragePath.endsWith("/") || qrcodeStoragePath.endsWith("\\") ?
                qrcodeStoragePath + qrCodeFileName :
                qrcodeStoragePath + File.separator + qrCodeFileName;

            // 生成二维码
            QRCodeUtil.generateQRCode(pageUrl, 200, 200, qrCodeFilePath);

            // 返回二维码访问URL
            String qrCodeUrl = baseUrl + "/jeecg-boot/api/aigc/qrcode/" + qrCodeFileName;

            log.info("页面二维码生成成功: {}", qrCodeUrl);
            return qrCodeUrl;

        } catch (Exception e) {
            log.error("生成页面二维码失败: {}", pageUrl, e);
            // 降级到第三方服务
            try {
                return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" +
                       URLEncoder.encode(pageUrl, StandardCharsets.UTF_8.toString());
            } catch (Exception urlException) {
                log.error("URL编码失败: {}", pageUrl, urlException);
                return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" + pageUrl;
            }
        }
    }

    /**
     * HTML转义，防止XSS攻击
     */
    private String escapeHtml(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }

    /**
     * JavaScript转义，防止JavaScript注入
     */
    private String escapeJavaScript(String input) {
        if (input == null) return "";
        return input.replace("\\", "\\\\")
                   .replace("'", "\\'")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t")
                   .replace("\b", "\\b")
                   .replace("\f", "\\f")
                   .replace("/", "\\/")
                   .replace("<", "\\u003c")
                   .replace(">", "\\u003e");
    }

    /**
     * 记录API使用情况（标准流程）
     */
    private void recordApiUsage(HttpServletRequest request, String userId, String apiKey,
                               String pluginKey, String pluginName, int responseStatus,
                               long startTime, Integer tokensUsed, BigDecimal costAmount, String errorMessage) {
        try {
            log.info("=== recordApiUsage 方法开始 ===");
            long responseTime = System.currentTimeMillis() - startTime;
            String requestParams = buildRequestParams(pluginKey, pluginName);
            String ipAddress = getClientIpAddress(request);
            String userAgent = request != null ? request.getHeader("User-Agent") : null;

            log.info("准备调用 apiUsageService.recordUsage，参数：");
            log.info("  userId: {}", userId);
            log.info("  apiKey: {}", apiKey);
            log.info("  endpoint: /api/aigc/coze/xiaohongshu/generate-share-page");
            log.info("  method: POST");
            log.info("  requestParams: {}", requestParams);
            log.info("  responseStatus: {}", responseStatus);
            log.info("  responseTime: {}", responseTime);
            log.info("  tokensUsed: {}", tokensUsed);
            log.info("  costAmount: {}", costAmount);
            log.info("  ipAddress: {}", ipAddress);
            log.info("  userAgent: {}", userAgent);
            log.info("  errorMessage: {}", errorMessage);

            // 调用标准的API使用记录服务
            Object result = apiUsageService.recordUsage(
                userId,
                apiKey,
                "/api/aigc/coze/xiaohongshu/generate-share-page",
                "POST",
                requestParams,
                responseStatus,
                (int) responseTime,
                tokensUsed,
                costAmount,
                ipAddress,
                userAgent,
                errorMessage
            );

            log.info("apiUsageService.recordUsage 调用成功，返回结果: {}", result != null ? result.toString() : "null");
            log.info("API使用记录保存成功 - 用户: {}, 插件: {} ({}), 费用: {}",
                    userId, pluginName, pluginKey, costAmount);
        } catch (Exception e) {
            log.error("记录API使用情况失败", e);
            throw e; // 重新抛出异常以便上层捕获
        }
    }

    /**
     * 构建请求参数JSON
     */
    private String buildRequestParams(String pluginKey, String pluginName) {
        Map<String, Object> params = new HashMap<>();
        params.put("pluginKey", pluginKey);
        params.put("pluginName", pluginName);
        params.put("shareType", "图文笔记");
        try {
            return new ObjectMapper().writeValueAsString(params);
        } catch (Exception e) {
            return "{}";
        }
    }

    /**
     * 记录插件使用情况
     */
    private void recordPluginUsage(Map<String, Object> userInfo, String pluginName,
                                  Map<String, Object> contentResult, Map<String, Object> htmlData, String qrCodeUrl) {
        try {
            String userId = (String) userInfo.get("userId");
            BigDecimal deductedAmount = (BigDecimal) userInfo.get("deductedAmount");
            BigDecimal balanceAfter = (BigDecimal) userInfo.get("balanceAfter");

            // 1. 插件调用统计通过 aicg_user_api_usage 表来实现，不需要冗余字段

            // 2. 记录插件使用记录
            String insertUsageRecordSql = "INSERT INTO plugin_usage_record " +
                "(user_id, plugin_name, cost_amount, content_type, content_title, " +
                "page_url, qr_code_url, usage_time, create_time) VALUES " +
                "(?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            jdbcTemplate.update(insertUsageRecordSql,
                userId,
                pluginName,
                deductedAmount,
                "xiaohongshu_content",
                contentResult.get("title"),
                htmlData.get("htmlUrl"),
                qrCodeUrl
            );

            // 3. 记录金额变动记录
            String insertBalanceRecordSql = "INSERT INTO user_balance_record " +
                "(user_id, change_type, change_amount, balance_before, balance_after, " +
                "description, related_plugin, create_time) VALUES " +
                "(?, 'PLUGIN_USAGE', ?, ?, ?, ?, ?, NOW())";

            BigDecimal balanceBefore = balanceAfter.add(deductedAmount);
            String description = String.format("使用插件：%s，生成内容：%s",
                pluginName, contentResult.get("title"));

            jdbcTemplate.update(insertBalanceRecordSql,
                userId,
                deductedAmount.negate(), // 负数表示扣费
                balanceBefore,
                balanceAfter,
                description,
                pluginName
            );

            log.info("插件使用记录已保存 - 用户ID: {}, 插件: {}, 扣费: {}",
                userId, pluginName, deductedAmount);

        } catch (Exception e) {
            log.error("记录插件使用情况失败: {}", e.getMessage());
            if (e.getMessage() != null && e.getMessage().contains("doesn't exist")) {
                log.warn("数据库表不存在，请先创建相关表结构");
            }
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 记录分享状态
     */
    private void recordShareStatus(String pageId, String platform, Long timestamp) {
        try {
            // 1. 更新分享记录表
            String insertShareRecordSql = "INSERT INTO share_record " +
                "(page_id, platform, share_time) VALUES " +
                "(?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE " +
                "share_time = NOW()";

            jdbcTemplate.update(insertShareRecordSql,
                pageId,
                platform,
                new Date(timestamp)
            );

            // 2. 更新页面状态为已分享
            String updatePageStatusSql = "UPDATE plugin_usage_record SET " +
                "is_shared = 1, share_time = ?, share_platform = ? " +
                "WHERE page_url LIKE ?";

            jdbcTemplate.update(updatePageStatusSql,
                new Date(timestamp),
                platform,
                "%" + pageId + "%"
            );

            log.info("分享状态记录已保存 - 页面ID: {}, 平台: {}", pageId, platform);

        } catch (Exception e) {
            log.error("记录分享状态失败: {}", e.getMessage());
            if (e.getMessage() != null && e.getMessage().contains("doesn't exist")) {
                log.warn("数据库表不存在，请先创建相关表结构");
            }
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从URL中提取页面ID
     */
    private String extractPageIdFromUrl(String url) {
        try {
            if (oConvertUtils.isEmpty(url)) {
                return "unknown";
            }
            // 从URL中提取文件名，去掉扩展名
            String fileName = url.substring(url.lastIndexOf("/") + 1);
            if (fileName.contains(".")) {
                fileName = fileName.substring(0, fileName.lastIndexOf("."));
            }
            return fileName;
        } catch (Exception e) {
            log.warn("提取页面ID失败: {}", url);
            return "unknown_" + System.currentTimeMillis();
        }
    }



    /**
     * 加载小红书HTML模板文件（包含触摸滑动功能）
     */
    private String loadXiaohongshuTemplate() {
        try {
            // 读取模板文件
            String templatePath = "templates/xiaohongshu-share-template.html";
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(templatePath);
            if (inputStream == null) {
                log.warn("新模板文件未找到: {}, 使用内嵌模板", templatePath);
                return null; // 返回null，让调用方使用旧模板
            }

            // Java 8兼容的读取方式
            StringBuilder templateBuilder = new StringBuilder();
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    templateBuilder.append(line).append("\n");
                }
            }

            log.info("成功加载小红书模板文件，包含触摸滑动功能");
            return templateBuilder.toString();

        } catch (Exception e) {
            log.error("加载小红书模板文件失败: {}", e.getMessage(), e);
            return null; // 返回null，让调用方使用旧模板
        }
    }

    /**
     * 获取小红书HTML模板（旧版本，内嵌模板）
     */
    private String getXiaohongshuHtmlTemplate(Map<String, Object> contentResult) {
        return "<!DOCTYPE html>\n" +
            "<html lang=\"zh-CN\">\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
            "    <title>{{title}} - 智界AIGC</title>\n" +
            "    <style>\n" +
            "        * { margin: 0; padding: 0; box-sizing: border-box; }\n" +
            "        body {\n" +
            "            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n" +
            "            background: linear-gradient(135deg, #ff6b6b, #ff8e8e, #ffa8a8);\n" +
            "            min-height: 100vh; padding: 20px;\n" +
            "        }\n" +
            "        .container {\n" +
            "            max-width: 400px; margin: 0 auto; background: white;\n" +
            "            border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden;\n" +
            "        }\n" +
            "        /* 图片轮播区域 */\n" +
            "        .images-carousel {\n" +
            "            position: relative; width: 100%; height: 400px; overflow: hidden;\n" +
            "            background: #f5f5f5;\n" +
            "        }\n" +
            "        .carousel-slides {\n" +
            "            display: flex; width: 100%; height: 100%; transition: transform 0.3s ease;\n" +
            "        }\n" +
            "        .image-slide {\n" +
            "            min-width: 100%; height: 100%; display: flex; align-items: center;\n" +
            "            justify-content: center; position: relative; flex-shrink: 0;\n" +
            "        }\n" +
            "        .image-slide img {\n" +
            "            width: 100%; height: 100%; object-fit: cover; display: block;\n" +
            "            object-position: center center;\n" +
            "        }\n" +
            "        /* 智能显示：宽图用cover，竖图用contain */\n" +
            "        .image-slide img.wide-image {\n" +
            "            width: 100%; height: 100%; object-fit: cover;\n" +
            "        }\n" +
            "        .image-slide img.tall-image {\n" +
            "            max-width: 100%; max-height: 100%; object-fit: contain;\n" +
            "        }\n" +
            "        .image-placeholder {\n" +
            "            width: 100%; height: 100%; display: flex; align-items: center;\n" +
            "            justify-content: center; background: linear-gradient(135deg, #ff6b6b, #ff8e8e);\n" +
            "            color: white; font-size: 18px; flex-direction: column;\n" +
            "        }\n" +
            "        .carousel-dots {\n" +
            "            display: flex; justify-content: center; padding: 20px; gap: 8px;\n" +
            "        }\n" +
            "        .carousel-dot {\n" +
            "            width: 8px; height: 8px; border-radius: 50%; background: #ddd;\n" +
            "            cursor: pointer; transition: all 0.3s ease;\n" +
            "        }\n" +
            "        .carousel-dot.active {\n" +
            "            background: #ff6b6b; transform: scale(1.2);\n" +
            "        }\n" +
            "        .image-counter {\n" +
            "            position: absolute; top: 15px; right: 15px;\n" +
            "            background: rgba(0,0,0,0.6); color: white;\n" +
            "            padding: 5px 10px; border-radius: 15px; font-size: 12px;\n" +
            "        }\n" +
            "        /* 轮播导航按钮 */\n" +
            "        .carousel-nav {\n" +
            "            position: absolute; top: 50%; transform: translateY(-50%);\n" +
            "            background: rgba(0,0,0,0.5); color: white; border: none;\n" +
            "            width: 40px; height: 40px; border-radius: 50%;\n" +
            "            font-size: 18px; cursor: pointer; z-index: 10;\n" +
            "            transition: background 0.3s ease;\n" +
            "        }\n" +
            "        .carousel-nav:hover { background: rgba(0,0,0,0.7); }\n" +
            "        .carousel-nav.prev { left: 15px; }\n" +
            "        .carousel-nav.next { right: 15px; }\n" +
            "        .content { padding: 20px; }\n" +
            "        .title {\n" +
            "            font-size: 20px; font-weight: bold; color: #333;\n" +
            "            margin-bottom: 20px; line-height: 1.4;\n" +
            "        }\n" +
            "        .text {\n" +
            "            font-size: 16px; line-height: 1.6; color: #666;\n" +
            "            margin-bottom: 20px; white-space: pre-wrap;\n" +
            "        }\n" +
            "        .tags { margin-bottom: 30px; }\n" +
            "        .tag {\n" +
            "            display: inline-block; background: #ff2442; color: white;\n" +
            "            padding: 6px 12px; border-radius: 15px; font-size: 14px;\n" +
            "            margin: 5px 5px 5px 0;\n" +
            "        }\n" +
            "        .share-section {\n" +
            "            text-align: center; padding: 20px; margin: 0 20px 20px 20px;\n" +
            "            background: #f8f9fa; border-radius: 15px;\n" +
            "        }\n" +
            "        .share-btn {\n" +
            "            background: linear-gradient(135deg, #ff2442, #ff6b6b);\n" +
            "            color: white; border: none; padding: 15px 30px;\n" +
            "            border-radius: 25px; font-size: 16px; font-weight: bold;\n" +
            "            cursor: pointer; transition: all 0.3s ease;\n" +
            "            margin-bottom: 15px; min-width: 200px;\n" +
            "        }\n" +
            "        .share-btn:hover:not(:disabled) {\n" +
            "            transform: translateY(-2px);\n" +
            "            box-shadow: 0 10px 20px rgba(255, 36, 66, 0.3);\n" +
            "        }\n" +
            "        .share-btn:disabled {\n" +
            "            background: #ccc; cursor: not-allowed;\n" +
            "            transform: none; box-shadow: none;\n" +
            "        }\n" +
            "        .share-btn.used { background: #28a745; cursor: default; }\n" +
            "        .modal-overlay {\n" +
            "            display: none; position: fixed; top: 0; left: 0;\n" +
            "            width: 100%; height: 100%; background: rgba(0, 0, 0, 0.7);\n" +
            "            z-index: 1000; animation: fadeIn 0.3s ease;\n" +
            "        }\n" +
            "        .modal-content {\n" +
            "            position: absolute; top: 50%; left: 50%;\n" +
            "            transform: translate(-50%, -50%); background: white;\n" +
            "            border-radius: 20px; padding: 40px; text-align: center;\n" +
            "            max-width: 400px; width: 90%; animation: slideIn 0.3s ease;\n" +
            "        }\n" +
            "        .modal-title {\n" +
            "            font-size: 20px; font-weight: bold; color: #333; margin-bottom: 10px;\n" +
            "        }\n" +
            "        .modal-subtitle { color: #666; font-size: 14px; margin-bottom: 20px; }\n" +
            "        .qr-code {\n" +
            "            width: 200px; height: 200px; margin: 20px auto;\n" +
            "            border: 2px solid #ff2442; border-radius: 10px; display: block;\n" +
            "        }\n" +
            "        .qr-tips {\n" +
            "            margin-top: 15px; color: #666; font-size: 14px; line-height: 1.5;\n" +
            "        }\n" +
            "        .close-btn {\n" +
            "            background: #6c757d; color: white; border: none;\n" +
            "            padding: 10px 20px; border-radius: 20px; cursor: pointer;\n" +
            "            font-size: 14px; margin-top: 20px;\n" +
            "        }\n" +
            "        .mobile-tip { color: #666; font-size: 14px; margin-top: 10px; }\n" +
            "        .footer {\n" +
            "            text-align: center; padding: 20px; color: #999; font-size: 12px;\n" +
            "        }\n" +
            "        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }\n" +
            "        @keyframes slideIn {\n" +
            "            from { opacity: 0; transform: translate(-50%, -60%); }\n" +
            "            to { opacity: 1; transform: translate(-50%, -50%); }\n" +
            "        }\n" +
            "        @keyframes slideOut {\n" +
            "            from { opacity: 1; transform: translateX(0); }\n" +
            "            to { opacity: 0; transform: translateX(100%); }\n" +
            "        }\n" +
            "        .qr-code.loading {\n" +
            "            background: #f8f9fa;\n" +
            "            display: flex;\n" +
            "            align-items: center;\n" +
            "            justify-content: center;\n" +
            "        }\n" +
            "        @media (max-width: 768px) {\n" +
            "            .container { margin: 10px; border-radius: 15px; }\n" +
            "            .header, .content { padding: 20px; }\n" +
            "            .modal-content { padding: 30px 20px; }\n" +
            "            .qr-code { width: 180px; height: 180px; }\n" +
            "        }\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "    <div class=\"container\">\n" +
            "        <!-- 图片轮播区域 -->\n" +
            "        <div class=\"images-carousel\" id=\"imagesCarousel\">\n" +
            "            <div class=\"carousel-slides\" id=\"carouselSlides\">\n" +
            "                {{images}}\n" +
            "            </div>\n" +
            "        </div>\n" +
            "        \n" +
            "        <div class=\"carousel-dots\" id=\"carouselDots\">\n" +
            "            {{dots}}\n" +
            "        </div>\n" +
            "        \n" +
            "        <!-- 内容区域 -->\n" +
            "        <div class=\"content\">\n" +
            "            <div class=\"title\">{{title}}</div>\n" +
            "            <div class=\"text\">{{content}}</div>\n" +
            "            <div class=\"tags\">{{tags}}</div>\n" +
            "        </div>\n" +
            "        \n" +
            "        <!-- 分享区域 -->\n" +
            "        <div class=\"share-section\">\n" +
            "            <button class=\"share-btn\" id=\"shareBtn\" onclick=\"publishToXiaohongshu()\">\n" +
            "                <span id=\"shareBtnText\">📱 分享到小红书</span>\n" +
            "            </button>\n" +
            "            <div class=\"mobile-tip\" id=\"mobileTip\">点击按钮即可分享到小红书</div>\n" +
            "        </div>\n" +
            "        \n" +
            "        <div class=\"footer\">\n" +
            "            <div>本内容由智界AIGC分享</div>\n" +
            "            <div>页面将在24小时后过期</div>\n" +
            "        </div>\n" +
            "        \n" +

            "    </div>\n" +
            "    <div class=\"modal-overlay\" id=\"qrModal\">\n" +
            "        <div class=\"modal-content\">\n" +
            "            <div class=\"modal-title\">📱 手机扫码分享</div>\n" +
            "            <div class=\"modal-subtitle\">请使用手机微信扫描下方二维码</div>\n" +
            "            <img class=\"qr-code\" id=\"qrCodeImg\" alt=\"二维码\" src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200'%3E%3Crect width='200' height='200' fill='%23f8f9fa'/%3E%3Ctext x='100' y='100' text-anchor='middle' dy='0.3em' fill='%23666' font-size='14'%3E加载中...%3C/text%3E%3C/svg%3E\" onerror=\"handleQrCodeError(this)\">\n" +
            "            <div class=\"qr-tips\">\n" +
            "                1. 使用微信扫描二维码<br>\n" +
            "                2. 在手机上打开此页面<br>\n" +
            "                3. 点击分享按钮即可发布到小红书\n" +
            "            </div>\n" +
            "            <button class=\"close-btn\" onclick=\"closeQrModal()\">关闭</button>\n" +
            "        </div>\n" +
            "    </div>\n" +
            "    <script src=\"https://fe-static.xhscdn.com/biz-static/goten/xhs-1.0.1.js\"></script>\n" +
            "    <script>\n" +
            "        // 设备检测函数\n" +
            "        function isMobileDevice() {\n" +
            "            var userAgent = navigator.userAgent.toLowerCase();\n" +
            "            var mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];\n" +
            "            return mobileKeywords.some(function(keyword) { return userAgent.indexOf(keyword) !== -1; }) ||\n" +
            "                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||\n" +
            "                   window.innerWidth <= 768;\n" +
            "        }\n" +
            "        \n" +
            "        function isAndroidWechat() {\n" +
            "            var userAgent = navigator.userAgent.toLowerCase();\n" +
            "            return userAgent.indexOf('android') !== -1 && userAgent.indexOf('micromessenger') !== -1;\n" +
            "        }\n" +
            "        \n" +
            "        // 显示消息函数\n" +
            "        function showMessage(message, type) {\n" +
            "            if (typeof type === 'undefined') type = 'info';\n" +
            "            var messageDiv = document.createElement('div');\n" +
            "            var bgColor = '#5352ed';\n" +
            "            if (type === 'error') bgColor = '#ff4757';\n" +
            "            else if (type === 'success') bgColor = '#2ed573';\n" +
            "            messageDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000; padding: 15px 20px; border-radius: 8px; color: white; font-size: 14px; max-width: 300px; word-wrap: break-word; background: ' + bgColor + '; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';\n" +
            "            messageDiv.textContent = message;\n" +
            "            document.body.appendChild(messageDiv);\n" +
            "            setTimeout(function() {\n" +
            "                if (messageDiv.parentNode) {\n" +
            "                    document.body.removeChild(messageDiv);\n" +
            "                }\n" +
            "            }, 3000);\n" +
            "        }\n" +
            "        \n" +
            "        // 主分享函数\n" +
            "        async function publishToXiaohongshu() {\n" +
            "            console.log('🚀 开始发布到小红书...');\n" +
            "            \n" +
            "            if (isShared) {\n" +
            "                showMessage('此内容已经分享过了', 'error');\n" +
            "                return;\n" +
            "            }\n" +
            "            \n" +
            "            // 设备检测和分流处理\n" +
            "            if (isMobileDevice()) {\n" +
            "                if (isAndroidWechat()) {\n" +
            "                    console.log('🤖 检测到安卓微信环境，显示浏览器切换提示');\n" +
            "                    showAndroidWechatTip();\n" +
            "                } else {\n" +
            "                    console.log('📱 检测到移动设备，使用官方SDK直接分享');\n" +
            "                    var shareTitle = shareConfig.title || '来自COZE插件的分享';\n" +
            "                    var shareContent = shareConfig.content || '精彩内容分享';\n" +
            "                    var images = shareConfig.images || [];\n" +
            "                    await shareWithOfficialSDK(shareTitle, shareContent, images);\n" +
            "                }\n" +
            "            } else {\n" +
            "                console.log('💻 检测到桌面设备，显示二维码引导扫码');\n" +
            "                showQrModal();\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        // 移动端分享函数\n" +
            "        async function shareWithOfficialSDK(title, content, images) {\n" +
            "            console.log('📱 开始移动端分享:', title, content, images);\n" +
            "            \n" +
            "            // 检查小红书SDK是否加载\n" +
            "            if (typeof xhs === 'undefined') {\n" +
            "                console.error('❌ 小红书JS SDK未加载');\n" +
            "                showMessage('小红书SDK加载失败，请刷新页面重试', 'error');\n" +
            "                return;\n" +
            "            }\n" +
            "            \n" +
            "            showMessage('正在准备分享...', 'info');\n" +
            "            \n" +
            "            // 这里先简化，不使用复杂的fetch逻辑\n" +
            "            // 测试fetch功能\n" +
            "            try {\n" +
            "                const response = await fetch('/jeecg-boot/api/aigc/xiaohongshu/get-fresh-signature', {\n" +
            "                    method: 'POST',\n" +
            "                    headers: { 'Content-Type': 'application/json' },\n" +
            "                    body: JSON.stringify({ pageId: shareConfig.pageId })\n" +
            "                });\n" +
            "                \n" +
            "                console.log('签名请求响应:', response.status);\n" +
            "                \n" +
            "                if (response.ok) {\n" +
            "                    const data = await response.json();\n" +
            "                    console.log('签名数据:', data);\n" +
            "                    showMessage('签名获取成功！', 'success');\n" +
            "                } else {\n" +
            "                    showMessage('签名获取失败: HTTP ' + response.status, 'error');\n" +
            "                }\n" +
            "                \n" +
            "            } catch (error) {\n" +
            "                console.error('获取签名失败:', error);\n" +
            "                showMessage('获取签名失败: ' + error.message, 'error');\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        // 安卓微信环境提示\n" +
            "        function showAndroidWechatTip() {\n" +
            "            var tipModal = document.createElement('div');\n" +
            "            tipModal.id = 'androidWechatTip';\n" +
            "            tipModal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;';\n" +
            "            tipModal.innerHTML = '<div style=\"background: white; border-radius: 16px; padding: 24px; margin: 20px; max-width: 320px; text-align: center;\"><h3>检测到微信环境</h3><p>请点击右上角菜单，选择「在浏览器中打开」</p><button onclick=\"closeAndroidWechatTip()\">我知道了</button></div>';\n" +
            "            document.body.appendChild(tipModal);\n" +
            "        }\n" +
            "        \n" +
            "        function closeAndroidWechatTip() {\n" +
            "            var tipModal = document.getElementById('androidWechatTip');\n" +
            "            if (tipModal) tipModal.remove();\n" +
            "        }\n" +
            "        \n" +
            "        // 显示二维码模态框\n" +
            "        function showQrModal() {\n" +
            "            console.log('显示二维码模态框');\n" +
            "            showMessage('请用手机扫描二维码分享', 'info');\n" +
            "        }\n" +
            "        \n" +
            "        // 页面初始化变量\n" +
            "        let isShared = {{isShared}};\n" +
            "        const pageId = '{{pageId}}';\n" +
            "        \n" +
            "        // 分享配置 - 从服务端传递\n" +
            "        const shareConfig = {\n" +
            "            type: 'normal',\n" +
            "            pageId: pageId,\n" +
            "            title: '{{titleJs}}',\n" +
            "            content: '{{contentJs}}',\n" +
            "            images: {{imagesJs}}\n" +
            "        };\n" +
            "        \n" +

            "        \n" +

            "        \n" +
            "        // 全局错误处理\n" +
            "        window.addEventListener('error', function(event) {\n" +
            "            console.error('JavaScript错误:', event.error ? event.error.message : event.message);\n" +
            "        });\n" +
            "        \n" +
            "        function isMobile() {\n" +
            "            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|Tablet|Touch/i.test(navigator.userAgent) || \n" +
            "                   (window.innerWidth <= 768) || \n" +
            "                   ('ontouchstart' in window);\n" +
            "        }\n" +
            "        function isMobileDevice() {\n" +
            "            var userAgent = navigator.userAgent.toLowerCase();\n" +
            "            var mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];\n" +
            "            return mobileKeywords.some(function(keyword) { return userAgent.indexOf(keyword) !== -1; }) ||\n" +
            "                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||\n" +
            "                   window.innerWidth <= 768;\n" +
            "        }\n" +
            "        function isAndroidWechat() {\n" +
            "            var userAgent = navigator.userAgent.toLowerCase();\n" +
            "            return userAgent.indexOf('android') !== -1 && userAgent.indexOf('micromessenger') !== -1;\n" +
            "        }\n" +
            "        function handleQrCodeError(img) {\n" +
            "            img.src = 'data:image/svg+xml,%3Csvg xmlns=\"http://www.w3.org/2000/svg\" width=\"200\" height=\"200\"%3E%3Crect width=\"200\" height=\"200\" fill=\"%23f8f9fa\" stroke=\"%23ddd\"/%3E%3Ctext x=\"100\" y=\"90\" text-anchor=\"middle\" fill=\"%23999\" font-size=\"12\"%3E二维码加载失败%3C/text%3E%3Ctext x=\"100\" y=\"110\" text-anchor=\"middle\" fill=\"%23666\" font-size=\"10\"%3E点击重试%3C/text%3E%3C/svg%3E';\n" +
            "            img.style.cursor = 'pointer';\n" +
            "            img.onclick = function() { retryLoadQrCode(); };\n" +
            "        }\n" +
            "        function retryLoadQrCode() {\n" +
            "            const qrCodeImg = document.getElementById('qrCodeImg');\n" +
            "            qrCodeImg.style.cursor = 'default';\n" +
            "            qrCodeImg.onclick = null;\n" +
            "            qrCodeImg.src = '{{qrCodeUrl}}?retry=' + Date.now();\n" +
            "        }\n" +
            "        function initPageState() {\n" +
            "            const shareBtn = document.getElementById('shareBtn');\n" +
            "            const shareBtnText = document.getElementById('shareBtnText');\n" +
            "            const mobileTip = document.getElementById('mobileTip');\n" +
            "            if (isShared) {\n" +
            "                shareBtn.className = 'share-btn used';\n" +
            "                shareBtn.disabled = true;\n" +
            "                shareBtnText.textContent = '✅ 已分享到小红书';\n" +
            "                mobileTip.textContent = '此内容已经分享过了';\n" +
            "            }\n" +
            "        }\n" +
            "        function showMessage(message, type) {\n" +
            "            if (typeof type === 'undefined') type = 'info';\n" +
            "            var messageDiv = document.createElement('div');\n" +
            "            var bgColor = '#5352ed';\n" +
            "            if (type === 'error') bgColor = '#ff4757';\n" +
            "            else if (type === 'success') bgColor = '#2ed573';\n" +
            "            messageDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000; padding: 15px 20px; border-radius: 8px; color: white; font-size: 14px; max-width: 300px; word-wrap: break-word; background: ' + bgColor + '; box-shadow: 0 4px 12px rgba(0,0,0,0.15); animation: slideIn 0.3s ease;';\n" +
            "            messageDiv.textContent = message;\n" +
            "            document.body.appendChild(messageDiv);\n" +
            "            setTimeout(function() {\n" +
            "                messageDiv.style.animation = 'slideOut 0.3s ease';\n" +
            "                setTimeout(function() { document.body.removeChild(messageDiv); }, 300);\n" +
            "            }, 3000);\n" +
            "        }\n" +

            "        function showQrModal() {\n" +
            "            const modal = document.getElementById('qrModal');\n" +
            "            const qrCodeImg = document.getElementById('qrCodeImg');\n" +
            "            const closeBtn = modal.querySelector('.close-btn');\n" +
            "            \n" +
            "            // 生成包含移动端参数的二维码\n" +
            "            const baseUrl = window.location.href.split('?')[0];\n" +
            "            const mobileUrl = baseUrl + '?mobile=1&auto_share=1';\n" +
            "            const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(mobileUrl);\n" +
            "            qrCodeImg.src = qrUrl;\n" +
            "            qrCodeImg.onload = function() {\n" +
            "                qrCodeImg.classList.remove('loading');\n" +
            "            };\n" +
            "            \n" +
            "            modal.style.display = 'block';\n" +
            "            modal.setAttribute('aria-hidden', 'false');\n" +
            "            \n" +
            "            // 焦点管理\n" +
            "            setTimeout(function() { closeBtn.focus(); }, 100);\n" +
            "            \n" +
            "            modal.onclick = function(e) { \n" +
            "                if (e.target === modal) closeQrModal(); \n" +
            "            };\n" +
            "        }\n" +
            "        function closeQrModal() {\n" +
            "            const modal = document.getElementById('qrModal');\n" +
            "            modal.style.display = 'none';\n" +
            "            modal.setAttribute('aria-hidden', 'true');\n" +
            "            \n" +
            "            // 恢复焦点到分享按钮\n" +
            "            const shareBtn = document.getElementById('shareBtn');\n" +
            "            if (shareBtn) shareBtn.focus();\n" +
            "        }\n" +
            "        \n" +
            "        // 轮播图功能（按照测试页面的逻辑）\n" +
            "        let currentImageIndex = 0;\n" +
            "        let totalImages = 0;\n" +
            "        let touchStartX = 0;\n" +
            "        let touchEndX = 0;\n" +
            "        let touchStartY = 0;\n" +
            "        let touchEndY = 0;\n" +
            "        let isDragging = false;\n" +
            "        \n" +
            "        function updateCarousel() {\n" +
            "            const slides = document.getElementById('carouselSlides');\n" +
            "            const dots = document.querySelectorAll('.carousel-dot');\n" +
            "            \n" +
            "            if (slides) {\n" +
            "                slides.style.transform = 'translateX(-' + (currentImageIndex * 100) + '%)';\n" +
            "            }\n" +
            "            \n" +
            "            dots.forEach(function(dot, index) {\n" +
            "                dot.classList.toggle('active', index === currentImageIndex);\n" +
            "            });\n" +
            "            \n" +
            "            console.log('切换到图片 ' + (currentImageIndex + 1));\n" +
            "        }\n" +
            "        \n" +
            "        function prevImage() {\n" +
            "            currentImageIndex = (currentImageIndex - 1 + totalImages) % totalImages;\n" +
            "            updateCarousel();\n" +
            "        }\n" +
            "        \n" +
            "        function nextImage() {\n" +
            "            currentImageIndex = (currentImageIndex + 1) % totalImages;\n" +
            "            updateCarousel();\n" +
            "        }\n" +
            "        \n" +
            "        function goToImage(index) {\n" +
            "            currentImageIndex = index;\n" +
            "            updateCarousel();\n" +
            "        }\n" +
            "        \n" +
            "        function handleTouchStart(e) {\n" +
            "            touchStartX = e.touches[0].clientX;\n" +
            "            touchStartY = e.touches[0].clientY;\n" +
            "            isDragging = false;\n" +
            "            console.log('触摸开始: X=' + Math.round(touchStartX) + ', Y=' + Math.round(touchStartY));\n" +
            "        }\n" +
            "        \n" +
            "        function handleTouchMove(e) {\n" +
            "            if (!touchStartX) return;\n" +
            "            \n" +
            "            const touchCurrentX = e.touches[0].clientX;\n" +
            "            const touchCurrentY = e.touches[0].clientY;\n" +
            "            \n" +
            "            const deltaX = Math.abs(touchCurrentX - touchStartX);\n" +
            "            const deltaY = Math.abs(touchCurrentY - touchStartY);\n" +
            "            \n" +
            "            if (deltaX > deltaY && deltaX > 10) {\n" +
            "                e.preventDefault();\n" +
            "                isDragging = true;\n" +
            "                console.log('水平滑动中: ΔX=' + Math.round(deltaX) + ', ΔY=' + Math.round(deltaY));\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        function handleTouchEnd(e) {\n" +
            "            if (!touchStartX || !isDragging) {\n" +
            "                console.log('触摸结束: 未检测到有效滑动');\n" +
            "                return;\n" +
            "            }\n" +
            "            \n" +
            "            touchEndX = e.changedTouches[0].clientX;\n" +
            "            touchEndY = e.changedTouches[0].clientY;\n" +
            "            \n" +
            "            const deltaX = touchEndX - touchStartX;\n" +
            "            const deltaY = Math.abs(touchEndY - touchStartY);\n" +
            "            \n" +
            "            console.log('触摸结束: ΔX=' + Math.round(deltaX) + ', ΔY=' + Math.round(deltaY));\n" +
            "            \n" +
            "            if (Math.abs(deltaX) > 50 && deltaY < 100) {\n" +
            "                if (deltaX > 0) {\n" +
            "                    console.log('👈 向右滑动，显示上一张图片');\n" +
            "                    prevImage();\n" +
            "                } else {\n" +
            "                    console.log('👉 向左滑动，显示下一张图片');\n" +
            "                    nextImage();\n" +
            "                }\n" +
            "            } else {\n" +
            "                console.log('滑动距离不足，未切换图片');\n" +
            "            }\n" +
            "            \n" +
            "            touchStartX = 0;\n" +
            "            touchEndX = 0;\n" +
            "            touchStartY = 0;\n" +
            "            touchEndY = 0;\n" +
            "            isDragging = false;\n" +
            "        }\n" +
            "        \n" +
            "        // 初始化轮播功能\n" +
            "        function initCarousel() {\n" +
            "            const imageContainer = document.getElementById('imagesCarousel');\n" +
            "            const slides = document.querySelectorAll('.image-slide');\n" +
            "            \n" +
            "            totalImages = slides.length;\n" +
            "            console.log('初始化轮播，图片数量: ' + totalImages);\n" +
            "            \n" +
            "            if (imageContainer && totalImages > 0) {\n" +
            "                imageContainer.addEventListener('touchstart', handleTouchStart, { passive: false });\n" +
            "                imageContainer.addEventListener('touchmove', handleTouchMove, { passive: false });\n" +
            "                imageContainer.addEventListener('touchend', handleTouchEnd, { passive: false });\n" +
            "                \n" +
            "                console.log('✅ 触摸滑动功能已启用');\n" +
            "                updateCarousel();\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        // 页面加载完成后初始化轮播\n" +
            "        if (document.readyState === 'loading') {\n" +
            "            document.addEventListener('DOMContentLoaded', initCarousel);\n" +
            "        } else {\n" +
            "            initCarousel();\n" +
            "        }\n" +
            "        \n" +
            "        /*\n" +
            "        function publishToXiaohongshu() {\n" +
            "            console.log('🚀 publishToXiaohongshu函数被调用了！');\n" +
            "            alert('publishToXiaohongshu函数正常工作！');\n" +
            "            \n" +
            "            if (isShared) {\n" +
            "                showMessage('此内容已经分享过了', 'error');\n" +
            "                return;\n" +
            "            }\n" +
            "            \n" +
            "            console.log('🚀 开始发布到小红书...');\n" +
            "            \n" +
            "            // 设备检测和分流处理\n" +
            "            if (isMobileDevice()) {\n" +
            "                if (isAndroidWechat()) {\n" +
            "                    console.log('🤖 检测到安卓微信环境，显示浏览器切换提示');\n" +
            "                    showAndroidWechatTip();\n" +
            "                } else {\n" +
            "                    console.log('📱 检测到移动设备，使用官方SDK直接分享');\n" +
            "                    const shareTitle = shareConfig.title || '来自COZE插件的分享';\n" +
            "                    const shareContent = shareConfig.content || '精彩内容分享';\n" +
            "                    const images = shareConfig.images || [];\n" +
            "                    shareWithOfficialSDK(shareTitle, shareContent, images);\n" +
            "                }\n" +
            "            } else {\n" +
            "                console.log('💻 检测到桌面设备，显示二维码引导扫码');\n" +
            "                showQrModal();\n" +
            "            }\n" +
            "        }\n" +
            "        */\n" +
            "        \n" +
            "        /*\n" +
            "        // 移动端：使用官方SDK分享\n" +
            "        async function shareWithOfficialSDK(title, content, images) {\n" +
            "            try {\n" +
            "                if (typeof xhs === 'undefined') {\n" +
            "                    console.error('❌ 小红书JS SDK未加载');\n" +
            "                    alert('小红书SDK加载失败，请刷新页面重试');\n" +
            "                    return;\n" +
            "                }\n" +
            "            \n" +
            "            // 获取服务端生成的签名\n" +
            "            console.log('🔐 正在获取签名...');\n" +
            "            showMessage('正在获取分享签名...', 'info');\n" +
            "            var freshSignature;\n" +
            "            try {\n" +
            "                var signatureResponse = await fetch('/jeecg-boot/api/aigc/xiaohongshu/get-fresh-signature', {\n" +
            "                    method: 'POST',\n" +
            "                    headers: { 'Content-Type': 'application/json' },\n" +
            "                    body: JSON.stringify({\n" +
            "                        pageId: shareConfig.pageId\n" +
            "                    })\n" +
            "                });\n" +
            "                \n" +
            "                if (!signatureResponse.ok) {\n" +
            "                    if (signatureResponse.status === 403) {\n" +
            "                        console.log('🛡️ 检测到403错误，页面已失效');\n" +
            "                        showExpiredPage('page_expired', '页面已失效，无法重复使用');\n" +
            "                        return;\n" +
            "                    }\n" +
            "                    throw new Error('获取签名失败: HTTP ' + signatureResponse.status);\n" +
            "                }\n" +
            "                \n" +
            "                var signatureData = await signatureResponse.json();\n" +
            "                if (!signatureData.success) {\n" +
            "                    throw new Error(signatureData.message || '签名获取失败');\n" +
            "                }\n" +
            "                \n" +
            "                console.log('✅ 签名获取成功');\n" +
            "                \n" +
            "                // 调用小红书官方JS SDK\n" +
            "                console.log('📱 调用小红书官方SDK...');\n" +
            "                showMessage('正在分享到小红书...', 'info');\n" +
            "                var shareInfo = {\n" +
            "                    type: 'normal',\n" +
            "                    title: title,\n" +
            "                    content: content,\n" +
            "                    images: images\n" +
            "                };\n" +
            "                \n" +
            "                xhs.share({\n" +
            "                    shareInfo: shareInfo,\n" +
            "                    verifyConfig: signatureData.result,\n" +
            "                    success: function(result) {\n" +
            "                        console.log('✅ 小红书分享成功');\n" +
            "                        alert('分享成功！');\n" +
            "                        markAsShared();\n" +
            "                    },\n" +
            "                    fail: function(error) {\n" +
            "                        console.error('❌ 小红书分享失败:', error);\n" +
            "                        alert('分享失败: ' + (error.message || '未知错误'));\n" +
            "                    }\n" +
            "                });\n" +
            "            } catch (error) {\n" +
            "                console.error('❌ 发布过程出错:', error);\n" +
            "                showMessage('发布失败: ' + error.message, 'error');\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        // 显示安卓微信环境提示\n" +
            "        function showAndroidWechatTip() {\n" +
            "            const tipModal = document.createElement('div');\n" +
            "            tipModal.id = 'androidWechatTip';\n" +
            "            tipModal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); display: flex; align-items: center; justify-content: center; z-index: 10000; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif;';\n" +
            "            \n" +
            "            tipModal.innerHTML = '<div style=\"background: white; border-radius: 16px; padding: 24px; margin: 20px; max-width: 320px; text-align: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\"><div style=\"font-size: 48px; margin-bottom: 16px;\">📱</div><h3 style=\"margin: 0 0 16px 0; color: #333; font-size: 18px; font-weight: 600;\">检测到微信环境</h3><p style=\"margin: 0 0 20px 0; color: #666; font-size: 14px; line-height: 1.5;\">为了正常使用小红书分享功能，请点击右上角 <strong>···</strong> 菜单，选择<strong>「在浏览器中打开」</strong></p><button onclick=\"closeAndroidWechatTip()\" style=\"background: #ff2442; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 600; cursor: pointer; width: 100%;\">我知道了</button></div>';\n" +
            "            \n" +
            "            document.body.appendChild(tipModal);\n" +
            "        }\n" +
            "        \n" +
            "        // 关闭安卓微信环境提示\n" +
            "        function closeAndroidWechatTip() {\n" +
            "            const tipModal = document.getElementById('androidWechatTip');\n" +
            "            if (tipModal) {\n" +
            "                tipModal.remove();\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        // 显示页面失效提示\n" +
            "        function showExpiredPage(reason, message) {\n" +
            "            const container = document.querySelector('.container');\n" +
            "            if (container) {\n" +
            "                container.style.display = 'none';\n" +
            "            }\n" +
            "            \n" +
            "            const expiredDiv = document.createElement('div');\n" +
            "            expiredDiv.innerHTML = '<div style=\"position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\"><div style=\"text-align: center; padding: 40px 20px; max-width: 400px; background: white; border-radius: 16px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);\"><div style=\"font-size: 64px; margin-bottom: 20px;\">🔒</div><h2 style=\"margin: 0 0 16px 0; color: #333; font-size: 20px; font-weight: 600;\">此分享页面已失效</h2><p style=\"margin: 0 0 20px 0; color: #666; font-size: 15px; line-height: 1.5;\">为了保护内容质量，每个分享页面只能使用一次</p><p style=\"margin: 0 0 24px 0; color: #999; font-size: 13px;\">如需重新分享，请返回COZE重新生成</p><button onclick=\"window.close()\" style=\"background: #ff2442; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 600; cursor: pointer; width: 100%;\">关闭页面</button></div></div>';\n" +
            "            \n" +
            "            document.body.appendChild(expiredDiv);\n" +
            "        }\n" +
            "        \n" +
            "        function markAsShared() {\n" +
            "            isShared = true;\n" +
            "            const shareBtn = document.getElementById('shareBtn');\n" +
            "            const shareBtnText = document.getElementById('shareBtnText');\n" +
            "            const mobileTip = document.getElementById('mobileTip');\n" +
            "            shareBtn.className = 'share-btn used';\n" +
            "            shareBtn.disabled = true;\n" +
            "            shareBtnText.textContent = '✅ 已分享到小红书';\n" +
            "            mobileTip.textContent = '此内容已经分享过了';\n" +
            "            fetch('/jeecg-boot/api/aigc/coze/xiaohongshu/update-share-status', {\n" +
            "                method: 'POST',\n" +
            "                headers: { 'Content-Type': 'application/json' },\n" +
            "                body: JSON.stringify({\n" +
            "                    pageId: pageId,\n" +
            "                    platform: 'xiaohongshu',\n" +
            "                    timestamp: Date.now()\n" +
            "                })\n" +
            "            }).catch(function(err) { console.error('更新分享状态失败:', err); });\n" +
            "        }\n" +
            "        \n" +
            "        document.addEventListener('DOMContentLoaded', function() {\n" +
            "            initPageState();\n" +
            "            \n" +
            "            // 检查是否是移动端自动分享\n" +
            "            const urlParams = new URLSearchParams(window.location.search);\n" +
            "            if (urlParams.get('mobile') === '1' && urlParams.get('auto_share') === '1') {\n" +
            "                console.log('📱 检测到移动端自动分享参数，延迟1秒后自动分享');\n" +
            "                setTimeout(function() {\n" +
            "                    if (!isShared) {\n" +
            "                        publishToXiaohongshu();\n" +
            "                    }\n" +
            "                }, 1000);\n" +
            "            }\n" +
            "            document.addEventListener('keydown', function(e) {\n" +
            "                if (e.key === 'Escape') closeQrModal();\n" +
            "            });\n" +
            "        });\n" +
            "        \n" +
            "        */\n" +
            "    </script>\n" +
            "</body>\n" +
            "</html>";
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "127.0.0.1"; // 默认本地IP
        }

        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip != null ? ip : "127.0.0.1";
    }

    /**
     * 🔥 转存外部图片到我们的服务器（增强版 - 并发+重试+验证）
     */
    private List<String> convertExternalImagesToLocal(List<String> externalImages) {
        if (externalImages == null || externalImages.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> localImages = Collections.synchronizedList(new ArrayList<>());
        String requestBaseUrl = this.baseUrl;

        log.info("🚀 开始转存外部图片，共{}张", externalImages.size());

        // 🔥 使用线程池并发下载，限制并发数为3避免资源竞争
        ExecutorService executor = Executors.newFixedThreadPool(3);
        List<Future<ImageTransferResult>> futures = new ArrayList<>();

        for (int i = 0; i < externalImages.size(); i++) {
            final int index = i;
            final String externalUrl = externalImages.get(i);

            if (oConvertUtils.isEmpty(externalUrl)) {
                continue;
            }

            // 检查是否已经是我们的域名
            if (externalUrl.contains(requestBaseUrl) || externalUrl.startsWith("https://aigcview.com")) {
                localImages.add(externalUrl);
                log.info("✅ 图片[{}]已是本地域名: {}", index, externalUrl);
                continue;
            }

            Future<ImageTransferResult> future = executor.submit(() -> {
                return downloadImageWithEnhancedRetry(externalUrl, index, requestBaseUrl);
            });
            futures.add(future);
        }

        // 🔥 收集结果，设置合理超时时间
        for (int i = 0; i < futures.size(); i++) {
            try {
                ImageTransferResult result = futures.get(i).get(45, TimeUnit.SECONDS); // 45秒超时
                if (result.isSuccess()) {
                    localImages.add(result.getLocalUrl());
                    log.info("✅ 图片[{}]转存成功: {} -> {}", result.getIndex(), result.getOriginalUrl(), result.getLocalUrl());
                } else {
                    log.warn("❌ 图片[{}]转存失败: {}, 原因: {}", result.getIndex(), result.getOriginalUrl(), result.getErrorMessage());
                }
            } catch (TimeoutException e) {
                log.error("❌ 图片下载超时(45秒)");
            } catch (Exception e) {
                log.error("❌ 图片处理异常: {}", e.getMessage());
            }
        }

        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
        }

        log.info("🎯 图片转存完成，原始数量: {}, 转存成功: {}", externalImages.size(), localImages.size());

        // 🚨 如果成功率过低，给出警告
        double successRate = externalImages.isEmpty() ? 0 : (double) localImages.size() / externalImages.size();
        if (successRate < 0.8) {
            log.warn("⚠️ 警告：图片转存成功率较低({:.1f}%)，可能影响分享效果", successRate * 100);
        }

        return localImages;
    }

    /**
     * 🔥 使用OkHttp强力下载图片
     */
    private byte[] downloadImageWithMultipleMethods(String imageUrl) {
        log.info("🚀 开始OkHttp强力下载: {}", imageUrl);

        // 方法1：OkHttp标准浏览器模式
        byte[] result1 = downloadWithOkHttp(imageUrl, "browser");
        if (result1 != null) {
            log.info("✅ OkHttp浏览器模式成功");
            return result1;
        }

        // 方法2：OkHttp移动端模式
        byte[] result2 = downloadWithOkHttp(imageUrl, "mobile");
        if (result2 != null) {
            log.info("✅ OkHttp移动端模式成功");
            return result2;
        }

        // 方法3：OkHttp爬虫模式
        byte[] result3 = downloadWithOkHttp(imageUrl, "crawler");
        if (result3 != null) {
            log.info("✅ OkHttp爬虫模式成功");
            return result3;
        }

        // 方法4：OkHttp简化模式
        byte[] result4 = downloadWithOkHttp(imageUrl, "simple");
        if (result4 != null) {
            log.info("✅ OkHttp简化模式成功");
            return result4;
        }

        // 备选方案：RestTemplate方法
        log.info("🔄 OkHttp失败，尝试RestTemplate备选方案");
        byte[] result5 = tryDownloadWithRestTemplate(imageUrl);
        if (result5 != null) {
            log.info("✅ RestTemplate备选方案成功");
            return result5;
        }

        // 最后方案：Java原生HTTP
        byte[] result6 = tryDownloadWithNativeHttp(imageUrl);
        if (result6 != null) {
            log.info("✅ Java原生HTTP成功");
            return result6;
        }

        log.warn("🚨 所有下载方法都失败了: {}", imageUrl);
        return null;
    }

    /**
     * 🚀 使用OkHttp下载图片
     */
    private byte[] downloadWithOkHttp(String imageUrl, String mode) {
        try {
            // 创建OkHttp客户端
            okhttp3.OkHttpClient.Builder clientBuilder = new okhttp3.OkHttpClient.Builder()
                .connectTimeout(15, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)  // 🔥 自动重试
                .followRedirects(true)           // 🔥 自动跟随重定向
                .followSslRedirects(true);

            okhttp3.OkHttpClient client = clientBuilder.build();

            // 根据模式创建请求
            okhttp3.Request.Builder requestBuilder = new okhttp3.Request.Builder().url(imageUrl);

            switch (mode) {
                case "browser":
                    requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                                 .addHeader("Accept", "image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8")
                                 .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                                 .addHeader("Cache-Control", "no-cache")
                                 .addHeader("Referer", "https://www.coze.cn/");
                    break;
                case "mobile":
                    requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1")
                                 .addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                                 .addHeader("Referer", "https://www.coze.cn/");
                    break;
                case "crawler":
                    requestBuilder.addHeader("User-Agent", "Googlebot/2.1 (+http://www.google.com/bot.html)")
                                 .addHeader("Accept", "*/*");
                    break;
                case "simple":
                    requestBuilder.addHeader("User-Agent", "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1)");
                    break;
            }

            okhttp3.Request request = requestBuilder.build();

            // 执行请求
            try (okhttp3.Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    byte[] data = response.body().bytes();
                    log.info("✅ OkHttp下载成功 [{}模式]，大小: {} bytes", mode, data.length);
                    return data;
                } else {
                    log.debug("❌ OkHttp响应失败 [{}模式]，状态码: {}", mode, response.code());
                }
            }

        } catch (Exception e) {
            log.debug("❌ OkHttp下载失败 [{}模式]: {}", mode, e.getMessage());
        }
        return null;
    }

    /**
     * 创建浏览器请求头（备用）
     */
    private HttpHeaders createBrowserHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        headers.set("Accept", "image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        headers.set("Cache-Control", "no-cache");
        headers.set("Referer", "https://www.coze.cn/");
        return headers;
    }

    /**
     * 创建移动端请求头
     */
    private HttpHeaders createMobileHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1");
        headers.set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9");
        headers.set("Referer", "https://www.coze.cn/");
        return headers;
    }

    /**
     * 创建爬虫请求头
     */
    private HttpHeaders createCrawlerHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Googlebot/2.1 (+http://www.google.com/bot.html)");
        headers.set("Accept", "*/*");
        return headers;
    }

    /**
     * 创建简化请求头
     */
    private HttpHeaders createSimpleHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1)");
        return headers;
    }

    /**
     * 🔄 RestTemplate备选方案
     */
    private byte[] tryDownloadWithRestTemplate(String imageUrl) {
        log.info("🔄 尝试RestTemplate备选方案");

        // 尝试多种请求头
        HttpHeaders[] headersList = {
            createBrowserHeaders(),
            createMobileHeaders(),
            createCrawlerHeaders(),
            createSimpleHeaders(),
            new HttpHeaders()
        };

        for (int i = 0; i < headersList.length; i++) {
            try {
                RestTemplate restTemplate = new RestTemplate();
                HttpEntity<String> entity = new HttpEntity<>(headersList[i]);

                ResponseEntity<byte[]> response = restTemplate.exchange(
                    imageUrl, org.springframework.http.HttpMethod.GET, entity, byte[].class);

                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    byte[] data = response.getBody();
                    log.info("✅ RestTemplate方案{}成功，大小: {} bytes", i+1, data.length);
                    return data;
                }
            } catch (Exception e) {
                log.debug("❌ RestTemplate方案{}失败: {}", i+1, e.getMessage());
            }
        }
        return null;
    }

    /**
     * 尝试用指定请求头下载（保留备用）
     */
    @Deprecated
    private byte[] tryDownloadWithHeaders(String imageUrl, HttpHeaders headers) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<byte[]> response = restTemplate.exchange(
                imageUrl, org.springframework.http.HttpMethod.GET, entity, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                byte[] data = response.getBody();
                log.info("✅ 下载成功，大小: {} bytes", data.length);
                return data;
            }
        } catch (Exception e) {
            log.debug("❌ 下载尝试失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 🔥 尝试修改URL参数下载
     */
    private byte[] tryDownloadWithModifiedUrl(String originalUrl) {
        try {
            // 移除签名参数，有时候签名过期会导致403
            String modifiedUrl = originalUrl;
            if (modifiedUrl.contains("?")) {
                String baseUrl = modifiedUrl.substring(0, modifiedUrl.indexOf("?"));
                // 只保留基本参数，移除签名相关参数
                modifiedUrl = baseUrl;
                log.info("🔧 尝试简化URL: {}", modifiedUrl);

                HttpHeaders headers = createBrowserHeaders();
                return tryDownloadWithHeaders(modifiedUrl, headers);
            }
        } catch (Exception e) {
            log.debug("❌ URL修改下载失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 🔥 使用Java原生HTTP连接下载
     */
    private byte[] tryDownloadWithNativeHttp(String imageUrl) {
        try {
            log.info("🔧 尝试Java原生HTTP下载");
            java.net.URL url = new java.net.URL(imageUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();

            // 设置请求头
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Accept", "image/*,*/*;q=0.8");
            connection.setRequestProperty("Referer", "https://www.coze.cn/");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                try (java.io.InputStream inputStream = connection.getInputStream();
                     java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream()) {

                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    byte[] data = outputStream.toByteArray();
                    log.info("✅ 原生HTTP下载成功，大小: {} bytes", data.length);
                    return data;
                }
            } else {
                log.debug("❌ 原生HTTP响应码: {}", responseCode);
            }
        } catch (Exception e) {
            log.debug("❌ 原生HTTP下载失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从URL获取图片扩展名
     */
    private String getImageExtensionFromUrl(String url) {
        if (url.contains(".jpg") || url.contains(".jpeg")) return "jpg";
        if (url.contains(".png")) return "png";
        if (url.contains(".gif")) return "gif";
        if (url.contains(".webp")) return "webp";
        return "jpg"; // 默认
    }

    /**
     * 获取图片扩展名（保留备用）
     */
    @Deprecated
    private String getImageExtension(String url, HttpHeaders headers) {
        // 先从Content-Type获取
        String contentType = headers.getFirst("Content-Type");
        if (contentType != null) {
            if (contentType.contains("jpeg") || contentType.contains("jpg")) return "jpg";
            if (contentType.contains("png")) return "png";
            if (contentType.contains("gif")) return "gif";
            if (contentType.contains("webp")) return "webp";
        }

        // 从URL获取
        if (url.contains(".jpg") || url.contains(".jpeg")) return "jpg";
        if (url.contains(".png")) return "png";
        if (url.contains(".gif")) return "gif";
        if (url.contains(".webp")) return "webp";

        return "jpg"; // 默认
    }

    /**
     * 🔥 保存图片到系统静态文件目录
     */
    private String saveImageToSystemPath(byte[] imageData, String fileName) throws IOException {
        // 🔥 保存到系统静态文件目录下的imgs子目录，确保能正常访问
        String uploadDir = "C:/aigcview/upload/";
        String imgsDir = uploadDir + "imgs/";

        File dir = new File(imgsDir);
        if (!dir.exists()) {
            dir.mkdirs();
            log.info("📁 创建图片目录: {}", imgsDir);
        }

        // 保存文件
        String filePath = imgsDir + fileName;
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath)) {
            fos.write(imageData);
        }

        log.info("💾 图片保存到: {}", filePath);

        // 返回相对路径（用于URL拼接）
        return "imgs/" + fileName;
    }

    /**
     * 保存图片到本地（废弃，使用saveImageToSystemPath）
     */
    @Deprecated
    private String saveImageToLocal(byte[] imageData, String fileName) throws IOException {
        String imagePath = System.getProperty("user.dir") + File.separator + "images" + File.separator;
        File imageDir = new File(imagePath);
        if (!imageDir.exists()) {
            imageDir.mkdirs();
        }

        String filePath = imagePath + fileName;
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath)) {
            fos.write(imageData);
        }

        return filePath;
    }


    /**
     * 🔥 增强版图片下载（智能重试+验证）
     */
    private ImageTransferResult downloadImageWithEnhancedRetry(String imageUrl, int index, String requestBaseUrl) {
        for (int attempt = 1; attempt <= 3; attempt++) {
            try {
                log.info("🔄 图片[{}]第{}次尝试下载: {}", index, attempt, imageUrl);

                // 🔥 根据尝试次数调整策略
                byte[] imageData = null;
                switch (attempt) {
                    case 1:
                        // 第一次：快速模式
                        imageData = downloadWithOkHttp(imageUrl, "browser");
                        break;
                    case 2:
                        // 第二次：增加延迟，使用移动端模式
                        Thread.sleep(1000);
                        imageData = downloadWithOkHttp(imageUrl, "mobile");
                        break;
                    case 3:
                        // 第三次：最大兼容性模式
                        Thread.sleep(2000);
                        imageData = downloadImageWithMultipleMethods(imageUrl);
                        break;
                }

                if (imageData != null && imageData.length > 0) {
                    // 🔥 验证图片数据完整性
                    if (isValidImageData(imageData)) {
                        String fileName = generateImageFileName(imageUrl, index);
                        String relativePath = saveImageToSystemPath(imageData, fileName);
                        String localUrl = requestBaseUrl + "/jeecg-boot/sys/common/static/" + relativePath;
                        return ImageTransferResult.success(imageData, fileName, localUrl, imageUrl, index);
                    } else {
                        log.warn("⚠️ 图片[{}]数据不完整，尝试重新下载", index);
                    }
                }

            } catch (Exception e) {
                log.warn("❌ 图片[{}]第{}次尝试失败: {}", index, attempt, e.getMessage());
                if (attempt < 3) {
                    try {
                        // 指数退避策略
                        Thread.sleep(attempt * 1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 🔥 所有常规重试都失败后，尝试备用方案
        log.info("🔄 图片[{}]常规重试失败，启动备用方案: {}", index, imageUrl);
        String backupResult = tryBackupDownloadMethods(imageUrl, index, requestBaseUrl);
        if (backupResult != null) {
            return ImageTransferResult.success(null, null, backupResult, imageUrl, index);
        }

        return ImageTransferResult.failure("所有下载方案都失败了", imageUrl, index);
    }

    /**
     * 🔥 验证图片数据完整性
     */
    private boolean isValidImageData(byte[] imageData) {
        if (imageData == null || imageData.length < 100) {
            return false;
        }

        // 检查常见图片格式的文件头
        String hex = bytesToHex(Arrays.copyOf(imageData, Math.min(20, imageData.length)));

        // JPEG: FF D8 FF
        if (hex.startsWith("FFD8FF")) return true;
        // PNG: 89 50 4E 47
        if (hex.startsWith("89504E47")) return true;
        // GIF: 47 49 46 38
        if (hex.startsWith("47494638")) return true;
        // WebP: 52 49 46 46 ... 57 45 42 50
        if (hex.startsWith("52494646") && hex.contains("57454250")) return true;

        log.warn("⚠️ 未识别的图片格式，文件头: {}", hex.substring(0, Math.min(16, hex.length())));
        return false;
    }

    /**
     * 🔥 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b));
        }
        return result.toString();
    }

    /**
     * 🔥 备用下载方案集合
     */
    private String tryBackupDownloadMethods(String originalUrl, int index, String requestBaseUrl) {
        // 备用方案1：尝试不同的User-Agent
        String[] backupUserAgents = {
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15",
            "Mozilla/5.0 (Android 10; Mobile; rv:81.0) Gecko/81.0 Firefox/81.0",
            "curl/7.68.0",
            "Wget/1.20.3 (linux-gnu)"
        };

        for (int i = 0; i < backupUserAgents.length; i++) {
            try {
                log.info("🔄 图片[{}]备用方案{}：自定义User-Agent", index, i+1);
                byte[] data = downloadWithCustomUserAgent(originalUrl, backupUserAgents[i]);
                if (data != null && isValidImageData(data)) {
                    String fileName = generateImageFileName(originalUrl, index);
                    String relativePath = saveImageToSystemPath(data, fileName);
                    String localUrl = requestBaseUrl + "/jeecg-boot/sys/common/static/" + relativePath;
                    log.info("✅ 图片[{}]备用方案{}成功", index, i+1);
                    return localUrl;
                }
            } catch (Exception e) {
                log.debug("❌ 图片[{}]备用方案{}失败: {}", index, i+1, e.getMessage());
            }
        }

        // 备用方案2：CDN域名替换
        if (originalUrl.contains("cdn") || originalUrl.contains("oss") || originalUrl.contains("cos")) {
            try {
                log.info("🔄 图片[{}]备用方案：CDN域名替换", index);
                String alternativeUrl = tryAlternativeCdnUrl(originalUrl);
                if (alternativeUrl != null && !alternativeUrl.equals(originalUrl)) {
                    byte[] data = downloadImageWithMultipleMethods(alternativeUrl);
                    if (data != null && isValidImageData(data)) {
                        String fileName = generateImageFileName(originalUrl, index);
                        String relativePath = saveImageToSystemPath(data, fileName);
                        String localUrl = requestBaseUrl + "/jeecg-boot/sys/common/static/" + relativePath;
                        log.info("✅ 图片[{}]CDN替换方案成功", index);
                        return localUrl;
                    }
                }
            } catch (Exception e) {
                log.debug("❌ 图片[{}]CDN替换方案失败: {}", index, e.getMessage());
            }
        }

        // 备用方案3：协议切换（HTTPS ↔ HTTP）
        try {
            log.info("🔄 图片[{}]备用方案：协议切换", index);
            String protocolSwitchedUrl = switchProtocol(originalUrl);
            if (!protocolSwitchedUrl.equals(originalUrl)) {
                byte[] data = downloadImageWithMultipleMethods(protocolSwitchedUrl);
                if (data != null && isValidImageData(data)) {
                    String fileName = generateImageFileName(originalUrl, index);
                    String relativePath = saveImageToSystemPath(data, fileName);
                    String localUrl = requestBaseUrl + "/jeecg-boot/sys/common/static/" + relativePath;
                    log.info("✅ 图片[{}]协议切换方案成功", index);
                    return localUrl;
                }
            }
        } catch (Exception e) {
            log.debug("❌ 图片[{}]协议切换方案失败: {}", index, e.getMessage());
        }

        // 备用方案4：URL解码重试
        try {
            log.info("🔄 图片[{}]备用方案：URL解码重试", index);
            String decodedUrl = java.net.URLDecoder.decode(originalUrl, "UTF-8");
            if (!decodedUrl.equals(originalUrl)) {
                byte[] data = downloadImageWithMultipleMethods(decodedUrl);
                if (data != null && isValidImageData(data)) {
                    String fileName = generateImageFileName(originalUrl, index);
                    String relativePath = saveImageToSystemPath(data, fileName);
                    String localUrl = requestBaseUrl + "/jeecg-boot/sys/common/static/" + relativePath;
                    log.info("✅ 图片[{}]URL解码方案成功", index);
                    return localUrl;
                }
            }
        } catch (Exception e) {
            log.debug("❌ 图片[{}]URL解码方案失败: {}", index, e.getMessage());
        }

        log.warn("🚨 图片[{}]所有备用方案都失败了: {}", index, originalUrl);
        return null;
    }

    /**
     * 🔥 使用自定义User-Agent下载
     */
    private byte[] downloadWithCustomUserAgent(String imageUrl, String userAgent) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", userAgent);
            headers.set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");
            headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            headers.set("Cache-Control", "no-cache");
            headers.set("Pragma", "no-cache");

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> response = restTemplate.exchange(
                imageUrl, org.springframework.http.HttpMethod.GET, entity, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            }
        } catch (Exception e) {
            log.debug("自定义User-Agent下载失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 🔥 尝试CDN域名替换
     */
    private String tryAlternativeCdnUrl(String originalUrl) {
        // 常见CDN域名替换规则
        String[][] cdnReplacements = {
            {"cdn.example.com", "img.example.com"},
            {"oss-cn-", "oss-accelerate-"},
            {"cos.ap-", "cos.accelerate."},
            {".cdn.", ".img."},
            {"-cdn-", "-img-"},
            {"static-cdn", "static-img"}
        };

        for (String[] replacement : cdnReplacements) {
            if (originalUrl.contains(replacement[0])) {
                return originalUrl.replace(replacement[0], replacement[1]);
            }
        }

        return originalUrl;
    }

    /**
     * 🔥 协议切换（HTTPS ↔ HTTP）
     */
    private String switchProtocol(String url) {
        if (url.startsWith("https://")) {
            return url.replace("https://", "http://");
        } else if (url.startsWith("http://")) {
            return url.replace("http://", "https://");
        }
        return url;
    }

    /**
     * 🔥 生成图片文件名
     */
    private String generateImageFileName(String imageUrl, int index) {
        String fileName = "xiaohongshu_img_" + System.currentTimeMillis() + "_" + index + "_" + generateRandomString(6);
        String fileExtension = getImageExtensionFromUrl(imageUrl);
        return fileName + "." + fileExtension;
    }

    /**
     * 🔥 图片转存结果类
     */
    private static class ImageTransferResult {
        private boolean success;
        private byte[] imageData;
        private String fileName;
        private String localUrl;
        private String originalUrl;
        private int index;
        private String errorMessage;

        private ImageTransferResult(boolean success, byte[] imageData, String fileName, String localUrl,
                                  String originalUrl, int index, String errorMessage) {
            this.success = success;
            this.imageData = imageData;
            this.fileName = fileName;
            this.localUrl = localUrl;
            this.originalUrl = originalUrl;
            this.index = index;
            this.errorMessage = errorMessage;
        }

        public static ImageTransferResult success(byte[] imageData, String fileName, String localUrl,
                                                String originalUrl, int index) {
            return new ImageTransferResult(true, imageData, fileName, localUrl, originalUrl, index, null);
        }

        public static ImageTransferResult failure(String errorMessage, String originalUrl, int index) {
            return new ImageTransferResult(false, null, null, null, originalUrl, index, errorMessage);
        }

        public boolean isSuccess() { return success; }
        public byte[] getImageData() { return imageData; }
        public String getFileName() { return fileName; }
        public String getLocalUrl() { return localUrl; }
        public String getOriginalUrl() { return originalUrl; }
        public int getIndex() { return index; }
        public String getErrorMessage() { return errorMessage; }
    }

}
