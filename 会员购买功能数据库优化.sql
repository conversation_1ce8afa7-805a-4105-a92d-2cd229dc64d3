-- =============================================
-- 智界Aigc 会员购买功能数据库优化脚本
-- 创建时间：2025-01-29
-- 版本：V1.0
-- 说明：完善会员购买功能的数据库支持
-- =============================================

-- ================================
-- 1. 检查现有数据库结构
-- ================================

-- 检查用户扩展表是否存在会员相关字段
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aicg_user_profile' 
  AND COLUMN_NAME IN ('member_level', 'member_expire_time');

-- 检查交易记录表是否支持会员订阅类型
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aicg_user_transaction' 
  AND COLUMN_NAME IN ('transaction_type', 'order_type');

-- 检查角色表中的会员角色
SELECT id, role_name, role_code, description 
FROM sys_role 
WHERE role_code IN ('user', 'VIP', 'SVIP', 'admin');

-- ================================
-- 2. 确保会员角色存在
-- ================================

-- 插入会员角色（如果不存在）
INSERT IGNORE INTO sys_role (id, role_name, role_code, description, create_time, create_by) VALUES
('user_role_id', '普通用户', 'user', '系统默认普通用户角色', NOW(), 'system'),
('vip_role_id', 'VIP会员', 'VIP', 'VIP会员角色，享受VIP权益', NOW(), 'system'),
('svip_role_id', 'SVIP会员', 'SVIP', 'SVIP会员角色，享受最高级权益', NOW(), 'system');

-- ================================
-- 3. 优化索引（可选）
-- ================================

-- 为会员相关查询添加索引
ALTER TABLE aicg_user_profile 
ADD INDEX idx_member_level_expire (member_level, member_expire_time),
ADD INDEX idx_member_expire_time (member_expire_time);

-- 为交易记录添加会员订单索引
ALTER TABLE aicg_user_transaction 
ADD INDEX idx_membership_orders (user_id, order_type, transaction_type, order_status) 
WHERE order_type = 'membership';

-- 为用户角色关系表添加索引
ALTER TABLE sys_user_role 
ADD INDEX idx_user_role_lookup (user_id, role_id);

-- ================================
-- 4. 会员等级与角色对应关系说明
-- ================================

/*
会员等级与角色对应关系：
- member_level = 1 → role_code = 'user'   (普通用户)
- member_level = 2 → role_code = 'VIP'    (VIP会员)
- member_level = 3 → role_code = 'SVIP'   (SVIP会员)

交易类型说明：
- transaction_type = 1: 消费
- transaction_type = 2: 充值
- transaction_type = 3: 退款
- transaction_type = 4: 兑换
- transaction_type = 5: 会员订阅

订单类型说明：
- order_type = 'recharge': 充值订单
- order_type = 'membership': 会员订阅订单
- order_type = 'plugin': 插件购买订单
*/

-- ================================
-- 5. 数据一致性检查脚本
-- ================================

-- 检查会员等级与角色不一致的用户
SELECT 
    u.id as user_id,
    u.username,
    p.member_level,
    p.member_expire_time,
    r.role_code as current_role,
    CASE p.member_level
        WHEN 1 THEN 'user'
        WHEN 2 THEN 'VIP'
        WHEN 3 THEN 'SVIP'
        ELSE 'unknown'
    END as expected_role
FROM sys_user u
LEFT JOIN aicg_user_profile p ON u.id = p.user_id
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
WHERE p.member_level IS NOT NULL 
  AND r.role_code NOT IN (
      CASE p.member_level
          WHEN 1 THEN 'user'
          WHEN 2 THEN 'VIP'
          WHEN 3 THEN 'SVIP'
          ELSE 'admin'
      END
  )
  AND r.role_code != 'admin'; -- 排除管理员角色

-- ================================
-- 6. 会员到期检查脚本
-- ================================

-- 查询即将到期的会员（7天内）
SELECT 
    u.username,
    p.member_level,
    p.member_expire_time,
    DATEDIFF(p.member_expire_time, NOW()) as days_remaining
FROM sys_user u
JOIN aicg_user_profile p ON u.id = p.user_id
WHERE p.member_level > 1 
  AND p.member_expire_time IS NOT NULL
  AND p.member_expire_time > NOW()
  AND DATEDIFF(p.member_expire_time, NOW()) <= 7
ORDER BY p.member_expire_time ASC;

-- 查询已过期的会员
SELECT 
    u.username,
    p.member_level,
    p.member_expire_time,
    DATEDIFF(NOW(), p.member_expire_time) as days_expired
FROM sys_user u
JOIN aicg_user_profile p ON u.id = p.user_id
WHERE p.member_level > 1 
  AND p.member_expire_time IS NOT NULL
  AND p.member_expire_time < NOW()
ORDER BY p.member_expire_time DESC;

-- ================================
-- 7. 会员统计查询
-- ================================

-- 会员等级分布统计
SELECT 
    CASE p.member_level
        WHEN 1 THEN '普通用户'
        WHEN 2 THEN 'VIP会员'
        WHEN 3 THEN 'SVIP会员'
        ELSE '未知等级'
    END as member_type,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM aicg_user_profile), 2) as percentage
FROM aicg_user_profile p
GROUP BY p.member_level
ORDER BY p.member_level;

-- 会员订阅订单统计
SELECT 
    DATE(create_time) as order_date,
    COUNT(*) as order_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount
FROM aicg_user_transaction
WHERE order_type = 'membership' 
  AND transaction_type = 5
  AND order_status = 3  -- 已完成
  AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY order_date DESC;

-- ================================
-- 8. 数据修复脚本（谨慎使用）
-- ================================

-- 修复会员等级与角色不一致的数据（请先备份数据）
/*
-- 第一步：删除不一致的角色关系
DELETE ur FROM sys_user_role ur
JOIN aicg_user_profile p ON ur.user_id = p.user_id
JOIN sys_role r ON ur.role_id = r.id
WHERE r.role_code IN ('user', 'VIP', 'SVIP')
  AND r.role_code != CASE p.member_level
      WHEN 1 THEN 'user'
      WHEN 2 THEN 'VIP'
      WHEN 3 THEN 'SVIP'
      ELSE r.role_code
  END;

-- 第二步：添加正确的角色关系
INSERT INTO sys_user_role (user_id, role_id)
SELECT DISTINCT p.user_id, r.id
FROM aicg_user_profile p
JOIN sys_role r ON r.role_code = CASE p.member_level
    WHEN 1 THEN 'user'
    WHEN 2 THEN 'VIP'
    WHEN 3 THEN 'SVIP'
    ELSE 'user'
END
WHERE NOT EXISTS (
    SELECT 1 FROM sys_user_role ur2 
    WHERE ur2.user_id = p.user_id AND ur2.role_id = r.id
);
*/

-- ================================
-- 9. 会员到期时间计算测试
-- ================================

-- 测试12月购买1个月会员的到期时间计算
SELECT
    '2024-12-15' as purchase_date,
    DATE_ADD('2024-12-15', INTERVAL 1 MONTH) as correct_expire_time,
    '应该是2025-01-15' as expected_result;

-- 测试续费逻辑
SELECT
    '2025-01-10' as current_time,
    '2025-01-15' as current_expire_time,
    DATE_ADD('2025-01-15', INTERVAL 1 MONTH) as renew_expire_time,
    '续费应该从现有到期时间开始延长' as logic;

-- ================================
-- 10. 定时任务相关配置
-- ================================

-- 检查定时任务是否正常运行（需要在应用启动后查看日志）
/*
定时任务说明：
1. MembershipExpirationTask.processExpiredMemberships()
   - 执行时间：每天凌晨1点
   - 功能：自动处理过期会员，降级为普通用户

2. MembershipExpirationTask.sendExpirationReminders()
   - 执行时间：每天上午9点
   - 功能：发送会员到期提醒

日志关键字：
- "🕐 开始执行会员过期处理任务"
- "📧 开始执行会员到期提醒任务"
*/

-- ================================
-- 11. 会员购买业务场景测试SQL
-- ================================

-- 场景1：新用户首次购买VIP月卡
/*
预期结果：
- member_level: 1 → 2
- member_expire_time: 当前时间 + 1个月
- 角色：user → VIP
*/

-- 场景2：VIP用户续费VIP月卡（未过期）
/*
预期结果：
- member_level: 2 → 2 (不变)
- member_expire_time: 现有到期时间 + 1个月
- 角色：VIP → VIP (不变)
*/

-- 场景3：VIP用户升级为SVIP年卡
/*
预期结果：
- member_level: 2 → 3
- member_expire_time: 当前时间 + 1年
- 角色：VIP → SVIP
*/

-- 场景4：过期VIP用户重新购买VIP月卡
/*
预期结果：
- member_level: 2 → 2 (不变)
- member_expire_time: 当前时间 + 1个月 (从当前时间开始)
- 角色：user → VIP (因为过期后已被降级)
*/

-- ================================
-- 12. 性能优化建议
-- ================================

/*
性能优化建议：

1. 定期清理过期数据：
   - 清理超过1年的交易记录
   - 归档历史会员订单数据

2. 索引优化：
   - 为常用查询字段添加复合索引
   - 定期分析慢查询并优化

3. 缓存策略：
   - 缓存用户会员状态
   - 缓存角色权限信息

4. 数据一致性：
   - 定期检查会员等级与角色的一致性
   - 自动处理过期会员的降级

5. 定时任务监控：
   - 监控定时任务执行状态
   - 设置任务执行失败告警
   - 记录任务执行统计信息
*/

-- ================================
-- 13. 会员系统完整性检查
-- ================================

-- 检查会员到期时间计算是否正确
SELECT
    user_id,
    member_level,
    member_expire_time,
    CASE
        WHEN member_expire_time > NOW() THEN '有效'
        WHEN member_expire_time < NOW() THEN '已过期'
        ELSE '无到期时间'
    END as status,
    DATEDIFF(member_expire_time, NOW()) as days_remaining
FROM aicg_user_profile
WHERE member_level > 1
ORDER BY member_expire_time ASC;

-- 检查角色与会员等级一致性
SELECT
    u.id,
    u.username,
    p.member_level,
    p.member_expire_time,
    GROUP_CONCAT(r.role_code) as roles,
    CASE
        WHEN p.member_expire_time < NOW() AND p.member_level > 1 THEN '需要降级'
        WHEN p.member_level = 1 AND r.role_code != 'user' THEN '角色不匹配'
        WHEN p.member_level = 2 AND r.role_code != 'VIP' THEN '角色不匹配'
        WHEN p.member_level = 3 AND r.role_code != 'SVIP' THEN '角色不匹配'
        ELSE '正常'
    END as check_result
FROM sys_user u
LEFT JOIN aicg_user_profile p ON u.id = p.user_id
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
WHERE r.role_code IN ('user', 'VIP', 'SVIP')
GROUP BY u.id, u.username, p.member_level, p.member_expire_time
HAVING check_result != '正常';
