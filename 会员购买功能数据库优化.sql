-- =============================================
-- 智界Aigc 会员购买功能数据库优化脚本
-- 创建时间：2025-01-29
-- 版本：V1.0
-- 说明：完善会员购买功能的数据库支持
-- =============================================

-- ================================
-- 1. 检查现有数据库结构
-- ================================

-- 检查用户扩展表是否存在会员相关字段
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aicg_user_profile' 
  AND COLUMN_NAME IN ('member_level', 'member_expire_time');

-- 检查交易记录表是否支持会员订阅类型
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aicg_user_transaction' 
  AND COLUMN_NAME IN ('transaction_type', 'order_type');

-- 检查角色表中的会员角色
SELECT id, role_name, role_code, description 
FROM sys_role 
WHERE role_code IN ('user', 'VIP', 'SVIP', 'admin');

-- ================================
-- 2. 确保会员角色存在
-- ================================

-- 插入会员角色（如果不存在）
INSERT IGNORE INTO sys_role (id, role_name, role_code, description, create_time, create_by) VALUES
('user_role_id', '普通用户', 'user', '系统默认普通用户角色', NOW(), 'system'),
('vip_role_id', 'VIP会员', 'VIP', 'VIP会员角色，享受VIP权益', NOW(), 'system'),
('svip_role_id', 'SVIP会员', 'SVIP', 'SVIP会员角色，享受最高级权益', NOW(), 'system');

-- ================================
-- 3. 优化索引（可选）
-- ================================

-- 为会员相关查询添加索引
ALTER TABLE aicg_user_profile 
ADD INDEX idx_member_level_expire (member_level, member_expire_time),
ADD INDEX idx_member_expire_time (member_expire_time);

-- 为交易记录添加会员订单索引
ALTER TABLE aicg_user_transaction 
ADD INDEX idx_membership_orders (user_id, order_type, transaction_type, order_status) 
WHERE order_type = 'membership';

-- 为用户角色关系表添加索引
ALTER TABLE sys_user_role 
ADD INDEX idx_user_role_lookup (user_id, role_id);

-- ================================
-- 4. 会员等级与角色对应关系说明
-- ================================

/*
会员等级与角色对应关系：
- member_level = 1 → role_code = 'user'   (普通用户)
- member_level = 2 → role_code = 'VIP'    (VIP会员)
- member_level = 3 → role_code = 'SVIP'   (SVIP会员)

交易类型说明：
- transaction_type = 1: 消费
- transaction_type = 2: 充值
- transaction_type = 3: 退款
- transaction_type = 4: 兑换
- transaction_type = 5: 会员订阅

订单类型说明：
- order_type = 'recharge': 充值订单
- order_type = 'membership': 会员订阅订单
- order_type = 'plugin': 插件购买订单
*/

-- ================================
-- 5. 数据一致性检查脚本
-- ================================

-- 检查会员等级与角色不一致的用户
SELECT 
    u.id as user_id,
    u.username,
    p.member_level,
    p.member_expire_time,
    r.role_code as current_role,
    CASE p.member_level
        WHEN 1 THEN 'user'
        WHEN 2 THEN 'VIP'
        WHEN 3 THEN 'SVIP'
        ELSE 'unknown'
    END as expected_role
FROM sys_user u
LEFT JOIN aicg_user_profile p ON u.id = p.user_id
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
WHERE p.member_level IS NOT NULL 
  AND r.role_code NOT IN (
      CASE p.member_level
          WHEN 1 THEN 'user'
          WHEN 2 THEN 'VIP'
          WHEN 3 THEN 'SVIP'
          ELSE 'admin'
      END
  )
  AND r.role_code != 'admin'; -- 排除管理员角色

-- ================================
-- 6. 会员到期检查脚本
-- ================================

-- 查询即将到期的会员（7天内）
SELECT 
    u.username,
    p.member_level,
    p.member_expire_time,
    DATEDIFF(p.member_expire_time, NOW()) as days_remaining
FROM sys_user u
JOIN aicg_user_profile p ON u.id = p.user_id
WHERE p.member_level > 1 
  AND p.member_expire_time IS NOT NULL
  AND p.member_expire_time > NOW()
  AND DATEDIFF(p.member_expire_time, NOW()) <= 7
ORDER BY p.member_expire_time ASC;

-- 查询已过期的会员
SELECT 
    u.username,
    p.member_level,
    p.member_expire_time,
    DATEDIFF(NOW(), p.member_expire_time) as days_expired
FROM sys_user u
JOIN aicg_user_profile p ON u.id = p.user_id
WHERE p.member_level > 1 
  AND p.member_expire_time IS NOT NULL
  AND p.member_expire_time < NOW()
ORDER BY p.member_expire_time DESC;

-- ================================
-- 7. 会员统计查询
-- ================================

-- 会员等级分布统计
SELECT 
    CASE p.member_level
        WHEN 1 THEN '普通用户'
        WHEN 2 THEN 'VIP会员'
        WHEN 3 THEN 'SVIP会员'
        ELSE '未知等级'
    END as member_type,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM aicg_user_profile), 2) as percentage
FROM aicg_user_profile p
GROUP BY p.member_level
ORDER BY p.member_level;

-- 会员订阅订单统计
SELECT 
    DATE(create_time) as order_date,
    COUNT(*) as order_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount
FROM aicg_user_transaction
WHERE order_type = 'membership' 
  AND transaction_type = 5
  AND order_status = 3  -- 已完成
  AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY order_date DESC;

-- ================================
-- 8. 数据修复脚本（谨慎使用）
-- ================================

-- 修复会员等级与角色不一致的数据（请先备份数据）
/*
-- 第一步：删除不一致的角色关系
DELETE ur FROM sys_user_role ur
JOIN aicg_user_profile p ON ur.user_id = p.user_id
JOIN sys_role r ON ur.role_id = r.id
WHERE r.role_code IN ('user', 'VIP', 'SVIP')
  AND r.role_code != CASE p.member_level
      WHEN 1 THEN 'user'
      WHEN 2 THEN 'VIP'
      WHEN 3 THEN 'SVIP'
      ELSE r.role_code
  END;

-- 第二步：添加正确的角色关系
INSERT INTO sys_user_role (user_id, role_id)
SELECT DISTINCT p.user_id, r.id
FROM aicg_user_profile p
JOIN sys_role r ON r.role_code = CASE p.member_level
    WHEN 1 THEN 'user'
    WHEN 2 THEN 'VIP'
    WHEN 3 THEN 'SVIP'
    ELSE 'user'
END
WHERE NOT EXISTS (
    SELECT 1 FROM sys_user_role ur2 
    WHERE ur2.user_id = p.user_id AND ur2.role_id = r.id
);
*/

-- ================================
-- 9. 性能优化建议
-- ================================

/*
性能优化建议：

1. 定期清理过期数据：
   - 清理超过1年的交易记录
   - 归档历史会员订单数据

2. 索引优化：
   - 为常用查询字段添加复合索引
   - 定期分析慢查询并优化

3. 缓存策略：
   - 缓存用户会员状态
   - 缓存角色权限信息

4. 数据一致性：
   - 定期检查会员等级与角色的一致性
   - 自动处理过期会员的降级
*/
