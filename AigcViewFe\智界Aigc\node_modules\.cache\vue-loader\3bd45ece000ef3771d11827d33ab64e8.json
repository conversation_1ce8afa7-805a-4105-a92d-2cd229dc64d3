{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753808171915}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { gsap } from 'gsap'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { isAdmin, getUserRole } from '@/utils/roleUtils'\nimport Vue from 'vue'\nimport LogoImage from '@/components/common/LogoImage.vue'\n\nexport default {\n  name: 'WebsiteHeader',\n  components: {\n    LogoImage\n  },\n  props: {\n    // 是否使用透明背景（首页专用）\n    transparent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: [],\n      userInfo: {},\n      isLoggedIn: false,\n      isAdmin: false\n    }\n  },\n  computed: {\n    isTransparent() {\n      return this.transparent && !this.isScrolled\n    }\n  },\n  async mounted() {\n    await this.loadMenuData()\n    await this.checkUserStatus()\n    this.initScrollListener()\n    this.initNavbarAnimations()\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  methods: {\n    async loadMenuData() {\n      try {\n        // TODO: 从API获取菜单数据\n        // const response = await this.$http.get('/api/website/header/menu')\n        // this.menuItems = response.data\n        \n        // 临时数据，后续替换为API调用\n        this.menuItems = [\n          { name: '首页', path: '/home', icon: 'home' },\n          { name: '插件中心', path: '/market', icon: 'shop' },\n          // { name: '客户案例', path: '/cases', icon: 'trophy' },\n          // { name: '教程中心', path: '/tutorials', icon: 'book' },\n          // { name: '签到奖励', path: '/signin', icon: 'gift' },\n          { name: '客户案例', path: '', icon: 'trophy' },\n          { name: '教程中心', path: '', icon: 'book' },\n          { name: '签到奖励', path: '', icon: 'gift' },\n          { name: '订阅会员', path: '/membership', icon: 'crown' },\n          { name: '邀请奖励', path: '/affiliate', icon: 'team' },\n          { name: '个人中心', path: '/usercenter', icon: 'user' }\n        ]\n      } catch (error) {\n        console.error('加载菜单数据失败:', error)\n        // 降级方案\n        this.menuItems = [\n          { name: '首页', path: '/', icon: 'home' },\n          { name: '插件中心', path: '/market', icon: 'shop' },\n          { name: '个人中心', path: '/usercenter', icon: 'user' }\n        ]\n      }\n    },\n    \n    initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll)\n    },\n    \n    handleScroll() {\n      this.isScrolled = window.scrollY > 50\n    },\n    \n    toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen\n    },\n    \n    closeMobileMenu() {\n      this.mobileMenuOpen = false\n    },\n\n    // 🔥 处理桌面端开发中功能点击\n    handleDevelopingClick(featureName) {\n      console.log('🎯 桌面端开发中功能点击:', featureName)\n      this.$message.info(`${featureName}功能正在开发中，敬请期待！`, 3)\n    },\n\n    // 🔥 处理移动端菜单点击\n    handleMobileMenuClick(item) {\n      console.log('🎯 移动端菜单点击:', item.name, 'path:', item.path)\n      if (!item.path || item.path === '') {\n        console.log('🎯 移动端开发中功能点击:', item.name)\n        this.$message.info(`${item.name}功能正在开发中，敬请期待！`, 3)\n      } else {\n        // 有效路径，进行跳转\n        this.$router.push(item.path)\n      }\n      this.closeMobileMenu()\n    },\n\n    goHome() {\n      this.$router.push('/')\n    },\n    \n    handleLogin() {\n      this.$router.push('/login')\n    },\n\n    async checkUserStatus() {\n      try {\n        // 检查是否有TOKEN\n        const token = Vue.ls.get(ACCESS_TOKEN)\n        if (!token) {\n          this.isLoggedIn = false\n          this.isAdmin = false\n          return\n        }\n\n        this.isLoggedIn = true\n\n        // 检查用户角色\n        const userRole = await getUserRole()\n        this.isAdmin = await isAdmin()\n\n        // 获取用户信息\n        this.userInfo = {\n          username: this.$store.getters.username || '用户',\n          role: userRole\n        }\n\n        console.log('🔍 WebsiteHeader用户状态:', {\n          isLoggedIn: this.isLoggedIn,\n          isAdmin: this.isAdmin,\n          userInfo: this.userInfo\n        })\n\n      } catch (error) {\n        console.error('检查用户状态失败:', error)\n        this.isLoggedIn = false\n        this.isAdmin = false\n      }\n    },\n\n    goToAdmin() {\n      // 跳转到后台管理首页\n      this.$router.push('/dashboard/analysis')\n      this.closeMobileMenu()\n    },\n\n    initNavbarAnimations() {\n      // 导航栏入场动画 - 添加refs存在检查\n      this.$nextTick(() => {\n        const elements = [this.$refs.navBrand, this.$refs.navMenu, this.$refs.navActions].filter(el => el)\n        if (elements.length > 0) {\n          gsap.from(elements, {\n            duration: 0.4,\n            y: -20,\n            opacity: 0,\n            ease: \"power2.out\",\n            stagger: 0.05\n          })\n        }\n      })\n    }\n  }\n}\n", null]}