{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=template&id=68052031&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753813837752}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"membership-container\">\n    <!-- 简洁页面标题 -->\n    <div class=\"simple-header\">\n      <h1 class=\"simple-title\">订阅会员</h1>\n      <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n    </div>\n\n    <!-- 会员套餐区域 -->\n    <section class=\"plans-section\">\n      <div class=\"container\">\n        <div class=\"plans-grid\">\n          <div \n            v-for=\"plan in plans\" \n            :key=\"plan.id\"\n            class=\"plan-card\"\n            :class=\"{ 'featured': plan.featured }\"\n          >\n            <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n            <div class=\"plan-header\">\n              <h3 class=\"plan-name\">{{ plan.name }}</h3>\n              <div class=\"plan-price\">\n                <!-- 原价显示 -->\n                <div v-if=\"plan.originalPrice\" class=\"original-price\">\n                  <span class=\"original-price-text\">原价：¥{{ plan.originalPrice }}</span>\n                  <span class=\"discount-badge\">{{ plan.discountText }}</span>\n                </div>\n                <!-- 现价显示 -->\n                <div class=\"current-price\">\n                  <span class=\"price-symbol\">¥</span>\n                  <span class=\"price-amount\">{{ plan.price }}</span>\n                  <span class=\"price-period\">/{{ plan.period }}</span>\n                </div>\n                <!-- 立省金额 -->\n                <div v-if=\"plan.saveAmount\" class=\"save-amount\">\n                  立省¥{{ plan.saveAmount }}\n                </div>\n              </div>\n              <p class=\"plan-description\">{{ plan.description }}</p>\n            </div>\n            \n            <div class=\"plan-features\">\n              <div\n                v-for=\"feature in plan.features\"\n                :key=\"feature.text || feature\"\n                class=\"feature-item\"\n                :class=\"{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }\"\n              >\n                <a-icon\n                  :type=\"feature.disabled ? 'close' : 'check'\"\n                  :class=\"{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }\"\n                />\n                <span class=\"feature-text\" v-html=\"feature.text || feature\"></span>\n                <span v-if=\"feature.exclusive\" class=\"exclusive-badge\">专属</span>\n                <span v-if=\"feature.disabled\" class=\"disabled-text\">（SVIP专享）</span>\n              </div>\n            </div>\n            \n            <button \n              class=\"btn-subscribe\"\n              :class=\"{ 'featured': plan.featured }\"\n              @click=\"handleSubscribe(plan)\"\n            >\n              {{ plan.buttonText }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n</WebsitePage>\n", null]}