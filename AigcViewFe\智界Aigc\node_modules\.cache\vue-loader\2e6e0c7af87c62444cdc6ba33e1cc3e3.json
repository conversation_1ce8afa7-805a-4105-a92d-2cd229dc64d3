{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue?vue&type=template&id=38b897e5&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue", "mtime": 1753835250707}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"main user-layout-register\"},[_vm._m(0),_c('a-form-model',{ref:\"form\",attrs:{\"model\":_vm.model,\"rules\":_vm.validatorRules}},[_c('a-form-model-item',{attrs:{\"prop\":\"username\"}},[_c('a-input',{attrs:{\"size\":\"large\",\"type\":\"text\",\"autocomplete\":\"false\",\"placeholder\":\"请输入用户名\"},model:{value:(_vm.model.username),callback:function ($$v) {_vm.$set(_vm.model, \"username\", $$v)},expression:\"model.username\"}})],1),_c('a-popover',{attrs:{\"placement\":\"rightTop\",\"trigger\":\"click\",\"visible\":_vm.state.passwordLevelChecked}},[_c('template',{slot:\"content\"},[_c('div',{style:({ width: '240px' })},[_c('div',{class:['user-register', _vm.passwordLevelClass]},[_vm._v(\"强度：\"),_c('span',[_vm._v(_vm._s(_vm.passwordLevelName))])]),_c('a-progress',{attrs:{\"percent\":_vm.state.percent,\"showInfo\":false,\"strokeColor\":_vm.passwordLevelColor}}),_c('div',{staticStyle:{\"margin-top\":\"10px\"}},[_c('span',[_vm._v(\"请至少输入 8 个字符。请不要使用容易被猜到的密码。\")])])],1)]),_c('a-form-model-item',{attrs:{\"prop\":\"password\"}},[_c('a-input',{attrs:{\"size\":\"large\",\"type\":\"password\",\"autocomplete\":\"false\",\"placeholder\":\"至少8位密码，区分大小写\"},on:{\"click\":_vm.handlePasswordInputClick},model:{value:(_vm.model.password),callback:function ($$v) {_vm.$set(_vm.model, \"password\", $$v)},expression:\"model.password\"}})],1)],2),_c('a-form-model-item',{attrs:{\"prop\":\"password2\"}},[_c('a-input',{attrs:{\"size\":\"large\",\"type\":\"password\",\"autocomplete\":\"false\",\"placeholder\":\"确认密码\"},model:{value:(_vm.model.password2),callback:function ($$v) {_vm.$set(_vm.model, \"password2\", $$v)},expression:\"model.password2\"}})],1),_c('a-form-model-item',{attrs:{\"prop\":\"mobile\"}},[_c('a-input',{attrs:{\"size\":\"large\",\"placeholder\":\"11 位手机号\"},model:{value:(_vm.model.mobile),callback:function ($$v) {_vm.$set(_vm.model, \"mobile\", $$v)},expression:\"model.mobile\"}},[_c('a-select',{attrs:{\"slot\":\"addonBefore\",\"size\":\"large\",\"defaultValue\":\"+86\"},slot:\"addonBefore\"},[_c('a-select-option',{attrs:{\"value\":\"+86\"}},[_vm._v(\"+86\")]),_c('a-select-option',{attrs:{\"value\":\"+87\"}},[_vm._v(\"+87\")])],1)],1)],1),_c('a-row',{attrs:{\"gutter\":16}},[_c('a-col',{staticClass:\"gutter-row\",attrs:{\"span\":16}},[_c('a-form-model-item',{attrs:{\"prop\":\"captcha\"}},[_c('a-input',{attrs:{\"size\":\"large\",\"type\":\"text\",\"placeholder\":\"验证码\"},model:{value:(_vm.model.captcha),callback:function ($$v) {_vm.$set(_vm.model, \"captcha\", $$v)},expression:\"model.captcha\"}},[_c('a-icon',{style:({ color: 'rgba(0,0,0,.25)' }),attrs:{\"slot\":\"prefix\",\"type\":\"mail\"},slot:\"prefix\"})],1)],1)],1),_c('a-col',{staticClass:\"gutter-row\",attrs:{\"span\":8}},[_c('a-button',{staticClass:\"getCaptcha\",attrs:{\"size\":\"large\",\"disabled\":_vm.state.smsSendBtn},domProps:{\"textContent\":_vm._s(!_vm.state.smsSendBtn && '获取验证码'||(_vm.state.time+' s'))},on:{\"click\":function($event){$event.stopPropagation();$event.preventDefault();return _vm.getCaptcha($event)}}})],1)],1),_c('a-form-model-item',[_c('a-button',{staticClass:\"register-button\",attrs:{\"size\":\"large\",\"type\":\"primary\",\"htmlType\":\"submit\",\"loading\":_vm.registerBtn,\"disabled\":_vm.registerBtn},on:{\"click\":function($event){$event.stopPropagation();$event.preventDefault();return _vm.handleSubmit($event)}}},[_vm._v(\"注册\\n      \")]),_c('router-link',{staticClass:\"login\",attrs:{\"to\":{ name: 'login' }}},[_vm._v(\"使用已有账户登录\")])],1)],1)],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('h3',[_c('span',[_vm._v(\"注册\")])])}]\n\nexport { render, staticRenderFns }"]}