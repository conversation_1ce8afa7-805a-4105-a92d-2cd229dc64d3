{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=template&id=311f9019&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753808450477}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('WebsitePage',[_c('div',{staticClass:\"usercenter-container\"},[_c('div',{staticClass:\"usercenter-main\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"usercenter-layout\"},[_c('Sidebar',{ref:\"sidebar\",attrs:{\"current-page\":_vm.currentPage,\"user-info\":_vm.userInfo},on:{\"menu-change\":_vm.handleMenuChange,\"action\":_vm.handleSidebarAction}}),_c('div',{staticClass:\"usercenter-content\"},[(_vm.currentPage === 'overview')?_c('Overview',{key:'overview',attrs:{\"user-info\":_vm.userInfo},on:{\"navigate\":_vm.handleNavigate}}):(_vm.currentPage === 'profile')?_c('Profile',{key:'profile',attrs:{\"user-info\":_vm.userInfo},on:{\"navigate\":_vm.handleNavigate,\"avatar-updated\":_vm.handleAvatarUpdated,\"info-updated\":_vm.handleInfoUpdated,\"api-key-updated\":_vm.handleApiKeyUpdated,\"password-changed\":_vm.handlePasswordChanged,\"refresh-user-info\":_vm.getUserInfo}}):(_vm.currentPage === 'credits')?_c('Credits',{key:'credits',on:{\"navigate\":_vm.handleNavigate}}):(_vm.currentPage === 'orders')?_c('Orders',{key:'orders',on:{\"navigate\":_vm.handleNavigate}}):(_vm.currentPage === 'usage')?_c('Usage',{key:'usage',on:{\"navigate\":_vm.handleNavigate}}):(_vm.currentPage === 'notifications')?_c('Notifications',{key:'notifications',on:{\"navigate\":_vm.handleNavigate,\"notification-updated\":_vm.handleNotificationUpdated}}):_vm._e()],1)],1)])])]),_c('FloatingNotifications',{ref:\"floatingNotifications\",on:{\"navigate-to-notifications\":_vm.handleNavigateToNotifications}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}