{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue", "mtime": 1753835250707}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./Register.vue?vue&type=template&id=38b897e5&scoped=true&\"\nimport script from \"./Register.vue?vue&type=script&lang=js&\"\nexport * from \"./Register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Register.vue?vue&type=style&index=0&lang=less&\"\nimport style1 from \"./Register.vue?vue&type=style&index=1&id=38b897e5&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"38b897e5\",\n  null\n  \n)\n\nexport default component.exports"]}