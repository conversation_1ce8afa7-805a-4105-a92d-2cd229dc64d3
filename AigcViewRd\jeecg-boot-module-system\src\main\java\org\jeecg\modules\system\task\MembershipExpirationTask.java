package org.jeecg.modules.system.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员到期处理定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-29
 */
@Slf4j
@Component
public class MembershipExpirationTask {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    /**
     * 处理过期会员 - 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void processExpiredMemberships() {
        log.info("🕐 开始执行会员过期处理任务...");
        
        try {
            // 1. 查找所有已过期的会员
            List<Map<String, Object>> expiredMembers = findExpiredMembers();
            
            if (expiredMembers.isEmpty()) {
                log.info("✅ 没有发现过期会员");
                return;
            }
            
            log.info("🔍 发现 {} 个过期会员，开始处理...", expiredMembers.size());
            
            // 2. 批量处理过期会员
            int processedCount = 0;
            for (Map<String, Object> member : expiredMembers) {
                String userId = (String) member.get("user_id");
                String oldRole = (String) member.get("role_code");
                Date expireTime = (Date) member.get("member_expire_time");

                try {
                    // 降级为普通用户
                    downgradeMemberToUser(userId, oldRole, expireTime);
                    processedCount++;

                } catch (Exception e) {
                    log.error("❌ 处理过期会员失败 - 用户: {}", userId, e);
                }
            }
            
            log.info("✅ 会员过期处理完成 - 处理成功: {}/{}", processedCount, expiredMembers.size());
            
        } catch (Exception e) {
            log.error("❌ 会员过期处理任务执行失败", e);
        }
    }

    /**
     * 发送会员到期提醒 - 每天上午9点执行
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendExpirationReminders() {
        log.info("📧 开始执行会员到期提醒任务...");
        
        try {
            // 查找7天内即将到期的会员
            List<Map<String, Object>> expiringMembers = findExpiringMembers(7);
            
            if (expiringMembers.isEmpty()) {
                log.info("✅ 没有即将到期的会员");
                return;
            }
            
            log.info("🔍 发现 {} 个即将到期的会员", expiringMembers.size());
            
            for (Map<String, Object> member : expiringMembers) {
                String userId = (String) member.get("user_id");
                String roleCode = (String) member.get("role_code");
                Date expireTime = (Date) member.get("member_expire_time");
                Integer daysRemaining = (Integer) member.get("days_remaining");

                // 这里可以发送邮件、短信或站内消息提醒
                log.info("📧 会员到期提醒 - 用户: {}, 角色: {}, 剩余天数: {}, 到期时间: {}",
                        userId, roleCode, daysRemaining, expireTime);

                // TODO: 实现具体的提醒发送逻辑
                // sendExpirationNotification(userId, roleCode, daysRemaining, expireTime);
            }
            
        } catch (Exception e) {
            log.error("❌ 会员到期提醒任务执行失败", e);
        }
    }

    /**
     * 查找已过期的会员（基于角色和到期时间）
     */
    private List<Map<String, Object>> findExpiredMembers() {
        String sql = "SELECT DISTINCT p.user_id, r.role_code, p.member_expire_time " +
                    "FROM aicg_user_profile p " +
                    "JOIN sys_user_role ur ON p.user_id = ur.user_id " +
                    "JOIN sys_role r ON ur.role_id = r.id " +
                    "WHERE r.role_code IN ('VIP', 'SVIP') " +
                    "  AND p.member_expire_time < NOW() " +
                    "  AND p.member_expire_time IS NOT NULL";

        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 查找即将到期的会员（基于角色和到期时间）
     */
    private List<Map<String, Object>> findExpiringMembers(int days) {
        String sql = "SELECT DISTINCT p.user_id, r.role_code, p.member_expire_time, " +
                    "       DATEDIFF(p.member_expire_time, NOW()) as days_remaining " +
                    "FROM aicg_user_profile p " +
                    "JOIN sys_user_role ur ON p.user_id = ur.user_id " +
                    "JOIN sys_role r ON ur.role_id = r.id " +
                    "WHERE r.role_code IN ('VIP', 'SVIP') " +
                    "  AND p.member_expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY) " +
                    "  AND p.member_expire_time IS NOT NULL " +
                    "ORDER BY p.member_expire_time ASC";

        return jdbcTemplate.queryForList(sql, days);
    }

    /**
     * 将过期会员降级为普通用户（基于角色机制）
     */
    private void downgradeMemberToUser(String userId, String oldRole, Date expireTime) {
        try {
            // 1. 直接更新用户角色为普通用户（不需要更新member_level字段）
            updateUserRoleToUser(userId);

            // 2. 更新最后修改时间
            String updateSql = "UPDATE aicg_user_profile SET update_time = ? WHERE user_id = ?";
            int updated = jdbcTemplate.update(updateSql, new Date(), userId);

            if (updated >= 0) {
                log.info("👑 会员降级成功 - 用户: {}, 原角色: {}, 过期时间: {}",
                        userId, oldRole, expireTime);
            } else {
                log.warn("⚠️ 会员降级失败 - 用户不存在: {}", userId);
            }

        } catch (Exception e) {
            log.error("❌ 会员降级异常 - 用户: {}", userId, e);
            throw e;
        }
    }

    /**
     * 更新用户角色为普通用户
     */
    private void updateUserRoleToUser(String userId) {
        try {
            // 删除用户现有的会员角色（保留admin等其他角色）
            String deleteRoleSql = "DELETE FROM sys_user_role WHERE user_id = ? AND role_id IN " +
                                  "(SELECT id FROM sys_role WHERE role_code IN ('VIP', 'SVIP'))";
            jdbcTemplate.update(deleteRoleSql, userId);
            
            // 确保用户有普通用户角色
            String checkUserRoleSql = "SELECT COUNT(*) FROM sys_user_role ur " +
                                     "JOIN sys_role r ON ur.role_id = r.id " +
                                     "WHERE ur.user_id = ? AND r.role_code = 'user'";
            
            int userRoleCount = jdbcTemplate.queryForObject(checkUserRoleSql, Integer.class, userId);
            
            if (userRoleCount == 0) {
                // 添加普通用户角色
                String addUserRoleSql = "INSERT INTO sys_user_role (user_id, role_id) " +
                                       "SELECT ?, id FROM sys_role WHERE role_code = 'user' LIMIT 1";
                jdbcTemplate.update(addUserRoleSql, userId);
            }
            
            log.info("🎭 用户角色更新为普通用户成功 - 用户: {}", userId);
            
        } catch (Exception e) {
            log.error("🎭 更新用户角色异常 - 用户: {}", userId, e);
            throw e;
        }
    }
}
