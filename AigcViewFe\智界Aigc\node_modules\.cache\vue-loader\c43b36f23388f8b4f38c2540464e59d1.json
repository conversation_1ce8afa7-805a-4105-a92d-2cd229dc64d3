{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=template&id=64fb4ad8&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753808171915}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"nav\",\n    {\n      ref: \"navbar\",\n      staticClass: \"website-navbar\",\n      class: { scrolled: _vm.isScrolled, transparent: _vm.isTransparent }\n    },\n    [\n      _c(\"div\", { staticClass: \"nav-container\" }, [\n        _c(\n          \"div\",\n          {\n            ref: \"navBrand\",\n            staticClass: \"nav-brand\",\n            on: { click: _vm.goHome }\n          },\n          [\n            _c(\"LogoImage\", {\n              attrs: {\n                size: \"medium\",\n                hover: true,\n                \"container-class\": \"brand-logo-container\",\n                \"image-class\": \"brand-logo-image\",\n                \"fallback-class\": \"brand-logo-fallback\"\n              }\n            }),\n            _c(\"span\", { staticClass: \"brand-text\" }, [_vm._v(\"智界AIGC\")])\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { ref: \"navMenu\", staticClass: \"nav-menu\" },\n          _vm._l(_vm.menuItems, function(item) {\n            return _c(\n              item.path && item.path !== \"\" ? \"router-link\" : \"span\",\n              {\n                key: item.name,\n                tag: \"component\",\n                staticClass: \"nav-link\",\n                class: {\n                  active: _vm.$route.path === item.path,\n                  \"nav-link-disabled\": !item.path || item.path === \"\"\n                },\n                attrs: {\n                  to: item.path && item.path !== \"\" ? item.path : undefined\n                },\n                on: {\n                  click: function($event) {\n                    !item.path || item.path === \"\"\n                      ? _vm.handleDevelopingClick(item.name)\n                      : undefined\n                  }\n                }\n              },\n              [\n                _c(\"a-icon\", {\n                  staticClass: \"nav-icon\",\n                  attrs: { type: item.icon }\n                }),\n                _c(\"span\", { staticClass: \"nav-text\" }, [\n                  _vm._v(_vm._s(item.name))\n                ])\n              ],\n              1\n            )\n          }),\n          1\n        ),\n        _c(\"div\", { ref: \"navActions\", staticClass: \"nav-actions\" }, [\n          !_vm.isLoggedIn\n            ? _c(\n                \"button\",\n                {\n                  staticClass: \"btn-secondary\",\n                  on: { click: _vm.handleLogin }\n                },\n                [_vm._v(\"登录\")]\n              )\n            : _vm.isAdmin\n            ? _c(\n                \"button\",\n                { staticClass: \"btn-admin\", on: { click: _vm.goToAdmin } },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"dashboard\" } }),\n                  _vm._v(\"\\n        后台管理\\n      \")\n                ],\n                1\n              )\n            : _vm._e()\n        ]),\n        _c(\n          \"button\",\n          {\n            ref: \"mobileMenuBtn\",\n            staticClass: \"mobile-menu-btn\",\n            on: { click: _vm.toggleMobileMenu }\n          },\n          [\n            _c(\"a-icon\", {\n              attrs: { type: _vm.mobileMenuOpen ? \"close\" : \"menu\" }\n            })\n          ],\n          1\n        )\n      ]),\n      _c(\n        \"div\",\n        {\n          ref: \"mobileMenu\",\n          staticClass: \"mobile-menu\",\n          class: { open: _vm.mobileMenuOpen }\n        },\n        [\n          _vm._l(_vm.menuItems, function(item) {\n            return _c(\n              item.path && item.path !== \"\" ? \"router-link\" : \"span\",\n              {\n                key: item.name,\n                tag: \"component\",\n                staticClass: \"mobile-nav-link\",\n                class: {\n                  \"mobile-nav-link-disabled\": !item.path || item.path === \"\"\n                },\n                attrs: {\n                  to: item.path && item.path !== \"\" ? item.path : undefined\n                },\n                on: {\n                  click: function($event) {\n                    return _vm.handleMobileMenuClick(item)\n                  }\n                }\n              },\n              [\n                _c(\"a-icon\", {\n                  staticClass: \"mobile-nav-icon\",\n                  attrs: { type: item.icon }\n                }),\n                _c(\"span\", { staticClass: \"mobile-nav-text\" }, [\n                  _vm._v(_vm._s(item.name))\n                ])\n              ],\n              1\n            )\n          }),\n          _c(\"div\", { staticClass: \"mobile-actions\" }, [\n            !_vm.isLoggedIn\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"mobile-btn-login\",\n                    on: { click: _vm.handleLogin }\n                  },\n                  [_vm._v(\"登录\")]\n                )\n              : _vm.isAdmin\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"mobile-btn-admin\",\n                    on: { click: _vm.goToAdmin }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"dashboard\" } }),\n                    _vm._v(\"\\n        后台管理\\n      \")\n                  ],\n                  1\n                )\n              : _vm._e()\n          ])\n        ],\n        2\n      )\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}