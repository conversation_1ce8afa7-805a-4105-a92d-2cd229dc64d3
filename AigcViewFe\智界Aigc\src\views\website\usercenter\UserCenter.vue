<template>
  <WebsitePage>
    <div class="usercenter-container">


      <!-- 主要内容区域 -->
      <div class="usercenter-main">
        <div class="container">
          <div class="usercenter-layout">
            <!-- 侧边栏 -->
            <Sidebar
              ref="sidebar"
              :current-page="currentPage"
              :user-info="userInfo"
              @menu-change="handleMenuChange"
              @action="handleSidebarAction"
            />

            <!-- 内容区域 -->
            <div class="usercenter-content">
              <!-- 概览页面 -->
              <Overview
                v-if="currentPage === 'overview'"
                :key="'overview'"
                :user-info="userInfo"
                @navigate="handleNavigate"
              />

              <!-- 账户设置页面 -->
              <Profile
                v-else-if="currentPage === 'profile'"
                :key="'profile'"
                :user-info="userInfo"
                @navigate="handleNavigate"
                @avatar-updated="handleAvatarUpdated"
                @info-updated="handleInfoUpdated"
                @api-key-updated="handleApiKeyUpdated"
                @password-changed="handlePasswordChanged"
                @refresh-user-info="getUserInfo"
              />

              <!-- 账户管理页面 -->
              <Credits
                v-else-if="currentPage === 'credits'"
                :key="'credits'"
                @navigate="handleNavigate"
              />

              <!-- 订单记录页面 -->
              <Orders
                v-else-if="currentPage === 'orders'"
                :key="'orders'"
                @navigate="handleNavigate"
              />

              <!-- 使用记录页面 -->
              <Usage
                v-else-if="currentPage === 'usage'"
                :key="'usage'"
                @navigate="handleNavigate"
              />

              <!-- 会员服务页面 -->
              <Membership
                v-else-if="currentPage === 'membership'"
                :key="'membership'"
                @navigate="handleNavigate"
              />

              <!-- <Referral
                v-else-if="currentPage === 'referral'"
                :key="'referral'"
                @navigate="handleNavigate"
              /> -->

              <!-- 系统通知页面 -->
              <Notifications
                v-else-if="currentPage === 'notifications'"
                :key="'notifications'"
                @navigate="handleNavigate"
                @notification-updated="handleNotificationUpdated"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬浮系统通知 -->
    <FloatingNotifications
      ref="floatingNotifications"
      @navigate-to-notifications="handleNavigateToNotifications"
    />
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import Sidebar from './components/Sidebar.vue'
import FloatingNotifications from './components/FloatingNotifications.vue'
import Overview from './views/Overview.vue'
import Profile from './views/Profile.vue'
import Credits from './views/Credits.vue'
import Orders from './views/Orders.vue'
import Usage from './views/Usage.vue'
// 会员服务和推荐奖励组件
import Membership from './views/Membership.vue'
// import Referral from './views/Referral.vue'
import Notifications from './pages/Notifications.vue'
import { getUserFullInfo } from '@/api/usercenter'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { usercenterAnimations } from '@/animations/gsap/pages/usercenterAnimations.js'
import Vue from 'vue'

export default {
  name: 'UserCenter',
  components: {
    WebsitePage,
    Sidebar,
    FloatingNotifications,
    Overview,
    Profile,
    Credits,
    Orders,
    Usage,
    // 会员服务和推荐奖励组件
    Membership,
    // Referral,
    Notifications
  },
  data() {
    return {
      loading: true,
      currentPage: 'overview',
      userInfo: {
        nickname: '',
        email: '',
        avatar: '',
        phone: '',
        accountBalance: 0,
        currentRole: '普通用户',
        totalConsumption: 0,
        totalRecharge: 0,
        memberExpireTime: null,
        apiKey: '',
        createTime: null,
        // 🔑 关键：添加密码修改状态字段
        passwordChanged: 0
      },
      pageMap: {
        overview: '概览',
        profile: '账户设置',
        credits: '账户管理',
        orders: '订单记录',
        usage: '使用记录',
        notifications: '系统通知'
        // 🚫 临时注释掉会员服务和推荐奖励页面
        // membership: '会员服务',
        // referral: '推荐奖励'
      }
    }
  },
  computed: {
    currentPageTitle() {
      return this.pageMap[this.currentPage] || ''
    }
  },
  async mounted() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      return
    }

    await this.loadUserInfo()
    this.initAnimations()

    // 处理从官网传递过来的查询参数
    this.handleQueryParams()
  },
  methods: {
    /**
     * 检查登录状态 - 使用与路由守卫相同的TOKEN检查方法
     */
    checkLoginStatus() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      if (!token) {
        console.log('🔍 UserCenter: 未登录，重定向到登录页')
        this.$message.warning('请先登录')
        this.$router.push({
          path: '/login',
          query: { redirect: this.$route.fullPath }
        })
        return false
      }
      console.log('🔍 UserCenter: 已登录，TOKEN存在')
      return true
    },

    async loadUserInfo() {
      try {
        this.loading = true

        const response = await getUserFullInfo()
        console.log('🔍 UserCenter: 完整的响应对象:', response)
        console.log('🔍 UserCenter: response.success:', response.success)
        console.log('🔍 UserCenter: response.data:', response.data)
        console.log('🔍 UserCenter: response.result:', response.result)
        console.log('🔍 UserCenter: response.message:', response.message)

        if (response.success) {
          // 使用正确的字段：response.result 而不是 response.data
          const rawData = response.result || response.data || {}
          console.log('🔍 UserCenter: 后端返回的原始数据:', rawData)

          // 字段名映射：后端下划线 -> 前端驼峰命名
          const mappedData = {
            nickname: rawData.nickname || '',
            email: rawData.email || '',
            avatar: rawData.avatar || '',
            phone: rawData.phone || '',
            accountBalance: rawData.account_balance || 0,
            totalConsumption: rawData.total_consumption || 0,
            totalRecharge: rawData.total_recharge || 0,
            currentRole: rawData.current_role || '普通用户',
            apiKey: rawData.api_key || '',
            createTime: rawData.user_create_time || null,
            username: rawData.username || '',
            realname: rawData.realname || '',
            // 🔑 关键：添加密码修改状态字段
            passwordChanged: rawData.password_changed || 0
          }

          console.log('🔍 UserCenter: 映射后的数据:', mappedData)

          // 使用Object.assign确保响应式更新
          Object.assign(this.userInfo, mappedData)

          console.log('🔍 UserCenter: 最终的userInfo:', this.userInfo)

          // 强制触发视图更新
          this.$forceUpdate()
        } else {
          console.error('🔍 UserCenter: 获取用户信息失败')
          console.error('🔍 UserCenter: response.code:', response.code)
          console.error('🔍 UserCenter: response.message:', response.message)
          console.error('🔍 UserCenter: 完整响应:', response)

          // 检查是否是认证失败
          if (response.code === 401 || (response.message && response.message.includes('Token')) || (response.message && response.message.includes('登录'))) {
            this.$message.warning('登录已过期，请重新登录')
            this.$router.push({
              path: '/login',
              query: { redirect: this.$route.fullPath }
            })
            return
          }
          this.$message.error(`获取用户信息失败: ${response.message || '未知错误'}`)
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        // 检查是否是认证相关错误
        if (error.response && error.response.status === 401) {
          this.$message.warning('登录已过期，请重新登录')
          this.$router.push({
            path: '/login',
            query: { redirect: this.$route.fullPath }
          })
          return
        }
        this.$message.error('加载用户信息失败，请刷新重试')
      } finally {
        this.loading = false
      }
    },

    // 刷新用户信息的方法（供子组件调用）
    async getUserInfo() {
      console.log('🔍 UserCenter: 收到刷新用户信息请求')
      await this.loadUserInfo()
    },

    initAnimations() {
      this.$nextTick(() => {
        // 初始化GSAP动画
        if (this.$animationManager) {
          usercenterAnimations.init(this.$animationManager)
        }
      })
    },

    handleMenuChange(page) {
      if (this.currentPage !== page) {
        // 页面切换动画
        const fromPage = `.page-${this.currentPage}`
        const toPage = `.page-${page}`

        if (this.$animationManager) {
          usercenterAnimations.switchPage(fromPage, toPage)
        }

        this.currentPage = page

        // 更新URL（可选）
        this.$router.replace({
          path: '/usercenter',
          query: { page }
        })
      }
    },

    handleNavigate(page) {
      this.handleMenuChange(page)
    },

    handleSidebarAction(action) {
      switch (action) {
        case 'recharge':
          this.handleMenuChange('credits')
          break
        case 'upgrade':
          this.handleMenuChange('membership')
          break
        default:
          console.log('未知操作:', action)
      }
    },

    handleNavigateToNotifications() {
      // 导航到系统通知页面
      this.handleMenuChange('notifications')
    },

    // 处理从官网传递过来的查询参数
    handleQueryParams() {
      const { page, planId, planName, price } = this.$route.query

      // 如果有page参数，切换到对应页面
      if (page && this.pageMap[page]) {
        console.log('🎯 UserCenter: 从官网跳转到页面:', page)
        this.currentPage = page

        // 如果是会员页面且有套餐信息，可以在这里处理
        if (page === 'membership' && planId) {
          console.log('🎯 UserCenter: 选择的套餐信息:', { planId, planName, price })
          // 可以将套餐信息传递给会员组件
          this.$nextTick(() => {
            // 通过事件总线或其他方式通知会员组件
            this.$bus && this.$bus.$emit('select-membership-plan', {
              planId: parseInt(planId),
              planName,
              price: parseFloat(price)
            })
          })
        }
      }
    },

    handleNotificationUpdated() {
      // 通知更新时，刷新悬浮通知组件的数据
      if (this.$refs.floatingNotifications) {
        this.$refs.floatingNotifications.loadNotifications()
      }

      // 同时通知Sidebar组件更新未读通知数量
      if (this.$refs.sidebar) {
        this.$refs.sidebar.loadUnreadNotificationCount()
      }
    },

    handleAvatarUpdated(newAvatar) {
      // 头像更新时，更新用户信息
      console.log('🔍 UserCenter: 头像更新事件，新头像:', newAvatar)
      this.userInfo.avatar = newAvatar
      this.$forceUpdate()
    },

    handleInfoUpdated(updatedInfo) {
      // 基本信息更新时，更新用户信息
      console.log('🔍 UserCenter: 基本信息更新事件:', updatedInfo)
      Object.assign(this.userInfo, updatedInfo)
      this.$forceUpdate()
    },

    handleApiKeyUpdated(newApiKey) {
      // API Key更新时，保存当前滚动位置
      const currentScrollY = window.pageYOffset || document.documentElement.scrollTop
      console.log('🔍 UserCenter: API Key更新事件，当前滚动位置:', currentScrollY)

      // 使用Vue.set确保响应式更新
      this.$set(this.userInfo, 'apiKey', newApiKey)

      // 在下一个tick恢复滚动位置
      this.$nextTick(() => {
        window.scrollTo(0, currentScrollY)
        console.log('🔍 UserCenter: 已恢复滚动位置到:', currentScrollY)
      })
    },

    // 🔑 新增：密码修改处理
    handlePasswordChanged() {
      console.log('🔍 UserCenter: 收到密码修改事件')
      if (this.userInfo) {
        this.userInfo.passwordChanged = 1
        console.log('🔍 UserCenter: passwordChanged已更新为1')
      }
    }
  },

  // 路由守卫：根据URL参数设置当前页面
  beforeRouteEnter(to, _from, next) {
    next(vm => {
      const page = to.query.page
      if (page && vm.pageMap[page]) {
        vm.currentPage = page
      }
    })
  },

  beforeRouteUpdate(to, _from, next) {
    const page = to.query.page
    if (page && this.pageMap[page]) {
      this.currentPage = page
    }
    next()
  }
}
</script>

<style scoped>
.usercenter-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
}



.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 主要内容区域 */
.usercenter-main {
  padding: 2rem 0;
}

.usercenter-layout {
  display: grid;
  grid-template-columns: 260px 1fr;
  gap: 1.5rem;
  align-items: flex-start;
  /* 取消左边距，改为正常的两列布局 */
  /* margin-left: 320px; */
}

.usercenter-content {
  min-height: 600px;
  position: relative;
}

/* 页面切换动画 */
.usercenter-content > * {
  opacity: 0.85;
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .usercenter-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 768px) {
  .usercenter-main {
    padding: 1rem 0;
  }

  .usercenter-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}


</style>
