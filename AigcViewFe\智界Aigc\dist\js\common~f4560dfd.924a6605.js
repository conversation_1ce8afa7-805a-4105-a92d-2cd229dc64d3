(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~f4560dfd"],{"08be":function(e,t,a){"use strict";var r=a("c9e1"),i=a.n(r);i.a},"0a27":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"推荐人ID"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.queryParam.referrerId,callback:function(t){e.$set(e.queryParam,"referrerId",t)},expression:"queryParam.referrerId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"奖励类型"}},[a("a-select",{attrs:{placeholder:"请选择奖励类型",allowClear:""},model:{value:e.queryParam.rewardType,callback:function(t){e.$set(e.queryParam,"rewardType",t)},expression:"queryParam.rewardType"}},[a("a-select-option",{attrs:{value:1}},[e._v("注册奖励")]),a("a-select-option",{attrs:{value:2}},[e._v("首充奖励")]),a("a-select-option",{attrs:{value:3}},[e._v("升级奖励")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待发放")]),a("a-select-option",{attrs:{value:2}},[e._v("已发放")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("推荐奖励记录")}}},[e._v("导出")]),e.selectedRowKeys.length>0?a("a-button",{attrs:{type:"primary",icon:"check"},on:{click:e.batchPay}},[e._v("批量发放")]):e._e(),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handlePay(r)}}},[e._v("发放奖励")])]):e._e(),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleCancel(r)}}},[e._v("取消奖励")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-referral-reward-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("6973"),l={name:"AicgUserReferralRewardList",mixins:[s["a"],n["b"]],components:{AicgUserReferralRewardModal:o["default"]},data:function(){return{description:"推荐奖励记录管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"推荐人ID",align:"center",dataIndex:"referrerId"},{title:"被推荐人ID",align:"center",dataIndex:"refereeId"},{title:"奖励类型",align:"center",dataIndex:"rewardType",customRender:function(e){var t={1:"注册奖励",2:"首充奖励",3:"升级奖励"};return t[e]||e}},{title:"奖励金额",align:"center",dataIndex:"rewardAmount",customRender:function(e){return e?"¥".concat(e):"-"}},{title:"触发事件",align:"center",dataIndex:"triggerEvent"},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){var t={1:"待发放",2:"已发放",3:"已取消"},a={1:"orange",2:"green",3:"red"};return'<span style="color: '.concat(a[e],'">').concat(t[e]||e,"</span>")},scopedSlots:{customRender:"htmlSlot"}},{title:"发放时间",align:"center",dataIndex:"rewardTime"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/referralreward/list",delete:"/demo/referralreward/delete",deleteBatch:"/demo/referralreward/deleteBatch",exportXlsUrl:"/demo/referralreward/exportXls",importExcelUrl:"demo/referralreward/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"referrerId",text:"推荐人ID"}),e.push({type:"string",value:"refereeId",text:"被推荐人ID"}),e.push({type:"int",value:"rewardType",text:"奖励类型"}),e.push({type:"BigDecimal",value:"rewardAmount",text:"奖励金额"}),e.push({type:"string",value:"triggerEvent",text:"触发事件"}),e.push({type:"int",value:"status",text:"状态"}),e.push({type:"Date",value:"rewardTime",text:"发放时间"}),this.superFieldList=e},handlePay:function(e){var t=this;this.$confirm({title:"发放奖励",content:"确定要发放 ¥".concat(e.rewardAmount," 的奖励吗？"),onOk:function(){t.$http.post("/demo/referralreward/pay?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("发放成功"),t.loadData()):t.$message.error(e.message)}))}})},handleCancel:function(e){var t=this;this.$confirm({title:"取消奖励",content:"确定要取消该奖励吗？",onOk:function(){t.$http.post("/demo/referralreward/cancel?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("取消成功"),t.loadData()):t.$message.error(e.message)}))}})},batchPay:function(){var e=this;0!==this.selectedRowKeys.length?this.$confirm({title:"批量发放奖励",content:"确定要发放选中的 ".concat(this.selectedRowKeys.length," 条奖励吗？"),onOk:function(){e.$http.post("/demo/referralreward/batchPay?ids=".concat(e.selectedRowKeys.join(","))).then((function(t){t.success?(e.$message.success(t.message),e.loadData(),e.onClearSelected()):e.$message.error(t.message)}))}}):this.$message.warning("请选择要发放的奖励")}}},c=l,d=(a("463f"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"69602dd8",null);t["default"]=u.exports},"0b61":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"会员等级"}},[a("a-select",{attrs:{placeholder:"请选择会员等级",allowClear:""},model:{value:e.queryParam.memberLevel,callback:function(t){e.$set(e.queryParam,"memberLevel",t)},expression:"queryParam.memberLevel"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通用户")]),a("a-select-option",{attrs:{value:2}},[e._v("VIP会员")]),a("a-select-option",{attrs:{value:3}},[e._v("SVIP会员")]),a("a-select-option",{attrs:{value:4}},[e._v("至尊会员")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("生效中")]),a("a-select-option",{attrs:{value:2}},[e._v("已过期")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("会员订阅历史")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"status",fn:function(t){return[a("a-tag",{attrs:{color:1===t?"green":2===t?"red":"orange"}},[e._v("\n          "+e._s(1===t?"生效中":2===t?"已过期":3===t?"已取消":t)+"\n        ")])]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleCancel(r)}}},[e._v("取消订阅")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-membership-history-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("192a"),l={name:"AicgUserMembershipHistoryList",mixins:[s["a"],n["b"]],components:{AicgUserMembershipHistoryModal:o["default"]},data:function(){return{description:"用户会员订阅历史管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"userId"},{title:"订单ID",align:"center",dataIndex:"orderId"},{title:"会员等级",align:"center",dataIndex:"memberLevel",customRender:function(e){var t={1:"普通用户",2:"VIP会员",3:"SVIP会员",4:"至尊会员"};return t[e]||e}},{title:"订阅时长(月)",align:"center",dataIndex:"durationMonths"},{title:"订阅金额",align:"center",dataIndex:"amount",customRender:function(e){return e?"¥".concat(e):"-"}},{title:"开始时间",align:"center",dataIndex:"startTime"},{title:"结束时间",align:"center",dataIndex:"endTime"},{title:"状态",align:"center",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/membershiphistory/list",delete:"/demo/membershiphistory/delete",deleteBatch:"/demo/membershiphistory/deleteBatch",exportXlsUrl:"/demo/membershiphistory/exportXls",importExcelUrl:"demo/membershiphistory/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"userId",text:"用户ID"}),e.push({type:"string",value:"orderId",text:"订单ID"}),e.push({type:"int",value:"memberLevel",text:"会员等级"}),e.push({type:"int",value:"durationMonths",text:"订阅时长(月)"}),e.push({type:"BigDecimal",value:"amount",text:"订阅金额"}),e.push({type:"Date",value:"startTime",text:"开始时间"}),e.push({type:"Date",value:"endTime",text:"结束时间"}),e.push({type:"int",value:"status",text:"状态"}),this.superFieldList=e},handleCancel:function(e){var t=this;this.$confirm({title:"确认取消",content:"确定要取消该会员订阅吗？",onOk:function(){t.$http.post("/demo/membershiphistory/cancel?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("取消成功"),t.loadData()):t.$message.error(e.message)}))}})}}},c=l,d=(a("84f8"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"4dc07baf",null);t["default"]=u.exports},"113a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"withdrawal-management"},[e._m(0),a("div",{staticClass:"search-section"},[a("a-card",{attrs:{bordered:!1}},[a("a-form",{attrs:{layout:"inline",model:e.searchForm},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"申请状态"}},[a("a-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.searchForm.status,callback:function(t){e.$set(e.searchForm,"status",t)},expression:"searchForm.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待审核")]),a("a-select-option",{attrs:{value:2}},[e._v("已发放")]),a("a-select-option",{attrs:{value:3}},[e._v("审核拒绝")]),a("a-select-option",{attrs:{value:4}},[e._v("已取消")])],1)],1),a("a-form-item",{attrs:{label:"申请时间"}},[a("a-range-picker",{attrs:{format:"YYYY-MM-DD",placeholder:["开始时间","结束时间"]},model:{value:e.searchForm.dateRange,callback:function(t){e.$set(e.searchForm,"dateRange",t)},expression:"searchForm.dateRange"}})],1),a("a-form-item",{attrs:{label:"用户名"}},[a("a-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入用户名"},model:{value:e.searchForm.username,callback:function(t){e.$set(e.searchForm,"username",t)},expression:"searchForm.username"}})],1),a("a-form-item",{attrs:{label:"支付宝信息"}},[a("a-input",{staticStyle:{width:"150px"},attrs:{placeholder:"支付宝账号或姓名"},model:{value:e.searchForm.alipayInfo,callback:function(t){e.$set(e.searchForm,"alipayInfo",t)},expression:"searchForm.alipayInfo"}})],1),a("a-form-item",[a("a-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSearch}},[a("a-icon",{attrs:{type:"search"}}),e._v("\n            搜索\n          ")],1),a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.handleReset}},[a("a-icon",{attrs:{type:"reload"}}),e._v("\n            重置\n          ")],1)],1)],1)],1)],1),a("div",{staticClass:"table-section"},[a("a-card",{attrs:{bordered:!1}},[a("a-table",{attrs:{columns:e.columns,"data-source":e.dataSource,loading:e.loading,pagination:e.pagination,"row-key":"id",scroll:{x:1200}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"userInfo",fn:function(t,r){return[r?a("div",{staticClass:"user-info"},[a("div",{staticClass:"username"},[e._v(e._s(r.username||"-"))]),a("div",{staticClass:"user-id"},[e._v("ID: "+e._s(r.user_id||"-"))])]):a("span",[e._v("-")])]}},{key:"amount",fn:function(t,r){return[r?a("div",{staticClass:"amount-info"},[a("div",{staticClass:"amount"},[e._v("¥"+e._s(e.formatNumber(r.withdrawal_amount)))])]):a("span",[e._v("-")])]}},{key:"alipayInfo",fn:function(t,r){return[r?a("div",{staticClass:"alipay-info"},[a("div",{staticClass:"name"},[e._v(e._s(r.alipay_name||"-"))]),a("div",{staticClass:"account"},[e._v(e._s(r.alipay_account||"-"))])]):a("span",[e._v("-")])]}},{key:"status",fn:function(t,r){return[r?a("a-tag",{attrs:{color:e.getStatusColor(r&&r.status)}},[e._v("\n            "+e._s(e.getStatusText(r.status,r.review_remark))+"\n          ")]):a("span",[e._v("-")])]}},{key:"applyTime",fn:function(t,r){return[a("span",[e._v(e._s(r&&r.apply_time?e.formatDateTime(r.apply_time):"-"))])]}},{key:"reviewTime",fn:function(t,r){return[a("span",[e._v(e._s(r&&r.review_time?e.formatDateTime(r.review_time):"-"))])]}},{key:"action",fn:function(t,r){return[r?a("div",{staticClass:"action-buttons"},[1===r.status?a("a-button",{attrs:{type:"primary",size:"small",loading:r.approving},on:{click:function(t){return e.handleApprove(r)}}},[e._v("\n              审核通过\n            ")]):e._e(),1===r.status?a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"danger",size:"small",loading:r.rejecting},on:{click:function(t){return e.handleReject(r)}}},[e._v("\n              审核拒绝\n            ")]):e._e(),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{size:"small"},on:{click:function(t){return e.handleViewDetail(r)}}},[e._v("\n              查看详情\n            ")])],1):a("span",[e._v("-")])]}}])})],1)],1),a("a-modal",{attrs:{title:"审核拒绝",footer:null,width:"500px"},model:{value:e.showRejectModal,callback:function(t){e.showRejectModal=t},expression:"showRejectModal"}},[a("div",{staticClass:"reject-modal"},[a("a-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{message:"请填写拒绝原因",type:"warning","show-icon":""}}),a("a-form",{attrs:{layout:"vertical"}},[a("a-form-item",{attrs:{label:"拒绝原因",required:""}},[a("a-textarea",{attrs:{placeholder:"请输入拒绝原因",rows:4,maxLength:200},model:{value:e.rejectReason,callback:function(t){e.rejectReason=t},expression:"rejectReason"}})],1)],1),a("div",{staticClass:"modal-actions"},[a("a-button",{on:{click:function(t){e.showRejectModal=!1}}},[e._v("\n          取消\n        ")]),a("a-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",loading:e.rejecting,disabled:!e.rejectReason.trim()},on:{click:e.confirmReject}},[e._v("\n          确认拒绝\n        ")])],1)],1)]),a("a-modal",{attrs:{title:"提现申请详情",footer:null,width:"600px"},model:{value:e.showDetailModal,callback:function(t){e.showDetailModal=t},expression:"showDetailModal"}},[e.currentRecord?a("div",{staticClass:"detail-modal"},[a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"申请ID"}},[e._v("\n          "+e._s(e.currentRecord.id)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"用户名"}},[e._v("\n          "+e._s(e.currentRecord.username)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"提现金额"}},[a("span",{staticClass:"amount-text"},[e._v("¥"+e._s(e.formatNumber(e.currentRecord.withdrawal_amount)))])]),a("a-descriptions-item",{attrs:{label:"申请状态"}},[a("a-tag",{attrs:{color:e.getStatusColor(e.currentRecord.status)}},[e._v("\n            "+e._s(e.getStatusText(e.currentRecord.status,e.currentRecord.review_remark))+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"真实姓名"}},[e._v("\n          "+e._s(e.currentRecord.alipay_name)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"支付宝账号"}},[e._v("\n          "+e._s(e.currentRecord.alipay_account)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"申请时间"}},[e._v("\n          "+e._s(e.currentRecord.apply_time?e.formatDateTime(e.currentRecord.apply_time):"-")+"\n        ")]),a("a-descriptions-item",{attrs:{label:"审核时间"}},[e._v("\n          "+e._s(e.currentRecord.review_time?e.formatDateTime(e.currentRecord.review_time):"-")+"\n        ")]),e.currentRecord.review_by?a("a-descriptions-item",{attrs:{label:"审核人"}},[e._v("\n          "+e._s(e.currentRecord.review_by)+"\n        ")]):e._e(),e.currentRecord.review_remark?a("a-descriptions-item",{attrs:{label:"审核备注"}},[e._v("\n          "+e._s(e.currentRecord.review_remark)+"\n        ")]):e._e()],1)],1):e._e()])],1)},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page-header"},[a("h2",[e._v("提现管理")]),a("p",[e._v("管理用户提现申请，审核通过或拒绝申请")])])}],n=a("a34a"),s=a.n(n);function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function l(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){c(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function c(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t,a,r,i,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){d(n,r,i,s,o,"next",e)}function o(e){d(n,r,i,s,o,"throw",e)}s(void 0)}))}}var m={name:"AigcWithdrawalList",data:function(){return{loading:!1,searchForm:{status:void 0,dateRange:[],username:"",alipayInfo:""},dataSource:[],pagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条记录")}},columns:[{title:"用户信息",key:"userInfo",width:150,scopedSlots:{customRender:"userInfo"}},{title:"提现金额",key:"amount",width:120,align:"right",scopedSlots:{customRender:"amount"}},{title:"支付宝信息",key:"alipayInfo",width:180,scopedSlots:{customRender:"alipayInfo"}},{title:"申请时间",dataIndex:"apply_time",key:"applyTime",width:150,scopedSlots:{customRender:"applyTime"}},{title:"审核时间",dataIndex:"review_time",key:"reviewTime",width:150,scopedSlots:{customRender:"reviewTime"}},{title:"状态",dataIndex:"status",key:"status",width:100,scopedSlots:{customRender:"status"}},{title:"操作",key:"action",width:280,fixed:"right",scopedSlots:{customRender:"action"}}],showRejectModal:!1,showDetailModal:!1,currentRecord:null,rejectReason:"",rejecting:!1}},mounted:function(){this.loadData()},methods:{loadData:function(){var e=u(s.a.mark((function e(){var t,a,r,i,n;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.loading=!0,t=l({current:this.pagination.current,size:this.pagination.pageSize},this.getSearchParams()),e.next=5,this.$http.get("/api/usercenter/admin/withdrawalList",{params:t});case 5:a=e.sent,r=a.data||a,r&&r.success?(this.dataSource=r.result.records||[],this.pagination.total=r.result.total||0,this.dataSource[0]&&(i=this.dataSource[0],Object.keys(i).forEach((function(e){})))):(n=r&&r.message||"获取数据失败",this.$message.error(n),this.dataSource=[],this.pagination.total=0),e.next=16;break;case 12:e.prev=12,e.t0=e["catch"](0),this.$message.error("加载数据失败");case 16:return e.prev=16,this.loading=!1,e.finish(16);case 19:case"end":return e.stop()}}),e,this,[[0,12,16,19]])})));function t(){return e.apply(this,arguments)}return t}(),getSearchParams:function(){var e={};return void 0!==this.searchForm.status&&(e.status=this.searchForm.status),this.searchForm.username&&(e.username=this.searchForm.username.trim()),this.searchForm.alipayInfo&&(e.alipayInfo=this.searchForm.alipayInfo.trim()),this.searchForm.dateRange&&2===this.searchForm.dateRange.length&&(e.startDate=this.searchForm.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.searchForm.dateRange[1].format("YYYY-MM-DD")),e},handleSearch:function(){this.pagination.current=1,this.loadData()},handleReset:function(){this.searchForm={status:void 0,dateRange:[],username:"",alipayInfo:""},this.pagination.current=1,this.loadData()},handleTableChange:function(e){this.pagination=l(l({},this.pagination),e),this.loadData()},handleApprove:function(){var e=u(s.a.mark((function e(t){var a=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm({title:"确认审核通过",content:"确定要审核通过用户 ".concat(t.username," 的提现申请吗？\n提现金额：¥").concat(this.formatNumber(t.withdrawal_amount)),onOk:function(){var e=u(s.a.mark((function e(){var r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a.$set(t,"approving",!0),e.next=4,a.$http.post("/api/usercenter/admin/approveWithdrawal",{id:t.id});case 4:r=e.sent,i=r.data||r,i.success?(a.$message.success("审核通过成功"),a.loadData()):a.$message.error(i.message||"审核失败"),e.next=13;break;case 9:e.prev=9,e.t0=e["catch"](0),a.$message.error("审核失败，请重试");case 13:return e.prev=13,a.$set(t,"approving",!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,9,13,16]])})));function r(){return e.apply(this,arguments)}return r}()});case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleReject:function(e){this.currentRecord=e,this.rejectReason="",this.showRejectModal=!0},confirmReject:function(){var e=u(s.a.mark((function e(){var t,a;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.rejectReason.trim()){e.next=3;break}return this.$message.warning("请填写拒绝原因"),e.abrupt("return");case 3:return e.prev=3,this.rejecting=!0,e.next=7,this.$http.post("/api/usercenter/admin/rejectWithdrawal",{id:this.currentRecord.id,reason:this.rejectReason.trim()});case 7:t=e.sent,a=t.data||t,a.success?(this.$message.success("审核拒绝成功"),this.showRejectModal=!1,this.loadData()):this.$message.error(a.message||"审核失败"),e.next=16;break;case 12:e.prev=12,e.t0=e["catch"](3),this.$message.error("审核失败，请重试");case 16:return e.prev=16,this.rejecting=!1,e.finish(16);case 19:case"end":return e.stop()}}),e,this,[[3,12,16,19]])})));function t(){return e.apply(this,arguments)}return t}(),handleViewDetail:function(e){this.currentRecord=e,this.showDetailModal=!0},getStatusColor:function(e){var t={1:"orange",2:"green",3:"red",4:"gray"};return t[e]||"volcano"},getStatusText:function(e,t){var a={1:"待审核",2:"已发放",3:"审核拒绝",4:"已取消"},r=a[e]||"未知状态";return 3===e&&t&&(r+="（".concat(t,"）")),r},formatNumber:function(e){return e?parseFloat(e).toFixed(2):"0.00"},formatDateTime:function(e){if(!e)return"-";try{var t=new Date(e);return t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}}}},p=m,h=(a("155d"),a("2877")),f=Object(h["a"])(p,r,i,!1,null,"e299c5fe",null);t["default"]=f.exports},"12bc":function(e,t,a){},"155d":function(e,t,a){"use strict";var r=a("94d2"),i=a.n(r);i.a},"192a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"用户ID",prop:"userId"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.model.userId,callback:function(t){e.$set(e.model,"userId",t)},expression:"model.userId"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"订单ID",prop:"orderId"}},[a("a-input",{attrs:{placeholder:"请输入订单ID"},model:{value:e.model.orderId,callback:function(t){e.$set(e.model,"orderId",t)},expression:"model.orderId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"升级前角色",prop:"fromRoleId"}},[a("a-select",{attrs:{placeholder:"请选择升级前角色",allowClear:""},model:{value:e.model.fromRoleId,callback:function(t){e.$set(e.model,"fromRoleId",t)},expression:"model.fromRoleId"}},e._l(e.roleOptions,(function(t){return a("a-select-option",{key:t.id,attrs:{value:t.id}},[e._v("\n                "+e._s(t.roleName)+"\n              ")])})),1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"升级后角色",prop:"toRoleId"}},[a("a-select",{attrs:{placeholder:"请选择升级后角色",allowClear:""},model:{value:e.model.toRoleId,callback:function(t){e.$set(e.model,"toRoleId",t)},expression:"model.toRoleId"}},e._l(e.roleOptions,(function(t){return a("a-select-option",{key:t.id,attrs:{value:t.id}},[e._v("\n                "+e._s(t.roleName)+"\n              ")])})),1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"会员等级",prop:"memberLevel"}},[a("a-select",{attrs:{placeholder:"请选择会员等级"},model:{value:e.model.memberLevel,callback:function(t){e.$set(e.model,"memberLevel",t)},expression:"model.memberLevel"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通用户")]),a("a-select-option",{attrs:{value:2}},[e._v("VIP会员")]),a("a-select-option",{attrs:{value:3}},[e._v("SVIP会员")]),a("a-select-option",{attrs:{value:4}},[e._v("至尊会员")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"订阅时长(月)",prop:"durationMonths"}},[a("a-input-number",{attrs:{min:1,max:120,placeholder:"请输入订阅时长"},model:{value:e.model.durationMonths,callback:function(t){e.$set(e.model,"durationMonths",t)},expression:"model.durationMonths"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"订阅金额",prop:"amount"}},[a("a-input-number",{attrs:{min:0,precision:2,placeholder:"请输入订阅金额"},model:{value:e.model.amount,callback:function(t){e.$set(e.model,"amount",t)},expression:"model.amount"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("生效中")]),a("a-select-option",{attrs:{value:2}},[e._v("已过期")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"开始时间",prop:"startTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择开始时间"},model:{value:e.model.startTime,callback:function(t){e.$set(e.model,"startTime",t)},expression:"model.startTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"结束时间",prop:"endTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择结束时间"},model:{value:e.model.endTime,callback:function(t){e.$set(e.model,"endTime",t)},expression:"model.endTime"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=a("c1df"),o=a.n(s),l={name:"AicgUserMembershipHistoryModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),roleOptions:[],validatorRules:{userId:[{required:!0,message:"请输入用户ID!"}],toRoleId:[{required:!0,message:"请选择升级后角色!"}],memberLevel:[{required:!0,message:"请选择会员等级!"}],durationMonths:[{required:!0,message:"请输入订阅时长!"}],amount:[{required:!0,message:"请输入订阅金额!"}],startTime:[{required:!0,message:"请选择开始时间!"}],endTime:[{required:!0,message:"请选择结束时间!"}]},url:{add:"/demo/membershiphistory/add",edit:"/demo/membershiphistory/edit",queryById:"/demo/membershiphistory/queryById"}}},created:function(){this.loadRoleOptions()},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={userId:t.model.userId,orderId:t.model.orderId,fromRoleId:t.model.fromRoleId,toRoleId:t.model.toRoleId,memberLevel:t.model.memberLevel,durationMonths:t.model.durationMonths,amount:t.model.amount,status:t.model.status};t.form.setFieldsValue(e),t.model.startTime&&(t.model.startTime=o()(t.model.startTime)),t.model.endTime&&(t.model.endTime=o()(t.model.endTime))})),e.id?this.title="编辑":this.title="新增"},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var s=Object.assign(e.model);s.startTime&&(s.startTime=s.startTime.format("YYYY-MM-DD HH:mm:ss")),s.endTime&&(s.endTime=s.endTime.format("YYYY-MM-DD HH:mm:ss")),Object(n["h"])(r,s,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},loadRoleOptions:function(){var e=this;Object(n["c"])("/sys/role/list").then((function(t){t.success&&(e.roleOptions=t.result.records||[])}))}}},c=l,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},"1a05":function(e,t,a){},"1de8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐人ID",prop:"referrerId"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.model.referrerId,callback:function(t){e.$set(e.model,"referrerId",t)},expression:"model.referrerId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"被推荐人ID",prop:"refereeId"}},[a("a-input",{attrs:{placeholder:"请输入被推荐人ID"},model:{value:e.model.refereeId,callback:function(t){e.$set(e.model,"refereeId",t)},expression:"model.refereeId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐码",prop:"referralCode"}},[a("a-input",{attrs:{placeholder:"请输入推荐码"},model:{value:e.model.referralCode,callback:function(t){e.$set(e.model,"referralCode",t)},expression:"model.referralCode"}},[a("a-button",{attrs:{slot:"suffix"},on:{click:e.generateCode},slot:"suffix"},[e._v("生成")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待确认")]),a("a-select-option",{attrs:{value:2}},[e._v("已确认")]),a("a-select-option",{attrs:{value:3}},[e._v("已奖励")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"注册时间",prop:"registerTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择注册时间"},model:{value:e.model.registerTime,callback:function(t){e.$set(e.model,"registerTime",t)},expression:"model.registerTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"首次充值时间",prop:"firstRechargeTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择首次充值时间"},model:{value:e.model.firstRechargeTime,callback:function(t){e.$set(e.model,"firstRechargeTime",t)},expression:"model.firstRechargeTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"首次充值金额",prop:"firstRechargeAmount"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入首次充值金额"},model:{value:e.model.firstRechargeAmount,callback:function(t){e.$set(e.model,"firstRechargeAmount",t)},expression:"model.firstRechargeAmount"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=a("c1df"),o=a.n(s),l={name:"AicgUserReferralModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{referrerId:[{required:!0,message:"请输入推荐人ID!"}],refereeId:[{required:!0,message:"请输入被推荐人ID!"}],referralCode:[{required:!0,message:"请输入推荐码!"}],registerTime:[{required:!0,message:"请选择注册时间!"}]},url:{add:"/demo/referral/add",edit:"/demo/referral/edit",queryById:"/demo/referral/queryById"}}},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={referrerId:t.model.referrerId,refereeId:t.model.refereeId,referralCode:t.model.referralCode,status:t.model.status,firstRechargeAmount:t.model.firstRechargeAmount};t.form.setFieldsValue(e),t.model.registerTime&&(t.model.registerTime=o()(t.model.registerTime)),t.model.firstRechargeTime&&(t.model.firstRechargeTime=o()(t.model.firstRechargeTime))})),e.id?this.title="编辑":(this.title="新增",this.model.registerTime=o()(),this.model.status=1)},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var s=Object.assign(e.model);s.registerTime&&(s.registerTime=s.registerTime.format("YYYY-MM-DD HH:mm:ss")),s.firstRechargeTime&&(s.firstRechargeTime=s.firstRechargeTime.format("YYYY-MM-DD HH:mm:ss")),Object(n["h"])(r,s,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},generateCode:function(){var e=this;this.model.referrerId?Object(n["c"])("/demo/referral/generateCode?userId=".concat(this.model.referrerId)).then((function(t){t.success?(e.model.referralCode=t.result,e.$message.success("推荐码生成成功")):e.$message.error(t.message)})):this.$message.warning("请先输入推荐人ID")}}},c=l,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},"24e6":function(e,t,a){},"266d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-plub-author-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("d901"),s={name:"AigcPlubAuthorModal",components:{AigcPlubAuthorForm:n["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},"2b44":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-video-tutorial-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("7bf4"),s={name:"AigcVideoTutorialModal",components:{AigcVideoTutorialForm:n["default"]},data:function(){return{title:"操作",width:896,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("5672"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"3b54e72b",null);t["default"]=c.exports},"2f42":function(e,t,a){},"31bf":function(e,t,a){},"33d4":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-video-teacher-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("aff5"),s={name:"AigcVideoTeacherModal",components:{AigcVideoTeacherForm:n["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},3450:function(e,t,a){"use strict";var r=a("1a05"),i=a.n(r);i.a},3491:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否系列视频"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否系列视频",dictCode:"isTrue"},model:{value:e.queryParam.isseries,callback:function(t){e.$set(e.queryParam,"isseries",t)},expression:"queryParam.isseries"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"系列名称"}},[a("a-input",{attrs:{placeholder:"请输入系列名称"},model:{value:e.queryParam.seriesname,callback:function(t){e.$set(e.queryParam,"seriesname",t)},expression:"queryParam.seriesname"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"讲师"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择讲师",dictCode:"aigc_video_teacher,teachername,id"},model:{value:e.queryParam.teacher,callback:function(t){e.$set(e.queryParam,"teacher",t)},expression:"queryParam.teacher"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"点击量"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.clicknum_begin,callback:function(t){e.$set(e.queryParam,"clicknum_begin",t)},expression:"queryParam.clicknum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.clicknum_end,callback:function(t){e.$set(e.queryParam,"clicknum_end",t)},expression:"queryParam.clicknum_end"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"标题"}},[a("a-input",{attrs:{placeholder:"请输入标题"},model:{value:e.queryParam.titile,callback:function(t){e.$set(e.queryParam,"titile",t)},expression:"queryParam.titile"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"设置等级"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择设置等级",dictCode:"setLevel"},model:{value:e.queryParam.setlevel,callback:function(t){e.$set(e.queryParam,"setlevel",t)},expression:"queryParam.setlevel"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"课程标签"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.tag_begin,callback:function(t){e.$set(e.queryParam,"tag_begin",t)},expression:"queryParam.tag_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.tag_end,callback:function(t){e.$set(e.queryParam,"tag_end",t)},expression:"queryParam.tag_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"上传日期"}},[a("j-date",{staticClass:"query-group-cust",attrs:{placeholder:"请选择开始日期"},model:{value:e.queryParam.uptime_begin,callback:function(t){e.$set(e.queryParam,"uptime_begin",t)},expression:"queryParam.uptime_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("j-date",{staticClass:"query-group-cust",attrs:{placeholder:"请选择结束日期"},model:{value:e.queryParam.uptime_end,callback:function(t){e.$set(e.queryParam,"uptime_end",t)},expression:"queryParam.uptime_end"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("视频教程")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-video-tutorial-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("b7b2"),l=(a("89f2"),{name:"AigcVideoTutorialList",mixins:[s["a"],n["b"]],components:{AigcVideoTutorialModal:o["default"]},data:function(){return{description:"视频教程管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"是否系列视频",align:"center",dataIndex:"isseries_dictText"},{title:"系列名称",align:"center",dataIndex:"seriesname"},{title:"视频文件",align:"center",dataIndex:"videofile",scopedSlots:{customRender:"fileSlot"}},{title:"讲师",align:"center",dataIndex:"teacher_dictText"},{title:"点击量",align:"center",dataIndex:"clicknum"},{title:"标题",align:"center",dataIndex:"titile"},{title:"设置等级",align:"center",dataIndex:"setlevel_dictText"},{title:"课程介绍",align:"center",dataIndex:"intro"},{title:"课程标签",align:"center",dataIndex:"tag_dictText"},{title:"上传日期",align:"center",dataIndex:"uptime",customRender:function(e){return e?e.length>10?e.substr(0,10):e:""}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/videotutorial/aigcVideoTutorial/list",delete:"/videotutorial/aigcVideoTutorial/delete",deleteBatch:"/videotutorial/aigcVideoTutorial/deleteBatch",exportXlsUrl:"/videotutorial/aigcVideoTutorial/exportXls",importExcelUrl:"videotutorial/aigcVideoTutorial/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"isseries",text:"是否系列视频",dictCode:"isTrue"}),e.push({type:"string",value:"seriesname",text:"系列名称",dictCode:""}),e.push({type:"string",value:"videofile",text:"视频文件",dictCode:""}),e.push({type:"string",value:"teacher",text:"讲师",dictCode:"aigc_video_teacher,teachername,id"}),e.push({type:"int",value:"clicknum",text:"点击量",dictCode:""}),e.push({type:"string",value:"titile",text:"标题",dictCode:""}),e.push({type:"string",value:"setlevel",text:"设置等级",dictCode:"setLevel"}),e.push({type:"string",value:"intro",text:"课程介绍",dictCode:""}),e.push({type:"string",value:"tag",text:"课程标签",dictCode:"tag"}),e.push({type:"date",value:"uptime",text:"上传日期"}),this.superFieldList=e}}}),c=l,d=(a("e292"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"6aaa529c",null);t["default"]=u.exports},"34d1":function(e,t,a){},"384e":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户昵称"}},[a("a-input",{attrs:{placeholder:"请输入用户昵称"},model:{value:e.queryParam.userNickname,callback:function(t){e.$set(e.queryParam,"userNickname",t)},expression:"queryParam.userNickname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件名称"}},[a("a-input",{attrs:{placeholder:"请输入插件名称"},model:{value:e.queryParam.pluginName,callback:function(t){e.$set(e.queryParam,"pluginName",t)},expression:"queryParam.pluginName"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件标识"}},[a("a-input",{attrs:{placeholder:"请输入插件Key"},model:{value:e.queryParam.pluginKey,callback:function(t){e.$set(e.queryParam,"pluginKey",t)},expression:"queryParam.pluginKey"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"调用状态"}},[a("a-select",{attrs:{placeholder:"请选择调用状态",allowClear:""},model:{value:e.queryParam.responseStatus,callback:function(t){e.$set(e.queryParam,"responseStatus",t)},expression:"queryParam.responseStatus"}},[a("a-select-option",{attrs:{value:200}},[e._v("成功")]),a("a-select-option",{attrs:{value:400}},[e._v("请求错误")]),a("a-select-option",{attrs:{value:401}},[e._v("未授权")]),a("a-select-option",{attrs:{value:500}},[e._v("服务器错误")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"调用时间"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",placeholder:"['开始时间', '结束时间']"},model:{value:e.queryParam.callTimeRange,callback:function(t){e.$set(e.queryParam,"callTimeRange",t)},expression:"queryParam.callTimeRange"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("插件使用记录")}}},[e._v("导出")]),a("a-button",{attrs:{type:"primary",icon:"bar-chart"},on:{click:e.showStats}},[e._v("统计分析")]),e.selectedRowKeys.length>0?a("a-button",{attrs:{type:"danger",icon:"delete"},on:{click:e.batchDelete}},[e._v("批量删除")]):e._e()],1),a("div",[a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:e.rowSelection},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"responseStatus",fn:function(t){return[a("a-tag",{attrs:{color:200===t?"green":"red"}},[e._v("\n          "+e._s(200===t?"成功":"失败("+t+")")+"\n        ")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)}}])})],1),a("a-modal",{attrs:{title:"插件使用统计分析",width:1200,visible:e.statsVisible,footer:null},on:{cancel:function(t){e.statsVisible=!1}}},[e.statsData?a("div",[a("a-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总调用次数",value:e.statsData.totalCalls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功调用",value:e.statsData.successCalls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"失败调用",value:e.statsData.errorCalls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功率",value:e.statsData.successRate,suffix:"%",precision:2}})],1)],1),a("a-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总消耗Token",value:e.statsData.totalTokens}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总消耗金额",value:e.statsData.totalCost,prefix:"¥",precision:2}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"平均响应时间",value:e.statsData.avgResponseTime,suffix:"ms"}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"活跃用户数",value:e.statsData.activeUsers}})],1)],1),a("a-divider"),a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:12}},[a("h4",[e._v("热门插件排行")]),a("a-table",{attrs:{columns:e.hotPluginColumns,dataSource:e.statsData.hotPlugins,pagination:!1,size:"small"}})],1),a("a-col",{attrs:{span:12}},[a("h4",[e._v("活跃用户排行")]),a("a-table",{attrs:{columns:e.activeUserColumns,dataSource:e.statsData.activeUserList,pagination:!1,size:"small"}})],1)],1)],1):e._e()]),a("a-modal",{attrs:{title:"插件使用详情",width:800,visible:e.detailVisible,footer:null},on:{cancel:function(t){e.detailVisible=!1}}},[e.detailData?a("div",[a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.detailData.user_id))]),a("a-descriptions-item",{attrs:{label:"用户昵称"}},[e._v(e._s(e.detailData.userNickname||"-"))]),a("a-descriptions-item",{attrs:{label:"API密钥"}},[e._v(e._s(e.detailData.api_key))]),a("a-descriptions-item",{attrs:{label:"插件名称"}},[e._v(e._s(e.detailData.plugin_name))]),a("a-descriptions-item",{attrs:{label:"插件标识"}},[e._v(e._s(e.detailData.plugin_key))]),a("a-descriptions-item",{attrs:{label:"API接口"}},[e._v(e._s(e.detailData.api_endpoint))]),a("a-descriptions-item",{attrs:{label:"请求方法"}},[e._v(e._s(e.detailData.api_method))]),a("a-descriptions-item",{attrs:{label:"响应状态"}},[a("a-tag",{attrs:{color:200===e.detailData.response_status?"green":"red"}},[e._v("\n            "+e._s(e.detailData.response_status)+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"响应时间"}},[e._v(e._s(e.detailData.response_time)+"ms")]),a("a-descriptions-item",{attrs:{label:"消耗Token"}},[e._v(e._s(e.detailData.tokens_used||"-"))]),a("a-descriptions-item",{attrs:{label:"消耗金额"}},[e._v("¥"+e._s(e.detailData.cost_amount||"0.00"))]),a("a-descriptions-item",{attrs:{label:"IP地址"}},[e._v(e._s(e.detailData.ip_address))]),a("a-descriptions-item",{attrs:{label:"调用时间"}},[e._v(e._s(e.formatDateTime(e.detailData.call_time)))])],1),a("a-divider"),a("h4",[e._v("请求参数")]),a("pre",{staticStyle:{background:"#f5f5f5",padding:"10px","border-radius":"4px","max-height":"200px","overflow-y":"auto"}},[e._v(e._s(e.detailData.request_params))]),e.detailData.error_message?a("div",[a("h4",[e._v("错误信息")]),a("a-alert",{attrs:{message:e.detailData.error_message,type:"error"}})],1):e._e()],1):e._e()])],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o={name:"AicgUserPluginUsageList",mixins:[s["a"],n["b"]],components:{},data:function(){return{description:"用户插件使用记录管理页面",statsVisible:!1,statsData:null,detailVisible:!1,detailData:null,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"user_id",width:120,ellipsis:!0},{title:"用户昵称",align:"center",dataIndex:"userNickname",width:120,ellipsis:!0},{title:"插件名称",align:"center",dataIndex:"plugin_name",width:150,ellipsis:!0},{title:"插件标识",align:"center",dataIndex:"plugin_key",width:120,ellipsis:!0},{title:"API接口",align:"center",dataIndex:"api_endpoint",width:200,ellipsis:!0},{title:"调用状态",align:"center",dataIndex:"response_status",width:100,scopedSlots:{customRender:"responseStatus"}},{title:"响应时间",align:"center",dataIndex:"response_time",width:100,customRender:function(e){return e?"".concat(e,"ms"):"-"}},{title:"消耗Token",align:"center",dataIndex:"tokens_used",width:100,customRender:function(e){return e||"-"}},{title:"消耗金额",align:"center",dataIndex:"cost_amount",width:100,customRender:function(e){return e?"¥".concat(e):"-"}},{title:"调用时间",align:"center",dataIndex:"call_time",width:150,customRender:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:120,scopedSlots:{customRender:"action"}}],url:{list:"/demo/apiusage/pluginUsageList",delete:"/demo/apiusage/delete",deleteBatch:"/demo/apiusage/deleteBatch",exportXlsUrl:"/demo/apiusage/exportXls",importExcelUrl:"demo/apiusage/importExcel",stats:"/demo/apiusage/getPluginStats"},dictOptions:{},superFieldList:[],hotPluginColumns:[{title:"排名",dataIndex:"rank",width:60,align:"center"},{title:"插件名称",dataIndex:"pluginName",ellipsis:!0},{title:"调用次数",dataIndex:"callCount",width:100,align:"center"}],activeUserColumns:[{title:"排名",dataIndex:"rank",width:60,align:"center"},{title:"用户ID",dataIndex:"userId",ellipsis:!0},{title:"调用次数",dataIndex:"callCount",width:100,align:"center"}]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"user_id",text:"用户ID"}),e.push({type:"string",value:"userNickname",text:"用户昵称"}),e.push({type:"string",value:"plugin_name",text:"插件名称"}),e.push({type:"string",value:"plugin_key",text:"插件标识"}),e.push({type:"string",value:"api_endpoint",text:"API接口地址"}),e.push({type:"string",value:"api_method",text:"请求方法"}),e.push({type:"int",value:"response_status",text:"响应状态码"}),e.push({type:"int",value:"response_time",text:"响应时间"}),e.push({type:"int",value:"tokens_used",text:"消耗Token数量"}),e.push({type:"BigDecimal",value:"cost_amount",text:"消耗金额"}),e.push({type:"Date",value:"call_time",text:"调用时间"}),this.superFieldList=e},showStats:function(){var e=this;this.loading=!0,this.$http.get(this.url.stats).then((function(t){e.loading=!1,t.success?(e.statsData=t.result,e.statsVisible=!0):e.$message.error(t.message||"获取统计数据失败")})).catch((function(t){e.loading=!1,e.$message.error("获取统计数据失败")}))},handleDetail:function(e){this.detailData=e,this.detailVisible=!0},batchDelete:function(){var e=this;0!==this.selectedRowKeys.length?this.$confirm({title:"确认删除",content:"确定要删除选中的 ".concat(this.selectedRowKeys.length," 条记录吗？"),onOk:function(){e.loading=!0,e.$http.delete(e.url.deleteBatch,{data:e.selectedRowKeys}).then((function(t){e.loading=!1,t.success?(e.$message.success("删除成功"),e.loadData(),e.onClearSelected()):e.$message.error(t.message||"删除失败")})).catch((function(t){e.loading=!1,e.$message.error("删除失败")}))}}):this.$message.warning("请选择要删除的记录")},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"},loadData:function(e){var t=this;if(this.url.list){var a=Object.assign({},this.queryParam,this.isorter,this.filters);this.queryParam.callTimeRange&&2===this.queryParam.callTimeRange.length&&(a.callTimeStart=this.queryParam.callTimeRange[0].format("YYYY-MM-DD"),a.callTimeEnd=this.queryParam.callTimeRange[1].format("YYYY-MM-DD"),delete a.callTimeRange),a.hasPluginInfo=!0,a.pageNo=this.ipagination.current,a.pageSize=this.ipagination.pageSize,this.loading=!0,this.$http.get(this.url.list,{params:a}).then((function(e){t.loading=!1,e.success?(t.dataSource=e.result.records||e.result,t.ipagination.total=e.result.total):t.$message.error(e.message||"查询失败")})).catch((function(e){t.loading=!1,t.$message.error("查询失败")}))}else this.$message.error("请设置url.list属性!")}}},l=o,c=(a("d403"),a("2877")),d=Object(c["a"])(l,r,i,!1,null,"3b48e4c0",null);t["default"]=d.exports},"388b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-form-model-item",{attrs:{label:"用户ID",prop:"userId"}},[a("a-input",{attrs:{placeholder:"请输入用户ID",disabled:!!e.model.id},model:{value:e.model.userId,callback:function(t){e.$set(e.model,"userId",t)},expression:"model.userId"}})],1),a("a-form-model-item",{attrs:{label:"API密钥",prop:"apiKey"}},[a("a-input",{attrs:{placeholder:"请输入API密钥",disabled:!!e.model.id},model:{value:e.model.apiKey,callback:function(t){e.$set(e.model,"apiKey",t)},expression:"model.apiKey"}})],1),a("a-form-model-item",{attrs:{label:"API接口",prop:"apiEndpoint"}},[a("a-input",{attrs:{placeholder:"请输入API接口地址",disabled:!!e.model.id},model:{value:e.model.apiEndpoint,callback:function(t){e.$set(e.model,"apiEndpoint",t)},expression:"model.apiEndpoint"}})],1),a("a-form-model-item",{attrs:{label:"请求方法",prop:"apiMethod"}},[a("a-select",{attrs:{placeholder:"请选择请求方法",disabled:!!e.model.id},model:{value:e.model.apiMethod,callback:function(t){e.$set(e.model,"apiMethod",t)},expression:"model.apiMethod"}},[a("a-select-option",{attrs:{value:"GET"}},[e._v("GET")]),a("a-select-option",{attrs:{value:"POST"}},[e._v("POST")]),a("a-select-option",{attrs:{value:"PUT"}},[e._v("PUT")]),a("a-select-option",{attrs:{value:"DELETE"}},[e._v("DELETE")])],1)],1),a("a-form-model-item",{attrs:{label:"插件ID",prop:"pluginId"}},[a("a-input",{attrs:{placeholder:"请输入插件ID",disabled:!!e.model.id},model:{value:e.model.pluginId,callback:function(t){e.$set(e.model,"pluginId",t)},expression:"model.pluginId"}})],1),a("a-form-model-item",{attrs:{label:"插件标识",prop:"pluginKey"}},[a("a-input",{attrs:{placeholder:"请输入插件Key",disabled:!!e.model.id},model:{value:e.model.pluginKey,callback:function(t){e.$set(e.model,"pluginKey",t)},expression:"model.pluginKey"}})],1),a("a-form-model-item",{attrs:{label:"插件名称",prop:"pluginName"}},[a("a-input",{attrs:{placeholder:"请输入插件名称",disabled:!!e.model.id},model:{value:e.model.pluginName,callback:function(t){e.$set(e.model,"pluginName",t)},expression:"model.pluginName"}})],1),a("a-form-model-item",{attrs:{label:"响应状态",prop:"responseStatus"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入响应状态码",disabled:!!e.model.id},model:{value:e.model.responseStatus,callback:function(t){e.$set(e.model,"responseStatus",t)},expression:"model.responseStatus"}})],1),a("a-form-model-item",{attrs:{label:"响应时间(ms)",prop:"responseTime"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入响应时间",disabled:!!e.model.id},model:{value:e.model.responseTime,callback:function(t){e.$set(e.model,"responseTime",t)},expression:"model.responseTime"}})],1),a("a-form-model-item",{attrs:{label:"消耗Token",prop:"tokensUsed"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入消耗Token数量",disabled:!!e.model.id},model:{value:e.model.tokensUsed,callback:function(t){e.$set(e.model,"tokensUsed",t)},expression:"model.tokensUsed"}})],1),a("a-form-model-item",{attrs:{label:"消耗金额",prop:"costAmount"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入消耗金额",precision:2,disabled:!!e.model.id},model:{value:e.model.costAmount,callback:function(t){e.$set(e.model,"costAmount",t)},expression:"model.costAmount"}})],1),a("a-form-model-item",{attrs:{label:"IP地址",prop:"ipAddress"}},[a("a-input",{attrs:{placeholder:"请输入IP地址",disabled:!!e.model.id},model:{value:e.model.ipAddress,callback:function(t){e.$set(e.model,"ipAddress",t)},expression:"model.ipAddress"}})],1),a("a-form-model-item",{attrs:{label:"用户代理",prop:"userAgent"}},[a("a-textarea",{attrs:{placeholder:"请输入用户代理信息",rows:2,disabled:!!e.model.id},model:{value:e.model.userAgent,callback:function(t){e.$set(e.model,"userAgent",t)},expression:"model.userAgent"}})],1),a("a-form-model-item",{attrs:{label:"请求参数",prop:"requestParams"}},[a("a-textarea",{attrs:{placeholder:"请输入请求参数",rows:4,disabled:!!e.model.id},model:{value:e.model.requestParams,callback:function(t){e.$set(e.model,"requestParams",t)},expression:"model.requestParams"}})],1),e.model.errorMessage?a("a-form-model-item",{attrs:{label:"错误信息",prop:"errorMessage"}},[a("a-textarea",{attrs:{placeholder:"错误信息",rows:3,disabled:!0},model:{value:e.model.errorMessage,callback:function(t){e.$set(e.model,"errorMessage",t)},expression:"model.errorMessage"}})],1):e._e(),a("a-form-model-item",{attrs:{label:"调用时间",prop:"callTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择调用时间",format:"YYYY-MM-DD HH:mm:ss","show-time":"",disabled:!!e.model.id},model:{value:e.model.callTime,callback:function(t){e.$set(e.model,"callTime",t)},expression:"model.callTime"}})],1)],1)],1)],1)},i=[],n=a("0fea"),s={name:"AicgPluginUsageModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{userId:[{required:!0,message:"请输入用户ID!"}],apiEndpoint:[{required:!0,message:"请输入API接口地址!"}],apiMethod:[{required:!0,message:"请选择请求方法!"}],responseStatus:[{required:!0,message:"请输入响应状态码!"}]},url:{add:"/demo/apiusage/add",edit:"/demo/apiusage/edit"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={userId:t.model.userId,apiKey:t.model.apiKey,apiEndpoint:t.model.apiEndpoint,apiMethod:t.model.apiMethod,pluginId:t.model.pluginId,pluginKey:t.model.pluginKey,pluginName:t.model.pluginName,responseStatus:t.model.responseStatus,responseTime:t.model.responseTime,tokensUsed:t.model.tokensUsed,costAmount:t.model.costAmount,ipAddress:t.model.ipAddress,userAgent:t.model.userAgent,requestParams:t.model.requestParams,errorMessage:t.model.errorMessage,callTime:t.model.callTime};t.form.setFieldsValue(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},popupCallback:function(e){this.model=Object.assign(this.model,e)}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},"3ac8":function(e,t,a){},"3d1f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"程序类型",prop:"programType"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择程序类型",dictCode:"program_type",disabled:!!e.model.id},on:{change:e.handleProgramTypeChange},model:{value:e.model.programType,callback:function(t){e.$set(e.model,"programType",t)},expression:"model.programType"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"版本号",prop:"versionNumber"}},[a("a-input",{attrs:{placeholder:"请输入版本号，如：1.0.0"},model:{value:e.model.versionNumber,callback:function(t){e.$set(e.model,"versionNumber",t)},expression:"model.versionNumber"}},[a("a-button",{attrs:{slot:"addonAfter",disabled:!e.model.programType},on:{click:e.getSuggestedVersion},slot:"addonAfter"},[e._v("建议")])],1)],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"发布日期",prop:"releaseDate"}},[a("j-date",{attrs:{placeholder:"请选择发布日期","show-time":!0,"date-format":"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.releaseDate,callback:function(t){e.$set(e.model,"releaseDate",t)},expression:"model.releaseDate"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否最新版本",prop:"isLatest"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择",dictCode:"isTrue"},model:{value:e.model.isLatest,callback:function(t){e.$set(e.model,"isLatest",t)},expression:"model.isLatest"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择状态",dictCode:"valid_status"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"更新内容",prop:"updateContent"}},[a("a-textarea",{attrs:{rows:6,placeholder:"请输入详细的更新内容说明"},model:{value:e.model.updateContent,callback:function(t){e.$set(e.model,"updateContent",t)},expression:"model.updateContent"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"下载链接",prop:"downloadUrl"}},[a("a-input",{attrs:{placeholder:"请输入下载链接，如：https://aigcview.com/download/desktop/v1.0.0"},model:{value:e.model.downloadUrl,callback:function(t){e.$set(e.model,"downloadUrl",t)},expression:"model.downloadUrl"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),a("c1df")),o=a.n(s),l={name:"AigcVersionControlModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,validatorRules:{programType:[{required:!0,message:"请选择程序类型!"}],versionNumber:[{required:!0,message:"请输入版本号!"},{pattern:/^\d+\.\d+\.\d+$/,message:"版本号格式不正确，请使用x.y.z格式!"},{validator:this.validateVersionNumber}],releaseDate:[{required:!0,message:"请选择发布日期!"}],updateContent:[{max:5e3,message:"更新内容不能超过5000个字符!"}],downloadUrl:[{required:!0,message:"请输入下载链接!"},{type:"url",message:"请输入正确的URL格式!"}]},url:{add:"/aigcview/versioncontrol/add",edit:"/aigcview/versioncontrol/edit",queryById:"/aigcview/versioncontrol/queryById",checkVersionExists:"/aigcview/versioncontrol/checkVersionExists",getNextSuggestedVersion:"/aigcview/versioncontrol/getNextSuggestedVersion"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){this.model=Object.assign({},e),this.visible=!0,this.model.id||(this.model.isLatest=2,this.model.status=1,this.model.releaseDate=o()().format("YYYY-MM-DD HH:mm:ss"))},close:function(){this.$refs.form.resetFields(),this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},validateVersionNumber:function(e,t,a){if(t)if(this.model.programType){var r={programType:this.model.programType,versionNumber:t,excludeId:this.model.id||""};Object(n["c"])(this.url.checkVersionExists,r).then((function(e){e.success&&e.result?a(new Error("该程序类型下版本号已存在!")):a()})).catch((function(){a()}))}else a();else a()},getSuggestedVersion:function(){var e=this;if(this.model.programType){var t={programType:this.model.programType,versionType:"patch"};Object(n["c"])(this.url.getNextSuggestedVersion,t).then((function(t){t.success?(e.model.versionNumber=t.result,e.$message.success("已自动填入建议版本号")):e.$message.warning("获取建议版本号失败")}))}else this.$message.warning("请先选择程序类型")},handleProgramTypeChange:function(){this.model.versionNumber=""}}},c=l,d=(a("db58"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"f92d3798",null);t["default"]=u.exports},"3dbb":function(e,t,a){},4638:function(e,t,a){},"463f":function(e,t,a){"use strict";var r=a("a3f8"),i=a.n(r);i.a},"484b":function(e,t,a){},"4de9":function(e,t,a){},5672:function(e,t,a){"use strict";var r=a("12bc"),i=a.n(r);i.a},"5d40":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件名称"}},[a("a-input",{attrs:{placeholder:"请输入插件名称"},model:{value:e.queryParam.plubname,callback:function(t){e.$set(e.queryParam,"plubname",t)},expression:"queryParam.plubname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件创作者"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择插件创作者",dictCode:"aigc_plub_author,authorname,id"},model:{value:e.queryParam.plubwrite,callback:function(t){e.$set(e.queryParam,"plubwrite",t)},expression:"queryParam.plubwrite"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件状态"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择插件状态",dictCode:"plugin_status"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否组合插件"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否组合插件",dictCode:"isTrue"},model:{value:e.queryParam.isCombined,callback:function(t){e.$set(e.queryParam,"isCombined",t)},expression:"queryParam.isCombined"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"组合插件名"}},[a("a-input",{attrs:{placeholder:"请输入组合插件名"},model:{value:e.queryParam.combinedName,callback:function(t){e.$set(e.queryParam,"combinedName",t)},expression:"queryParam.combinedName"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"收益金额"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.income_begin,callback:function(t){e.$set(e.queryParam,"income_begin",t)},expression:"queryParam.income_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.income_end,callback:function(t){e.$set(e.queryParam,"income_end",t)},expression:"queryParam.income_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"调用次数"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.usernum_begin,callback:function(t){e.$set(e.queryParam,"usernum_begin",t)},expression:"queryParam.usernum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.usernum_end,callback:function(t){e.$set(e.queryParam,"usernum_end",t)},expression:"queryParam.usernum_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"需要金额"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.neednum_begin,callback:function(t){e.$set(e.queryParam,"neednum_begin",t)},expression:"queryParam.neednum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.neednum_end,callback:function(t){e.$set(e.queryParam,"neednum_end",t)},expression:"queryParam.neednum_end"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("插件商城")}}},[e._v("导出")]),e.isAdmin?[a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()]:e._e()],2),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"tutorialLinkSlot",fn:function(t){return[t?a("a",{staticStyle:{color:"#1890ff"},attrs:{href:t,target:"_blank"}},[a("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:"link"}}),e._v("\n          查看教程\n        ")],1):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("暂无")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),e.isAdmin?[a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)]:e._e()],2)}}])})],1),a("aigc-plub-shop-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("9caa"),l=(a("89f2"),{name:"AigcPlubShopList",mixins:[s["a"],n["b"]],components:{AigcPlubShopModal:o["default"]},data:function(){return{description:"插件商城管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"插件名称",align:"center",dataIndex:"plubname"},{title:"图片",align:"center",dataIndex:"plubimg",scopedSlots:{customRender:"imgSlot"}},{title:"插件创作者",align:"center",dataIndex:"plubwrite_dictText"},{title:"插件介绍",align:"center",dataIndex:"plubinfo"},{title:"插件分类",align:"center",dataIndex:"plubCategory_dictText"},{title:"插件状态",align:"center",dataIndex:"status_dictText"},{title:"是否组合插件",align:"center",dataIndex:"isCombined_dictText",width:120},{title:"组合插件名",align:"center",dataIndex:"combinedName",width:150,customRender:function(e){return e||"-"}},{title:"组合插件图片",align:"center",dataIndex:"combinedImage",width:120,scopedSlots:{customRender:"imgSlot"}},{title:"插件唯一标识",align:"center",dataIndex:"pluginKey"},{title:"教程链接",align:"center",dataIndex:"tutorialLink",scopedSlots:{customRender:"tutorialLinkSlot"}},{title:"排序权重",align:"center",dataIndex:"sortOrder"},{title:"插件教程视频",align:"center",dataIndex:"plubvideo",scopedSlots:{customRender:"fileSlot"}},{title:"收益金额",align:"center",dataIndex:"income",customRender:function(e){return e?"¥"+parseFloat(e).toFixed(2):"¥0.00"}},{title:"调用次数",align:"center",dataIndex:"usernum"},{title:"需要金额",align:"center",dataIndex:"neednum",customRender:function(e){return e?"¥"+parseFloat(e).toFixed(2):"¥0.00"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:this.isAdmin?200:80,scopedSlots:{customRender:"action"}}],url:{list:"/plubshop/aigcPlubShop/list",delete:"/plubshop/aigcPlubShop/delete",deleteBatch:"/plubshop/aigcPlubShop/deleteBatch",exportXlsUrl:"/plubshop/aigcPlubShop/exportXls",importExcelUrl:"plubshop/aigcPlubShop/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)},isAdmin:function(){var e=localStorage.getItem("userRole");return e&&e.toLowerCase().includes("admin")}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"plubname",text:"插件名称",dictCode:""}),e.push({type:"string",value:"plubimg",text:"图片",dictCode:""}),e.push({type:"string",value:"plubwrite",text:"插件创作者",dictCode:"aigc_plub_author,authorname,id"}),e.push({type:"string",value:"plubinfo",text:"插件介绍",dictCode:""}),e.push({type:"string",value:"plubContent",text:"插件详细内容",dictCode:""}),e.push({type:"string",value:"plubCategory",text:"插件分类",dictCode:"plugin_category"}),e.push({type:"int",value:"status",text:"插件状态",dictCode:"plugin_status"}),e.push({type:"int",value:"isCombined",text:"是否组合插件",dictCode:"isTrue"}),e.push({type:"string",value:"combinedName",text:"组合插件名",dictCode:""}),e.push({type:"string",value:"combinedDescription",text:"组合插件介绍",dictCode:""}),e.push({type:"string",value:"combinedImage",text:"组合插件图片",dictCode:""}),e.push({type:"string",value:"pluginKey",text:"插件唯一标识",dictCode:""}),e.push({type:"int",value:"sortOrder",text:"排序权重",dictCode:""}),e.push({type:"string",value:"plubvideo",text:"插件教程视频",dictCode:""}),e.push({type:"BigDecimal",value:"income",text:"收益金额",dictCode:""}),e.push({type:"int",value:"usernum",text:"调用次数",dictCode:""}),e.push({type:"BigDecimal",value:"neednum",text:"需要金额",dictCode:""}),this.superFieldList=e}}}),c=l,d=(a("08be"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"689421f9",null);t["default"]=u.exports},6198:function(e,t,a){},"63be":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否组合插件",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-dict-select-tag",{attrs:{dictCode:"isTrue",placeholder:"请选择是否为组合插件"},model:{value:e.model.isCombined,callback:function(t){e.$set(e.model,"isCombined",t)},expression:"model.isCombined"}})],1)],1),e.isCombinedPlugin?a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"组合插件名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{attrs:{placeholder:"请输入组合插件名称"},model:{value:e.model.combinedName,callback:function(t){e.$set(e.model,"combinedName",t)},expression:"model.combinedName"}}),e.combinedNameError?a("div",{staticStyle:{color:"#f5222d","font-size":"12px","margin-top":"4px"}},[a("a-icon",{attrs:{type:"exclamation-circle"}}),e._v(" "+e._s(e.combinedNameError)+"\n            ")],1):e._e(),e.foundExistingCombined&&!e.combinedNameError?a("div",{staticStyle:{color:"#52c41a","font-size":"12px","margin-top":"4px"}},[a("a-icon",{attrs:{type:"check-circle"}}),e._v(" 已找到现有组合插件，相关信息已自动回填\n            ")],1):e._e()],1)],1):e._e()],1),e.isCombinedPlugin?a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"组合插件介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{attrs:{rows:"3",placeholder:"请输入组合插件介绍"},model:{value:e.model.combinedDescription,callback:function(t){e.$set(e.model,"combinedDescription",t)},expression:"model.combinedDescription"}})],1)],1)],1):e._e(),e.isCombinedPlugin?a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"组合插件图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-image-upload-deferred",{ref:"combinedImageUpload",model:{value:e.model.combinedImage,callback:function(t){e.$set(e.model,"combinedImage",t)},expression:"model.combinedImage"}})],1)],1)],1):e._e(),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubname"}},[a("a-input",{attrs:{placeholder:"请输入插件名称"},model:{value:e.model.plubname,callback:function(t){e.$set(e.model,"plubname",t)},expression:"model.plubname"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubimg"}},[a("j-image-upload-deferred",{ref:"pluginImageUpload",attrs:{isMultiple:""},model:{value:e.model.plubimg,callback:function(t){e.$set(e.model,"plubimg",t)},expression:"model.plubimg"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件创作者",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubwrite"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"aigc_plub_author,authorname,id",placeholder:"请选择插件创作者"},model:{value:e.model.plubwrite,callback:function(t){e.$set(e.model,"plubwrite",t)},expression:"model.plubwrite"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubCategory"}},[a("j-dict-select-tag",{attrs:{dictCode:"plugin_category",placeholder:"请选择插件分类"},model:{value:e.model.plubCategory,callback:function(t){e.$set(e.model,"plubCategory",t)},expression:"model.plubCategory"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"适用场景",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"scenarios"}},[a("j-multi-select-tag",{attrs:{dictCode:"plugin_scenarios",placeholder:"请选择适用场景（可多选）"},model:{value:e.model.scenarios,callback:function(t){e.$set(e.model,"scenarios",t)},expression:"model.scenarios"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubinfo"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入插件介绍"},model:{value:e.model.plubinfo,callback:function(t){e.$set(e.model,"plubinfo",t)},expression:"model.plubinfo"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{staticClass:"j-field-content",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"插件详细内容",prop:"plubContent"}},[a("j-editor",{attrs:{placeholder:"请输入插件详细内容"},model:{value:e.model.plubContent,callback:function(t){e.$set(e.model,"plubContent",t)},expression:"model.plubContent"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"status"}},[a("j-dict-select-tag",{attrs:{dictCode:"plugin_status",placeholder:"请选择插件状态",disabled:!e.isAdmin},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}}),e.isAdmin?e._e():a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v('\n              非管理员用户默认为"审核中"状态\n            ')])],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"排序权重",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"sortOrder"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入排序权重(数字越大越靠前)",disabled:!e.isAdmin},on:{change:e.handleSortOrderChange},model:{value:e.model.sortOrder,callback:function(t){e.$set(e.model,"sortOrder",t)},expression:"model.sortOrder"}}),e.isAdmin?e._e():a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              非管理员用户自动分配权重\n            ")])],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件唯一标识",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"pluginKey"}},[a("a-input-group",{attrs:{compact:""}},[a("a-input",{class:{"error-input":e.isDuplicate},staticStyle:{width:"70%"},attrs:{placeholder:"插件唯一标识（可手动编辑）",disabled:!1,suffix:e.checkingUnique?"检查中...":""},on:{blur:e.onPluginKeyBlur,input:e.onPluginKeyInput},model:{value:e.model.pluginKey,callback:function(t){e.$set(e.model,"pluginKey",t)},expression:"model.pluginKey"}}),a("a-button",{staticStyle:{width:"15%"},attrs:{type:"primary",loading:e.generating},on:{click:e.regeneratePluginKey}},[e._v("\n                重新生成\n              ")]),a("a-button",{staticStyle:{width:"15%"},attrs:{type:"default",loading:e.checkingUnique},on:{click:e.checkUnique}},[e._v("\n                检查重复\n              ")])],1),a("div",{staticStyle:{"margin-top":"4px","font-size":"12px"}},[e.isDuplicate?a("span",{staticStyle:{color:"#ff4d4f"}},[a("a-icon",{attrs:{type:"close-circle"}}),e._v(" 标识已存在，请重新生成或手动修改\n              ")],1):e.isUnique?a("span",{staticStyle:{color:"#52c41a"}},[a("a-icon",{attrs:{type:"check-circle"}}),e._v(" 标识可用\n              ")],1):e.model.pluginKey&&e.model.pluginKey.trim()?a("span",{staticStyle:{color:"#1890ff"}},[a("a-icon",{attrs:{type:"info-circle"}}),e._v(' 请点击"检查重复"验证标识可用性\n              ')],1):a("span",{staticStyle:{color:"#999"}},[e._v("\n                基于插件名称自动生成，也可手动编辑\n              ")])])],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件教程视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubvideo"}},[a("a-button",{staticStyle:{width:"100%"},attrs:{disabled:""},on:{click:e.showVideoUploadTip}},[a("a-icon",{attrs:{type:"upload"}}),e._v("\n              暂不支持上传文件\n            ")],1),a("div",{staticStyle:{"margin-top":"8px",color:"#999","font-size":"12px"}},[e._v("\n              视频上传功能暂时禁用，请使用下方的教程链接字段\n            ")])],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"需要金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"neednum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入需要金额",precision:2},model:{value:e.model.neednum,callback:function(t){e.$set(e.model,"neednum",t)},expression:"model.neednum"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件教程链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"tutorialLink"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入插件教程链接，如：https://www.example.com/tutorial"},model:{value:e.model.tutorialLink,callback:function(t){e.$set(e.model,"tutorialLink",t)},expression:"model.tutorialLink"}},[a("a-icon",{attrs:{slot:"prefix",type:"link"},slot:"prefix"})],1),a("div",{staticStyle:{"margin-top":"8px",color:"#666","font-size":"12px"}},[a("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:"info-circle"}}),e._v("\n              支持外部视频链接，如B站、YouTube、腾讯视频等平台链接\n            ")],1)],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"收益金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"income"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",precision:2,disabled:!0},model:{value:e.model.income,callback:function(t){e.$set(e.model,"income",t)},expression:"model.income"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              系统根据实际使用情况自动计算\n            ")])],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"调用次数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"usernum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",disabled:!0},model:{value:e.model.usernum,callback:function(t){e.$set(e.model,"usernum",t)},expression:"model.usernum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              系统根据实际调用自动统计\n            ")])],1)],1)],1)],1)],1)],1)},i=[],n=a("a34a"),s=a.n(n),o=a("0fea"),l=a("6745");function c(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t,a,r,i,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){d(n,r,i,s,o,"next",e)}function o(e){d(n,r,i,s,o,"throw",e)}s(void 0)}))}}var m={name:"AigcPlubShopForm",components:{JImageUploadDeferred:l["default"]},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{usernum:0,income:0,isCombined:2,combinedName:"",combinedDescription:"",combinedImage:""},generating:!1,checkingUnique:!1,isDuplicate:!1,isUnique:!1,keyCounter:1,checkTimer:null,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,queryTimer:null,isAutoFilledDescription:!1,isAutoFilledImage:!1,isAutoFilledAuthor:!1,isAutoFilledSortOrder:!1,foundExistingCombined:!1,combinedNameError:"",validatorRules:{plubname:[{required:!0,message:"请输入插件名称!"}],plubwrite:[{required:!0,message:"请输入插件创作者!"}],plubinfo:[{required:!0,message:"请输入插件介绍!"}],plubCategory:[{required:!0,message:"请选择插件分类!"}],status:[{required:!0,message:"请选择插件状态!"}],pluginKey:[{required:!0,message:"请输入插件唯一标识!"},{pattern:/^[a-z0-9_]+$/,message:"插件标识只能包含小写字母、数字和下划线!"}],tutorialLink:[{pattern:/^https?:\/\/.+/,message:"请输入有效的链接地址，必须以http://或https://开头!"}]},url:{add:"/plubshop/aigcPlubShop/add",edit:"/plubshop/aigcPlubShop/edit",queryById:"/plubshop/aigcPlubShop/queryById"}}},computed:{formDisabled:function(){return this.disabled},isAdmin:function(){var e=localStorage.getItem("userRole");return e&&e.toLowerCase().includes("admin")},isCombinedPlugin:function(){return"1"===this.model.isCombined||1===this.model.isCombined}},watch:{"model.plubname":{handler:function(e,t){e&&e!==t&&(this.keyCounter=1,this.model.pluginKey&&""!==this.model.pluginKey.trim()&&!this.isAutoGeneratedKey()||this.generatePluginKey(),this.isAdmin||(this.model.status=2,this.setDefaultSortOrder()))},immediate:!0},"model.pluginKey":{handler:function(e){e&&this.debounceCheckUnique()}},"model.isCombined":{handler:function(e){"2"!==e&&2!==e||(this.model.combinedName="",this.model.combinedDescription="")},immediate:!0},"model.combinedName":{handler:function(e,t){this.isCombinedPlugin&&e&&e.trim()&&e!==t?this.debounceQueryCombinedPlugin():e&&e.trim()||(this.clearAutoFilledFields(),this.combinedNameError="",this.foundExistingCombined=!1)}}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},generatePluginKey:function(){var e=u(s.a.mark((function e(){var t,a;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.model.plubname){e.next=2;break}return e.abrupt("return");case 2:return this.generating=!0,this.isDuplicate=!1,this.isUnique=!1,e.prev=5,t=this.createBaseKey(this.model.plubname),a=t,this.keyCounter>1&&(a="".concat(t,"_").concat(this.keyCounter)),this.model.pluginKey=a,e.next=12,this.checkUnique();case 12:return e.prev=12,this.generating=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[5,,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),regeneratePluginKey:function(){var e=u(s.a.mark((function e(){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.model.plubname){e.next=3;break}return this.$message.warning("请先输入插件名称"),e.abrupt("return");case 3:return this.keyCounter++,e.next=6,this.generatePluginKey();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),createBaseKey:function(e){return e.toLowerCase().replace(/[\u4e00-\u9fa5]/g,(function(e){var t,a=(t={"扣":"kou","子":"zi","智":"zhi","能":"neng","体":"ti","插":"cha","件":"jian","机":"ji","器":"qi","人":"ren","助":"zhu","手":"shou","客":"ke","服":"fu","聊":"liao","天":"tian","对":"dui","话":"hua","问":"wen","答":"da","抖":"dou","音":"yin","快":"kuai"},c(t,"手","shou"),c(t,"小","xiao"),c(t,"红","hong"),c(t,"书","shu"),c(t,"微","wei"),c(t,"信","xin"),c(t,"博","bo"),c(t,"微","wei"),c(t,"博","bo"),c(t,"知","zhi"),c(t,"乎","hu"),c(t,"豆","dou"),c(t,"瓣","ban"),c(t,"贴","tie"),c(t,"吧","ba"),c(t,"论","lun"),c(t,"坛","tan"),c(t,"社","she"),c(t,"区","qu"),c(t,"群","qun"),c(t,"组","zu"),c(t,"圈","quan"),c(t,"飞","fei"),c(t,"书","shu"),c(t,"钉","ding"),c(t,"钉","ding"),c(t,"企","qi"),c(t,"微","wei"),c(t,"腾","teng"),c(t,"讯","xun"),c(t,"会","hui"),c(t,"议","yi"),c(t,"文","wen"),c(t,"档","dang"),c(t,"表","biao"),c(t,"格","ge"),c(t,"演","yan"),c(t,"示","shi"),c(t,"邮","you"),c(t,"件","jian"),c(t,"日","ri"),c(t,"历","li"),c(t,"任","ren"),c(t,"务","wu"),c(t,"项","xiang"),c(t,"目","mu"),c(t,"团","tuan"),c(t,"队","dui"),c(t,"协","xie"),c(t,"作","zuo"),c(t,"办","ban"),c(t,"公","gong"),c(t,"A","a"),c(t,"I","i"),c(t,"人","ren"),c(t,"工","gong"),c(t,"智","zhi"),c(t,"能","neng"),c(t,"学","xue"),c(t,"习","xi"),c(t,"深","shen"),c(t,"度","du"),c(t,"神","shen"),c(t,"经","jing"),c(t,"网","wang"),c(t,"络","luo"),c(t,"算","suan"),c(t,"法","fa"),c(t,"模","mo"),c(t,"型","xing"),c(t,"训","xun"),c(t,"练","lian"),c(t,"预","yu"),c(t,"测","ce"),c(t,"识","shi"),c(t,"别","bie"),c(t,"检","jian"),c(t,"测","ce"),c(t,"分","fen"),c(t,"析","xi"),c(t,"处","chu"),c(t,"理","li"),c(t,"自","zi"),c(t,"动","dong"),c(t,"化","hua"),c(t,"优","you"),c(t,"化","hua"),c(t,"创","chuang"),c(t,"作","zuo"),c(t,"编","bian"),c(t,"辑","ji"),c(t,"写","xie"),c(t,"作","zuo"),c(t,"文","wen"),c(t,"章","zhang"),c(t,"标","biao"),c(t,"题","ti"),c(t,"摘","zhai"),c(t,"要","yao"),c(t,"总","zong"),c(t,"结","jie"),c(t,"翻","fan"),c(t,"译","yi"),c(t,"润","run"),c(t,"色","se"),c(t,"校","jiao"),c(t,"对","dui"),c(t,"审","shen"),c(t,"核","he"),c(t,"发","fa"),c(t,"布","bu"),c(t,"推","tui"),c(t,"广","guang"),c(t,"营","ying"),c(t,"销","xiao"),c(t,"宣","xuan"),c(t,"传","chuan"),c(t,"图","tu"),c(t,"片","pian"),c(t,"照","zhao"),c(t,"片","pian"),c(t,"画","hua"),c(t,"像","xiang"),c(t,"视","shi"),c(t,"频","pin"),c(t,"音","yin"),c(t,"频","pin"),c(t,"声","sheng"),c(t,"音","yin"),c(t,"录","lu"),c(t,"制","zhi"),c(t,"剪","jian"),c(t,"辑","ji"),c(t,"合","he"),c(t,"成","cheng"),c(t,"特","te"),c(t,"效","xiao"),c(t,"滤","lv"),c(t,"镜","jing"),c(t,"美","mei"),c(t,"颜","yan"),c(t,"修","xiu"),c(t,"图","tu"),c(t,"水","shui"),c(t,"印","yin"),c(t,"压","ya"),c(t,"缩","suo"),c(t,"淘","tao"),c(t,"宝","bao"),c(t,"天","tian"),c(t,"猫","mao"),c(t,"京","jing"),c(t,"东","dong"),c(t,"拼","pin"),c(t,"多","duo"),c(t,"多","duo"),c(t,"苏","su"),c(t,"宁","ning"),c(t,"唯","wei"),c(t,"品","pin"),c(t,"会","hui"),c(t,"商","shang"),c(t,"品","pin"),c(t,"店","dian"),c(t,"铺","pu"),c(t,"货","huo"),c(t,"物","wu"),c(t,"订","ding"),c(t,"单","dan"),c(t,"支","zhi"),c(t,"付","fu"),c(t,"配","pei"),c(t,"送","song"),c(t,"物","wu"),c(t,"流","liu"),c(t,"退","tui"),c(t,"换","huan"),c(t,"大","da"),c(t,"师","shi"),c(t,"打","da"),c(t,"洞","dong"),c(t,"可","ke"),c(t,"口","kou"),c(t,"乐","le"),c(t,"生","sheng"),c(t,"成","cheng"),c(t,"工","gong"),c(t,"具","ju"),c(t,"功","gong"),c(t,"能","neng"),c(t,"应","ying"),c(t,"用","yong"),c(t,"软","ruan"),c(t,"件","jian"),c(t,"程","cheng"),c(t,"序","xu"),c(t,"系","xi"),c(t,"统","tong"),c(t,"平","ping"),c(t,"台","tai"),c(t,"框","kuang"),c(t,"架","jia"),c(t,"库","ku"),c(t,"包","bao"),c(t,"模","mo"),c(t,"块","kuai"),c(t,"组","zu"),c(t,"件","jian"),c(t,"接","jie"),c(t,"口","kou"),c(t,"服","fu"),c(t,"务","wu"),c(t,"端","duan"),c(t,"前","qian"),c(t,"后","hou"),c(t,"全","quan"),c(t,"栈","zhan"),c(t,"开","kai"),c(t,"发","fa"),c(t,"设","she"),c(t,"计","ji"),c(t,"实","shi"),c(t,"现","xian"),c(t,"部","bu"),c(t,"署","shu"),c(t,"运","yun"),c(t,"维","wei"),c(t,"测","ce"),c(t,"试","shi"),c(t,"调","tiao"),c(t,"试","shi"),c(t,"数","shu"),c(t,"据","ju"),c(t,"库","ku"),c(t,"表","biao"),c(t,"字","zi"),c(t,"段","duan"),c(t,"索","suo"),c(t,"引","yin"),c(t,"查","cha"),c(t,"询","xun"),c(t,"增","zeng"),c(t,"删","shan"),c(t,"改","gai"),c(t,"查","cha"),c(t,"存","cun"),c(t,"储","chu"),c(t,"备","bei"),c(t,"份","fen"),c(t,"恢","hui"),c(t,"复","fu"),c(t,"同","tong"),c(t,"步","bu"),c(t,"异","yi"),c(t,"步","bu"),c(t,"缓","huan"),c(t,"存","cun"),c(t,"内","nei"),c(t,"存","cun"),c(t,"磁","ci"),c(t,"盘","pan"),c(t,"网","wang"),c(t,"络","luo"),c(t,"互","hu"),c(t,"联","lian"),c(t,"网","wang"),c(t,"协","xie"),c(t,"议","yi"),c(t,"传","chuan"),c(t,"输","shu"),c(t,"通","tong"),c(t,"信","xin"),c(t,"连","lian"),c(t,"接","jie"),c(t,"请","qing"),c(t,"求","qiu"),c(t,"响","xiang"),c(t,"应","ying"),c(t,"状","zhuang"),c(t,"态","tai"),c(t,"码","ma"),c(t,"头","tou"),c(t,"部","bu"),c(t,"体","ti"),c(t,"参","can"),c(t,"数","shu"),c(t,"安","an"),c(t,"全","quan"),c(t,"权","quan"),c(t,"限","xian"),c(t,"角","jiao"),c(t,"色","se"),c(t,"用","yong"),c(t,"户","hu"),c(t,"认","ren"),c(t,"证","zheng"),c(t,"授","shou"),c(t,"权","quan"),c(t,"登","deng"),c(t,"录","lu"),c(t,"注","zhu"),c(t,"册","ce"),c(t,"退","tui"),c(t,"出","chu"),c(t,"密","mi"),c(t,"码","ma"),c(t,"加","jia"),c(t,"密","mi"),c(t,"解","jie"),c(t,"密","mi"),c(t,"签","qian"),c(t,"名","ming"),c(t,"验","yan"),c(t,"证","zheng"),c(t,"令","ling"),c(t,"牌","pai"),c(t,"界","jie"),c(t,"面","mian"),c(t,"页","ye"),c(t,"面","mian"),c(t,"窗","chuang"),c(t,"口","kou"),c(t,"对","dui"),c(t,"话","hua"),c(t,"框","kuang"),c(t,"按","an"),c(t,"钮","niu"),c(t,"链","lian"),c(t,"接","jie"),c(t,"菜","cai"),c(t,"单","dan"),c(t,"导","dao"),c(t,"航","hang"),c(t,"标","biao"),c(t,"签","qian"),c(t,"选","xuan"),c(t,"项","xiang"),c(t,"卡","ka"),c(t,"片","pian"),c(t,"列","lie"),c(t,"表","biao"),c(t,"表","biao"),c(t,"格","ge"),c(t,"表","biao"),c(t,"单","dan"),c(t,"输","shu"),c(t,"入","ru"),c(t,"框","kuang"),c(t,"下","xia"),c(t,"拉","la"),c(t,"框","kuang"),c(t,"复","fu"),c(t,"选","xuan"),c(t,"框","kuang"),c(t,"单","dan"),c(t,"选","xuan"),c(t,"按","an"),c(t,"钮","niu"),c(t,"滑","hua"),c(t,"块","kuai"),c(t,"进","jin"),c(t,"度","du"),c(t,"条","tiao"),c(t,"加","jia"),c(t,"载","zai"),c(t,"刷","shua"),c(t,"新","xin"),c(t,"更","geng"),c(t,"新","xin"),c(t,"提","ti"),c(t,"示","shi"),c(t,"消","xiao"),c(t,"息","xi"),c(t,"通","tong"),c(t,"知","zhi"),c(t,"警","jing"),c(t,"告","gao"),c(t,"确","que"),c(t,"认","ren"),c(t,"取","qu"),c(t,"消","xiao"),t);return a[e]||e.charCodeAt(0).toString(36)})).replace(/[^a-z0-9]/g,"_").replace(/_+/g,"_").replace(/^_|_$/g,"")},checkUnique:function(){var e=u(s.a.mark((function e(){var t,a=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.model.pluginKey){e.next=2;break}return e.abrupt("return");case 2:return this.checkingUnique=!0,this.isDuplicate=!1,this.isUnique=!1,e.prev=5,e.next=8,Object(o["c"])("/plubshop/aigcPlubShop/checkPluginKey",{pluginKey:this.model.pluginKey,excludeId:this.model.id});case 8:t=e.sent,t.success&&(this.isDuplicate=t.result.exists,this.isUnique=!t.result.exists,this.isDuplicate&&(this.$message.warning("标识已存在，正在自动生成新的标识..."),setTimeout((function(){a.regeneratePluginKey()}),1e3))),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](5),this.$message.error("检查标识唯一性失败");case 15:return e.prev=15,this.checkingUnique=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,this,[[5,12,15,18]])})));function t(){return e.apply(this,arguments)}return t}(),debounceCheckUnique:function(){var e=this;clearTimeout(this.checkTimer),this.checkTimer=setTimeout((function(){e.checkUnique()}),500)},onPluginKeyInput:function(e){var t=e.toLowerCase().replace(/[^a-z0-9_]/g,"");t!==e&&(this.model.pluginKey=t),this.isDuplicate=!1,this.isUnique=!1,t&&t.trim()&&this.debounceCheckUnique()},onPluginKeyBlur:function(){this.model.pluginKey&&this.model.pluginKey.trim()&&this.checkUnique()},isAutoGeneratedKey:function(){if(!this.model.pluginKey||!this.model.plubname)return!0;var e=this.createBaseKey(this.model.plubname);return this.model.pluginKey===e||this.model.pluginKey.startsWith(e+"_")},setDefaultSortOrder:function(){var e=u(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["c"])("/plubshop/aigcPlubShop/getMaxSortOrder");case 3:t=e.sent,this.model.sortOrder=(t.result||0)+1,e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),this.model.sortOrder=1;case 11:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),handleSortOrderChange:function(){var e=u(s.a.mark((function e(t){var a,r=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(this.isAdmin&&t&&t>0)){e.next=12;break}return e.prev=1,e.next=5,Object(o["c"])("/plubshop/aigcPlubShop/checkSortOrderConflict",{sortOrder:parseInt(t),excludeId:this.model.id||""});case 5:a=e.sent,a.success&&a.result.hasConflict&&this.$confirm({title:"权重冲突",content:"权重 ".concat(t," 已存在，是否将现有插件权重后移？"),onOk:function(){r.adjustSortOrder(t)},onCancel:function(){r.setDefaultSortOrder()}}),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1);case 12:case"end":return e.stop()}}),e,this,[[1,9]])})));function t(t){return e.apply(this,arguments)}return t}(),adjustSortOrder:function(){var e=u(s.a.mark((function e(t){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=4,Object(o["h"])("/plubshop/aigcPlubShop/adjustSortOrder",{targetOrder:parseInt(t),excludeId:this.model.id||""},"post");case 4:this.$message.success("权重调整成功"),e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),this.$message.error("权重调整失败");case 11:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(t){return e.apply(this,arguments)}return t}(),debounceQueryCombinedPlugin:function(){var e=this;clearTimeout(this.queryTimer),this.queryTimer=setTimeout((function(){e.queryCombinedPluginInfo()}),500)},queryCombinedPluginInfo:function(){var e=u(s.a.mark((function e(){var t,a,r;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["c"])("/plubshop/aigcPlubShop/validateAndQueryCombinedPlugin",{combinedName:this.model.combinedName,excludeId:this.model.id||""});case 3:if(t=e.sent,!t.success||!t.result){e.next=13;break}if(a=t.result,a.hasPermission){e.next=11;break}return this.combinedNameError=a.message,this.foundExistingCombined=!1,this.clearAutoFilledFields(),e.abrupt("return");case 11:this.combinedNameError="",a.foundExisting?(r=[],this.model.combinedDescription&&""!==this.model.combinedDescription.trim()&&!this.isAutoFilledDescription||(this.model.combinedDescription=a.combinedDescription,this.isAutoFilledDescription=!0,r.push("组合插件介绍")),this.model.combinedImage&&""!==this.model.combinedImage.trim()&&!this.isAutoFilledImage||(this.model.combinedImage=a.combinedImage,this.isAutoFilledImage=!0,r.push("组合插件图片")),this.model.plubwrite&&""!==this.model.plubwrite.trim()&&!this.isAutoFilledAuthor||(this.model.plubwrite=a.plubwrite,this.isAutoFilledAuthor=!0,r.push("插件创作者")),this.model.sortOrder&&0!==this.model.sortOrder&&!this.isAutoFilledSortOrder||(this.model.sortOrder=a.sortOrder,this.isAutoFilledSortOrder=!0,r.push("排序权重")),this.foundExistingCombined=!0,r.length>0&&this.$message.info('已自动回填"'.concat(this.model.combinedName,'"的').concat(r.join("、")))):(this.foundExistingCombined=!1,this.clearAutoFilledFlags());case 13:e.next=20;break;case 15:e.prev=15,e.t0=e["catch"](0),this.combinedNameError="查询失败，请重试",this.foundExistingCombined=!1;case 20:case"end":return e.stop()}}),e,this,[[0,15]])})));function t(){return e.apply(this,arguments)}return t}(),clearAutoFilledFields:function(){this.isAutoFilledDescription&&(this.model.combinedDescription=""),this.isAutoFilledImage&&(this.model.combinedImage=""),this.isAutoFilledAuthor&&(this.model.plubwrite=""),this.isAutoFilledSortOrder&&(this.model.sortOrder=null),this.clearAutoFilledFlags()},clearAutoFilledFlags:function(){this.isAutoFilledDescription=!1,this.isAutoFilledImage=!1,this.isAutoFilledAuthor=!1,this.isAutoFilledSortOrder=!1},checkCombinedPluginCount:function(){var e=u(s.a.mark((function e(){var t,a,r,i=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,this.model.combinedName&&this.isCombinedPlugin){e.next=3;break}return e.abrupt("return",0);case 3:return e.next=5,Object(o["c"])("/plubshop/aigcPlubShop/list",{combinedName:this.model.combinedName,isCombined:1,pageNo:1,pageSize:1e3});case 5:if(t=e.sent,!(t.success&&t.result&&t.result.records)){e.next=10;break}return a=this.model.id,r=t.result.records.filter((function(e){return e.id!==a&&e.combinedName===i.model.combinedName})),e.abrupt("return",r.length);case 10:return e.abrupt("return",0);case 13:return e.prev=13,e.t0=e["catch"](0),e.abrupt("return",0);case 17:case"end":return e.stop()}}),e,this,[[0,13]])})));function t(){return e.apply(this,arguments)}return t}(),submitForm:function(){var e=u(s.a.mark((function e(){var t,a,r,i=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this,1!==this.model.isCombined||!this.model.combinedName||!this.model.combinedDescription){e.next=17;break}return e.prev=2,e.next=5,this.checkCombinedPluginCount();case 5:if(a=e.sent,!(a>0)){e.next=12;break}return e.next=9,new Promise((function(e){i.$confirm({title:"确认保存",content:"检测到还有 ".concat(a,' 个插件使用相同的组合插件名"').concat(i.model.combinedName,'"，保存后将同步更新它们的介绍。是否继续？'),okText:"确认保存",cancelText:"取消",onOk:function(){return e(!0)},onCancel:function(){return e(!1)}})}));case 9:if(r=e.sent,r){e.next=12;break}return e.abrupt("return");case 12:e.next=17;break;case 14:e.prev=14,e.t0=e["catch"](2);case 17:this.$refs.form.validate(function(){var e=u(s.a.mark((function e(a){var r,n,l;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=28;break}return t.confirmLoading=!0,e.prev=2,e.next=5,i.uploadPendingImages();case 5:return r="",n="",i.model.id?(r+=i.url.edit,n="put"):(r+=i.url.add,n="post"),e.next=10,Object(o["h"])(r,i.model,n);case 10:if(l=e.sent,!l.success){e.next=18;break}return e.next=14,i.confirmDeleteOriginalFiles();case 14:t.$message.success(l.message),t.$emit("ok"),e.next=19;break;case 18:t.$message.warning(l.message);case 19:e.next=25;break;case 21:e.prev=21,e.t0=e["catch"](2),t.$message.error("保存失败: "+(e.t0.message||"未知错误"));case 25:return e.prev=25,t.confirmLoading=!1,e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[2,21,25,28]])})));return function(t){return e.apply(this,arguments)}}());case 18:case"end":return e.stop()}}),e,this,[[2,14]])})));function t(){return e.apply(this,arguments)}return t}(),uploadPendingImages:function(){var e=u(s.a.mark((function e(){var t,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.$refs.pluginImageUpload){e.next=23;break}if(!this.$refs.pluginImageUpload.hasPendingFiles()){e.next=20;break}return e.prev=4,e.next=7,this.$refs.pluginImageUpload.performUpload();case 7:e.sent,t=this.$refs.pluginImageUpload.getCurrentValue(),this.model.plubimg=t,e.next=18;break;case 14:throw e.prev=14,e.t0=e["catch"](4),e.t0;case 18:e.next=23;break;case 20:a=this.$refs.pluginImageUpload.getCurrentValue(),this.model.plubimg=a;case 23:if(!this.$refs.combinedImageUpload){e.next=45;break}if(!this.$refs.combinedImageUpload.hasPendingFiles()){e.next=42;break}return e.prev=26,e.next=29,this.$refs.combinedImageUpload.performUpload();case 29:e.sent,r=this.$refs.combinedImageUpload.getCurrentValue(),this.model.combinedImage=r,e.next=40;break;case 36:throw e.prev=36,e.t1=e["catch"](26),e.t1;case 40:e.next=45;break;case 42:i=this.$refs.combinedImageUpload.getCurrentValue(),this.model.combinedImage=i;case 45:case 46:case"end":return e.stop()}}),e,this,[[4,14],[26,36]])})));function t(){return e.apply(this,arguments)}return t}(),confirmDeleteOriginalFiles:function(){var e=u(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=[],this.$refs.pluginImageUpload&&t.push(this.$refs.pluginImageUpload.confirmDeleteOriginalFiles()),this.$refs.combinedImageUpload&&t.push(this.$refs.combinedImageUpload.confirmDeleteOriginalFiles()),!(t.length>0)){e.next=13;break}return e.prev=4,e.next=7,Promise.all(t);case 7:e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](4);case 13:case"end":return e.stop()}}),e,this,[[4,10]])})));function t(){return e.apply(this,arguments)}return t}(),handleClose:function(){this.$refs.pluginImageUpload&&this.$refs.pluginImageUpload.rollbackChanges(),this.$refs.combinedImageUpload&&this.$refs.combinedImageUpload.rollbackChanges(),this.$emit("close")},showVideoUploadTip:function(){this.$message.info("暂不支持上传文件，请使用教程链接字段输入外部视频链接")}}},p=m,h=(a("facd8"),a("2877")),f=Object(h["a"])(p,r,i,!1,null,"0c5419f3",null);t["default"]=f.exports},"663e":function(e,t,a){},6973:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐关系ID",prop:"referralId"}},[a("a-input",{attrs:{placeholder:"请输入推荐关系ID"},model:{value:e.model.referralId,callback:function(t){e.$set(e.model,"referralId",t)},expression:"model.referralId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐人ID",prop:"referrerId"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.model.referrerId,callback:function(t){e.$set(e.model,"referrerId",t)},expression:"model.referrerId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"被推荐人ID",prop:"refereeId"}},[a("a-input",{attrs:{placeholder:"请输入被推荐人ID"},model:{value:e.model.refereeId,callback:function(t){e.$set(e.model,"refereeId",t)},expression:"model.refereeId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"奖励类型",prop:"rewardType"}},[a("a-select",{attrs:{placeholder:"请选择奖励类型"},model:{value:e.model.rewardType,callback:function(t){e.$set(e.model,"rewardType",t)},expression:"model.rewardType"}},[a("a-select-option",{attrs:{value:1}},[e._v("注册奖励")]),a("a-select-option",{attrs:{value:2}},[e._v("首充奖励")]),a("a-select-option",{attrs:{value:3}},[e._v("升级奖励")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"奖励金额",prop:"rewardAmount"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入奖励金额"},model:{value:e.model.rewardAmount,callback:function(t){e.$set(e.model,"rewardAmount",t)},expression:"model.rewardAmount"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待发放")]),a("a-select-option",{attrs:{value:2}},[e._v("已发放")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"触发事件描述",prop:"triggerEvent"}},[a("a-input",{attrs:{placeholder:"请输入触发事件描述"},model:{value:e.model.triggerEvent,callback:function(t){e.$set(e.model,"triggerEvent",t)},expression:"model.triggerEvent"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"交易记录ID",prop:"transactionId"}},[a("a-input",{attrs:{placeholder:"请输入交易记录ID"},model:{value:e.model.transactionId,callback:function(t){e.$set(e.model,"transactionId",t)},expression:"model.transactionId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"发放时间",prop:"rewardTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择发放时间"},model:{value:e.model.rewardTime,callback:function(t){e.$set(e.model,"rewardTime",t)},expression:"model.rewardTime"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=a("c1df"),o=a.n(s),l={name:"AicgUserReferralRewardModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{referralId:[{required:!0,message:"请输入推荐关系ID!"}],referrerId:[{required:!0,message:"请输入推荐人ID!"}],refereeId:[{required:!0,message:"请输入被推荐人ID!"}],rewardType:[{required:!0,message:"请选择奖励类型!"}],rewardAmount:[{required:!0,message:"请输入奖励金额!"}]},url:{add:"/demo/referralreward/add",edit:"/demo/referralreward/edit",queryById:"/demo/referralreward/queryById"}}},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={referralId:t.model.referralId,referrerId:t.model.referrerId,refereeId:t.model.refereeId,rewardType:t.model.rewardType,rewardAmount:t.model.rewardAmount,status:t.model.status,triggerEvent:t.model.triggerEvent,transactionId:t.model.transactionId};t.form.setFieldsValue(e),t.model.rewardTime&&(t.model.rewardTime=o()(t.model.rewardTime))})),e.id?this.title="编辑":(this.title="新增",this.model.status=1)},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var s=Object.assign(e.model);s.rewardTime&&(s.rewardTime=s.rewardTime.format("YYYY-MM-DD HH:mm:ss")),Object(n["h"])(r,s,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()}}},c=l,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},"6bf5":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"程序类型"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择程序类型",dictCode:"program_type"},model:{value:e.queryParam.programType,callback:function(t){e.$set(e.queryParam,"programType",t)},expression:"queryParam.programType"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"版本号"}},[a("a-input",{attrs:{placeholder:"请输入版本号"},model:{value:e.queryParam.versionNumber,callback:function(t){e.$set(e.queryParam,"versionNumber",t)},expression:"queryParam.versionNumber"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否最新"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择",dictCode:"isTrue"},model:{value:e.queryParam.isLatest,callback:function(t){e.$set(e.queryParam,"isLatest",t)},expression:"queryParam.isLatest"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{display:"block","white-space":"nowrap"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("程序版本控制")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1!==r.isLatest?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleSetLatest(r)}}},[e._v("设为最新")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-version-control-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("3d1f"),l={name:"AigcVersionControlList",mixins:[s["a"],n["b"]],components:{AigcVersionControlModal:o["default"]},data:function(){return{description:"程序版本控制管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"程序类型",align:"center",dataIndex:"programType",customRender:function(e){var t={frontend:"前端",backend:"后端",desktop:"桌面应用",plugin:"扣子插件"};return t[e]||e}},{title:"版本号",align:"center",dataIndex:"versionNumber"},{title:"更新内容",align:"center",dataIndex:"updateContent",width:200,customRender:function(e){return e?e.length>50?e.substring(0,50)+"...":e:"-"}},{title:"下载链接",align:"center",dataIndex:"downloadUrl",width:150,customRender:function(e){return e?'<a href="'.concat(e,'" target="_blank" style="color: #1890ff;">下载</a>'):"-"}},{title:"发布日期",align:"center",dataIndex:"releaseDate",customRender:function(e){return e?e.length>10?e.substr(0,10):e:""}},{title:"是否最新",align:"center",dataIndex:"isLatest",customRender:function(e){return 1===e?"是":"否"}},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){return 1===e?"启用":"禁用"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/aigcview/versioncontrol/list",delete:"/aigcview/versioncontrol/delete",deleteBatch:"/aigcview/versioncontrol/deleteBatch",exportXlsUrl:"/aigcview/versioncontrol/exportXls",importExcelUrl:"aigcview/versioncontrol/importExcel",setLatest:"/aigcview/versioncontrol/setAsLatest"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"programType",text:"程序类型",dictCode:"program_type"}),e.push({type:"string",value:"versionNumber",text:"版本号"}),e.push({type:"string",value:"updateContent",text:"更新内容"}),e.push({type:"date",value:"releaseDate",text:"发布日期"}),e.push({type:"int",value:"isLatest",text:"是否最新版本",dictCode:"isTrue"}),e.push({type:"int",value:"status",text:"状态",dictCode:"valid_status"}),this.superFieldList=e},handleSetLatest:function(e){var t=this;this.$confirm({title:"确认操作",content:"确定要将此版本设为最新版本吗？",onOk:function(){t.doSetLatest(e.id)}})},doSetLatest:function(e){var t=this;this.$http.put(this.url.setLatest,{},{params:{id:e}}).then((function(e){e.success?(t.$message.success(e.message),t.loadData()):t.$message.warning(e.message)})).catch((function(e){t.$message.error("设置最新版本失败，请稍后重试")}))}}},c=l,d=(a("d26c"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"fcaf148a",null);t["default"]=u.exports},"6dba":function(e,t,a){"use strict";var r=a("3ac8"),i=a.n(r);i.a},"727c":function(e,t,a){},7290:function(e,t,a){"use strict";var r=a("6198"),i=a.n(r);i.a},7331:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-video-teacher-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("aff5"),s={name:"AigcVideoTeacherModal",components:{AigcVideoTeacherForm:n["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("8d93"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"3e23bd8c",null);t["default"]=c.exports},7695:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-home-carousel-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("b35e"),s={name:"AigcHomeCarouselModal",components:{AigcHomeCarouselForm:n["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("78d6"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"323fd63d",null);t["default"]=c.exports},7818:function(e,t,a){"use strict";var r=a("8b4b"),i=a.n(r);i.a},"78d6":function(e,t,a){"use strict";var r=a("9bce"),i=a.n(r);i.a},"7bf4":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否系列视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"isseries"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"isTrue",placeholder:"请选择是否系列视频"},on:{change:e.handleSeriesChange},model:{value:e.model.isseries,callback:function(t){e.$set(e.model,"isseries",t)},expression:"model.isseries"}})],1)],1),1==e.model.isseries?a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"系列名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"seriesname"}},[a("a-input",{attrs:{placeholder:"请输入系列名称"},model:{value:e.model.seriesname,callback:function(t){e.$set(e.model,"seriesname",t)},expression:"model.seriesname"}})],1)],1):e._e()],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"视频链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"videoUrl"}},[a("a-input",{attrs:{placeholder:"请输入视频链接"},model:{value:e.model.videoUrl,callback:function(t){e.$set(e.model,"videoUrl",t)},expression:"model.videoUrl"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"讲师",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"teacher"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"aigc_video_teacher,teachername,id",placeholder:"请选择讲师"},model:{value:e.model.teacher,callback:function(t){e.$set(e.model,"teacher",t)},expression:"model.teacher"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"titile"}},[a("a-input",{attrs:{placeholder:"请输入标题"},model:{value:e.model.titile,callback:function(t){e.$set(e.model,"titile",t)},expression:"model.titile"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"设置等级",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"setlevel"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"setLevel",placeholder:"请选择设置等级"},model:{value:e.model.setlevel,callback:function(t){e.$set(e.model,"setlevel",t)},expression:"model.setlevel"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"上传日期",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"uptime"}},[a("j-date",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择上传日期"},model:{value:e.model.uptime,callback:function(t){e.$set(e.model,"uptime",t)},expression:"model.uptime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"视频文件",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"videofile"}},[a("j-upload",{attrs:{disabled:""},model:{value:e.model.videofile,callback:function(t){e.$set(e.model,"videofile",t)},expression:"model.videofile"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("此字段已禁用")])],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"点击量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"clicknum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入点击量",disabled:""},model:{value:e.model.clicknum,callback:function(t){e.$set(e.model,"clicknum",t)},expression:"model.clicknum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("此字段已禁用")])],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"课程标签",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"tag"}},[a("j-multi-select-tag",{attrs:{type:"checkbox",dictCode:"tag",placeholder:"请选择课程标签"},model:{value:e.model.tag,callback:function(t){e.$set(e.model,"tag",t)},expression:"model.tag"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"课程介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"intro"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入课程介绍"},model:{value:e.model.intro,callback:function(t){e.$set(e.model,"intro",t)},expression:"model.intro"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea");function s(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function o(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?s(Object(a),!0).forEach((function(t){l(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function l(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var c={name:"AigcVideoTutorialForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{uptime:this.getCurrentDate(),isseries:2,clicknum:0},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{titile:[{required:!0,message:"请输入标题",trigger:"blur"}],videoUrl:[{required:!0,message:"请输入视频链接",trigger:"blur"},{type:"url",message:"请输入正确的URL格式",trigger:"blur"}],teacher:[{required:!0,message:"请选择讲师",trigger:"change"}],uptime:[{required:!0,message:"请选择上传日期",trigger:"change"}]},url:{add:"/videotutorial/aigcVideoTutorial/add",edit:"/videotutorial/aigcVideoTutorial/edit",queryById:"/videotutorial/aigcVideoTutorial/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{getCurrentDate:function(){var e=new Date,t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(a,"-").concat(r)},handleSeriesChange:function(e){2==e&&(this.model.seriesname="")},add:function(){var e={uptime:this.getCurrentDate(),isseries:2,clicknum:0};this.edit(e)},edit:function(e){this.model=Object.assign({},e),this.model.id||this.model.uptime||(this.model.uptime=this.getCurrentDate()),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var s=o({},e.model);2==s.isseries&&(s.seriesname=""),Object(n["h"])(r,s,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}},d=c,u=a("2877"),m=Object(u["a"])(d,r,i,!1,null,null,null);t["default"]=m.exports},"84f8":function(e,t,a){"use strict";var r=a("4638"),i=a.n(r);i.a},8660:function(e,t,a){},8877:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户昵称"}},[a("a-input",{attrs:{placeholder:"请输入用户昵称"},model:{value:e.queryParam.userNickname,callback:function(t){e.$set(e.queryParam,"userNickname",t)},expression:"queryParam.userNickname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"API接口"}},[a("a-input",{attrs:{placeholder:"请输入API接口地址"},model:{value:e.queryParam.apiEndpoint,callback:function(t){e.$set(e.queryParam,"apiEndpoint",t)},expression:"queryParam.apiEndpoint"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"请求方法"}},[a("a-select",{attrs:{placeholder:"请选择请求方法",allowClear:""},model:{value:e.queryParam.apiMethod,callback:function(t){e.$set(e.queryParam,"apiMethod",t)},expression:"queryParam.apiMethod"}},[a("a-select-option",{attrs:{value:"GET"}},[e._v("GET")]),a("a-select-option",{attrs:{value:"POST"}},[e._v("POST")]),a("a-select-option",{attrs:{value:"PUT"}},[e._v("PUT")]),a("a-select-option",{attrs:{value:"DELETE"}},[e._v("DELETE")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"响应状态"}},[a("a-select",{attrs:{placeholder:"请选择响应状态",allowClear:""},model:{value:e.queryParam.responseStatus,callback:function(t){e.$set(e.queryParam,"responseStatus",t)},expression:"queryParam.responseStatus"}},[a("a-select-option",{attrs:{value:200}},[e._v("200 成功")]),a("a-select-option",{attrs:{value:400}},[e._v("400 请求错误")]),a("a-select-option",{attrs:{value:401}},[e._v("401 未授权")]),a("a-select-option",{attrs:{value:403}},[e._v("403 禁止访问")]),a("a-select-option",{attrs:{value:404}},[e._v("404 未找到")]),a("a-select-option",{attrs:{value:500}},[e._v("500 服务器错误")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("API使用记录")}}},[e._v("导出")]),a("a-button",{attrs:{type:"primary",icon:"bar-chart"},on:{click:e.showStats}},[e._v("统计分析")])],1),a("div",[a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"responseStatus",fn:function(t){return[a("a-tag",{attrs:{color:200===t?"green":"red"}},[e._v("\n          "+e._s(200===t?"成功":"失败("+t+")")+"\n        ")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)}}])})],1),a("a-modal",{attrs:{title:"API使用记录详情",width:800,visible:e.detailVisible,footer:null},on:{cancel:function(t){e.detailVisible=!1}}},[e.detailData?a("div",[a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.detailData.user_id))]),a("a-descriptions-item",{attrs:{label:"用户昵称"}},[e._v(e._s(e.detailData.userNickname||"-"))]),a("a-descriptions-item",{attrs:{label:"API密钥"}},[e._v(e._s(e.detailData.api_key))]),a("a-descriptions-item",{attrs:{label:"API接口"}},[e._v(e._s(e.detailData.api_endpoint))]),a("a-descriptions-item",{attrs:{label:"请求方法"}},[e._v(e._s(e.detailData.api_method))]),a("a-descriptions-item",{attrs:{label:"响应状态"}},[a("a-tag",{attrs:{color:200===e.detailData.response_status?"green":"red"}},[e._v("\n            "+e._s(e.detailData.response_status)+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"响应时间"}},[e._v(e._s(e.detailData.response_time)+"ms")]),a("a-descriptions-item",{attrs:{label:"消耗Token"}},[e._v(e._s(e.detailData.tokens_used||"-"))]),a("a-descriptions-item",{attrs:{label:"消耗金额"}},[e._v("¥"+e._s(e.detailData.cost_amount||"0.00"))]),a("a-descriptions-item",{attrs:{label:"IP地址"}},[e._v(e._s(e.detailData.ip_address))]),a("a-descriptions-item",{attrs:{label:"调用时间"}},[e._v(e._s(e.formatDateTime(e.detailData.call_time)))]),a("a-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.formatDateTime(e.detailData.create_time)))])],1),a("h4",{staticStyle:{"margin-top":"16px"}},[e._v("请求参数")]),a("pre",{staticStyle:{background:"#f5f5f5",padding:"10px","border-radius":"4px","max-height":"200px","overflow-y":"auto"}},[e._v(e._s(e.detailData.request_params))]),e.detailData.error_message?a("div",[a("h4",[e._v("错误信息")]),a("a-alert",{attrs:{message:e.detailData.error_message,type:"error"}})],1):e._e(),e.detailData.plugin_name?a("div",[a("h4",[e._v("插件信息")]),a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"插件名称"}},[e._v(e._s(e.detailData.plugin_name))]),a("a-descriptions-item",{attrs:{label:"插件标识"}},[e._v(e._s(e.detailData.plugin_key))])],1)],1):e._e()],1):e._e()]),a("a-modal",{attrs:{title:"API使用统计分析",width:1e3,visible:e.statsVisible,footer:null},on:{cancel:function(t){e.statsVisible=!1}}},[e.statsData?a("div",[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总调用次数",value:e.statsData.total_calls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功调用",value:e.statsData.success_calls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"错误调用",value:e.statsData.error_calls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"平均响应时间",value:e.statsData.avg_response_time,suffix:"ms"}})],1)],1),a("a-divider"),a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:12}},[a("a-statistic",{attrs:{title:"总消耗Token",value:e.statsData.total_tokens}})],1),a("a-col",{attrs:{span:12}},[a("a-statistic",{attrs:{title:"总消耗金额",value:e.statsData.total_cost,prefix:"¥",precision:2}})],1)],1)],1):e._e()])],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o={name:"AicgUserApiUsageList",mixins:[s["a"],n["b"]],components:{},data:function(){return{description:"API使用记录管理页面",statsVisible:!1,statsData:null,detailVisible:!1,detailData:null,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"user_id",width:120},{title:"用户昵称",align:"center",dataIndex:"userNickname",width:120,ellipsis:!0},{title:"API接口",align:"center",dataIndex:"api_endpoint",width:200,ellipsis:!0},{title:"请求方法",align:"center",dataIndex:"api_method",width:80},{title:"响应状态",align:"center",dataIndex:"response_status",width:100,scopedSlots:{customRender:"responseStatus"}},{title:"响应时间(ms)",align:"center",dataIndex:"response_time",width:120,customRender:function(e){return e?"".concat(e,"ms"):"-"}},{title:"消耗Token",align:"center",dataIndex:"tokens_used",width:100,customRender:function(e){return e||"-"}},{title:"消耗金额",align:"center",dataIndex:"cost_amount",width:100,customRender:function(e){return e?"¥".concat(e):"-"}},{title:"调用时间",align:"center",dataIndex:"call_time",width:150,customRender:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:120,scopedSlots:{customRender:"action"}}],url:{list:"/demo/apiusage/list",delete:"/demo/apiusage/delete",deleteBatch:"/demo/apiusage/deleteBatch",exportXlsUrl:"/demo/apiusage/exportXls",importExcelUrl:"demo/apiusage/importExcel",edit:"/demo/apiusage/queryById"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"user_id",text:"用户ID"}),e.push({type:"string",value:"userNickname",text:"用户昵称"}),e.push({type:"string",value:"api_endpoint",text:"API接口地址"}),e.push({type:"string",value:"api_method",text:"请求方法"}),e.push({type:"int",value:"response_status",text:"响应状态码"}),e.push({type:"int",value:"response_time",text:"响应时间"}),e.push({type:"int",value:"tokens_used",text:"消耗Token数量"}),e.push({type:"BigDecimal",value:"cost_amount",text:"消耗金额"}),e.push({type:"Date",value:"call_time",text:"调用时间"}),this.superFieldList=e},showStats:function(){var e=this;this.$http.get("/demo/apiusage/getStats?userId=&timeRange=month").then((function(t){t.success?(e.statsData=t.result,e.statsVisible=!0):e.$message.error(t.message)}))},handleDetail:function(e){this.detailData=e,this.detailVisible=!0},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"}}},l=o,c=(a("c310"),a("2877")),d=Object(c["a"])(l,r,i,!1,null,"05aabdbb",null);t["default"]=d.exports},"899a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"推荐人ID"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.queryParam.referrerId,callback:function(t){e.$set(e.queryParam,"referrerId",t)},expression:"queryParam.referrerId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"被推荐人ID"}},[a("a-input",{attrs:{placeholder:"请输入被推荐人ID"},model:{value:e.queryParam.refereeId,callback:function(t){e.$set(e.queryParam,"refereeId",t)},expression:"queryParam.refereeId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"推荐码"}},[a("a-input",{attrs:{placeholder:"请输入推荐码"},model:{value:e.queryParam.referralCode,callback:function(t){e.$set(e.queryParam,"referralCode",t)},expression:"queryParam.referralCode"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待确认")]),a("a-select-option",{attrs:{value:2}},[e._v("已确认")]),a("a-select-option",{attrs:{value:3}},[e._v("已奖励")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("推荐关系")}}},[e._v("导出")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleConfirm(r)}}},[e._v("确认推荐")])]):e._e(),2===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleMarkRewarded(r)}}},[e._v("标记已奖励")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-referral-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("1de8"),l={name:"AicgUserReferralList",mixins:[s["a"],n["b"]],components:{AicgUserReferralModal:o["default"]},data:function(){return{description:"用户推荐关系管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"推荐人ID",align:"center",dataIndex:"referrerId"},{title:"被推荐人ID",align:"center",dataIndex:"refereeId"},{title:"推荐码",align:"center",dataIndex:"referralCode"},{title:"注册时间",align:"center",dataIndex:"registerTime"},{title:"首次充值时间",align:"center",dataIndex:"firstRechargeTime"},{title:"首次充值金额",align:"center",dataIndex:"firstRechargeAmount",customRender:function(e){return e?"¥".concat(e):"-"}},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){var t={1:"待确认",2:"已确认",3:"已奖励"},a={1:"orange",2:"blue",3:"green"};return'<span style="color: '.concat(a[e],'">').concat(t[e]||e,"</span>")},scopedSlots:{customRender:"htmlSlot"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/referral/list",delete:"/demo/referral/delete",deleteBatch:"/demo/referral/deleteBatch",exportXlsUrl:"/demo/referral/exportXls",importExcelUrl:"demo/referral/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"referrerId",text:"推荐人ID"}),e.push({type:"string",value:"refereeId",text:"被推荐人ID"}),e.push({type:"string",value:"referralCode",text:"推荐码"}),e.push({type:"Date",value:"registerTime",text:"注册时间"}),e.push({type:"Date",value:"firstRechargeTime",text:"首次充值时间"}),e.push({type:"BigDecimal",value:"firstRechargeAmount",text:"首次充值金额"}),e.push({type:"int",value:"status",text:"状态"}),this.superFieldList=e},handleConfirm:function(e){var t=this;this.$confirm({title:"确认推荐",content:"确定要确认该推荐关系吗？",onOk:function(){t.$http.post("/demo/referral/confirm?refereeId=".concat(e.refereeId,"&rechargeAmount=").concat(e.firstRechargeAmount||0)).then((function(e){e.success?(t.$message.success("确认成功"),t.loadData()):t.$message.error(e.message)}))}})},handleMarkRewarded:function(e){var t=this;this.$confirm({title:"标记已奖励",content:"确定要标记该推荐关系为已奖励吗？",onOk:function(){t.$http.post("/demo/referral/markRewarded?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("标记成功"),t.loadData()):t.$message.error(e.message)}))}})}}},c=l,d=(a("f7f1"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"63d19bce",null);t["default"]=u.exports},"8b4b":function(e,t,a){},"8d93":function(e,t,a){"use strict";var r=a("34d1"),i=a.n(r);i.a},"94d2":function(e,t,a){},"9bce":function(e,t,a){},"9caa":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-plub-shop-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("63be"),s={name:"AigcPlubShopModal",components:{AigcPlubShopForm:n["default"]},data:function(){return{title:"",width:1200,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.$refs.realForm&&this.$refs.realForm.handleClose&&this.$refs.realForm.handleClose(),this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},a214:function(e,t,a){"use strict";var r=a("727c"),i=a.n(r);i.a},a3f8:function(e,t,a){},aa09:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"讲师名"}},[a("a-input",{attrs:{placeholder:"请输入讲师名"},model:{value:e.queryParam.teachername,callback:function(t){e.$set(e.queryParam,"teachername",t)},expression:"queryParam.teachername"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"讲师介绍"}},[a("a-input",{attrs:{placeholder:"请输入讲师介绍"},model:{value:e.queryParam.teacherinfo,callback:function(t){e.$set(e.queryParam,"teacherinfo",t)},expression:"queryParam.teacherinfo"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"学习人数"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.studyperson_begin,callback:function(t){e.$set(e.queryParam,"studyperson_begin",t)},expression:"queryParam.studyperson_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.studyperson_end,callback:function(t){e.$set(e.queryParam,"studyperson_end",t)},expression:"queryParam.studyperson_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"课程数量"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.coursenum_begin,callback:function(t){e.$set(e.queryParam,"coursenum_begin",t)},expression:"queryParam.coursenum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.coursenum_end,callback:function(t){e.$set(e.queryParam,"coursenum_end",t)},expression:"queryParam.coursenum_end"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("视频讲师")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-video-teacher-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("33d4"),l={name:"AigcVideoTeacherList",mixins:[s["a"],n["b"]],components:{AigcVideoTeacherModal:o["default"]},data:function(){return{description:"视频讲师管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"讲师名",align:"center",dataIndex:"teachername"},{title:"讲师介绍",align:"center",dataIndex:"teacherinfo"},{title:"学习人数",align:"center",dataIndex:"studyperson"},{title:"课程数量",align:"center",dataIndex:"coursenum"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/videoteacher/aigcVideoTeacher/list",delete:"/videoteacher/aigcVideoTeacher/delete",deleteBatch:"/videoteacher/aigcVideoTeacher/deleteBatch",exportXlsUrl:"/videoteacher/aigcVideoTeacher/exportXls",importExcelUrl:"videoteacher/aigcVideoTeacher/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"teachername",text:"讲师名",dictCode:""}),e.push({type:"string",value:"teacherinfo",text:"讲师介绍",dictCode:""}),e.push({type:"int",value:"studyperson",text:"学习人数",dictCode:""}),e.push({type:"int",value:"coursenum",text:"课程数量",dictCode:""}),this.superFieldList=e}}},c=l,d=(a("a214"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"4f35a537",null);t["default"]=u.exports},aff5:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"讲师名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"teachername"}},[a("a-input",{attrs:{placeholder:"请输入讲师名"},model:{value:e.model.teachername,callback:function(t){e.$set(e.model,"teachername",t)},expression:"model.teachername"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"讲师介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"teacherinfo"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入讲师介绍"},model:{value:e.model.teacherinfo,callback:function(t){e.$set(e.model,"teacherinfo",t)},expression:"model.teacherinfo"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"学习人数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"studyperson"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入学习人数"},model:{value:e.model.studyperson,callback:function(t){e.$set(e.model,"studyperson",t)},expression:"model.studyperson"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"课程数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"coursenum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入课程数量"},model:{value:e.model.coursenum,callback:function(t){e.$set(e.model,"coursenum",t)},expression:"model.coursenum"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),{name:"AigcVideoTeacherForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{teachername:[{required:!0,message:"请输入讲师名!"}],teacherinfo:[{required:!0,message:"请输入讲师介绍!"}]},url:{add:"/videoteacher/aigcVideoTeacher/add",edit:"/videoteacher/aigcVideoTeacher/edit",queryById:"/videoteacher/aigcVideoTeacher/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}}),o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},b2e3:function(e,t,a){},b35e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"轮播图标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"title"}},[a("a-input",{attrs:{placeholder:"请输入轮播图标题"},model:{value:e.model.title,callback:function(t){e.$set(e.model,"title",t)},expression:"model.title"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"轮播图描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"description"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入轮播图描述"},model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"轮播图",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"imageUrl"}},[a("j-image-upload",{attrs:{isMultiple:""},model:{value:e.model.imageUrl,callback:function(t){e.$set(e.model,"imageUrl",t)},expression:"model.imageUrl"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"标签文字",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"badge"}},[a("a-input",{attrs:{placeholder:"请输入标签文字"},model:{value:e.model.badge,callback:function(t){e.$set(e.model,"badge",t)},expression:"model.badge"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"按钮文字",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"buttonText"}},[a("a-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.model.buttonText,callback:function(t){e.$set(e.model,"buttonText",t)},expression:"model.buttonText"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"按钮跳转链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"buttonLink"}},[a("a-input",{attrs:{placeholder:"请输入按钮跳转链接"},model:{value:e.model.buttonLink,callback:function(t){e.$set(e.model,"buttonLink",t)},expression:"model.buttonLink"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"排序序号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"sortOrder"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入排序序号"},model:{value:e.model.sortOrder,callback:function(t){e.$set(e.model,"sortOrder",t)},expression:"model.sortOrder"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"是否启用",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"status"}},[a("j-dict-select-tag",{attrs:{dictCode:"isTrue",placeholder:"请选择是否启用"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),{name:"AigcHomeCarouselForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{title:[{required:!0,message:"请输入轮播图标题!"}],imageUrl:[{required:!0,message:"请输入轮播图!"}],status:[{required:!0,message:"请输入是否启用!"}]},url:{add:"/aigc/aigcHomeCarousel/add",edit:"/aigc/aigcHomeCarousel/edit",queryById:"/aigc/aigcHomeCarousel/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}}),o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},b7b2:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-video-tutorial-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("7bf4"),s={name:"AigcVideoTutorialModal",components:{AigcVideoTutorialForm:n["default"]},data:function(){return{title:"",width:1200,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},bc94:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"用户ID",prop:"userId"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.model.userId,callback:function(t){e.$set(e.model,"userId",t)},expression:"model.userId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"通知类型",prop:"type"}},[a("a-select",{attrs:{placeholder:"请选择通知类型"},model:{value:e.model.type,callback:function(t){e.$set(e.model,"type",t)},expression:"model.type"}},[a("a-select-option",{attrs:{value:1}},[e._v("系统通知")]),a("a-select-option",{attrs:{value:2}},[e._v("交易通知")]),a("a-select-option",{attrs:{value:3}},[e._v("安全提醒")]),a("a-select-option",{attrs:{value:4}},[e._v("营销推送")])],1)],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"通知标题",prop:"title"}},[a("a-input",{attrs:{placeholder:"请输入通知标题"},model:{value:e.model.title,callback:function(t){e.$set(e.model,"title",t)},expression:"model.title"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"通知内容",prop:"content"}},[a("a-textarea",{attrs:{placeholder:"请输入通知内容",rows:4},model:{value:e.model.content,callback:function(t){e.$set(e.model,"content",t)},expression:"model.content"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"优先级",prop:"priority"}},[a("a-select",{attrs:{placeholder:"请选择优先级"},model:{value:e.model.priority,callback:function(t){e.$set(e.model,"priority",t)},expression:"model.priority"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通")]),a("a-select-option",{attrs:{value:2}},[e._v("重要")]),a("a-select-option",{attrs:{value:3}},[e._v("紧急")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否已读",prop:"isRead"}},[a("a-select",{attrs:{placeholder:"请选择是否已读"},model:{value:e.model.isRead,callback:function(t){e.$set(e.model,"isRead",t)},expression:"model.isRead"}},[a("a-select-option",{attrs:{value:0}},[e._v("未读")]),a("a-select-option",{attrs:{value:1}},[e._v("已读")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"跳转链接",prop:"actionUrl"}},[a("a-input",{attrs:{placeholder:"请输入跳转链接"},model:{value:e.model.actionUrl,callback:function(t){e.$set(e.model,"actionUrl",t)},expression:"model.actionUrl"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"阅读时间",prop:"readTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择阅读时间"},model:{value:e.model.readTime,callback:function(t){e.$set(e.model,"readTime",t)},expression:"model.readTime"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=a("c1df"),o=a.n(s),l={name:"AicgUserNotificationModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{userId:[{required:!0,message:"请输入用户ID!"}],type:[{required:!0,message:"请选择通知类型!"}],title:[{required:!0,message:"请输入通知标题!"}],content:[{required:!0,message:"请输入通知内容!"}],priority:[{required:!0,message:"请选择优先级!"}]},url:{add:"/demo/notification/add",edit:"/demo/notification/edit",queryById:"/demo/notification/queryById"}}},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={userId:t.model.userId,type:t.model.type,title:t.model.title,content:t.model.content,priority:t.model.priority,isRead:t.model.isRead,actionUrl:t.model.actionUrl};t.form.setFieldsValue(e),t.model.readTime&&(t.model.readTime=o()(t.model.readTime))})),e.id?this.title="编辑":(this.title="新增",this.model.priority=1,this.model.isRead=0)},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var s=Object.assign(e.model);s.readTime&&(s.readTime=s.readTime.format("YYYY-MM-DD HH:mm:ss")),Object(n["h"])(r,s,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()}}},c=l,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},c1ca:function(e,t,a){"use strict";var r=a("8660"),i=a.n(r);i.a},c310:function(e,t,a){"use strict";var r=a("484b"),i=a.n(r);i.a},c9ab:function(e,t,a){"use strict";var r=a("3dbb"),i=a.n(r);i.a},c9e1:function(e,t,a){},c9ec:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"轮播图标题"}},[a("a-input",{attrs:{placeholder:"请输入轮播图标题"},model:{value:e.queryParam.title,callback:function(t){e.$set(e.queryParam,"title",t)},expression:"queryParam.title"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否启用"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否启用",dictCode:"isTrue"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("首页轮播图")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-home-carousel-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("d1d1"),l=a("89f2"),c={name:"AigcHomeCarouselList",mixins:[s["a"],n["b"]],components:{AigcHomeCarouselModal:o["default"]},data:function(){var e=this;return{description:"首页轮播图管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"轮播图标题",align:"center",dataIndex:"title"},{title:"轮播图描述",align:"center",dataIndex:"description"},{title:"轮播图",align:"center",dataIndex:"imageUrl",scopedSlots:{customRender:"imgSlot"}},{title:"标签文字",align:"center",dataIndex:"badge"},{title:"按钮文字",align:"center",dataIndex:"buttonText"},{title:"按钮跳转链接",align:"center",dataIndex:"buttonLink"},{title:"排序序号",align:"center",dataIndex:"sortOrder"},{title:"是否启用",align:"center",dataIndex:"status",customRender:function(t){return Object(l["c"])(e.dictOptions.isTrue,t)}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/aigc/aigcHomeCarousel/list",delete:"/aigc/aigcHomeCarousel/delete",deleteBatch:"/aigc/aigcHomeCarousel/deleteBatch",exportXlsUrl:"/aigc/aigcHomeCarousel/exportXls",importExcelUrl:"aigc/aigcHomeCarousel/importExcel"},dictOptions:{isTrue:[]},superFieldList:[]}},created:function(){this.getSuperFieldList(),this.initDictConfig()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){var e=this;Object(l["d"])("isTrue").then((function(t){t.success&&(e.dictOptions.isTrue=t.result)}))},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"title",text:"轮播图标题",dictCode:""}),e.push({type:"string",value:"description",text:"轮播图描述",dictCode:""}),e.push({type:"string",value:"imageUrl",text:"轮播图",dictCode:""}),e.push({type:"string",value:"badge",text:"标签文字",dictCode:""}),e.push({type:"string",value:"buttonText",text:"按钮文字",dictCode:""}),e.push({type:"string",value:"buttonLink",text:"按钮跳转链接",dictCode:""}),e.push({type:"int",value:"sortOrder",text:"排序序号",dictCode:""}),e.push({type:"string",value:"status",text:"是否启用",dictCode:"isTrue"}),this.superFieldList=e}}},d=c,u=(a("6dba"),a("2877")),m=Object(u["a"])(d,r,i,!1,null,"fdec17ca",null);t["default"]=m.exports},d1d1:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-home-carousel-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("b35e"),s={name:"AigcHomeCarouselModal",components:{AigcHomeCarouselForm:n["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},d26c:function(e,t,a){"use strict";var r=a("2f42"),i=a.n(r);i.a},d403:function(e,t,a){"use strict";var r=a("24e6"),i=a.n(r);i.a},d600:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"user-center"},[a("a-card",{staticClass:"main-card",attrs:{title:"智界Aigc - 个人中心",bordered:!1}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:8}},[a("a-card",{staticClass:"info-card",attrs:{size:"small",title:"基本信息"}},[a("div",{staticClass:"user-info"},[a("a-avatar",{staticClass:"user-avatar",attrs:{size:80,src:e.getAvatarUrl(e.userInfo.avatar),icon:"user"}}),a("div",{staticClass:"info-content"},[a("h3",[e._v(e._s(e.userProfile.nickname||e.userInfo.realname))]),a("p",[e._v("用户ID: "+e._s(e.userInfo.id))]),a("p",[e._v("注册时间: "+e._s(e._f("formatDate")(e.userInfo.createTime)))])])],1),a("a-button",{attrs:{type:"primary",block:""},on:{click:function(t){e.showEditNickname=!0}}},[e._v("\n            编辑昵称\n          ")])],1)],1),a("a-col",{attrs:{span:8}},[a("a-card",{staticClass:"balance-card",attrs:{size:"small",title:"账户余额"}},[a("div",{staticClass:"balance-info"},[a("h2",{staticClass:"balance-amount"},[e._v("¥"+e._s(e.userProfile.accountBalance||0))]),a("p",[e._v("累计消费: ¥"+e._s(e.userProfile.totalConsumption||0))]),a("p",[e._v("累计充值: ¥"+e._s(e.userProfile.totalRecharge||0))])]),a("a-button",{attrs:{type:"primary",block:""},on:{click:function(t){e.showRecharge=!0}}},[e._v("\n            充值\n          ")])],1)],1),a("a-col",{attrs:{span:8}},[a("a-card",{staticClass:"api-card",attrs:{size:"small",title:"API密钥"}},[a("div",{staticClass:"api-key-info"},[a("a-input",{staticStyle:{"margin-bottom":"8px"},attrs:{value:e.maskedApiKey,readonly:"",placeholder:"暂无API密钥"}}),a("a-button-group",{staticStyle:{width:"100%"}},[a("a-button",{staticStyle:{width:"50%"},on:{click:e.toggleApiKeyVisibility}},[e._v("\n                "+e._s(e.apiKeyVisible?"隐藏":"显示")+"\n              ")]),a("a-button",{staticStyle:{width:"50%"},on:{click:e.regenerateApiKey}},[e._v("\n                重新生成\n              ")])],1),a("p",{staticClass:"api-key-tips"},[e._v("请妥善保管您的API密钥")])],1)])],1)],1),a("a-row",{staticStyle:{"margin-top":"24px"},attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-card",{staticClass:"exchange-card",attrs:{size:"small",title:"兑换码"}},[a("a-input-search",{attrs:{placeholder:"请输入兑换码","enter-button":"兑换"},on:{search:e.useExchangeCode},model:{value:e.exchangeCode,callback:function(t){e.exchangeCode=t},expression:"exchangeCode"}}),a("div",{staticStyle:{"margin-top":"16px"}},[a("a-button",{attrs:{block:""},on:{click:function(t){e.loadUsedCodes(),e.showUsedCodes=!0}}},[e._v("\n              查看兑换记录\n            ")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-card",{staticClass:"member-card",attrs:{size:"small",title:"会员信息"}},[a("div",{staticClass:"member-info"},[a("a-tag",{staticClass:"member-tag",attrs:{color:e.getMemberColor(e.userProfile.currentRole)}},[e._v("\n              "+e._s(e.getMemberText(e.userProfile.currentRole))+"\n            ")]),e.userProfile.memberExpireTime?a("p",[e._v("\n              到期时间: "+e._s(e._f("formatDate")(e.userProfile.memberExpireTime))+"\n            ")]):a("p",[e._v("\n              永久有效\n            ")])],1)])],1)],1),a("a-card",{staticClass:"transaction-card",staticStyle:{"margin-top":"24px"},attrs:{title:"消费记录"}},[a("a-table",{attrs:{columns:e.transactionColumns,"data-source":e.transactionList,pagination:e.transactionPagination,size:"small",loading:e.transactionLoading},on:{change:e.handleTransactionTableChange},scopedSlots:e._u([{key:"transactionType",fn:function(t){return[a("a-tag",{attrs:{color:e.getTransactionTypeColor(t)}},[e._v("\n            "+e._s(e.getTransactionTypeText(t))+"\n          ")])]}},{key:"amount",fn:function(t,r){return[a("span",{class:1===r.transactionType?"text-red":"text-green"},[e._v("\n            "+e._s(1===r.transactionType?"-":"+")+"¥"+e._s(t)+"\n          ")])]}},{key:"transactionTime",fn:function(t){return[e._v("\n          "+e._s(e._f("formatDateTime")(t))+"\n        ")]}}])})],1)],1),a("a-modal",{attrs:{title:"编辑昵称",visible:e.showEditNickname,confirmLoading:e.nicknameLoading},on:{ok:e.updateNickname,cancel:e.cancelEditNickname}},[a("a-input",{attrs:{placeholder:"请输入新昵称",maxLength:20},on:{pressEnter:e.updateNickname},model:{value:e.newNickname,callback:function(t){e.newNickname=t},expression:"newNickname"}})],1),a("a-modal",{attrs:{title:"账户充值",visible:e.showRecharge,confirmLoading:e.rechargeLoading},on:{ok:e.handleRecharge,cancel:function(t){e.showRecharge=!1}}},[a("p",[e._v("请选择充值金额或输入自定义金额：")]),a("a-radio-group",{staticStyle:{"margin-bottom":"16px"},model:{value:e.rechargeAmount,callback:function(t){e.rechargeAmount=t},expression:"rechargeAmount"}},[a("a-radio-button",{attrs:{value:10}},[e._v("¥10")]),a("a-radio-button",{attrs:{value:50}},[e._v("¥50")]),a("a-radio-button",{attrs:{value:100}},[e._v("¥100")]),a("a-radio-button",{attrs:{value:500}},[e._v("¥500")])],1),a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:1e4,placeholder:"自定义金额"},model:{value:e.customAmount,callback:function(t){e.customAmount=t},expression:"customAmount"}})],1),a("a-modal",{attrs:{title:"兑换记录",visible:e.showUsedCodes,footer:null,width:"800px"},on:{cancel:function(t){e.showUsedCodes=!1}}},[a("a-table",{attrs:{columns:e.exchangeCodeColumns,"data-source":e.usedCodesList,size:"small",pagination:!1,loading:e.exchangeCodesLoading},scopedSlots:e._u([{key:"codeType",fn:function(t){return[e._v("\n        "+e._s(e.getExchangeCodeTypeText(t))+"\n      ")]}},{key:"value",fn:function(t){return[e._v("\n        ¥"+e._s(t)+"\n      ")]}},{key:"usedTime",fn:function(t){return[e._v("\n        "+e._s(e._f("formatDateTime")(t))+"\n      ")]}}])})],1)],1)},i=[],n=a("a34a"),s=a.n(n),o=a("77ea"),l=a("2f62");function c(e,t,a,r,i,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(r,i)}function d(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){c(n,r,i,s,o,"next",e)}function o(e){c(n,r,i,s,o,"throw",e)}s(void 0)}))}}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function m(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var h={name:"UserCenter",data:function(){return{userProfile:{},showEditNickname:!1,showRecharge:!1,showUsedCodes:!1,newNickname:"",exchangeCode:"",rechargeAmount:50,customAmount:null,apiKeyVisible:!1,transactionList:[],usedCodesList:[],profileLoading:!1,transactionLoading:!1,exchangeCodesLoading:!1,nicknameLoading:!1,rechargeLoading:!1,transactionPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e,t){return"第 ".concat(t[0],"-").concat(t[1]," 条，共 ").concat(e," 条")}},transactionColumns:[{title:"交易时间",dataIndex:"transactionTime",key:"transactionTime",width:150,scopedSlots:{customRender:"transactionTime"}},{title:"交易类型",dataIndex:"transactionType",key:"transactionType",width:100,scopedSlots:{customRender:"transactionType"}},{title:"金额",dataIndex:"amount",key:"amount",width:120,scopedSlots:{customRender:"amount"}},{title:"余额",dataIndex:"balanceAfter",key:"balanceAfter",width:120,render:function(e){return"¥".concat(e)}},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0}],exchangeCodeColumns:[{title:"兑换码",dataIndex:"code",key:"code",width:150},{title:"类型",dataIndex:"codeType",key:"codeType",width:80,scopedSlots:{customRender:"codeType"}},{title:"价值",dataIndex:"value",key:"value",width:100,scopedSlots:{customRender:"value"}},{title:"使用时间",dataIndex:"usedTime",key:"usedTime",width:150,scopedSlots:{customRender:"usedTime"}}]}},computed:m(m({},Object(l["c"])(["userInfo"])),{},{maskedApiKey:function(){if(!this.userProfile.apiKey)return"";if(this.apiKeyVisible)return this.userProfile.apiKey;var e=this.userProfile.apiKey;return e.substring(0,8)+"****"+e.substring(e.length-4)}}),mounted:function(){this.loadUserProfile(),this.loadTransactions()},methods:{getAvatarUrl:function(e){return e?e.startsWith("http://")||e.startsWith("https://")?e:this.getFileAccessHttpUrl(e)||"":""},getFileAccessHttpUrl:function(e){return e?e.startsWith("http://")||e.startsWith("https://")?e:e.startsWith("uploads/")?window.getFileAccessHttpUrl?window.getFileAccessHttpUrl(e):e:this.$store.state.app.staticDomainURL+"/"+e:""},loadUserProfile:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.profileLoading=!0,e.next=4,Object(o["v"])();case 4:t=e.sent,t.success?this.userProfile=t.result:this.$message.error(t.message||"加载用户信息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$message.error("加载用户信息失败");case 12:return e.prev=12,this.profileLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[0,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),loadTransactions:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.transactionLoading=!0,e.next=4,Object(o["x"])({pageNo:this.transactionPagination.current,pageSize:this.transactionPagination.pageSize});case 4:t=e.sent,t.success?(this.transactionList=t.result.records||[],this.transactionPagination.total=t.result.total||0):this.$message.error(t.message||"加载交易记录失败"),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$message.error("加载交易记录失败");case 12:return e.prev=12,this.transactionLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[0,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),updateNickname:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.newNickname.trim()){e.next=3;break}return this.$message.warning("请输入昵称"),e.abrupt("return");case 3:return e.prev=3,this.nicknameLoading=!0,e.next=7,Object(o["E"])({nickname:this.newNickname});case 7:t=e.sent,t.success?(this.$message.success("昵称更新成功"),this.userProfile.nickname=this.newNickname,this.showEditNickname=!1,this.newNickname=""):this.$message.error(t.message||"昵称更新失败"),e.next=15;break;case 11:e.prev=11,e.t0=e["catch"](3),this.$message.error("昵称更新失败");case 15:return e.prev=15,this.nicknameLoading=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,this,[[3,11,15,18]])})));function t(){return e.apply(this,arguments)}return t}(),cancelEditNickname:function(){this.showEditNickname=!1,this.newNickname=""},regenerateApiKey:function(){var e=d(s.a.mark((function e(){var t=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm({title:"确认重新生成API密钥？",content:"重新生成后，原密钥将失效，请确保已更新相关配置",onOk:function(){var e=d(s.a.mark((function e(){var a;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["B"])();case 3:a=e.sent,a.success?(t.$message.success("API密钥重新生成成功"),t.userProfile.apiKey=a.result,t.apiKeyVisible=!0):t.$message.error(a.message||"API密钥重新生成失败"),e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),t.$message.error("API密钥重新生成失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));function a(){return e.apply(this,arguments)}return a}()});case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),toggleApiKeyVisibility:function(){this.apiKeyVisible=!this.apiKeyVisible},useExchangeCode:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.exchangeCode.trim()){e.next=3;break}return this.$message.warning("请输入兑换码"),e.abrupt("return");case 3:return e.prev=3,e.next=6,Object(o["I"])({code:this.exchangeCode});case 6:t=e.sent,t.success?(this.$message.success(t.message||"兑换成功"),this.exchangeCode="",this.loadUserProfile(),this.loadTransactions()):this.$message.error(t.message||"兑换失败"),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](3),this.$message.error("兑换失败");case 14:case"end":return e.stop()}}),e,this,[[3,10]])})));function t(){return e.apply(this,arguments)}return t}(),handleRecharge:function(){var e=this.customAmount||this.rechargeAmount;!e||e<=0?this.$message.warning("请选择或输入充值金额"):(this.$message.info("充值功能开发中，选择金额：¥".concat(e)),this.showRecharge=!1,this.customAmount=null)},loadUsedCodes:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.exchangeCodesLoading=!0,e.next=4,Object(o["z"])();case 4:t=e.sent,t.success?this.usedCodesList=t.result||[]:this.$message.error(t.message||"加载兑换记录失败"),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$message.error("加载兑换记录失败");case 12:return e.prev=12,this.exchangeCodesLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[0,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),handleTransactionTableChange:function(e){this.transactionPagination.current=e.current,this.transactionPagination.pageSize=e.pageSize,this.loadTransactions()},getMemberColor:function(e){var t={"普通用户":"default",VIP:"gold",SVIP:"purple","管理员":"red"};return t[e]||"default"},getMemberText:function(e){return e||"普通用户"},getTransactionTypeColor:function(e){var t={1:"red",2:"green",3:"blue",4:"orange"};return t[e]||"default"},getTransactionTypeText:function(e){var t={1:"消费",2:"充值",3:"退款",4:"兑换"};return t[e]||"未知"},getExchangeCodeTypeText:function(e){var t={1:"余额",2:"会员",3:"积分"};return t[e]||"未知"}},filters:{formatDate:function(e){return e?new Date(e).toLocaleDateString("zh-CN"):""},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN"):""}}},f=h,g=(a("c9ab"),a("2877")),b=Object(g["a"])(f,r,i,!1,null,"652ee442",null);t["default"]=b.exports},d846:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-plub-shop-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("63be"),s={name:"AigcPlubShopModal",components:{AigcPlubShopForm:n["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("7290"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"57b2909c",null);t["default"]=c.exports},d901:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"创作者名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"authorname"}},[a("a-input",{attrs:{placeholder:"请输入创作者名称"},model:{value:e.model.authorname,callback:function(t){e.$set(e.model,"authorname",t)},expression:"model.authorname"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"作者职位",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"title"}},[a("j-dict-select-tag",{attrs:{dictCode:"author_title",placeholder:"请选择作者职位",triggerChange:!0},model:{value:e.model.title,callback:function(t){e.$set(e.model,"title",t)},expression:"model.title"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"专业领域",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"expertise"}},[a("j-multi-select-tag",{attrs:{dictCode:"author_expertise",placeholder:"请选择专业领域（可多选）"},model:{value:e.model.expertise,callback:function(t){e.$set(e.model,"expertise",t)},expression:"model.expertise"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubnum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",disabled:!0},model:{value:e.model.plubnum,callback:function(t){e.$set(e.model,"plubnum",t)},expression:"model.plubnum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              此字段由系统自动统计，无需手动输入\n            ")])],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件使用总数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubusenum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",disabled:!0},model:{value:e.model.plubusenum,callback:function(t){e.$set(e.model,"plubusenum",t)},expression:"model.plubusenum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              此字段由系统自动统计，无需手动输入\n            ")])],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"创作者简介",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"createinfo"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入创作者简介"},model:{value:e.model.createinfo,callback:function(t){e.$set(e.model,"createinfo",t)},expression:"model.createinfo"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),{name:"AigcPlubAuthorForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{plubnum:0,plubusenum:0},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{},url:{add:"/plubauthor/aigcPlubAuthor/add",edit:"/plubauthor/aigcPlubAuthor/edit",queryById:"/plubauthor/aigcPlubAuthor/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){var e=Object.assign({},this.modelDefault,{plubnum:0,plubusenum:0});this.edit(e)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}}),o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},db58:function(e,t,a){"use strict";var r=a("4de9"),i=a.n(r);i.a},e292:function(e,t,a){"use strict";var r=a("b2e3"),i=a.n(r);i.a},e3ec:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-plub-author-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("d901"),s={name:"AigcPlubAuthorModal",components:{AigcPlubAuthorForm:n["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("3450"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"43dfa3dc",null);t["default"]=c.exports},f4b7:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"通知类型"}},[a("a-select",{attrs:{placeholder:"请选择通知类型",allowClear:""},model:{value:e.queryParam.type,callback:function(t){e.$set(e.queryParam,"type",t)},expression:"queryParam.type"}},[a("a-select-option",{attrs:{value:1}},[e._v("系统通知")]),a("a-select-option",{attrs:{value:2}},[e._v("交易通知")]),a("a-select-option",{attrs:{value:3}},[e._v("安全提醒")]),a("a-select-option",{attrs:{value:4}},[e._v("营销推送")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"优先级"}},[a("a-select",{attrs:{placeholder:"请选择优先级",allowClear:""},model:{value:e.queryParam.priority,callback:function(t){e.$set(e.queryParam,"priority",t)},expression:"queryParam.priority"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通")]),a("a-select-option",{attrs:{value:2}},[e._v("重要")]),a("a-select-option",{attrs:{value:3}},[e._v("紧急")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否已读"}},[a("a-select",{attrs:{placeholder:"请选择是否已读",allowClear:""},model:{value:e.queryParam.isRead,callback:function(t){e.$set(e.queryParam,"isRead",t)},expression:"queryParam.isRead"}},[a("a-select-option",{attrs:{value:0}},[e._v("未读")]),a("a-select-option",{attrs:{value:1}},[e._v("已读")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("用户通知消息")}}},[e._v("导出")]),e.selectedRowKeys.length>0?a("a-button",{attrs:{type:"primary",icon:"check"},on:{click:e.batchMarkRead}},[e._v("批量标记已读")]):e._e(),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"priority",fn:function(t){return[a("a-tag",{attrs:{color:1===t?"blue":2===t?"orange":"red"}},[e._v("\n          "+e._s(1===t?"普通":2===t?"重要":3===t?"紧急":t)+"\n        ")])]}},{key:"isRead",fn:function(t){return[a("a-tag",{attrs:{color:0===t?"red":"green"}},[e._v("\n          "+e._s(0===t?"未读":"已读")+"\n        ")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),0===r.isRead?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleMarkRead(r)}}},[e._v("标记已读")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-notification-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("bc94"),l={name:"AicgUserNotificationList",mixins:[s["a"],n["b"]],components:{AicgUserNotificationModal:o["default"]},data:function(){return{description:"用户通知消息管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"userId",width:120},{title:"通知标题",align:"center",dataIndex:"title",width:200,ellipsis:!0},{title:"通知类型",align:"center",dataIndex:"type",width:100,customRender:function(e){var t={1:"系统通知",2:"交易通知",3:"安全提醒",4:"营销推送"};return t[e]||e}},{title:"优先级",align:"center",dataIndex:"priority",width:80,scopedSlots:{customRender:"priority"}},{title:"是否已读",align:"center",dataIndex:"isRead",width:100,scopedSlots:{customRender:"isRead"}},{title:"阅读时间",align:"center",dataIndex:"readTime",width:150},{title:"创建时间",align:"center",dataIndex:"createTime",width:150},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/notification/list",delete:"/demo/notification/delete",deleteBatch:"/demo/notification/deleteBatch",exportXlsUrl:"/demo/notification/exportXls",importExcelUrl:"demo/notification/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"userId",text:"用户ID"}),e.push({type:"string",value:"title",text:"通知标题"}),e.push({type:"int",value:"type",text:"通知类型"}),e.push({type:"int",value:"priority",text:"优先级"}),e.push({type:"int",value:"isRead",text:"是否已读"}),e.push({type:"Date",value:"readTime",text:"阅读时间"}),e.push({type:"Date",value:"createTime",text:"创建时间"}),this.superFieldList=e},handleMarkRead:function(e){var t=this;this.$confirm({title:"标记已读",content:"确定要标记该通知为已读吗？",onOk:function(){t.$http.post("/demo/notification/markRead?id=".concat(e.id,"&userId=").concat(e.userId)).then((function(e){e.success?(t.$message.success("标记成功"),t.loadData()):t.$message.error(e.message)}))}})},batchMarkRead:function(){var e=this;0!==this.selectedRowKeys.length?this.$confirm({title:"批量标记已读",content:"确定要标记选中的 ".concat(this.selectedRowKeys.length," 条通知为已读吗？"),onOk:function(){var t=e.dataSource.find((function(t){return t.id===e.selectedRowKeys[0]}));t?e.$http.post("/demo/notification/batchMarkRead?userId=".concat(t.userId,"&ids=").concat(e.selectedRowKeys.join(","))).then((function(t){t.success?(e.$message.success(t.message),e.loadData(),e.onClearSelected()):e.$message.error(t.message)})):e.$message.error("获取用户ID失败")}}):this.$message.warning("请选择要标记的通知")}}},c=l,d=(a("7818"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"31be6548",null);t["default"]=u.exports},f7f1:function(e,t,a){"use strict";var r=a("663e"),i=a.n(r);i.a},facd8:function(e,t,a){"use strict";var r=a("31bf"),i=a.n(r);i.a},fe20:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"创作者名称"}},[a("a-input",{attrs:{placeholder:"请输入创作者名称"},model:{value:e.queryParam.authorname,callback:function(t){e.$set(e.queryParam,"authorname",t)},expression:"queryParam.authorname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件数"}},[a("a-input",{attrs:{placeholder:"请输入插件数"},model:{value:e.queryParam.plubnum,callback:function(t){e.$set(e.queryParam,"plubnum",t)},expression:"queryParam.plubnum"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件使用总数"}},[a("a-input",{attrs:{placeholder:"请输入插件使用总数"},model:{value:e.queryParam.plubusenum,callback:function(t){e.$set(e.queryParam,"plubusenum",t)},expression:"queryParam.plubusenum"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[e.isAdmin||!e.hasAuthorRecord?a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("\n      新增\n    ")]):e._e(),e.isAdmin?[a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("插件创作者")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("a-button",{attrs:{type:"default",icon:"sync",loading:e.updateAllLoading},on:{click:e.handleUpdateAllPluginCounts}},[e._v("更新所有插件数")]),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()]:e._e(),!e.isAdmin&&e.hasAuthorRecord?a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"8px"}},[e._v("\n      您已创建作者信息，如需修改请点击编辑\n    ")]):e._e()],2),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),e.isAdmin?[a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a",{attrs:{disabled:r.updateLoading},on:{click:function(t){return e.handleUpdatePluginCount(r.id)}}},[a("a-icon",{attrs:{type:"sync",spin:r.updateLoading}}),e._v("\n                  更新插件数\n                ")],1)]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)]:e._e()],2)}}])})],1),a("aigc-plub-author-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("266d"),l=a("ca00"),c={name:"AigcPlubAuthorList",mixins:[s["a"],n["b"]],components:{AigcPlubAuthorModal:o["default"]},data:function(){return{description:"插件创作者管理页面",updateAllLoading:!1,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"创作者名称",align:"center",dataIndex:"authorname"},{title:"作者职位",align:"center",dataIndex:"title_dictText"},{title:"专业领域",align:"center",dataIndex:"expertise_dictText",customRender:function(e){if(!e)return"-";var t=e.split(",");return t.length>2?t.slice(0,2).join(", ")+"...":e}},{title:"插件数",align:"center",dataIndex:"plubnum"},{title:"插件使用总数",align:"center",dataIndex:"plubusenum"},{title:"累计收益",align:"center",dataIndex:"totalIncome",customRender:function(e){return e?"¥"+parseFloat(e).toFixed(2):"¥0.00"}},{title:"创作者简介",align:"center",dataIndex:"createinfo"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/plubauthor/aigcPlubAuthor/list",delete:"/plubauthor/aigcPlubAuthor/delete",deleteBatch:"/plubauthor/aigcPlubAuthor/deleteBatch",exportXlsUrl:"/plubauthor/aigcPlubAuthor/exportXls",importExcelUrl:"plubauthor/aigcPlubAuthor/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)},isAdmin:function(){var e=localStorage.getItem("userRole");return e&&e.toLowerCase().includes("admin")},hasAuthorRecord:function(){return this.dataSource&&this.dataSource.length>0}},methods:{getQueryParams:function(){var e=Object.assign({},this.queryParam,this.isorter,this.filters);return this.isAdmin||(e.create_by=this.$store.getters.userInfo.username),e.field=this.getQueryField(),e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,Object(l["d"])(e)},initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"authorname",text:"创作者名称",dictCode:""}),e.push({type:"int",value:"plubnum",text:"插件数",dictCode:""}),e.push({type:"int",value:"plubusenum",text:"插件使用总数",dictCode:""}),e.push({type:"decimal",value:"totalIncome",text:"累计收益",dictCode:""}),e.push({type:"string",value:"createinfo",text:"创作者简介",dictCode:""}),this.superFieldList=e},handleUpdatePluginCount:function(e){var t=this,a=this,r=this.dataSource.find((function(t){return t.id===e}));r&&this.$set(r,"updateLoading",!0),this.$http.post("/plubauthor/aigcPlubAuthor/updatePluginCount",null,{params:{id:e}}).then((function(e){e.success?(a.$message.success("更新插件数成功！"),a.loadData()):a.$message.error(e.message||"更新插件数失败！")})).catch((function(e){a.$message.error("更新插件数异常！")})).finally((function(){r&&t.$set(r,"updateLoading",!1)}))},handleUpdateAllPluginCounts:function(){var e=this,t=this;this.updateAllLoading=!0,this.$http.post("/plubauthor/aigcPlubAuthor/updateAllPluginCounts").then((function(e){e.success?(t.$message.success(e.message||"批量更新插件数成功！"),t.loadData()):t.$message.error(e.message||"批量更新插件数失败！")})).catch((function(e){t.$message.error("批量更新插件数异常！")})).finally((function(){e.updateAllLoading=!1}))}}},d=c,u=(a("c1ca"),a("2877")),m=Object(u["a"])(d,r,i,!1,null,"7b4bf869",null);t["default"]=m.exports}}]);