<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Wed Jul 30 00:57:41 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,360</td>
<td class="Message">Shutting down ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">218</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,367</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,367</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,367</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,370</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,374</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">217</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,378</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2029</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,382</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2101</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,382</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">221</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Wed Jul 30 00:57:52 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:52,536</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:52,563</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 4292 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:52,564</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:57:52,911</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,095</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,097</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,236</td>
<td class="Message">Finished Spring Data repository scanning in 131ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,413</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,414</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,414</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,494</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,495</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,690</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,693</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,694</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,696</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,697</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,698</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,698</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,699</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,700</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,701</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,702</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,704</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,705</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,705</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,706</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,707</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,708</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,709</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,709</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,710</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,729</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,733</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,794</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,850</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,852</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$13d46adb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,888</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,312</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,313</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">239</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,315</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,318</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,352</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,498</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,505</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1e2693b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,511</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,521</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$31367b0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,554</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$c7b10092] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,558</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,795</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,802</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,803</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,803</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,967</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,968</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3370 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:56,597</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:56,598</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:56,598</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:57,986</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,795</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,795</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,908</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,911</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,911</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,911</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,727</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,727</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,730</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,732</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,732</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,732</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,908</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,135</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,557</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,566</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,597</td>
<td class="Message">剪映蒙版搜索服务初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">JianyingMaskSearchService.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,639</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,646</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,977</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,978</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,985</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,985</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,989</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,990</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,990</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1753808280978&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,991</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,991</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,991</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4e68aede</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:03,693</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,121</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,360</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,412</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,589</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,590</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,595</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,427</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,466</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,468</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,472</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,732</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,945</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,959</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,970</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,989</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,993</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,995</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,996</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,998</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,002</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,008</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,021</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,024</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,027</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,029</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,031</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,038</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,045</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,068</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,071</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,073</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,076</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,080</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,088</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,103</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,104</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,106</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,108</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,109</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,114</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,125</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,128</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,129</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,132</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,140</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,147</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,160</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,166</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,167</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,169</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,171</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,175</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,181</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,195</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,210</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,212</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,214</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,216</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,217</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,222</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,230</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,231</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,233</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,235</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,236</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,241</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,257</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,263</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,265</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,266</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,271</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,277</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,296</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,302</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,304</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,306</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,315</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,320</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,330</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,331</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,332</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,334</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,335</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,341</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,350</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,351</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,354</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,356</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,357</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,362</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,371</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,373</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,374</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,376</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,378</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,388</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,396</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,397</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,399</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,401</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,405</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,410</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,563</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,570</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,573</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,577</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,580</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,596</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,685</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,688</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,690</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,692</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,695</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,700</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,706</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,708</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,710</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,711</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,714</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,719</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,724</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,725</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,727</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,729</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,730</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,735</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,740</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,742</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,743</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,745</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,747</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,751</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,762</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,763</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,764</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,765</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,766</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,773</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,784</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,785</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,792</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,794</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,795</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,799</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,816</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,819</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,822</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,824</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,825</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,843</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,897</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,937</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,939</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,941</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,330</td>
<td class="Message">Started JeecgSystemApplication in 19.307 seconds (JVM running for 20.345)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,345</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,836</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,836</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,865</td>
<td class="Message">Completed initialization in 29 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,193</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTk5MjkxMDE0NjI2Mjk2OCwiZXhwIjoxNzUzNzM1MjQxLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNjkyMDQxOTY4fQ.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,194</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,195</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,194</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,241</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,801</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,810</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,811</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,825</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,825</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,866</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTk5MjkxMDE0NjI2Mjk2OCwiZXhwIjoxNzUzNzM1MjQxLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNjkyMDQxOTY4fQ.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,869</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,871</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,884</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,894</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,898</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,914</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,916</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,925</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,925</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,337</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,337</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,350</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,351</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,348</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,352</td>
<td class="Message">为用户 admin 使用新服务获取/生成邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,353</td>
<td class="Message">生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,358</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,360</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,360</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,361</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,363</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,364</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,429</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,429</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,473</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3725</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,474</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3744</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,641</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3756</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,641</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,643</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,195</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,197</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,197</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTk5MjkxMDE0NjI2Mjk2OCwiZXhwIjoxNzUzNzM1MjQxLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNjkyMDQxOTY4fQ.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,205</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,211</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,213</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,219</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,222</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,223</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTk5MjkxMDE0NjI2Mjk2OCwiZXhwIjoxNzUzNzM1MjQxLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNjkyMDQxOTY4fQ.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,224</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,224</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,221</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,225</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,231</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,232</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,232</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,235</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,236</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,246</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,248</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,249</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,836</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTk5MjkxMDE0NjI2Mjk2OCwiZXhwIjoxNzUzNzM1MjQxLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNjkyMDQxOTY4fQ.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,838</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,840</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,847</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,851</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,853</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,854</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,857</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,858</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,858</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,858</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,859</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,859</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,863</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,865</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTk5MjkxMDE0NjI2Mjk2OCwiZXhwIjoxNzUzNzM1MjQxLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNjkyMDQxOTY4fQ.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,865</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,866</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,866</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,870</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,872</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,872</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>
