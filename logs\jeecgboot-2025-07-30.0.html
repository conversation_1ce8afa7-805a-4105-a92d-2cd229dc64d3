<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Wed Jul 30 00:57:41 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,360</td>
<td class="Message">Shutting down ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">218</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,367</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,367</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,367</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,370</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,374</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">217</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,378</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2029</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,382</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2101</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:41,382</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">221</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Wed Jul 30 00:57:52 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:52,536</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:52,563</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 4292 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:52,564</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:57:52,911</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,095</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,097</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,236</td>
<td class="Message">Finished Spring Data repository scanning in 131ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,413</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,414</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,414</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,494</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,495</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,496</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,690</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,693</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,694</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,696</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,697</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,698</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,698</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,699</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,700</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,701</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,702</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,704</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,705</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,705</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,706</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,707</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,708</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,709</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,709</td>
<td class="Message">Bean &#39;(inner bean)#285a4fe3#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,710</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,729</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,733</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,794</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,850</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,852</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$13d46adb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:54,888</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,312</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,313</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">239</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,315</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,318</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,352</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,498</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,505</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1e2693b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,511</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,521</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$31367b0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,554</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$c7b10092] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,558</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,795</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,802</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,803</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,803</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,967</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:55,968</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3370 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:56,597</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:56,598</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:56,598</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:57,986</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,795</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,795</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,908</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,911</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,911</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:58,911</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,727</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,727</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,730</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,732</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,732</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,732</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:57:59,908</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,135</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,557</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,566</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,597</td>
<td class="Message">剪映蒙版搜索服务初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">JianyingMaskSearchService.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,639</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,646</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,977</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,978</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,985</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,985</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,989</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,990</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,990</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1753808280978&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,991</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,991</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:00,991</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4e68aede</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:03,693</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,121</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,360</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,412</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,589</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,590</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:05,595</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,427</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,466</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,468</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,472</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,732</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,945</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,959</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,970</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,989</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,993</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,995</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,996</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:07,998</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,002</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,008</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,021</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,024</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,027</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,029</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,031</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,038</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,045</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,068</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,071</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,073</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,076</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,080</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,088</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,103</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,104</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,106</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,108</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,109</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,114</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,125</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,128</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,129</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,132</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,140</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,147</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,160</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,166</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,167</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,169</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,171</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,175</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,181</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,195</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,210</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,212</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,214</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,216</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,217</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,222</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,230</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,231</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,233</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,235</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,236</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,241</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,257</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,263</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,265</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,266</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,271</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,277</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,296</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,302</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,304</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,306</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,315</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,320</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,330</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,331</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,332</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,334</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,335</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,341</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,350</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,351</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,354</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,356</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,357</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,362</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,371</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,373</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,374</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,376</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,378</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,388</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,396</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,397</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,399</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,401</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,405</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,410</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,563</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,570</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,573</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,577</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,580</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,596</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,685</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,688</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,690</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,692</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,695</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,700</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,706</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,708</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,710</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,711</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,714</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,719</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,724</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,725</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,727</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,729</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,730</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,735</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,740</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,742</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,743</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,745</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,747</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,751</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,762</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,763</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,764</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,765</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,766</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,773</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,784</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,785</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,792</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,794</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,795</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,799</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,816</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,819</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,822</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,824</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,825</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,843</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,897</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,937</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,939</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:08,941</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,330</td>
<td class="Message">Started JeecgSystemApplication in 19.307 seconds (JVM running for 20.345)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,345</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,836</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,836</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:11,865</td>
<td class="Message">Completed initialization in 29 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,193</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,194</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,195</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,194</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,241</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,801</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,810</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,811</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,825</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,825</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,824</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,866</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,869</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,871</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,884</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,894</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,898</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,914</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:41,916</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,919</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,925</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:41,925</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,337</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,337</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,350</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,351</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,348</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,352</td>
<td class="Message">为用户 admin 使用新服务获取/生成邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,353</td>
<td class="Message">生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,358</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,360</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,360</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,361</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,363</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,364</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,429</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,429</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,473</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3725</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,474</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3744</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:43,641</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3756</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,641</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:43,643</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,195</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,197</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,197</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,205</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,211</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,213</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,219</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,222</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,223</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,224</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,224</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,221</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,225</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,231</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,232</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,232</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,235</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,236</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:44,246</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,248</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:44,249</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,836</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,838</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,840</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,847</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,851</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,853</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,854</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,857</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,858</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,858</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,858</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,859</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,859</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,863</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,865</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,865</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,866</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,866</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:58:48,870</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,872</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:58:48,872</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,797</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,801</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,801</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,807</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,831</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,837</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,837</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,843</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,852</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,852</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,855</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,859</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,859</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,859</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 00:59:51,862</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,863</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,867</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,863</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,869</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,876</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 00:59:51,877</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,609</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,608</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,611</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,610</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,619</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,622</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,623</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,626</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,631</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,631</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,633</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,640</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,641</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,640</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,642</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,643</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,643</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,643</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:00:02,651</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,654</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:00:02,654</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,131</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,131</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,146</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,147</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,151</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,152</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,154</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,156</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:55,157</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:55,158</td>
<td class="Message">为用户 admin 使用新服务获取/生成邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:55,158</td>
<td class="Message">生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,158</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,159</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,162</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,165</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:55,247</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3725</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:55,248</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3744</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:55,322</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3756</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,323</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:55,323</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,706</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,706</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,719</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,720</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,720</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,721</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,721</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,721</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:58,724</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:58,725</td>
<td class="Message">为用户 admin 使用新服务获取/生成邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:58,725</td>
<td class="Message">生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,726</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,726</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,727</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,727</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:58,800</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3725</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:58,800</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3744</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:02:58,829</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3756</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,830</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:02:58,830</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,321</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,322</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,334</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,335</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:38:13,336</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,336</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,336</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,336</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:38:13,336</td>
<td class="Message">为用户 admin 使用新服务获取/生成邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:38:13,337</td>
<td class="Message">生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,337</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,338</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,338</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,345</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,345</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:38:13,438</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3725</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:38:13,439</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3744</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 01:38:13,539</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3756</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,540</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 01:38:13,540</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,125</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,126</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,127</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,128</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,140</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,145</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,147</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,146</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,151</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,153</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,154</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,154</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,154</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,286</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,287</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,291</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:47,296</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,296</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,296</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,298</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:47,300</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:48,814</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:48,822</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:41:48,823</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:48,824</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:48,825</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:48,825</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:41:48,825</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,767</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,770</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,773</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,775</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,778</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,779</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,781</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,781</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,782</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,783</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,785</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,785</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,786</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,786</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,786</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,787</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,795</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,797</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:42:15,801</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,803</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:42:15,804</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:48:30,207</td>
<td class="Message"> LogContent length : 21</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:48:30,208</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">80</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:48:30,209</td>
<td class="Message"> 用户名:  智界用户qO6NG6,退出成功！ </td>
<td class="MethodOfCaller">logout</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">216</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:48:30,209</td>
<td class="Message">🧹 开始清理用户登录缓存，用户：17382080720</td>
<td class="MethodOfCaller">cleanupUserLoginCache</td>
<td class="FileOfCaller">UserCacheCleanupService.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:48:30,211</td>
<td class="Message">✅ 用户 17382080720 登录缓存清理完成</td>
<td class="MethodOfCaller">cleanupUserLoginCache</td>
<td class="FileOfCaller">UserCacheCleanupService.java</td>
<td class="LineOfCaller">49</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:48:31,720</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:48:31,723</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:48:31,724</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:48:32,346</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:49:02,192</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:49:02,192</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:49:02,194</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:49:02,195</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:53:29,109</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:53:29,112</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:53:29,112</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:54:57,789</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:54:57,848</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:54:57,851</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:54:57,851</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:12,399</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:12,423</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:12,426</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:12,427</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:22,395</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:22,414</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:22,416</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:22,417</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:36,427</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:36,456</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:36,459</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:36,459</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,048</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,050</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,053</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,056</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,074</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,081</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,086</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,089</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,089</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,090</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,090</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,090</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,090</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,090</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,096</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,096</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,099</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,099</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:40,103</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,106</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:40,106</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:41,346</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:41,349</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:41,350</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,299</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,301</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,302</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,300</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,309</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,311</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,317</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,317</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,318</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,316</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,319</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,319</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,320</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,320</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,323</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,324</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:42,331</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,330</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,335</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,334</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:42,336</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,888</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,889</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:43,890</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:43,890</td>
<td class="Message">为用户 admin 使用新服务获取/生成邀请码: ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:43,890</td>
<td class="Message">生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,888</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,891</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,891</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,891</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,891</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,899</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,902</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,902</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,912</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:43,912</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:43,980</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3725</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:43,980</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3744</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:44,085</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3756</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:44,086</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:44,086</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:55:46,047</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:46,049</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:55:46,049</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:56:12,713</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:56:12,715</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:56:12,745</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:12,748</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:12,748</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:12,769</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:12,770</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:56:24,816</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:24,820</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:24,821</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:24,838</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:56:24,838</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:56:25,024</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:56:25,024</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,653</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1, 支付方式: alipay-page</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,681</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815425657_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:05,681</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:05,682</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,698</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815425657_e9ca23d6, 金额: 1</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,698</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815425657_e9ca23d6, 金额: 1</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">89</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,702</td>
<td class="Message">🔍 私钥调试信息:</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,703</td>
<td class="Message">🔍 私钥是否为空: false</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">47</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,703</td>
<td class="Message">🔍 私钥长度: 1624</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">49</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,703</td>
<td class="Message">🔍 私钥前50字符: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQ</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,704</td>
<td class="Message">🔍 私钥后50字符: XORYARFPfrcb5nvSh+GXn2TGujhV/JNnmeSI/gIpXbmN5MdLo=</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">51</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,704</td>
<td class="Message">🔍 私钥是否包含换行符: false</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,704</td>
<td class="Message">🔍 私钥是否包含回车符: false</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,704</td>
<td class="Message">🔍 私钥是否包含空格: false</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">54</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,704</td>
<td class="Message">🔍 私钥是否包含制表符: false</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,705</td>
<td class="Message">🔍 清理后私钥长度: 1624</td>
<td class="MethodOfCaller">getAlipayClient</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,769</td>
<td class="Message">💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815425657_e9ca23d6</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">116</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,769</td>
<td class="Message">🔍 支付表单内容长度: 1304</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,769</td>
<td class="Message">🔍 支付表单前100字符: &lt;form name=&quot;punchout_form&quot; method=&quot;post&quot; action=&quot;https://openapi-sandbox.dl.alipaydev.com/gateway.do</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">70</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:05,770</td>
<td class="Message">🔍 返回结果: orderId=RECHARGE_1753815425657_e9ca23d6, amount=1, payForm长度=1304</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:05,770</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:05,770</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:29,484</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:29,488</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:29,488</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:29,550</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:29,551</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:32,215</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-qr</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:32,223</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815452219_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:32,224</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:32,224</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:32,239</td>
<td class="Message">💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753815452219_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:32,239</td>
<td class="Message">💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753815452219_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">139</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:33,911</td>
<td class="Message">Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&amp;method=alipay.trade.precreate&amp;sign=v0Nk/hR0dAJZIvmGtTLUVpAOCGAcZPxa3v3xWv7Rjh7bQ8G+ORjryrysp14mjrrE9ft40ie50LrTtOQl8snQOx5QwdlXMN953M2zRMOQnEyWNVr99bwcopl3nY0wcF0gONq22lzEcHQ7zvFrRgZSgW6jJ0cnUNPz9b7nAg84LVT83lNO73JpJw6zIaaMJjcmnLhqD8ipsARoIcS0Cw0hh92VfUeu6HSzLapqqCzj4XgbHqYo4k0d/TplM9rylfmMI5CbCsD3V7iBJXPXpy8m1iUYin6TMQ3QgKhCJ+bRc8w/rvFcbACHlPXQCOQ7Ra6N3SXZtd2/SwRLqL6f3tqvXw==&amp;notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&amp;version=1.0&amp;app_id=9021000150681157&amp;sign_type=RSA2&amp;timestamp=2025-07-30 02:57:32^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&amp;format=json^_^ApplicationParams:biz_content={&quot;out_trade_no&quot;:&quot;RECHARGE_1753815452219_e9ca23d6&quot;,&quot;total_amount&quot;:&quot;1000&quot;,&quot;subject&quot;:&quot;智界Aigc账户充值&quot;,&quot;timeout_express&quot;:&quot;15m&quot;,&quot;goods_type&quot;:&quot;0&quot;,&quot;body&quot;:&quot;充值金额：¥1000&quot;}^_^15ms,1609ms,44ms^_^trace_id:0601ac3e175381545447274644924</td>
<td class="MethodOfCaller">logBizSummary</td>
<td class="FileOfCaller">AlipayLogger.java</td>
<td class="LineOfCaller">372</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:33,912</td>
<td class="Message">💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753815452219_e9ca23d6, 二维码: https://qr.alipay.com/bax096049brtnriphkr40018</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">164</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:33,912</td>
<td class="Message">🔍 扫码支付二维码URL: https://qr.alipay.com/bax096049brtnriphkr40018</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">103</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:33,913</td>
<td class="Message">🔍 返回扫码支付结果: orderId=RECHARGE_1753815452219_e9ca23d6, amount=1000, qrCode=已生成</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">110</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:33,913</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:33,913</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,769</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,776</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815458772_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:38,777</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:38,777</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,789</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815458772_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,789</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815458772_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">89</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,805</td>
<td class="Message">💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815458772_e9ca23d6</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">116</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,805</td>
<td class="Message">🔍 支付表单内容长度: 1304</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,805</td>
<td class="Message">🔍 支付表单前100字符: &lt;form name=&quot;punchout_form&quot; method=&quot;post&quot; action=&quot;https://openapi-sandbox.dl.alipaydev.com/gateway.do</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">70</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:57:38,805</td>
<td class="Message">🔍 返回结果: orderId=RECHARGE_1753815458772_e9ca23d6, amount=1000, payForm长度=1304</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:38,805</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:57:38,806</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,088</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,093</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815507091_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:27,094</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:27,094</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,108</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815507091_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,108</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815507091_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">89</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,122</td>
<td class="Message">💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815507091_e9ca23d6</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">116</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,123</td>
<td class="Message">🔍 支付表单内容长度: 1298</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,123</td>
<td class="Message">🔍 支付表单前100字符: &lt;form name=&quot;punchout_form&quot; method=&quot;post&quot; action=&quot;https://openapi-sandbox.dl.alipaydev.com/gateway.do</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">70</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:27,124</td>
<td class="Message">🔍 返回结果: orderId=RECHARGE_1753815507091_e9ca23d6, amount=1000, payForm长度=1298</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:27,124</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:27,124</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:28,994</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,001</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815508997_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:29,001</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:29,002</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,010</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815508997_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,011</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815508997_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">89</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,026</td>
<td class="Message">💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815508997_e9ca23d6</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">116</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,026</td>
<td class="Message">🔍 支付表单内容长度: 1304</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,026</td>
<td class="Message">🔍 支付表单前100字符: &lt;form name=&quot;punchout_form&quot; method=&quot;post&quot; action=&quot;https://openapi-sandbox.dl.alipaydev.com/gateway.do</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">70</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:29,027</td>
<td class="Message">🔍 返回结果: orderId=RECHARGE_1753815508997_e9ca23d6, amount=1000, payForm长度=1304</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:29,027</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:29,028</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:44,691</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:44,691</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:58:44,840</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:44,843</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:44,843</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:44,860</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:58:44,860</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:10,873</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:10,873</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:11,078</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:11,081</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:11,081</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:11,099</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:11,099</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:26,744</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:26,744</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:27,029</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:27,033</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:27,033</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:27,049</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:27,050</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:38,335</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:38,338</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:38,338</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:38,386</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:38,387</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:40,850</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-qr</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:40,855</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815580853_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:40,856</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:40,856</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:40,870</td>
<td class="Message">💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753815580853_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:40,871</td>
<td class="Message">💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753815580853_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">139</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:41,737</td>
<td class="Message">Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&amp;method=alipay.trade.precreate&amp;sign=ZcgWuEVQEmEfCqyYDrGIIVTUmbxIY5nZ2orMvslAlYiaXKk0qQgiOWYoiAnWW11V+095jHq3bJzBQ4WGi4dKomTOqT3QJ3KNCO5QaqNBlx32JRElR3Ul9UAjbtlTMnn7o8l+6C7RHBBARB/xFHK9R96uhGdd3Ze8cO1pxd8sXjWE0N+P/iacvh7BV27WWQaz0zH/cd7ajF/CauAC9myFkE16BdtHJqNw2Y7g/zHI6iKNeutxukhx5E0rs/cx7RV6vGD9Bbo5psJjQYekDvpKSBAFMB8UomjXNVFkflUakNH3qQgb9yotQhVNuhjJ4aQ04kdD8E8uFgioGzrRWvFoRA==&amp;notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&amp;version=1.0&amp;app_id=9021000150681157&amp;sign_type=RSA2&amp;timestamp=2025-07-30 02:59:40^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&amp;format=json^_^ApplicationParams:biz_content={&quot;out_trade_no&quot;:&quot;RECHARGE_1753815580853_e9ca23d6&quot;,&quot;total_amount&quot;:&quot;1000&quot;,&quot;subject&quot;:&quot;智界Aigc账户充值&quot;,&quot;timeout_express&quot;:&quot;15m&quot;,&quot;goods_type&quot;:&quot;0&quot;,&quot;body&quot;:&quot;充值金额：¥1000&quot;}^_^13ms,834ms,19ms^_^trace_id:0601ac3e175381558309980264924</td>
<td class="MethodOfCaller">logBizSummary</td>
<td class="FileOfCaller">AlipayLogger.java</td>
<td class="LineOfCaller">372</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:41,737</td>
<td class="Message">💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753815580853_e9ca23d6, 二维码: https://qr.alipay.com/bax06504ap84dfwtnibo0027</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">164</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:41,738</td>
<td class="Message">🔍 扫码支付二维码URL: https://qr.alipay.com/bax06504ap84dfwtnibo0027</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">103</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 02:59:41,738</td>
<td class="Message">🔍 返回扫码支付结果: orderId=RECHARGE_1753815580853_e9ca23d6, amount=1000, qrCode=已生成</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">110</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:41,738</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 02:59:41,738</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:25,804</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:25,805</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,814</td>
<td class="Message">【websocket消息】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,857</td>
<td class="Message">=== 开始检查用户admin角色 ===</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,858</td>
<td class="Message">用户ID：e9ca23d68d884d4ebb19d07889727dae</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,872</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">47</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,878</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,878</td>
<td class="Message">*** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">60</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,882</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:28,882</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:30,850</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:30,851</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:30,851</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:30,851</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:30,851</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:30,851</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:30,888</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:30,889</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:30,890</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:30,890</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:30,896</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:30,897</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:33,222</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:33,225</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:33,226</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:33,259</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:33,259</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:35,700</td>
<td class="Message">【websocket消息】有新的连接，总数为:2</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:37,140</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-qr</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:37,147</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815637143_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:37,149</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:37,149</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:37,163</td>
<td class="Message">💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753815637143_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:37,163</td>
<td class="Message">💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753815637143_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">139</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:38,191</td>
<td class="Message">Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&amp;method=alipay.trade.precreate&amp;sign=NORTi15KyUgYFo6IxYaA3s9w/a2jrM0Klyh1QzWZyluUvZX28Cdp7M6mZegENRvwLSYDT5kvU3SFQ+fXe47iAqifWFnoJahq64ID4CSkJK5LFNYcdOFOSiQ/VVaC522VlJzjzilFa7W4kJ9flETxvsBdVUxVJ2GIzshTGlq60aM+x/0fHd+iLEznuM8OQajXsrap2TAcFbQNvNmdOfzHPnq97aSYQJ2ZNddazxcLsUswDTYYhRKVoO2oZUnA8e2okryAufYLVjwRbHMJd9RwSc/4AONuJ8LjhqVmP3xSy5xCO4WrnxC4fleHaaQ76po9ttjnpQI12ocfU1ZFqVm0aQ==&amp;notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&amp;version=1.0&amp;app_id=9021000150681157&amp;sign_type=RSA2&amp;timestamp=2025-07-30 03:00:37^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&amp;format=json^_^ApplicationParams:biz_content={&quot;out_trade_no&quot;:&quot;RECHARGE_1753815637143_e9ca23d6&quot;,&quot;total_amount&quot;:&quot;1000&quot;,&quot;subject&quot;:&quot;智界Aigc账户充值&quot;,&quot;timeout_express&quot;:&quot;15m&quot;,&quot;goods_type&quot;:&quot;0&quot;,&quot;body&quot;:&quot;充值金额：¥1000&quot;}^_^15ms,997ms,16ms^_^trace_id:060d2b35175381563937879295993</td>
<td class="MethodOfCaller">logBizSummary</td>
<td class="FileOfCaller">AlipayLogger.java</td>
<td class="LineOfCaller">372</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:38,192</td>
<td class="Message">💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753815637143_e9ca23d6, 二维码: https://qr.alipay.com/bax09168vu7u5twelspg006f</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">164</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:38,192</td>
<td class="Message">🔍 扫码支付二维码URL: https://qr.alipay.com/bax09168vu7u5twelspg006f</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">103</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:38,192</td>
<td class="Message">🔍 返回扫码支付结果: orderId=RECHARGE_1753815637143_e9ca23d6, amount=1000, qrCode=已生成</td>
<td class="MethodOfCaller">createQrPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">110</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:38,193</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:38,193</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,611</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,618</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815645615_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:45,618</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:45,618</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,631</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815645615_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,631</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815645615_e9ca23d6, 金额: 1000</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">89</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,645</td>
<td class="Message">💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815645615_e9ca23d6</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">116</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,646</td>
<td class="Message">🔍 支付表单内容长度: 1312</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,646</td>
<td class="Message">🔍 支付表单前100字符: &lt;form name=&quot;punchout_form&quot; method=&quot;post&quot; action=&quot;https://openapi-sandbox.dl.alipaydev.com/gateway.do</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">70</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:45,646</td>
<td class="Message">🔍 返回结果: orderId=RECHARGE_1753815645615_e9ca23d6, amount=1000, payForm长度=1312</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:45,647</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:45,648</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:48,460</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:48,460</td>
<td class="Message">【websocket消息】连接断开，总数为:1</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:55,046</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:55,049</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:55,050</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:55,100</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:55,100</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,368</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,368</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,370</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,369</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,379</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,379</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=26, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,382</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,382</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,382</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,384</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,384</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,384</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,385</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,385</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,386</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,389</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,392</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,393</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:00:57,406</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,408</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:00:57,409</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:02,134</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:02,140</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:02,143</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:02,143</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:02,150</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:02,153</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:02,153</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,686</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 100, 支付方式: alipay-page</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,699</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753815690690_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1833</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:30,700</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:30,700</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,713</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815690690_e9ca23d6, 金额: 100</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,713</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815690690_e9ca23d6, 金额: 100</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">89</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,726</td>
<td class="Message">💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815690690_e9ca23d6</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">116</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,727</td>
<td class="Message">🔍 支付表单内容长度: 1300</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,727</td>
<td class="Message">🔍 支付表单前100字符: &lt;form name=&quot;punchout_form&quot; method=&quot;post&quot; action=&quot;https://openapi-sandbox.dl.alipaydev.com/gateway.do</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">70</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:30,728</td>
<td class="Message">🔍 返回结果: orderId=RECHARGE_1753815690690_e9ca23d6, amount=100, payForm长度=1300</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:30,728</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:30,728</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,100</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,101</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,101</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,103</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,105</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,107</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,108</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,109</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=27, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,109</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,113</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,114</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,112</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,117</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,118</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,110</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,118</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,123</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,123</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,124</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,127</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,132</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,132</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,134</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:42,135</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,136</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,136</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,138</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:42,138</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:01:44,659</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:44,662</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:44,662</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:44,685</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:01:44,686</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:02:45,454</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:02:45,454</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:02:45,466</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:02:45,469</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:02:45,470</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:02:45,502</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:02:45,504</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:02,861</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:02,861</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:02,883</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:02,886</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:02,886</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:02,911</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:02,912</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:14,803</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:14,803</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:14,977</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:14,979</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:14,980</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:15,013</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:15,013</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:26,298</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:26,299</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:03:26,546</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:26,549</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:26,549</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:26,590</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:03:26,590</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,805</td>
<td class="Message">【websocket消息】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,913</td>
<td class="Message">=== 开始检查用户admin角色 ===</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,914</td>
<td class="Message">用户ID：e9ca23d68d884d4ebb19d07889727dae</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,926</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">47</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,931</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,931</td>
<td class="Message">*** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">60</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,936</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:14,937</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:19,855</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:19,857</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:19,858</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:19,859</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:19,859</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:19,860</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:19,889</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:19,890</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:19,890</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:19,890</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:19,898</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:19,899</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:21,481</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:21,484</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:21,484</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:21,514</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:21,514</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:24,807</td>
<td class="Message">【websocket消息】有新的连接，总数为:2</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:47,773</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:47,773</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:04:47,785</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:47,787</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:47,788</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:47,813</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:04:47,813</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:08,406</td>
<td class="Message">【websocket消息】连接断开，总数为:1</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:08,406</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:09,817</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:09,817</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:11,554</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:11,557</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:11,557</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:11,609</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:11,610</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,932</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,934</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,936</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,934</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,942</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,947</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=27, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,953</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,954</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,955</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,956</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,956</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,957</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,957</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,969</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,970</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,971</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:16,974</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,973</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,976</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,978</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:16,978</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:18,220</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:18,224</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:18,226</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:18,227</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:18,227</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:18,229</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:18,229</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:25,528</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:25,528</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:30,826</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:30,854</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:30,854</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:30,876</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:30,876</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:35,145</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:35,145</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:35,164</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:35,167</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:35,168</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:35,206</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:35,206</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:58,453</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:58,453</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:05:59,877</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:59,880</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:59,881</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:59,943</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:05:59,944</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:07:16,410</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:07:16,410</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:07:18,955</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:07:18,958</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:07:18,959</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:07:19,006</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:07:19,007</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:03,777</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:03,780</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:03,780</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:03,822</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:03,823</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:09,472</td>
<td class="Message">🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=3, duration=12, amount=489, planName=SVIP年卡, features=[解锁全部课程, 插件最高折扣, 邀请奖励50%, 调用工作流最高折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2377</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:09,491</td>
<td class="Message">🎯 createMembershipOrder - 订单创建成功: {amount=489.0, orderId=ORDER_1753816449475_254, createTime=Wed Jul 30 03:14:09 CST 2025, planName=SVIP年卡, transactionId=406fb536bf914262869679e757f3c57b, status=pending}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2452</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:09,491</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:09,492</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:13,327</td>
<td class="Message">🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=2, duration=12, amount=298, planName=VIP年卡, features=[解锁VIP课程, 插件基础折扣, 邀请奖励35%基础比例, 调用工作流基础折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2377</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:13,334</td>
<td class="Message">🎯 createMembershipOrder - 订单创建成功: {amount=298.0, orderId=ORDER_1753816453330_687, createTime=Wed Jul 30 03:14:13 CST 2025, planName=VIP年卡, transactionId=6bc63a841cff4b84b578d88bd04c222a, status=pending}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2452</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:13,334</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:13,334</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:15,621</td>
<td class="Message">🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=1, duration=1, amount=29, planName=VIP月卡, features=[解锁VIP课程, 插件基础折扣, 邀请奖励35%基础比例, 调用工作流基础折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2377</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:15,627</td>
<td class="Message">🎯 createMembershipOrder - 订单创建成功: {amount=29.0, orderId=ORDER_1753816455624_790, createTime=Wed Jul 30 03:14:15 CST 2025, planName=VIP月卡, transactionId=72e0492041214f48b376cdc9b12a92b5, status=pending}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2452</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:15,628</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:15,628</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:17,479</td>
<td class="Message">🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=3, duration=12, amount=489, planName=SVIP年卡, features=[解锁全部课程, 插件最高折扣, 邀请奖励50%, 调用工作流最高折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2377</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:14:17,488</td>
<td class="Message">🎯 createMembershipOrder - 订单创建成功: {amount=489.0, orderId=ORDER_1753816457482_308, createTime=Wed Jul 30 03:14:17 CST 2025, planName=SVIP年卡, transactionId=ccb9a52e7784465e902b80e3776424ed, status=pending}</td>
<td class="MethodOfCaller">createMembershipOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">2452</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:17,488</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:14:17,489</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:16:48,505</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:16:48,505</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:16:48,893</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:16:48,895</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:16:48,896</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:16:48,924</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:16:48,925</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:17:11,612</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:17:11,612</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:17:11,855</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:11,858</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:11,858</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:11,886</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:11,887</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:17:32,789</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:17:32,789</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:17:33,100</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:33,102</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:33,103</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:33,127</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:17:33,128</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:18:52,021</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:18:52,021</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:18:52,428</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:18:52,431</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:18:52,431</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:18:52,468</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:18:52,469</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:19:08,383</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:19:08,383</td>
<td class="Message">————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1</td>
<td class="MethodOfCaller">doGetAuthenticationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">99</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:19:08,563</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3389</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:19:08,567</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:19:08,568</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:19:08,639</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:19:08,640</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,860</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,862</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,863</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,861</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,880</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,880</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,885</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,888</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,890</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,890</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,890</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,890</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,896</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,896</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-30 03:39:39,897</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,899</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,899</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,899</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,899</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,901</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-30 03:39:39,901</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>
