{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753821760867}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport QuickRecharge from '@/components/QuickRecharge.vue';\nimport { createMembershipOrder, payOrder } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage: WebsitePage,\n    QuickRecharge: QuickRecharge\n  },\n  data: function data() {\n    return {\n      // 支付相关\n      paymentLoading: false,\n      showPaymentModal: false,\n      selectedPlan: null,\n      selectedPaymentMethod: 'alipay-qr',\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null,\n      checkingStatus: false,\n      plans: [{\n        id: 1,\n        name: 'VIP月卡',\n        price: '29',\n        originalPrice: '39',\n        discountText: '限时7.4折',\n        saveAmount: '10',\n        period: '月',\n        description: '适合体验用户的基础功能',\n        features: [{\n          text: '解锁VIP课程',\n          disabled: false\n        }, {\n          text: '插件基础折扣',\n          disabled: false\n        }, {\n          text: '邀请奖励35%基础比例',\n          disabled: false\n        }, {\n          text: '调用工作流基础折扣',\n          disabled: false\n        }, {\n          text: '复制所有工作流',\n          disabled: true\n        }, {\n          text: '解锁流媒体转换',\n          disabled: true\n        }, {\n          text: '部分插件免费',\n          disabled: true\n        }],\n        buttonText: '立即购买',\n        featured: false\n      }, {\n        id: 2,\n        name: 'VIP年卡',\n        price: '298',\n        originalPrice: '468',\n        discountText: '限时6.4折',\n        saveAmount: '170',\n        period: '年',\n        description: '适合长期使用的优惠套餐',\n        features: [{\n          text: '解锁VIP课程',\n          disabled: false\n        }, {\n          text: '插件基础折扣',\n          disabled: false\n        }, {\n          text: '邀请奖励35%基础比例',\n          disabled: false\n        }, {\n          text: '调用工作流基础折扣',\n          disabled: false\n        }, {\n          text: '复制所有工作流',\n          disabled: true\n        }, {\n          text: '解锁流媒体转换',\n          disabled: true\n        }, {\n          text: '部分插件免费',\n          disabled: true\n        }],\n        buttonText: '立即购买',\n        featured: false\n      }, {\n        id: 3,\n        name: 'SVIP年卡',\n        price: '489',\n        originalPrice: '999',\n        discountText: '限时4.9折',\n        saveAmount: '510',\n        period: '年',\n        description: '适合专业用户的全功能套餐',\n        features: [{\n          text: '解锁<strong class=\"highlight\">全部课程</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '插件<strong class=\"highlight\">最高折扣</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '邀请<strong class=\"highlight\">奖励50%</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '调用工作流<strong class=\"highlight\">最高折扣</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '复制<strong class=\"highlight\">所有工作流</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '<strong class=\"highlight\">解锁流媒体转换</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '部分<strong class=\"highlight\">插件免费</strong>',\n          disabled: false,\n          exclusive: true\n        }],\n        buttonText: '立即购买',\n        featured: true\n      }]\n    };\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer);\n    }\n  },\n  methods: {\n    // 获取按钮文本（续费/升级/购买）\n    getButtonText: function getButtonText(plan) {\n      var userRole = this.getUserRole(); // 获取用户当前角色\n\n      var planRole = this.getPlanRole(plan.id); // 获取套餐对应角色\n      // 管理员显示\"立即购买\"（管理员不需要购买会员，但可以测试）\n\n      if (userRole === 'admin') {\n        return '立即购买';\n      }\n\n      if (userRole === planRole) {\n        return '续费会员'; // 相同等级显示续费\n      } else if (this.isUpgrade(userRole, planRole)) {\n        return '升级会员'; // 升级显示升级\n      } else {\n          return '立即购买'; // 新购买显示购买\n        }\n    },\n    // 获取套餐对应的角色\n    getPlanRole: function getPlanRole(planId) {\n      var roleMap = {\n        1: 'user',\n        2: 'VIP',\n        3: 'SVIP'\n      };\n      return roleMap[planId] || 'user';\n    },\n    // 判断是否为升级\n    isUpgrade: function isUpgrade(currentRole, targetRole) {\n      var roleLevel = {\n        'user': 1,\n        'VIP': 2,\n        'SVIP': 3,\n        'admin': 999 // 管理员级别最高\n\n      };\n      return roleLevel[targetRole] > (roleLevel[currentRole] || 0);\n    },\n    // 是否显示升级提示\n    showUpgradeNotice: function showUpgradeNotice(plan) {\n      var userRole = this.getUserRole();\n      var planRole = this.getPlanRole(plan.id); // 只有VIP升级到SVIP时显示特殊提示\n\n      return userRole === 'VIP' && planRole === 'SVIP';\n    },\n    // 获取升级提示文本\n    getUpgradeNoticeText: function getUpgradeNoticeText(plan) {\n      var userRole = this.getUserRole();\n      var planRole = this.getPlanRole(plan.id);\n\n      if (userRole === 'VIP' && planRole === 'SVIP') {\n        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~';\n      }\n\n      return '';\n    },\n    // 获取用户当前角色\n    getUserRole: function getUserRole() {\n      // 优先从userInfo中获取角色信息\n      if (this.userInfo && this.userInfo.currentRole) {\n        console.log('🔍 Membership: getUserRole - 从userInfo获取角色:', this.userInfo.currentRole);\n        return this.userInfo.currentRole;\n      } // 从localStorage获取用户角色\n\n\n      try {\n        // 尝试从多个可能的存储位置获取角色信息\n        var userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');\n        var userRole = localStorage.getItem('userRole');\n        var roleCode = localStorage.getItem('roleCode');\n        console.log('🔍 Membership: getUserRole - userInfo from localStorage:', userInfo);\n        console.log('🔍 Membership: getUserRole - userRole from localStorage:', userRole);\n        console.log('🔍 Membership: getUserRole - roleCode from localStorage:', roleCode); // 尝试多种可能的角色字段\n\n        var role = userInfo.role || userInfo.userRole || userInfo.roleCode || userRole || roleCode; // 如果还是没有找到角色信息，默认为普通用户\n\n        if (!role) {\n          role = 'user'; // 默认为普通用户角色\n        }\n\n        console.log('🔍 Membership: getUserRole - 最终获取到的角色:', role);\n        return role;\n      } catch (error) {\n        console.warn('🔍 Membership: 获取用户角色失败:', error);\n        return 'user'; // 出错时默认为普通用户\n      }\n    },\n    handleSubscribe: function () {\n      var _handleSubscribe = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(plan) {\n        var token, orderData, response, orderResult;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                console.log('选择购买套餐:', plan); // 检查用户登录状态\n\n                token = this.$ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context.next = 6;\n                  break;\n                }\n\n                this.$message.warning('请先登录后再购买会员');\n                this.$router.push('/user/login');\n                return _context.abrupt(\"return\");\n\n              case 6:\n                _context.prev = 6;\n                this.paymentLoading = true; // 准备会员订单数据（与充值订单结构保持一致）\n\n                orderData = {\n                  membershipLevel: plan.id,\n                  duration: plan.name.includes('月') ? 1 : 12,\n                  // 根据套餐名称判断时长\n                  amount: parseFloat(plan.price),\n                  planName: plan.name,\n                  paymentMethod: 'alipay' // 默认支付宝，与充值保持一致\n\n                };\n                console.log('🎯 创建会员订单:', orderData);\n                this.$message.loading('正在创建订单...', 0); // 创建会员订单（使用与充值相同的交易记录系统）\n\n                _context.next = 13;\n                return createMembershipOrder(orderData);\n\n              case 13:\n                response = _context.sent;\n                this.$message.destroy();\n\n                if (response.success) {\n                  orderResult = response.result;\n                  console.log('🎯 会员订单创建成功:', orderResult); // 显示支付选择弹窗\n\n                  this.selectedPlan = plan;\n                  this.currentOrderId = orderResult.orderId;\n                  this.currentOrderAmount = orderResult.amount;\n                  this.showPaymentModal = true;\n                } else {\n                  this.$message.error(response.message || '创建订单失败');\n                }\n\n                _context.next = 23;\n                break;\n\n              case 18:\n                _context.prev = 18;\n                _context.t0 = _context[\"catch\"](6);\n                this.$message.destroy();\n                console.error('创建会员订单失败:', _context.t0);\n                this.$message.error('创建订单失败，请重试');\n\n              case 23:\n                _context.prev = 23;\n                this.paymentLoading = false;\n                return _context.finish(23);\n\n              case 26:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[6, 18, 23, 26]]);\n      }));\n\n      function handleSubscribe(_x) {\n        return _handleSubscribe.apply(this, arguments);\n      }\n\n      return handleSubscribe;\n    }(),\n    // 处理支付\n    handlePayment: function () {\n      var _handlePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                if (this.currentOrderId) {\n                  _context2.next = 3;\n                  break;\n                }\n\n                this.$message.error('订单信息错误');\n                return _context2.abrupt(\"return\");\n\n              case 3:\n                _context2.prev = 3;\n                this.paymentLoading = true;\n\n                if (!(this.selectedPaymentMethod === 'alipay-page')) {\n                  _context2.next = 10;\n                  break;\n                }\n\n                _context2.next = 8;\n                return this.handleAlipayPagePayment();\n\n              case 8:\n                _context2.next = 13;\n                break;\n\n              case 10:\n                if (!(this.selectedPaymentMethod === 'alipay-qr')) {\n                  _context2.next = 13;\n                  break;\n                }\n\n                _context2.next = 13;\n                return this.handleAlipayQrPayment();\n\n              case 13:\n                this.showPaymentModal = false;\n                _context2.next = 20;\n                break;\n\n              case 16:\n                _context2.prev = 16;\n                _context2.t0 = _context2[\"catch\"](3);\n                console.error('支付处理失败:', _context2.t0);\n                this.$message.error('支付失败，请重试');\n\n              case 20:\n                _context2.prev = 20;\n                this.paymentLoading = false;\n                return _context2.finish(20);\n\n              case 23:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[3, 16, 20, 23]]);\n      }));\n\n      function handlePayment() {\n        return _handlePayment.apply(this, arguments);\n      }\n\n      return handlePayment;\n    }(),\n    // 处理支付宝网页支付（与充值功能完全一致）\n    handleAlipayPagePayment: function () {\n      var _handleAlipayPagePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var paymentData, payResponse, payForm, div, form;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId);\n                this.$message.loading('正在跳转到支付宝支付...', 0);\n                paymentData = {\n                  orderId: this.currentOrderId,\n                  amount: this.currentOrderAmount,\n                  subject: \"\\u667A\\u754CAigc\\u4F1A\\u5458 - \".concat(this.selectedPlan.name),\n                  body: \"\\u8D2D\\u4E70\".concat(this.selectedPlan.name, \"\\uFF0C\\u91D1\\u989D\\uFF1A\\xA5\").concat(this.currentOrderAmount)\n                };\n                console.log('🔍 发送支付请求数据:', paymentData);\n                _context3.next = 7;\n                return this.$http.post('/api/alipay/createOrder', paymentData);\n\n              case 7:\n                payResponse = _context3.sent;\n                console.log('🔍 支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context3.next = 24;\n                  break;\n                }\n\n                payForm = payResponse.result.payForm;\n                console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空');\n\n                if (payForm) {\n                  _context3.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付表单为空');\n                return _context3.abrupt(\"return\");\n\n              case 16:\n                // 创建表单并提交到支付宝\n                div = document.createElement('div');\n                div.innerHTML = payForm;\n                document.body.appendChild(div);\n                form = div.querySelector('form');\n\n                if (form) {\n                  console.log('🔍 找到支付表单，准备提交');\n                  form.submit();\n                } else {\n                  console.error('🔍 未找到支付表单');\n                  this.$message.error('支付表单创建失败');\n                } // 清理DOM\n\n\n                setTimeout(function () {\n                  if (document.body.contains(div)) {\n                    document.body.removeChild(div);\n                  }\n                }, 1000);\n                _context3.next = 26;\n                break;\n\n              case 24:\n                console.error('🔍 支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建支付订单失败');\n\n              case 26:\n                _context3.next = 33;\n                break;\n\n              case 28:\n                _context3.prev = 28;\n                _context3.t0 = _context3[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝网页支付失败:', _context3.t0);\n                this.$message.error('支付宝支付失败，请重试');\n\n              case 33:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 28]]);\n      }));\n\n      function handleAlipayPagePayment() {\n        return _handleAlipayPagePayment.apply(this, arguments);\n      }\n\n      return handleAlipayPagePayment;\n    }(),\n    // 处理支付宝扫码支付（与充值功能完全一致）\n    handleAlipayQrPayment: function () {\n      var _handleAlipayQrPayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var paymentData, payResponse, qrCode;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId);\n                this.$message.loading('正在生成支付二维码...', 0);\n                paymentData = {\n                  orderId: this.currentOrderId,\n                  amount: this.currentOrderAmount,\n                  subject: \"\\u667A\\u754CAigc\\u4F1A\\u5458 - \".concat(this.selectedPlan.name),\n                  body: \"\\u8D2D\\u4E70\".concat(this.selectedPlan.name, \"\\uFF0C\\u91D1\\u989D\\uFF1A\\xA5\").concat(this.currentOrderAmount)\n                };\n                console.log('🔍 发送扫码支付请求数据:', paymentData);\n                _context4.next = 7;\n                return this.$http.post('/api/alipay/createQrOrder', paymentData);\n\n              case 7:\n                payResponse = _context4.sent;\n                console.log('🔍 扫码支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context4.next = 19;\n                  break;\n                }\n\n                qrCode = payResponse.result.qrCode;\n                console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空');\n\n                if (qrCode) {\n                  _context4.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付二维码生成失败');\n                return _context4.abrupt(\"return\");\n\n              case 16:\n                // 显示二维码支付弹窗\n                this.showQrCodeModal(qrCode);\n                _context4.next = 21;\n                break;\n\n              case 19:\n                console.error('🔍 扫码支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建扫码支付订单失败');\n\n              case 21:\n                _context4.next = 28;\n                break;\n\n              case 23:\n                _context4.prev = 23;\n                _context4.t0 = _context4[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝扫码支付失败:', _context4.t0);\n                this.$message.error('支付宝扫码支付失败，请重试');\n\n              case 28:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 23]]);\n      }));\n\n      function handleAlipayQrPayment() {\n        return _handleAlipayQrPayment.apply(this, arguments);\n      }\n\n      return handleAlipayQrPayment;\n    }(),\n    // 显示二维码弹窗\n    showQrCodeModal: function showQrCodeModal(qrCode) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = \"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=\".concat(encodeURIComponent(qrCode));\n      this.showQrModal = true;\n      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount);\n      console.log('🔍 二维码URL:', this.qrCodeUrl); // 开始轮询支付状态\n\n      this.startPaymentStatusCheck();\n    },\n    // 关闭二维码弹窗\n    closeQrModal: function closeQrModal() {\n      this.showQrModal = false;\n      this.qrCodeUrl = '';\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n        this.paymentCheckTimer = null;\n      }\n    },\n    // 开始支付状态检查\n    startPaymentStatusCheck: function startPaymentStatusCheck() {\n      var _this = this;\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n      }\n\n      this.paymentCheckTimer = setInterval(function () {\n        _this.checkPaymentStatus();\n      }, 3000); // 每3秒检查一次\n    },\n    // 检查支付状态（与充值功能完全一致）\n    checkPaymentStatus: function () {\n      var _checkPaymentStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var _this2 = this;\n\n        var response;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                if (this.currentOrderId) {\n                  _context5.next = 2;\n                  break;\n                }\n\n                return _context5.abrupt(\"return\");\n\n              case 2:\n                _context5.prev = 2;\n                this.checkingStatus = true;\n                console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId);\n                _context5.next = 7;\n                return this.$http.get(\"/api/alipay/queryOrder/\".concat(this.currentOrderId));\n\n              case 7:\n                response = _context5.sent;\n                console.log('🔍 支付状态查询响应:', response);\n\n                if (response.success && response.result.status === 'TRADE_SUCCESS') {\n                  console.log('🔍 会员购买支付成功！');\n                  this.$message.success('支付成功！会员权益已生效');\n                  this.closeQrModal(); // 留在当前membership页面，刷新用户状态\n\n                  setTimeout(function () {\n                    _this2.$message.success('会员购买成功！页面即将刷新以更新会员状态'); // 刷新当前页面以更新会员状态和按钮显示\n\n\n                    window.location.reload();\n                  }, 2000);\n                } else {\n                  console.log('🔍 支付状态:', response.result && response.result.status || '未知');\n                }\n\n                _context5.next = 15;\n                break;\n\n              case 12:\n                _context5.prev = 12;\n                _context5.t0 = _context5[\"catch\"](2);\n                console.error('检查支付状态失败:', _context5.t0);\n\n              case 15:\n                _context5.prev = 15;\n                this.checkingStatus = false;\n                return _context5.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[2, 12, 15, 18]]);\n      }));\n\n      function checkPaymentStatus() {\n        return _checkPaymentStatus.apply(this, arguments);\n      }\n\n      return checkPaymentStatus;\n    }(),\n    // 处理充值成功事件\n    handleRechargeSuccess: function handleRechargeSuccess() {\n      this.$message.success('充值成功！您可以继续选择套餐'); // 可以在这里添加其他逻辑，比如刷新用户信息等\n    }\n  }\n};", {"version": 3, "sources": ["Membership.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyKA,OAAA,WAAA,MAAA,sCAAA;AACA,OAAA,aAAA,MAAA,gCAAA;AACA,SAAA,qBAAA,EAAA,QAAA,QAAA,kBAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA,WADA;AAEA,IAAA,aAAA,EAAA;AAFA,GAFA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA;AACA,MAAA,cAAA,EAAA,KAFA;AAGA,MAAA,gBAAA,EAAA,KAHA;AAIA,MAAA,YAAA,EAAA,IAJA;AAKA,MAAA,qBAAA,EAAA,WALA;AAOA;AACA,MAAA,WAAA,EAAA,KARA;AASA,MAAA,SAAA,EAAA,EATA;AAUA,MAAA,cAAA,EAAA,EAVA;AAWA,MAAA,kBAAA,EAAA,CAXA;AAYA,MAAA,iBAAA,EAAA,IAZA;AAaA,MAAA,cAAA,EAAA,KAbA;AAeA,MAAA,KAAA,EAAA,CACA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,KAAA,EAAA,IAHA;AAIA,QAAA,aAAA,EAAA,IAJA;AAKA,QAAA,YAAA,EAAA,QALA;AAMA,QAAA,UAAA,EAAA,IANA;AAOA,QAAA,MAAA,EAAA,GAPA;AAQA,QAAA,WAAA,EAAA,aARA;AASA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,CATA;AAkBA,QAAA,UAAA,EAAA,MAlBA;AAmBA,QAAA,QAAA,EAAA;AAnBA,OADA,EAsBA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,KAAA,EAAA,KAHA;AAIA,QAAA,aAAA,EAAA,KAJA;AAKA,QAAA,YAAA,EAAA,QALA;AAMA,QAAA,UAAA,EAAA,KANA;AAOA,QAAA,MAAA,EAAA,GAPA;AAQA,QAAA,WAAA,EAAA,aARA;AASA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,CATA;AAkBA,QAAA,UAAA,EAAA,MAlBA;AAmBA,QAAA,QAAA,EAAA;AAnBA,OAtBA,EA2CA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,KAHA;AAIA,QAAA,aAAA,EAAA,KAJA;AAKA,QAAA,YAAA,EAAA,QALA;AAMA,QAAA,UAAA,EAAA,KANA;AAOA,QAAA,MAAA,EAAA,GAPA;AAQA,QAAA,WAAA,EAAA,cARA;AASA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,2CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,2CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,4CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,8CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,4CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,4CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,2CAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAPA,CATA;AAkBA,QAAA,UAAA,EAAA,MAlBA;AAmBA,QAAA,QAAA,EAAA;AAnBA,OA3CA;AAfA,KAAA;AAiFA,GAxFA;AA0FA,EAAA,aA1FA,2BA0FA;AACA,QAAA,KAAA,iBAAA,EAAA;AACA,MAAA,aAAA,CAAA,KAAA,iBAAA,CAAA;AACA;AACA,GA9FA;AAgGA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,yBAEA,IAFA,EAEA;AACA,UAAA,QAAA,GAAA,KAAA,WAAA,EAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,KAAA,WAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAFA,CAEA;AAEA;;AACA,UAAA,QAAA,KAAA,OAAA,EAAA;AACA,eAAA,MAAA;AACA;;AAEA,UAAA,QAAA,KAAA,QAAA,EAAA;AACA,eAAA,MAAA,CADA,CACA;AACA,OAFA,MAEA,IAAA,KAAA,SAAA,CAAA,QAAA,EAAA,QAAA,CAAA,EAAA;AACA,eAAA,MAAA,CADA,CACA;AACA,OAFA,MAEA;AACA,iBAAA,MAAA,CADA,CACA;AACA;AACA,KAlBA;AAoBA;AACA,IAAA,WArBA,uBAqBA,MArBA,EAqBA;AACA,UAAA,OAAA,GAAA;AACA,WAAA,MADA;AAEA,WAAA,KAFA;AAGA,WAAA;AAHA,OAAA;AAKA,aAAA,OAAA,CAAA,MAAA,CAAA,IAAA,MAAA;AACA,KA5BA;AA8BA;AACA,IAAA,SA/BA,qBA+BA,WA/BA,EA+BA,UA/BA,EA+BA;AACA,UAAA,SAAA,GAAA;AACA,gBAAA,CADA;AAEA,eAAA,CAFA;AAGA,gBAAA,CAHA;AAIA,iBAAA,GAJA,CAIA;;AAJA,OAAA;AAMA,aAAA,SAAA,CAAA,UAAA,CAAA,IAAA,SAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA;AACA,KAvCA;AAyCA;AACA,IAAA,iBA1CA,6BA0CA,IA1CA,EA0CA;AACA,UAAA,QAAA,GAAA,KAAA,WAAA,EAAA;AACA,UAAA,QAAA,GAAA,KAAA,WAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAFA,CAIA;;AACA,aAAA,QAAA,KAAA,KAAA,IAAA,QAAA,KAAA,MAAA;AACA,KAhDA;AAkDA;AACA,IAAA,oBAnDA,gCAmDA,IAnDA,EAmDA;AACA,UAAA,QAAA,GAAA,KAAA,WAAA,EAAA;AACA,UAAA,QAAA,GAAA,KAAA,WAAA,CAAA,IAAA,CAAA,EAAA,CAAA;;AAEA,UAAA,QAAA,KAAA,KAAA,IAAA,QAAA,KAAA,MAAA,EAAA;AACA,eAAA,4CAAA;AACA;;AAEA,aAAA,EAAA;AACA,KA5DA;AA8DA;AACA,IAAA,WA/DA,yBA+DA;AACA;AACA,UAAA,KAAA,QAAA,IAAA,KAAA,QAAA,CAAA,WAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,6CAAA,EAAA,KAAA,QAAA,CAAA,WAAA;AACA,eAAA,KAAA,QAAA,CAAA,WAAA;AACA,OALA,CAOA;;;AACA,UAAA;AACA;AACA,YAAA,QAAA,GAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,KAAA,IAAA,CAAA;AACA,YAAA,QAAA,GAAA,YAAA,CAAA,OAAA,CAAA,UAAA,CAAA;AACA,YAAA,QAAA,GAAA,YAAA,CAAA,OAAA,CAAA,UAAA,CAAA;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,0DAAA,EAAA,QAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,0DAAA,EAAA,QAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,0DAAA,EAAA,QAAA,EARA,CAUA;;AACA,YAAA,IAAA,GAAA,QAAA,CAAA,IAAA,IAAA,QAAA,CAAA,QAAA,IAAA,QAAA,CAAA,QAAA,IAAA,QAAA,IAAA,QAAA,CAXA,CAaA;;AACA,YAAA,CAAA,IAAA,EAAA;AACA,UAAA,IAAA,GAAA,MAAA,CADA,CACA;AACA;;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,wCAAA,EAAA,IAAA;AACA,eAAA,IAAA;AACA,OApBA,CAoBA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,IAAA,CAAA,0BAAA,EAAA,KAAA;AACA,eAAA,MAAA,CAFA,CAEA;AACA;AACA,KA/FA;AAiGA,IAAA,eAjGA;AAAA,uGAiGA,IAjGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkGA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA,EAlGA,CAoGA;;AACA,gBAAA,KArGA,GAqGA,KAAA,GAAA,CAAA,GAAA,CAAA,YAAA,CArGA;;AAAA,oBAsGA,KAtGA;AAAA;AAAA;AAAA;;AAuGA,qBAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA,qBAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AAxGA;;AAAA;AAAA;AA6GA,qBAAA,cAAA,GAAA,IAAA,CA7GA,CA+GA;;AACA,gBAAA,SAhHA,GAgHA;AACA,kBAAA,eAAA,EAAA,IAAA,CAAA,EADA;AAEA,kBAAA,QAAA,EAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,IAAA,CAAA,GAAA,EAFA;AAEA;AACA,kBAAA,MAAA,EAAA,UAAA,CAAA,IAAA,CAAA,KAAA,CAHA;AAIA,kBAAA,QAAA,EAAA,IAAA,CAAA,IAJA;AAKA,kBAAA,aAAA,EAAA,QALA,CAKA;;AALA,iBAhHA;AAwHA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,SAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,EAzHA,CA2HA;;AA3HA;AAAA,uBA4HA,qBAAA,CAAA,SAAA,CA5HA;;AAAA;AA4HA,gBAAA,QA5HA;AA6HA,qBAAA,QAAA,CAAA,OAAA;;AAEA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,WADA,GACA,QAAA,CAAA,MADA;AAEA,kBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,WAAA,EAFA,CAIA;;AACA,uBAAA,YAAA,GAAA,IAAA;AACA,uBAAA,cAAA,GAAA,WAAA,CAAA,OAAA;AACA,uBAAA,kBAAA,GAAA,WAAA,CAAA,MAAA;AACA,uBAAA,gBAAA,GAAA,IAAA;AAEA,iBAVA,MAUA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,QAAA;AACA;;AA3IA;AAAA;;AAAA;AAAA;AAAA;AA6IA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,YAAA;;AA/IA;AAAA;AAiJA,qBAAA,cAAA,GAAA,KAAA;AAjJA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAqJA;AACA,IAAA,aAtJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAuJA,KAAA,cAvJA;AAAA;AAAA;AAAA;;AAwJA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AAxJA;;AAAA;AAAA;AA6JA,qBAAA,cAAA,GAAA,IAAA;;AA7JA,sBA+JA,KAAA,qBAAA,KAAA,aA/JA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAgKA,KAAA,uBAAA,EAhKA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBAiKA,KAAA,qBAAA,KAAA,WAjKA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAkKA,KAAA,qBAAA,EAlKA;;AAAA;AAqKA,qBAAA,gBAAA,GAAA,KAAA;AArKA;AAAA;;AAAA;AAAA;AAAA;AAuKA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AAxKA;AAAA;AA0KA,qBAAA,cAAA,GAAA,KAAA;AA1KA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8KA;AACA,IAAA,uBA/KA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiLA,gBAAA,OAAA,CAAA,GAAA,CAAA,uBAAA,EAAA,KAAA,cAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,eAAA,EAAA,CAAA;AAEA,gBAAA,WApLA,GAoLA;AACA,kBAAA,OAAA,EAAA,KAAA,cADA;AAEA,kBAAA,MAAA,EAAA,KAAA,kBAFA;AAGA,kBAAA,OAAA,2CAAA,KAAA,YAAA,CAAA,IAAA,CAHA;AAIA,kBAAA,IAAA,wBAAA,KAAA,YAAA,CAAA,IAAA,yCAAA,KAAA,kBAAA;AAJA,iBApLA;AA2LA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,WAAA;AA3LA;AAAA,uBA4LA,KAAA,KAAA,CAAA,IAAA,CAAA,yBAAA,EAAA,WAAA,CA5LA;;AAAA;AA4LA,gBAAA,WA5LA;AA6LA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,WAAA;AACA,qBAAA,QAAA,CAAA,OAAA;;AA9LA,qBAgMA,WAAA,CAAA,OAhMA;AAAA;AAAA;AAAA;;AAiMA,gBAAA,OAjMA,GAiMA,WAAA,CAAA,MAAA,CAAA,OAjMA;AAkMA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,OAAA,GAAA,KAAA,GAAA,IAAA;;AAlMA,oBAoMA,OApMA;AAAA;AAAA;AAAA;;AAqMA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AArMA;;AAAA;AAyMA;AACA,gBAAA,GA1MA,GA0MA,QAAA,CAAA,aAAA,CAAA,KAAA,CA1MA;AA2MA,gBAAA,GAAA,CAAA,SAAA,GAAA,OAAA;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA;AAEA,gBAAA,IA9MA,GA8MA,GAAA,CAAA,aAAA,CAAA,MAAA,CA9MA;;AA+MA,oBAAA,IAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;AACA,kBAAA,IAAA,CAAA,MAAA;AACA,iBAHA,MAGA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,iBArNA,CAuNA;;;AACA,gBAAA,UAAA,CAAA,YAAA;AACA,sBAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA;AACA;AACA,iBAJA,EAIA,IAJA,CAAA;AAxNA;AAAA;;AAAA;AA8NA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,WAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,IAAA,UAAA;;AA/NA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAkOA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,aAAA;;AApOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwOA;AACA,IAAA,qBAzOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2OA,gBAAA,OAAA,CAAA,GAAA,CAAA,uBAAA,EAAA,KAAA,cAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,cAAA,EAAA,CAAA;AAEA,gBAAA,WA9OA,GA8OA;AACA,kBAAA,OAAA,EAAA,KAAA,cADA;AAEA,kBAAA,MAAA,EAAA,KAAA,kBAFA;AAGA,kBAAA,OAAA,2CAAA,KAAA,YAAA,CAAA,IAAA,CAHA;AAIA,kBAAA,IAAA,wBAAA,KAAA,YAAA,CAAA,IAAA,yCAAA,KAAA,kBAAA;AAJA,iBA9OA;AAqPA,gBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,WAAA;AArPA;AAAA,uBAsPA,KAAA,KAAA,CAAA,IAAA,CAAA,2BAAA,EAAA,WAAA,CAtPA;;AAAA;AAsPA,gBAAA,WAtPA;AAuPA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,WAAA;AACA,qBAAA,QAAA,CAAA,OAAA;;AAxPA,qBA0PA,WAAA,CAAA,OA1PA;AAAA;AAAA;AAAA;;AA2PA,gBAAA,MA3PA,GA2PA,WAAA,CAAA,MAAA,CAAA,MA3PA;AA4PA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA;;AA5PA,oBA8PA,MA9PA;AAAA;AAAA;AAAA;;AA+PA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AA/PA;;AAAA;AAmQA;AACA,qBAAA,eAAA,CAAA,MAAA;AApQA;AAAA;;AAAA;AAsQA,gBAAA,OAAA,CAAA,KAAA,CAAA,cAAA,EAAA,WAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,IAAA,YAAA;;AAvQA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA0QA,qBAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA;;AA5QA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAgRA;AACA,IAAA,eAjRA,2BAiRA,MAjRA,EAiRA;AACA;AACA,WAAA,SAAA,2EAAA,kBAAA,CAAA,MAAA,CAAA;AACA,WAAA,WAAA,GAAA,IAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,mBAAA,EAAA,KAAA,cAAA,EAAA,KAAA,EAAA,KAAA,kBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,SAAA,EANA,CAQA;;AACA,WAAA,uBAAA;AACA,KA3RA;AA6RA;AACA,IAAA,YA9RA,0BA8RA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,EAAA;;AAEA,UAAA,KAAA,iBAAA,EAAA;AACA,QAAA,aAAA,CAAA,KAAA,iBAAA,CAAA;AACA,aAAA,iBAAA,GAAA,IAAA;AACA;AACA,KAtSA;AAwSA;AACA,IAAA,uBAzSA,qCAySA;AAAA;;AACA,UAAA,KAAA,iBAAA,EAAA;AACA,QAAA,aAAA,CAAA,KAAA,iBAAA,CAAA;AACA;;AAEA,WAAA,iBAAA,GAAA,WAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,kBAAA;AACA,OAFA,EAEA,IAFA,CAAA,CALA,CAOA;AACA,KAjTA;AAmTA;AACA,IAAA,kBApTA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAqTA,KAAA,cArTA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AAwTA,qBAAA,cAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,KAAA,cAAA;AAzTA;AAAA,uBA0TA,KAAA,KAAA,CAAA,GAAA,kCAAA,KAAA,cAAA,EA1TA;;AAAA;AA0TA,gBAAA,QA1TA;AA2TA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,QAAA;;AAEA,oBAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,MAAA,CAAA,MAAA,KAAA,eAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,cAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,cAAA;AACA,uBAAA,YAAA,GAHA,CAKA;;AACA,kBAAA,UAAA,CAAA,YAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,sBAAA,EADA,CAEA;;;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,MAAA;AACA,mBAJA,EAIA,IAJA,CAAA;AAKA,iBAXA,MAWA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,MAAA,CAAA,MAAA,IAAA,IAAA;AACA;;AA1UA;AAAA;;AAAA;AAAA;AAAA;AA4UA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;;AA5UA;AAAA;AA8UA,qBAAA,cAAA,GAAA,KAAA;AA9UA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkVA;AACA,IAAA,qBAnVA,mCAmVA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,gBAAA,EADA,CAEA;AACA;AAtVA;AAhGA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"membership-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">订阅会员</h1>\n        <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n      </div>\n\n      <!-- 快速充值模块 -->\n      <section class=\"recharge-section\">\n        <div class=\"container\">\n          <QuickRecharge @recharge-success=\"handleRechargeSuccess\" />\n        </div>\n      </section>\n\n      <!-- 会员套餐区域 -->\n      <section class=\"plans-section\">\n        <div class=\"container\">\n          <div class=\"plans-grid\">\n            <div \n              v-for=\"plan in plans\" \n              :key=\"plan.id\"\n              class=\"plan-card\"\n              :class=\"{ 'featured': plan.featured }\"\n            >\n              <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n              <div class=\"plan-header\">\n                <h3 class=\"plan-name\">{{ plan.name }}</h3>\n                <div class=\"plan-price\">\n                  <!-- 原价显示 -->\n                  <div v-if=\"plan.originalPrice\" class=\"original-price\">\n                    <span class=\"original-price-text\">原价：¥{{ plan.originalPrice }}</span>\n                    <span class=\"discount-badge\">{{ plan.discountText }}</span>\n                  </div>\n                  <!-- 现价显示 -->\n                  <div class=\"current-price\">\n                    <span class=\"price-symbol\">¥</span>\n                    <span class=\"price-amount\">{{ plan.price }}</span>\n                    <span class=\"price-period\">/{{ plan.period }}</span>\n                  </div>\n                  <!-- 立省金额 -->\n                  <div v-if=\"plan.saveAmount\" class=\"save-amount\">\n                    立省¥{{ plan.saveAmount }}\n                  </div>\n                </div>\n                <p class=\"plan-description\">{{ plan.description }}</p>\n              </div>\n              \n              <div class=\"plan-features\">\n                <div\n                  v-for=\"feature in plan.features\"\n                  :key=\"feature.text || feature\"\n                  class=\"feature-item\"\n                  :class=\"{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }\"\n                >\n                  <a-icon\n                    :type=\"feature.disabled ? 'close' : 'check'\"\n                    :class=\"{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }\"\n                  />\n                  <span class=\"feature-text\" v-html=\"feature.text || feature\"></span>\n                  <span v-if=\"feature.exclusive\" class=\"exclusive-badge\">专属</span>\n                  <span v-if=\"feature.disabled\" class=\"disabled-text\">（SVIP专享）</span>\n                </div>\n              </div>\n              \n              <button\n                class=\"btn-subscribe\"\n                :class=\"{ 'featured': plan.featured }\"\n                @click=\"handleSubscribe(plan)\"\n              >\n                {{ getButtonText(plan) }}\n              </button>\n\n              <!-- VIP升级SVIP的特殊提示 -->\n              <div v-if=\"showUpgradeNotice(plan)\" class=\"upgrade-notice\">\n                <i class=\"icon-info\"></i>\n                <span>{{ getUpgradeNoticeText(plan) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n\n    <!-- 支付方式选择弹窗 -->\n    <a-modal\n      title=\"选择支付方式\"\n      :visible=\"showPaymentModal\"\n      @cancel=\"showPaymentModal = false\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"payment-modal-content\">\n        <div class=\"order-info\">\n          <h4>订单信息</h4>\n          <div class=\"order-details\">\n            <div class=\"order-item\">\n              <span>套餐名称：</span>\n              <span>{{ selectedPlan ? selectedPlan.name : '' }}</span>\n            </div>\n            <div class=\"order-item\">\n              <span>支付金额：</span>\n              <span class=\"amount\">¥{{ currentOrderAmount }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"payment-methods\">\n          <h4>支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n            <a-radio-button value=\"alipay-qr\">\n              <i class=\"anticon anticon-qrcode\"></i>\n              支付宝扫码\n            </a-radio-button>\n            <a-radio-button value=\"alipay-page\">\n              <i class=\"anticon anticon-alipay\"></i>\n              支付宝网页\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n\n        <div class=\"payment-actions\">\n          <a-button\n            type=\"primary\"\n            size=\"large\"\n            :loading=\"paymentLoading\"\n            @click=\"handlePayment\"\n            block\n          >\n            确认支付\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 支付二维码弹窗 -->\n    <a-modal\n      title=\"扫码支付\"\n      :visible=\"showQrModal\"\n      @cancel=\"closeQrModal\"\n      :footer=\"null\"\n      width=\"400px\"\n    >\n      <div class=\"qr-payment-content\">\n        <div class=\"qr-code-container\">\n          <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n            <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n          </div>\n          <div v-else class=\"qr-loading\">\n            <a-spin size=\"large\" />\n            <p>正在生成二维码...</p>\n          </div>\n        </div>\n        <div class=\"qr-info\">\n          <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n          <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n        </div>\n        <div class=\"qr-status\">\n          <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n            检查支付状态\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport QuickRecharge from '@/components/QuickRecharge.vue'\nimport { createMembershipOrder, payOrder } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage,\n    QuickRecharge\n  },\n  data() {\n    return {\n      // 支付相关\n      paymentLoading: false,\n      showPaymentModal: false,\n      selectedPlan: null,\n      selectedPaymentMethod: 'alipay-qr',\n\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null,\n      checkingStatus: false,\n\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          originalPrice: '39',\n          discountText: '限时7.4折',\n          saveAmount: '10',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          originalPrice: '468',\n          discountText: '限时6.4折',\n          saveAmount: '170',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          originalPrice: '999',\n          discountText: '限时4.9折',\n          saveAmount: '510',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            { text: '解锁<strong class=\"highlight\">全部课程</strong>', disabled: false, exclusive: true },\n            { text: '插件<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '邀请<strong class=\"highlight\">奖励50%</strong>', disabled: false, exclusive: true },\n            { text: '调用工作流<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '复制<strong class=\"highlight\">所有工作流</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">解锁流媒体转换</strong>', disabled: false, exclusive: true },\n            { text: '部分<strong class=\"highlight\">插件免费</strong>', disabled: false, exclusive: true }\n          ],\n          buttonText: '立即购买',\n          featured: true\n        }\n      ]\n    }\n  },\n\n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n\n  methods: {\n    // 获取按钮文本（续费/升级/购买）\n    getButtonText(plan) {\n      const userRole = this.getUserRole() // 获取用户当前角色\n      const planRole = this.getPlanRole(plan.id) // 获取套餐对应角色\n\n      // 管理员显示\"立即购买\"（管理员不需要购买会员，但可以测试）\n      if (userRole === 'admin') {\n        return '立即购买'\n      }\n\n      if (userRole === planRole) {\n        return '续费会员' // 相同等级显示续费\n      } else if (this.isUpgrade(userRole, planRole)) {\n        return '升级会员' // 升级显示升级\n      } else {\n        return '立即购买' // 新购买显示购买\n      }\n    },\n\n    // 获取套餐对应的角色\n    getPlanRole(planId) {\n      const roleMap = {\n        1: 'user',\n        2: 'VIP',\n        3: 'SVIP'\n      }\n      return roleMap[planId] || 'user'\n    },\n\n    // 判断是否为升级\n    isUpgrade(currentRole, targetRole) {\n      const roleLevel = {\n        'user': 1,\n        'VIP': 2,\n        'SVIP': 3,\n        'admin': 999  // 管理员级别最高\n      }\n      return roleLevel[targetRole] > (roleLevel[currentRole] || 0)\n    },\n\n    // 是否显示升级提示\n    showUpgradeNotice(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      // 只有VIP升级到SVIP时显示特殊提示\n      return userRole === 'VIP' && planRole === 'SVIP'\n    },\n\n    // 获取升级提示文本\n    getUpgradeNoticeText(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      if (userRole === 'VIP' && planRole === 'SVIP') {\n        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~'\n      }\n\n      return ''\n    },\n\n    // 获取用户当前角色\n    getUserRole() {\n      // 优先从userInfo中获取角色信息\n      if (this.userInfo && this.userInfo.currentRole) {\n        console.log('🔍 Membership: getUserRole - 从userInfo获取角色:', this.userInfo.currentRole)\n        return this.userInfo.currentRole\n      }\n\n      // 从localStorage获取用户角色\n      try {\n        // 尝试从多个可能的存储位置获取角色信息\n        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')\n        const userRole = localStorage.getItem('userRole')\n        const roleCode = localStorage.getItem('roleCode')\n\n        console.log('🔍 Membership: getUserRole - userInfo from localStorage:', userInfo)\n        console.log('🔍 Membership: getUserRole - userRole from localStorage:', userRole)\n        console.log('🔍 Membership: getUserRole - roleCode from localStorage:', roleCode)\n\n        // 尝试多种可能的角色字段\n        let role = userInfo.role || userInfo.userRole || userInfo.roleCode || userRole || roleCode\n\n        // 如果还是没有找到角色信息，默认为普通用户\n        if (!role) {\n          role = 'user'  // 默认为普通用户角色\n        }\n\n        console.log('🔍 Membership: getUserRole - 最终获取到的角色:', role)\n        return role\n      } catch (error) {\n        console.warn('🔍 Membership: 获取用户角色失败:', error)\n        return 'user'  // 出错时默认为普通用户\n      }\n    },\n\n    async handleSubscribe(plan) {\n      console.log('选择购买套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$message.warning('请先登录后再购买会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        // 准备会员订单数据（与充值订单结构保持一致）\n        const orderData = {\n          membershipLevel: plan.id,\n          duration: plan.name.includes('月') ? 1 : 12, // 根据套餐名称判断时长\n          amount: parseFloat(plan.price),\n          planName: plan.name,\n          paymentMethod: 'alipay' // 默认支付宝，与充值保持一致\n        }\n\n        console.log('🎯 创建会员订单:', orderData)\n        this.$message.loading('正在创建订单...', 0)\n\n        // 创建会员订单（使用与充值相同的交易记录系统）\n        const response = await createMembershipOrder(orderData)\n        this.$message.destroy()\n\n        if (response.success) {\n          const orderResult = response.result\n          console.log('🎯 会员订单创建成功:', orderResult)\n\n          // 显示支付选择弹窗\n          this.selectedPlan = plan\n          this.currentOrderId = orderResult.orderId\n          this.currentOrderAmount = orderResult.amount\n          this.showPaymentModal = true\n\n        } else {\n          this.$message.error(response.message || '创建订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('创建会员订单失败:', error)\n        this.$message.error('创建订单失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付\n    async handlePayment() {\n      if (!this.currentOrderId) {\n        this.$message.error('订单信息错误')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        if (this.selectedPaymentMethod === 'alipay-page') {\n          await this.handleAlipayPagePayment()\n        } else if (this.selectedPaymentMethod === 'alipay-qr') {\n          await this.handleAlipayQrPayment()\n        }\n\n        this.showPaymentModal = false\n      } catch (error) {\n        console.error('支付处理失败:', error)\n        this.$message.error('支付失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付宝网页支付（与充值功能完全一致）\n    async handleAlipayPagePayment() {\n      try {\n        console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝网页支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n\n    // 处理支付宝扫码支付（与充值功能完全一致）\n    async handleAlipayQrPayment() {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode)\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n\n    // 显示二维码弹窗\n    showQrCodeModal(qrCode) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`\n      this.showQrModal = true\n\n      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount)\n      console.log('🔍 二维码URL:', this.qrCodeUrl)\n\n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n\n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n\n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n\n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n\n    // 检查支付状态（与充值功能完全一致）\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        this.checkingStatus = true\n        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        console.log('🔍 支付状态查询响应:', response)\n\n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          console.log('🔍 会员购买支付成功！')\n          this.$message.success('支付成功！会员权益已生效')\n          this.closeQrModal()\n\n          // 留在当前membership页面，刷新用户状态\n          setTimeout(() => {\n            this.$message.success('会员购买成功！页面即将刷新以更新会员状态')\n            // 刷新当前页面以更新会员状态和按钮显示\n            window.location.reload()\n          }, 2000)\n        } else {\n          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    },\n\n    // 处理充值成功事件\n    handleRechargeSuccess() {\n      this.$message.success('充值成功！您可以继续选择套餐')\n      // 可以在这里添加其他逻辑，比如刷新用户信息等\n    }\n  }\n}\n</script>\n\n<style>\n.membership-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 充值模块样式 */\n.recharge-section {\n  margin-bottom: 3rem;\n}\n\n.recharge-section .container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 会员套餐区域 */\n.plans-section {\n  padding: 1rem 0 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.plan-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.plan-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.plan-card.featured {\n  border-color: #3b82f6;\n  transform: scale(1.05);\n}\n\n.plan-badge {\n  position: absolute;\n  top: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1.5rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.plan-name {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.plan-price {\n  text-align: center;\n  margin-bottom: 1rem;\n}\n\n/* 原价显示 */\n.original-price {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.original-price-text {\n  font-size: 0.9rem;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);\n}\n\n/* 现价显示 */\n.current-price {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin: 0.5rem 0;\n}\n\n/* 立省金额 */\n.save-amount {\n  font-size: 0.9rem;\n  color: #27ae60;\n  font-weight: bold;\n  margin-top: 0.3rem;\n}\n\n.price-symbol {\n  font-size: 1.2rem;\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.price-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #3b82f6;\n  margin: 0 0.25rem;\n}\n\n.price-period {\n  font-size: 1rem;\n  color: #64748b;\n}\n\n.plan-description {\n  color: #64748b;\n  margin: 0;\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n  color: #1e293b;\n  transition: all 0.3s ease;\n  padding: 0.5rem 0;\n}\n\n.feature-item .anticon {\n  color: #10b981;\n  font-weight: bold;\n  flex-shrink: 0;\n}\n\n/* 禁用功能样式 */\n.feature-item.feature-disabled {\n  color: #94a3b8;\n  opacity: 0.6;\n}\n\n.feature-item.feature-disabled .anticon {\n  color: #ef4444;\n}\n\n.feature-item.feature-disabled .icon-disabled {\n  color: #ef4444;\n}\n\n.disabled-text {\n  font-size: 0.8rem;\n  color: #ef4444;\n  margin-left: auto;\n}\n\n/* 专属功能样式 */\n.feature-item.feature-exclusive {\n  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);\n  padding: 0.5rem;\n  border-radius: 8px;\n  border-left: 3px solid #0ea5e9;\n}\n\n.feature-item.feature-exclusive .anticon {\n  color: #0ea5e9;\n}\n\n.feature-item.feature-exclusive .icon-exclusive {\n  color: #0ea5e9;\n}\n\n.exclusive-badge {\n  background: linear-gradient(135deg, #0ea5e9, #0284c7);\n  color: white;\n  padding: 0.1rem 0.4rem;\n  border-radius: 8px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  margin-left: auto;\n  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);\n}\n\n.feature-text {\n  flex: 1;\n  font-size: 0.95rem;\n  line-height: 1.5;\n  font-weight: 500;\n  color: inherit;\n}\n\n/* 确保highlight样式不被覆盖 */\n.feature-text .highlight {\n  color: transparent !important;\n}\n\n/* 重点亮点样式 */\n.highlight {\n  color: #ff6b35 !important;\n  font-weight: bold !important;\n  font-size: 1em !important;\n  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;\n  -webkit-background-clip: text !important;\n  -webkit-text-fill-color: transparent !important;\n  background-clip: text !important;\n  display: inline !important;\n}\n\n/* 兼容性备用方案 */\n@supports not (-webkit-background-clip: text) {\n  .highlight {\n    color: #ff6b35 !important;\n    text-shadow: 0 2px 4px rgba(255, 107, 53, 0.4) !important;\n    -webkit-text-fill-color: initial !important;\n  }\n}\n\n.btn-subscribe {\n  width: 100%;\n  padding: 1rem;\n  background: transparent;\n  border: 2px solid #3b82f6;\n  color: #3b82f6;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 扫光特效 */\n.btn-subscribe::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.8s ease;\n  z-index: 1;\n}\n\n.btn-subscribe:hover::after {\n  left: 100%;\n}\n\n.btn-subscribe:hover {\n  background: #3b82f6;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.btn-subscribe.featured {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border: none;\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  animation: borderGlow 3s ease-in-out infinite;\n}\n\n/* 推荐套餐的斜向扫光特效 */\n.btn-subscribe.featured::after {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -150%;\n  width: 200%;\n  height: 200%;\n  background: linear-gradient(45deg,\n    transparent 30%,\n    rgba(240, 248, 255, 0.4) 45%,\n    rgba(255, 255, 255, 0.6) 50%,\n    rgba(240, 248, 255, 0.4) 55%,\n    transparent 70%\n  );\n  animation: diagonalSweep 3.5s ease-in-out infinite;\n  z-index: 1;\n  transform: rotate(-10deg);\n}\n\n/* 鼠标悬停时的扫光效果 */\n.btn-subscribe.featured:hover::after {\n  animation: hoverSweepIn 0.6s ease-out forwards;\n}\n\n/* 鼠标离开时先扫回来，然后延迟恢复自动动画 */\n.btn-subscribe.featured::after {\n  animation: diagonalSweep 3.5s ease-in-out infinite;\n}\n\n.btn-subscribe.featured:not(:hover)::after {\n  animation: hoverSweepOut 0.6s ease-in forwards, diagonalSweep 3.5s ease-in-out 2s infinite;\n}\n\n.btn-subscribe.featured:hover {\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), 0 0 15px rgba(139, 92, 246, 0.3);\n  transform: translateY(-2px);\n}\n\n/* 升级提示样式 */\n.upgrade-notice {\n  margin-top: 12px;\n  padding: 8px 12px;\n  background: rgba(255, 193, 7, 0.1);\n  border: 1px solid rgba(255, 193, 7, 0.3);\n  border-radius: 6px;\n  font-size: 12px;\n  color: #856404;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.upgrade-notice .icon-info {\n  width: 14px;\n  height: 14px;\n  background: #ffc107;\n  border-radius: 50%;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.upgrade-notice .icon-info::before {\n  content: 'i';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 10px;\n  font-weight: bold;\n  font-style: normal;\n}\n\n.upgrade-notice span {\n  line-height: 1.4;\n}\n\n/* 边框微光动画 */\n@keyframes borderGlow {\n  0%, 100% {\n    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);\n  }\n}\n\n/* 斜向来回扫光动画 */\n@keyframes diagonalSweep {\n  0% {\n    left: -150%;\n  }\n  25% {\n    left: 100%;\n  }\n  50% {\n    left: 100%;\n  }\n  75% {\n    left: -150%;\n  }\n  100% {\n    left: -150%;\n  }\n}\n\n/* 鼠标悬停时扫光进入动画 */\n@keyframes hoverSweepIn {\n  0% {\n    left: -150%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n\n/* 鼠标离开时扫光退出动画 */\n@keyframes hoverSweepOut {\n  0% {\n    left: 100%;\n  }\n  100% {\n    left: -150%;\n  }\n}\n\n/* 支付弹窗样式 */\n.payment-modal-content {\n  padding: 1rem 0;\n}\n\n.payment-modal-content h4 {\n  margin-bottom: 1rem;\n  color: #1f2937;\n  font-weight: 600;\n}\n\n.order-info {\n  margin-bottom: 2rem;\n  padding: 1rem;\n  background: #f9fafb;\n  border-radius: 8px;\n}\n\n.order-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.order-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.order-item .amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.payment-actions {\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n/* 二维码支付样式 */\n.qr-payment-content {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code img {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.qr-loading {\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.qr-info {\n  margin-bottom: 1.5rem;\n}\n\n.qr-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-bottom: 0.5rem;\n}\n\n.qr-tip {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card.featured {\n    transform: none;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  .order-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/membership"}]}