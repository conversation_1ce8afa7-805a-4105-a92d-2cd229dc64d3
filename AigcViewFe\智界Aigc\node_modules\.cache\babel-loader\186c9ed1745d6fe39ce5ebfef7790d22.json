{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753808199613}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage: WebsitePage\n  },\n  data: function data() {\n    return {\n      plans: [{\n        id: 1,\n        name: '基础版',\n        price: '29',\n        period: '月',\n        description: '适合个人用户的基础功能',\n        features: ['每月100次AI生成', '基础模板库', '标准客服支持', '基础数据分析'],\n        buttonText: '选择基础版',\n        featured: false\n      }, {\n        id: 2,\n        name: '专业版',\n        price: '99',\n        period: '月',\n        description: '适合内容创作者的专业功能',\n        features: ['每月500次AI生成', '高级模板库', '优先客服支持', '详细数据分析', '多平台发布', '自定义品牌'],\n        buttonText: '选择专业版',\n        featured: true\n      }, {\n        id: 3,\n        name: '企业版',\n        price: '299',\n        period: '月',\n        description: '适合团队和企业的高级功能',\n        features: ['无限次AI生成', '全部模板库', '专属客服支持', '高级数据分析', '团队协作功能', 'API接口访问', '定制化服务'],\n        buttonText: '联系销售',\n        featured: false\n      }]\n    };\n  },\n  methods: {\n    handleSubscribe: function handleSubscribe(plan) {\n      console.log('选择订阅套餐:', plan); // 检查用户登录状态\n\n      var token = this.$ls.get('ACCESS_TOKEN');\n\n      if (!token) {\n        this.$message.warning('请先登录后再订阅会员');\n        this.$router.push('/user/login');\n        return;\n      } // 跳转到用户中心的会员管理页面\n\n\n      this.$router.push({\n        path: '/usercenter',\n        query: {\n          page: 'membership',\n          planId: plan.id,\n          planName: plan.name,\n          price: plan.price\n        }\n      });\n    }\n  }\n};", {"version": 3, "sources": ["Membership.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,OAAA,WAAA,MAAA,sCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,CACA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,KAFA;AAGA,QAAA,KAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA,aALA;AAMA,QAAA,QAAA,EAAA,CACA,YADA,EAEA,OAFA,EAGA,QAHA,EAIA,QAJA,CANA;AAYA,QAAA,UAAA,EAAA,OAZA;AAaA,QAAA,QAAA,EAAA;AAbA,OADA,EAgBA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,KAFA;AAGA,QAAA,KAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA,cALA;AAMA,QAAA,QAAA,EAAA,CACA,YADA,EAEA,OAFA,EAGA,QAHA,EAIA,QAJA,EAKA,OALA,EAMA,OANA,CANA;AAcA,QAAA,UAAA,EAAA,OAdA;AAeA,QAAA,QAAA,EAAA;AAfA,OAhBA,EAiCA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,KAFA;AAGA,QAAA,KAAA,EAAA,KAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA,cALA;AAMA,QAAA,QAAA,EAAA,CACA,SADA,EAEA,OAFA,EAGA,QAHA,EAIA,QAJA,EAKA,QALA,EAMA,SANA,EAOA,OAPA,CANA;AAeA,QAAA,UAAA,EAAA,MAfA;AAgBA,QAAA,QAAA,EAAA;AAhBA,OAjCA;AADA,KAAA;AAsDA,GA5DA;AA6DA,EAAA,OAAA,EAAA;AACA,IAAA,eADA,2BACA,IADA,EACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA,EADA,CAGA;;AACA,UAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,cAAA,CAAA;;AACA,UAAA,CAAA,KAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA,OATA,CAWA;;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,aADA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,YADA;AAEA,UAAA,MAAA,EAAA,IAAA,CAAA,EAFA;AAGA,UAAA,QAAA,EAAA,IAAA,CAAA,IAHA;AAIA,UAAA,KAAA,EAAA,IAAA,CAAA;AAJA;AAFA,OAAA;AASA;AAtBA;AA7DA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"membership-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">订阅会员</h1>\n        <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n      </div>\n\n      <!-- 会员套餐区域 -->\n      <section class=\"plans-section\">\n        <div class=\"container\">\n          <div class=\"plans-grid\">\n            <div \n              v-for=\"plan in plans\" \n              :key=\"plan.id\"\n              class=\"plan-card\"\n              :class=\"{ 'featured': plan.featured }\"\n            >\n              <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n              <div class=\"plan-header\">\n                <h3 class=\"plan-name\">{{ plan.name }}</h3>\n                <div class=\"plan-price\">\n                  <span class=\"price-symbol\">¥</span>\n                  <span class=\"price-amount\">{{ plan.price }}</span>\n                  <span class=\"price-period\">/{{ plan.period }}</span>\n                </div>\n                <p class=\"plan-description\">{{ plan.description }}</p>\n              </div>\n              \n              <div class=\"plan-features\">\n                <div \n                  v-for=\"feature in plan.features\" \n                  :key=\"feature\"\n                  class=\"feature-item\"\n                >\n                  <a-icon type=\"check\" />\n                  <span>{{ feature }}</span>\n                </div>\n              </div>\n              \n              <button \n                class=\"btn-subscribe\"\n                :class=\"{ 'featured': plan.featured }\"\n                @click=\"handleSubscribe(plan)\"\n              >\n                {{ plan.buttonText }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      plans: [\n        {\n          id: 1,\n          name: '基础版',\n          price: '29',\n          period: '月',\n          description: '适合个人用户的基础功能',\n          features: [\n            '每月100次AI生成',\n            '基础模板库',\n            '标准客服支持',\n            '基础数据分析'\n          ],\n          buttonText: '选择基础版',\n          featured: false\n        },\n        {\n          id: 2,\n          name: '专业版',\n          price: '99',\n          period: '月',\n          description: '适合内容创作者的专业功能',\n          features: [\n            '每月500次AI生成',\n            '高级模板库',\n            '优先客服支持',\n            '详细数据分析',\n            '多平台发布',\n            '自定义品牌'\n          ],\n          buttonText: '选择专业版',\n          featured: true\n        },\n        {\n          id: 3,\n          name: '企业版',\n          price: '299',\n          period: '月',\n          description: '适合团队和企业的高级功能',\n          features: [\n            '无限次AI生成',\n            '全部模板库',\n            '专属客服支持',\n            '高级数据分析',\n            '团队协作功能',\n            'API接口访问',\n            '定制化服务'\n          ],\n          buttonText: '联系销售',\n          featured: false\n        }\n      ]\n    }\n  },\n  methods: {\n    handleSubscribe(plan) {\n      console.log('选择订阅套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get('ACCESS_TOKEN')\n      if (!token) {\n        this.$message.warning('请先登录后再订阅会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      // 跳转到用户中心的会员管理页面\n      this.$router.push({\n        path: '/usercenter',\n        query: {\n          page: 'membership',\n          planId: plan.id,\n          planName: plan.name,\n          price: plan.price\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.membership-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 会员套餐区域 */\n.plans-section {\n  padding: 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.plan-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.plan-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.plan-card.featured {\n  border-color: #3b82f6;\n  transform: scale(1.05);\n}\n\n.plan-badge {\n  position: absolute;\n  top: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1.5rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.plan-name {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.plan-price {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.price-symbol {\n  font-size: 1.2rem;\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.price-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #3b82f6;\n  margin: 0 0.25rem;\n}\n\n.price-period {\n  font-size: 1rem;\n  color: #64748b;\n}\n\n.plan-description {\n  color: #64748b;\n  margin: 0;\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: #1e293b;\n}\n\n.feature-item .anticon {\n  color: #10b981;\n  font-weight: bold;\n}\n\n.btn-subscribe {\n  width: 100%;\n  padding: 1rem;\n  background: transparent;\n  border: 2px solid #3b82f6;\n  color: #3b82f6;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-subscribe:hover {\n  background: #3b82f6;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.btn-subscribe.featured {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border: none;\n}\n\n.btn-subscribe.featured:hover {\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card.featured {\n    transform: none;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/membership"}]}