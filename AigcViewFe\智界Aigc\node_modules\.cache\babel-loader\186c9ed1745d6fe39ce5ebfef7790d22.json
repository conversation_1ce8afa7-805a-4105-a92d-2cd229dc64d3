{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753811045840}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage: WebsitePage\n  },\n  data: function data() {\n    return {\n      plans: [{\n        id: 1,\n        name: 'VIP月卡',\n        price: '29',\n        period: '月',\n        description: '适合体验用户的基础功能',\n        features: ['解锁部分课程', '插件基础折扣', '邀请奖励35%基础比例', '调用工作流基础折扣'],\n        buttonText: '立即订阅',\n        featured: false\n      }, {\n        id: 2,\n        name: 'VIP年卡',\n        price: '298',\n        period: '年',\n        description: '适合长期使用的优惠套餐',\n        features: ['解锁部分课程', '插件基础折扣', '邀请奖励35%基础比例', '调用工作流基础折扣'],\n        buttonText: '立即订阅',\n        featured: false\n      }, {\n        id: 3,\n        name: 'SVIP年卡',\n        price: '489',\n        period: '年',\n        description: '适合专业用户的全功能套餐',\n        features: ['复制所有工作流', '解锁所有课程', '插件最高折扣', '解锁流媒体转换', '邀请奖励50%比例', '部分插件免费', '调用工作流最高折扣'],\n        buttonText: '立即订阅',\n        featured: true\n      }]\n    };\n  },\n  methods: {\n    handleSubscribe: function handleSubscribe(plan) {\n      console.log('选择订阅套餐:', plan); // 检查用户登录状态\n\n      var token = this.$ls.get('ACCESS_TOKEN');\n\n      if (!token) {\n        this.$message.warning('请先登录后再订阅会员');\n        this.$router.push('/user/login');\n        return;\n      } // 显示订阅功能提示\n\n\n      this.$message.info(\"\\u60A8\\u9009\\u62E9\\u4E86 \".concat(plan.name, \"\\uFF0C\\u4F1A\\u5458\\u8BA2\\u9605\\u529F\\u80FD\\u5373\\u5C06\\u4E0A\\u7EBF\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85\\uFF01\"));\n    }\n  }\n};", {"version": 3, "sources": ["Membership.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,OAAA,WAAA,MAAA,sCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,CACA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,KAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA,aALA;AAMA,QAAA,QAAA,EAAA,CACA,QADA,EAEA,QAFA,EAGA,aAHA,EAIA,WAJA,CANA;AAYA,QAAA,UAAA,EAAA,MAZA;AAaA,QAAA,QAAA,EAAA;AAbA,OADA,EAgBA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,KAAA,EAAA,KAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA,aALA;AAMA,QAAA,QAAA,EAAA,CACA,QADA,EAEA,QAFA,EAGA,aAHA,EAIA,WAJA,CANA;AAYA,QAAA,UAAA,EAAA,MAZA;AAaA,QAAA,QAAA,EAAA;AAbA,OAhBA,EA+BA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,IAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,KAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA,cALA;AAMA,QAAA,QAAA,EAAA,CACA,SADA,EAEA,QAFA,EAGA,QAHA,EAIA,SAJA,EAKA,WALA,EAMA,QANA,EAOA,WAPA,CANA;AAeA,QAAA,UAAA,EAAA,MAfA;AAgBA,QAAA,QAAA,EAAA;AAhBA,OA/BA;AADA,KAAA;AAoDA,GA1DA;AA2DA,EAAA,OAAA,EAAA;AACA,IAAA,eADA,2BACA,IADA,EACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA,EADA,CAGA;;AACA,UAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,cAAA,CAAA;;AACA,UAAA,CAAA,KAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA,OATA,CAWA;;;AACA,WAAA,QAAA,CAAA,IAAA,oCAAA,IAAA,CAAA,IAAA;AACA;AAdA;AA3DA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"membership-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">订阅会员</h1>\n        <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n      </div>\n\n      <!-- 会员套餐区域 -->\n      <section class=\"plans-section\">\n        <div class=\"container\">\n          <div class=\"plans-grid\">\n            <div \n              v-for=\"plan in plans\" \n              :key=\"plan.id\"\n              class=\"plan-card\"\n              :class=\"{ 'featured': plan.featured }\"\n            >\n              <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n              <div class=\"plan-header\">\n                <h3 class=\"plan-name\">{{ plan.name }}</h3>\n                <div class=\"plan-price\">\n                  <span class=\"price-symbol\">¥</span>\n                  <span class=\"price-amount\">{{ plan.price }}</span>\n                  <span class=\"price-period\">/{{ plan.period }}</span>\n                </div>\n                <p class=\"plan-description\">{{ plan.description }}</p>\n              </div>\n              \n              <div class=\"plan-features\">\n                <div \n                  v-for=\"feature in plan.features\" \n                  :key=\"feature\"\n                  class=\"feature-item\"\n                >\n                  <a-icon type=\"check\" />\n                  <span>{{ feature }}</span>\n                </div>\n              </div>\n              \n              <button \n                class=\"btn-subscribe\"\n                :class=\"{ 'featured': plan.featured }\"\n                @click=\"handleSubscribe(plan)\"\n              >\n                {{ plan.buttonText }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            '解锁部分课程',\n            '插件基础折扣',\n            '邀请奖励35%基础比例',\n            '调用工作流基础折扣'\n          ],\n          buttonText: '立即订阅',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            '解锁部分课程',\n            '插件基础折扣',\n            '邀请奖励35%基础比例',\n            '调用工作流基础折扣'\n          ],\n          buttonText: '立即订阅',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            '复制所有工作流',\n            '解锁所有课程',\n            '插件最高折扣',\n            '解锁流媒体转换',\n            '邀请奖励50%比例',\n            '部分插件免费',\n            '调用工作流最高折扣'\n          ],\n          buttonText: '立即订阅',\n          featured: true\n        }\n      ]\n    }\n  },\n  methods: {\n    handleSubscribe(plan) {\n      console.log('选择订阅套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get('ACCESS_TOKEN')\n      if (!token) {\n        this.$message.warning('请先登录后再订阅会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      // 显示订阅功能提示\n      this.$message.info(`您选择了 ${plan.name}，会员订阅功能即将上线，敬请期待！`)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.membership-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 会员套餐区域 */\n.plans-section {\n  padding: 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.plan-card {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.plan-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n\n.plan-card.featured {\n  border-color: #3b82f6;\n  transform: scale(1.05);\n}\n\n.plan-badge {\n  position: absolute;\n  top: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1.5rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.plan-name {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.plan-price {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.price-symbol {\n  font-size: 1.2rem;\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.price-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #3b82f6;\n  margin: 0 0.25rem;\n}\n\n.price-period {\n  font-size: 1rem;\n  color: #64748b;\n}\n\n.plan-description {\n  color: #64748b;\n  margin: 0;\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  color: #1e293b;\n}\n\n.feature-item .anticon {\n  color: #10b981;\n  font-weight: bold;\n}\n\n.btn-subscribe {\n  width: 100%;\n  padding: 1rem;\n  background: transparent;\n  border: 2px solid #3b82f6;\n  color: #3b82f6;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-subscribe:hover {\n  background: #3b82f6;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.btn-subscribe.featured {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border: none;\n}\n\n.btn-subscribe.featured:hover {\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card.featured {\n    transform: none;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/membership"}]}