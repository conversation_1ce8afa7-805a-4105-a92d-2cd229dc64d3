{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753808306119}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport Sidebar from './components/Sidebar.vue';\nimport FloatingNotifications from './components/FloatingNotifications.vue';\nimport Overview from './views/Overview.vue';\nimport Profile from './views/Profile.vue';\nimport Credits from './views/Credits.vue';\nimport Orders from './views/Orders.vue';\nimport Usage from './views/Usage.vue'; // 🚫 临时注释掉会员服务和推荐奖励组件\n// import Membership from './views/Membership.vue'\n// import Referral from './views/Referral.vue'\n\nimport Notifications from './pages/Notifications.vue';\nimport { getUserFullInfo } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport { usercenterAnimations } from '@/animations/gsap/pages/usercenterAnimations.js';\nimport Vue from 'vue';\nexport default {\n  name: 'UserCenter',\n  components: {\n    WebsitePage: WebsitePage,\n    Sidebar: Sidebar,\n    FloatingNotifications: FloatingNotifications,\n    Overview: Overview,\n    Profile: Profile,\n    Credits: Credits,\n    Orders: Orders,\n    Usage: Usage,\n    // 🚫 临时注释掉会员服务和推荐奖励组件\n    // Membership,\n    // Referral,\n    Notifications: Notifications\n  },\n  data: function data() {\n    return {\n      loading: true,\n      currentPage: 'overview',\n      userInfo: {\n        nickname: '',\n        email: '',\n        avatar: '',\n        phone: '',\n        accountBalance: 0,\n        currentRole: '普通用户',\n        totalConsumption: 0,\n        totalRecharge: 0,\n        memberExpireTime: null,\n        apiKey: '',\n        createTime: null,\n        // 🔑 关键：添加密码修改状态字段\n        passwordChanged: 0\n      },\n      pageMap: {\n        overview: '概览',\n        profile: '账户设置',\n        credits: '账户管理',\n        orders: '订单记录',\n        usage: '使用记录',\n        notifications: '系统通知' // 🚫 临时注释掉会员服务和推荐奖励页面\n        // membership: '会员服务',\n        // referral: '推荐奖励'\n\n      }\n    };\n  },\n  computed: {\n    currentPageTitle: function currentPageTitle() {\n      return this.pageMap[this.currentPage] || '';\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              if (this.checkLoginStatus()) {\n                _context.next = 2;\n                break;\n              }\n\n              return _context.abrupt(\"return\");\n\n            case 2:\n              _context.next = 4;\n              return this.loadUserInfo();\n\n            case 4:\n              this.initAnimations(); // 处理从官网传递过来的查询参数\n\n              this.handleQueryParams();\n\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    /**\n     * 检查登录状态 - 使用与路由守卫相同的TOKEN检查方法\n     */\n    checkLoginStatus: function checkLoginStatus() {\n      var token = Vue.ls.get(ACCESS_TOKEN);\n\n      if (!token) {\n        console.log('🔍 UserCenter: 未登录，重定向到登录页');\n        this.$message.warning('请先登录');\n        this.$router.push({\n          path: '/login',\n          query: {\n            redirect: this.$route.fullPath\n          }\n        });\n        return false;\n      }\n\n      console.log('🔍 UserCenter: 已登录，TOKEN存在');\n      return true;\n    },\n    loadUserInfo: function () {\n      var _loadUserInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response, rawData, mappedData;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                this.loading = true;\n                _context2.next = 4;\n                return getUserFullInfo();\n\n              case 4:\n                response = _context2.sent;\n                console.log('🔍 UserCenter: 完整的响应对象:', response);\n                console.log('🔍 UserCenter: response.success:', response.success);\n                console.log('🔍 UserCenter: response.data:', response.data);\n                console.log('🔍 UserCenter: response.result:', response.result);\n                console.log('🔍 UserCenter: response.message:', response.message);\n\n                if (!response.success) {\n                  _context2.next = 20;\n                  break;\n                }\n\n                // 使用正确的字段：response.result 而不是 response.data\n                rawData = response.result || response.data || {};\n                console.log('🔍 UserCenter: 后端返回的原始数据:', rawData); // 字段名映射：后端下划线 -> 前端驼峰命名\n\n                mappedData = {\n                  nickname: rawData.nickname || '',\n                  email: rawData.email || '',\n                  avatar: rawData.avatar || '',\n                  phone: rawData.phone || '',\n                  accountBalance: rawData.account_balance || 0,\n                  totalConsumption: rawData.total_consumption || 0,\n                  totalRecharge: rawData.total_recharge || 0,\n                  currentRole: rawData.current_role || '普通用户',\n                  apiKey: rawData.api_key || '',\n                  createTime: rawData.user_create_time || null,\n                  username: rawData.username || '',\n                  realname: rawData.realname || '',\n                  // 🔑 关键：添加密码修改状态字段\n                  passwordChanged: rawData.password_changed || 0\n                };\n                console.log('🔍 UserCenter: 映射后的数据:', mappedData); // 使用Object.assign确保响应式更新\n\n                Object.assign(this.userInfo, mappedData);\n                console.log('🔍 UserCenter: 最终的userInfo:', this.userInfo); // 强制触发视图更新\n\n                this.$forceUpdate();\n                _context2.next = 29;\n                break;\n\n              case 20:\n                console.error('🔍 UserCenter: 获取用户信息失败');\n                console.error('🔍 UserCenter: response.code:', response.code);\n                console.error('🔍 UserCenter: response.message:', response.message);\n                console.error('🔍 UserCenter: 完整响应:', response); // 检查是否是认证失败\n\n                if (!(response.code === 401 || response.message && response.message.includes('Token') || response.message && response.message.includes('登录'))) {\n                  _context2.next = 28;\n                  break;\n                }\n\n                this.$message.warning('登录已过期，请重新登录');\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context2.abrupt(\"return\");\n\n              case 28:\n                this.$message.error(\"\\u83B7\\u53D6\\u7528\\u6237\\u4FE1\\u606F\\u5931\\u8D25: \".concat(response.message || '未知错误'));\n\n              case 29:\n                _context2.next = 39;\n                break;\n\n              case 31:\n                _context2.prev = 31;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('加载用户信息失败:', _context2.t0); // 检查是否是认证相关错误\n\n                if (!(_context2.t0.response && _context2.t0.response.status === 401)) {\n                  _context2.next = 38;\n                  break;\n                }\n\n                this.$message.warning('登录已过期，请重新登录');\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context2.abrupt(\"return\");\n\n              case 38:\n                this.$message.error('加载用户信息失败，请刷新重试');\n\n              case 39:\n                _context2.prev = 39;\n                this.loading = false;\n                return _context2.finish(39);\n\n              case 42:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 31, 39, 42]]);\n      }));\n\n      function loadUserInfo() {\n        return _loadUserInfo.apply(this, arguments);\n      }\n\n      return loadUserInfo;\n    }(),\n    // 刷新用户信息的方法（供子组件调用）\n    getUserInfo: function () {\n      var _getUserInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                console.log('🔍 UserCenter: 收到刷新用户信息请求');\n                _context3.next = 3;\n                return this.loadUserInfo();\n\n              case 3:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function getUserInfo() {\n        return _getUserInfo.apply(this, arguments);\n      }\n\n      return getUserInfo;\n    }(),\n    initAnimations: function initAnimations() {\n      var _this = this;\n\n      this.$nextTick(function () {\n        // 初始化GSAP动画\n        if (_this.$animationManager) {\n          usercenterAnimations.init(_this.$animationManager);\n        }\n      });\n    },\n    handleMenuChange: function handleMenuChange(page) {\n      if (this.currentPage !== page) {\n        // 页面切换动画\n        var fromPage = \".page-\".concat(this.currentPage);\n        var toPage = \".page-\".concat(page);\n\n        if (this.$animationManager) {\n          usercenterAnimations.switchPage(fromPage, toPage);\n        }\n\n        this.currentPage = page; // 更新URL（可选）\n\n        this.$router.replace({\n          path: '/usercenter',\n          query: {\n            page: page\n          }\n        });\n      }\n    },\n    handleNavigate: function handleNavigate(page) {\n      this.handleMenuChange(page);\n    },\n    handleSidebarAction: function handleSidebarAction(action) {\n      switch (action) {\n        case 'recharge':\n          this.handleMenuChange('credits');\n          break;\n\n        case 'upgrade':\n          this.handleMenuChange('membership');\n          break;\n\n        default:\n          console.log('未知操作:', action);\n      }\n    },\n    handleNavigateToNotifications: function handleNavigateToNotifications() {\n      // 导航到系统通知页面\n      this.handleMenuChange('notifications');\n    },\n    // 处理从官网传递过来的查询参数\n    handleQueryParams: function handleQueryParams() {\n      var _this2 = this;\n\n      var _this$$route$query = this.$route.query,\n          page = _this$$route$query.page,\n          planId = _this$$route$query.planId,\n          planName = _this$$route$query.planName,\n          price = _this$$route$query.price; // 如果有page参数，切换到对应页面\n\n      if (page && this.pageMap[page]) {\n        console.log('🎯 UserCenter: 从官网跳转到页面:', page);\n        this.currentPage = page; // 如果是会员页面且有套餐信息，可以在这里处理\n\n        if (page === 'membership' && planId) {\n          console.log('🎯 UserCenter: 选择的套餐信息:', {\n            planId: planId,\n            planName: planName,\n            price: price\n          }); // 可以将套餐信息传递给会员组件\n\n          this.$nextTick(function () {\n            // 通过事件总线或其他方式通知会员组件\n            _this2.$bus && _this2.$bus.$emit('select-membership-plan', {\n              planId: parseInt(planId),\n              planName: planName,\n              price: parseFloat(price)\n            });\n          });\n        }\n      }\n    },\n    handleNotificationUpdated: function handleNotificationUpdated() {\n      // 通知更新时，刷新悬浮通知组件的数据\n      if (this.$refs.floatingNotifications) {\n        this.$refs.floatingNotifications.loadNotifications();\n      } // 同时通知Sidebar组件更新未读通知数量\n\n\n      if (this.$refs.sidebar) {\n        this.$refs.sidebar.loadUnreadNotificationCount();\n      }\n    },\n    handleAvatarUpdated: function handleAvatarUpdated(newAvatar) {\n      // 头像更新时，更新用户信息\n      console.log('🔍 UserCenter: 头像更新事件，新头像:', newAvatar);\n      this.userInfo.avatar = newAvatar;\n      this.$forceUpdate();\n    },\n    handleInfoUpdated: function handleInfoUpdated(updatedInfo) {\n      // 基本信息更新时，更新用户信息\n      console.log('🔍 UserCenter: 基本信息更新事件:', updatedInfo);\n      Object.assign(this.userInfo, updatedInfo);\n      this.$forceUpdate();\n    },\n    handleApiKeyUpdated: function handleApiKeyUpdated(newApiKey) {\n      // API Key更新时，保存当前滚动位置\n      var currentScrollY = window.pageYOffset || document.documentElement.scrollTop;\n      console.log('🔍 UserCenter: API Key更新事件，当前滚动位置:', currentScrollY); // 使用Vue.set确保响应式更新\n\n      this.$set(this.userInfo, 'apiKey', newApiKey); // 在下一个tick恢复滚动位置\n\n      this.$nextTick(function () {\n        window.scrollTo(0, currentScrollY);\n        console.log('🔍 UserCenter: 已恢复滚动位置到:', currentScrollY);\n      });\n    },\n    // 🔑 新增：密码修改处理\n    handlePasswordChanged: function handlePasswordChanged() {\n      console.log('🔍 UserCenter: 收到密码修改事件');\n\n      if (this.userInfo) {\n        this.userInfo.passwordChanged = 1;\n        console.log('🔍 UserCenter: passwordChanged已更新为1');\n      }\n    }\n  },\n  // 路由守卫：根据URL参数设置当前页面\n  beforeRouteEnter: function beforeRouteEnter(to, _from, next) {\n    next(function (vm) {\n      var page = to.query.page;\n\n      if (page && vm.pageMap[page]) {\n        vm.currentPage = page;\n      }\n    });\n  },\n  beforeRouteUpdate: function beforeRouteUpdate(to, _from, next) {\n    var page = to.query.page;\n\n    if (page && this.pageMap[page]) {\n      this.currentPage = page;\n    }\n\n    next();\n  }\n};", {"version": 3, "sources": ["UserCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA,OAAA,WAAA,MAAA,sCAAA;AACA,OAAA,OAAA,MAAA,0BAAA;AACA,OAAA,qBAAA,MAAA,wCAAA;AACA,OAAA,QAAA,MAAA,sBAAA;AACA,OAAA,OAAA,MAAA,qBAAA;AACA,OAAA,OAAA,MAAA,qBAAA;AACA,OAAA,MAAA,MAAA,oBAAA;AACA,OAAA,KAAA,MAAA,mBAAA,C,CACA;AACA;AACA;;AACA,OAAA,aAAA,MAAA,2BAAA;AACA,SAAA,eAAA,QAAA,kBAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AACA,SAAA,oBAAA,QAAA,iDAAA;AACA,OAAA,GAAA,MAAA,KAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA,WADA;AAEA,IAAA,OAAA,EAAA,OAFA;AAGA,IAAA,qBAAA,EAAA,qBAHA;AAIA,IAAA,QAAA,EAAA,QAJA;AAKA,IAAA,OAAA,EAAA,OALA;AAMA,IAAA,OAAA,EAAA,OANA;AAOA,IAAA,MAAA,EAAA,MAPA;AAQA,IAAA,KAAA,EAAA,KARA;AASA;AACA;AACA;AACA,IAAA,aAAA,EAAA;AAZA,GAFA;AAgBA,EAAA,IAhBA,kBAgBA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AAEA,MAAA,WAAA,EAAA,UAFA;AAGA,MAAA,QAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,cAAA,EAAA,CALA;AAMA,QAAA,WAAA,EAAA,MANA;AAOA,QAAA,gBAAA,EAAA,CAPA;AAQA,QAAA,aAAA,EAAA,CARA;AASA,QAAA,gBAAA,EAAA,IATA;AAUA,QAAA,MAAA,EAAA,EAVA;AAWA,QAAA,UAAA,EAAA,IAXA;AAYA;AACA,QAAA,eAAA,EAAA;AAbA,OAHA;AAkBA,MAAA,OAAA,EAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,MAFA;AAGA,QAAA,OAAA,EAAA,MAHA;AAIA,QAAA,MAAA,EAAA,MAJA;AAKA,QAAA,KAAA,EAAA,MALA;AAMA,QAAA,aAAA,EAAA,MANA,CAOA;AACA;AACA;;AATA;AAlBA,KAAA;AA8BA,GA/CA;AAgDA,EAAA,QAAA,EAAA;AACA,IAAA,gBADA,8BACA;AACA,aAAA,KAAA,OAAA,CAAA,KAAA,WAAA,KAAA,EAAA;AACA;AAHA,GAhDA;AAqDA,EAAA,OArDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAuDA,KAAA,gBAAA,EAvDA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AAAA,qBA2DA,KAAA,YAAA,EA3DA;;AAAA;AA4DA,mBAAA,cAAA,GA5DA,CA8DA;;AACA,mBAAA,iBAAA;;AA/DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiEA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,gBAJA,8BAIA;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,CAAA;;AACA,UAAA,CAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,4BAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA;AAAA,YAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAFA,SAAA;AAIA,eAAA,KAAA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,4BAAA;AACA,aAAA,IAAA;AACA,KAjBA;AAmBA,IAAA,YAnBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,qBAAA,OAAA,GAAA,IAAA;AArBA;AAAA,uBAuBA,eAAA,EAvBA;;AAAA;AAuBA,gBAAA,QAvBA;AAwBA,gBAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,EAAA,QAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,QAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,iCAAA,EAAA,QAAA,CAAA,MAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,QAAA,CAAA,OAAA;;AA5BA,qBA8BA,QAAA,CAAA,OA9BA;AAAA;AAAA;AAAA;;AA+BA;AACA,gBAAA,OAhCA,GAgCA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,IAAA,EAhCA;AAiCA,gBAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,OAAA,EAjCA,CAmCA;;AACA,gBAAA,UApCA,GAoCA;AACA,kBAAA,QAAA,EAAA,OAAA,CAAA,QAAA,IAAA,EADA;AAEA,kBAAA,KAAA,EAAA,OAAA,CAAA,KAAA,IAAA,EAFA;AAGA,kBAAA,MAAA,EAAA,OAAA,CAAA,MAAA,IAAA,EAHA;AAIA,kBAAA,KAAA,EAAA,OAAA,CAAA,KAAA,IAAA,EAJA;AAKA,kBAAA,cAAA,EAAA,OAAA,CAAA,eAAA,IAAA,CALA;AAMA,kBAAA,gBAAA,EAAA,OAAA,CAAA,iBAAA,IAAA,CANA;AAOA,kBAAA,aAAA,EAAA,OAAA,CAAA,cAAA,IAAA,CAPA;AAQA,kBAAA,WAAA,EAAA,OAAA,CAAA,YAAA,IAAA,MARA;AASA,kBAAA,MAAA,EAAA,OAAA,CAAA,OAAA,IAAA,EATA;AAUA,kBAAA,UAAA,EAAA,OAAA,CAAA,gBAAA,IAAA,IAVA;AAWA,kBAAA,QAAA,EAAA,OAAA,CAAA,QAAA,IAAA,EAXA;AAYA,kBAAA,QAAA,EAAA,OAAA,CAAA,QAAA,IAAA,EAZA;AAaA;AACA,kBAAA,eAAA,EAAA,OAAA,CAAA,gBAAA,IAAA;AAdA,iBApCA;AAqDA,gBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,UAAA,EArDA,CAuDA;;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,KAAA,QAAA,EAAA,UAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,6BAAA,EAAA,KAAA,QAAA,EA1DA,CA4DA;;AACA,qBAAA,YAAA;AA7DA;AAAA;;AAAA;AA+DA,gBAAA,OAAA,CAAA,KAAA,CAAA,yBAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA,EAAA,QAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,kCAAA,EAAA,QAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,sBAAA,EAAA,QAAA,EAlEA,CAoEA;;AApEA,sBAqEA,QAAA,CAAA,IAAA,KAAA,GAAA,IAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CArEA;AAAA;AAAA;AAAA;;AAsEA,qBAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA,qBAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,IAAA,EAAA,QADA;AAEA,kBAAA,KAAA,EAAA;AAAA,oBAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAFA,iBAAA;AAvEA;;AAAA;AA6EA,qBAAA,QAAA,CAAA,KAAA,6DAAA,QAAA,CAAA,OAAA,IAAA,MAAA;;AA7EA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAgFA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,gBAhFA,CAiFA;;AAjFA,sBAkFA,aAAA,QAAA,IAAA,aAAA,QAAA,CAAA,MAAA,KAAA,GAlFA;AAAA;AAAA;AAAA;;AAmFA,qBAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA,qBAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,IAAA,EAAA,QADA;AAEA,kBAAA,KAAA,EAAA;AAAA,oBAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAFA,iBAAA;AApFA;;AAAA;AA0FA,qBAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;;AA1FA;AAAA;AA4FA,qBAAA,OAAA,GAAA,KAAA;AA5FA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAgGA;AACA,IAAA,WAjGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkGA,gBAAA,OAAA,CAAA,GAAA,CAAA,2BAAA;AAlGA;AAAA,uBAmGA,KAAA,YAAA,EAnGA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsGA,IAAA,cAtGA,4BAsGA;AAAA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA;AACA,YAAA,KAAA,CAAA,iBAAA,EAAA;AACA,UAAA,oBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,iBAAA;AACA;AACA,OALA;AAMA,KA7GA;AA+GA,IAAA,gBA/GA,4BA+GA,IA/GA,EA+GA;AACA,UAAA,KAAA,WAAA,KAAA,IAAA,EAAA;AACA;AACA,YAAA,QAAA,mBAAA,KAAA,WAAA,CAAA;AACA,YAAA,MAAA,mBAAA,IAAA,CAAA;;AAEA,YAAA,KAAA,iBAAA,EAAA;AACA,UAAA,oBAAA,CAAA,UAAA,CAAA,QAAA,EAAA,MAAA;AACA;;AAEA,aAAA,WAAA,GAAA,IAAA,CATA,CAWA;;AACA,aAAA,OAAA,CAAA,OAAA,CAAA;AACA,UAAA,IAAA,EAAA,aADA;AAEA,UAAA,KAAA,EAAA;AAAA,YAAA,IAAA,EAAA;AAAA;AAFA,SAAA;AAIA;AACA,KAjIA;AAmIA,IAAA,cAnIA,0BAmIA,IAnIA,EAmIA;AACA,WAAA,gBAAA,CAAA,IAAA;AACA,KArIA;AAuIA,IAAA,mBAvIA,+BAuIA,MAvIA,EAuIA;AACA,cAAA,MAAA;AACA,aAAA,UAAA;AACA,eAAA,gBAAA,CAAA,SAAA;AACA;;AACA,aAAA,SAAA;AACA,eAAA,gBAAA,CAAA,YAAA;AACA;;AACA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA;AARA;AAUA,KAlJA;AAoJA,IAAA,6BApJA,2CAoJA;AACA;AACA,WAAA,gBAAA,CAAA,eAAA;AACA,KAvJA;AAyJA;AACA,IAAA,iBA1JA,+BA0JA;AAAA;;AAAA,+BACA,KAAA,MAAA,CAAA,KADA;AAAA,UACA,IADA,sBACA,IADA;AAAA,UACA,MADA,sBACA,MADA;AAAA,UACA,QADA,sBACA,QADA;AAAA,UACA,KADA,sBACA,KADA,EAGA;;AACA,UAAA,IAAA,IAAA,KAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,IAAA;AACA,aAAA,WAAA,GAAA,IAAA,CAFA,CAIA;;AACA,YAAA,IAAA,KAAA,YAAA,IAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,EAAA;AAAA,YAAA,MAAA,EAAA,MAAA;AAAA,YAAA,QAAA,EAAA,QAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EADA,CAEA;;AACA,eAAA,SAAA,CAAA,YAAA;AACA;AACA,YAAA,MAAA,CAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,wBAAA,EAAA;AACA,cAAA,MAAA,EAAA,QAAA,CAAA,MAAA,CADA;AAEA,cAAA,QAAA,EAAA,QAFA;AAGA,cAAA,KAAA,EAAA,UAAA,CAAA,KAAA;AAHA,aAAA,CAAA;AAKA,WAPA;AAQA;AACA;AACA,KAhLA;AAkLA,IAAA,yBAlLA,uCAkLA;AACA;AACA,UAAA,KAAA,KAAA,CAAA,qBAAA,EAAA;AACA,aAAA,KAAA,CAAA,qBAAA,CAAA,iBAAA;AACA,OAJA,CAMA;;;AACA,UAAA,KAAA,KAAA,CAAA,OAAA,EAAA;AACA,aAAA,KAAA,CAAA,OAAA,CAAA,2BAAA;AACA;AACA,KA5LA;AA8LA,IAAA,mBA9LA,+BA8LA,SA9LA,EA8LA;AACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,4BAAA,EAAA,SAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,SAAA;AACA,WAAA,YAAA;AACA,KAnMA;AAqMA,IAAA,iBArMA,6BAqMA,WArMA,EAqMA;AACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,WAAA;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,KAAA,QAAA,EAAA,WAAA;AACA,WAAA,YAAA;AACA,KA1MA;AA4MA,IAAA,mBA5MA,+BA4MA,SA5MA,EA4MA;AACA;AACA,UAAA,cAAA,GAAA,MAAA,CAAA,WAAA,IAAA,QAAA,CAAA,eAAA,CAAA,SAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EAAA,cAAA,EAHA,CAKA;;AACA,WAAA,IAAA,CAAA,KAAA,QAAA,EAAA,QAAA,EAAA,SAAA,EANA,CAQA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,CAAA,EAAA,cAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,cAAA;AACA,OAHA;AAIA,KAzNA;AA2NA;AACA,IAAA,qBA5NA,mCA4NA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,yBAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,eAAA,GAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,qCAAA;AACA;AACA;AAlOA,GAjEA;AAsSA;AACA,EAAA,gBAvSA,4BAuSA,EAvSA,EAuSA,KAvSA,EAuSA,IAvSA,EAuSA;AACA,IAAA,IAAA,CAAA,UAAA,EAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA,CAAA,KAAA,CAAA,IAAA;;AACA,UAAA,IAAA,IAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AACA,QAAA,EAAA,CAAA,WAAA,GAAA,IAAA;AACA;AACA,KALA,CAAA;AAMA,GA9SA;AAgTA,EAAA,iBAhTA,6BAgTA,EAhTA,EAgTA,KAhTA,EAgTA,IAhTA,EAgTA;AACA,QAAA,IAAA,GAAA,EAAA,CAAA,KAAA,CAAA,IAAA;;AACA,QAAA,IAAA,IAAA,KAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA;;AACA,IAAA,IAAA;AACA;AAtTA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"usercenter-container\">\n\n\n      <!-- 主要内容区域 -->\n      <div class=\"usercenter-main\">\n        <div class=\"container\">\n          <div class=\"usercenter-layout\">\n            <!-- 侧边栏 -->\n            <Sidebar\n              ref=\"sidebar\"\n              :current-page=\"currentPage\"\n              :user-info=\"userInfo\"\n              @menu-change=\"handleMenuChange\"\n              @action=\"handleSidebarAction\"\n            />\n\n            <!-- 内容区域 -->\n            <div class=\"usercenter-content\">\n              <!-- 概览页面 -->\n              <Overview\n                v-if=\"currentPage === 'overview'\"\n                :key=\"'overview'\"\n                :user-info=\"userInfo\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 账户设置页面 -->\n              <Profile\n                v-else-if=\"currentPage === 'profile'\"\n                :key=\"'profile'\"\n                :user-info=\"userInfo\"\n                @navigate=\"handleNavigate\"\n                @avatar-updated=\"handleAvatarUpdated\"\n                @info-updated=\"handleInfoUpdated\"\n                @api-key-updated=\"handleApiKeyUpdated\"\n                @password-changed=\"handlePasswordChanged\"\n                @refresh-user-info=\"getUserInfo\"\n              />\n\n              <!-- 账户管理页面 -->\n              <Credits\n                v-else-if=\"currentPage === 'credits'\"\n                :key=\"'credits'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 订单记录页面 -->\n              <Orders\n                v-else-if=\"currentPage === 'orders'\"\n                :key=\"'orders'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 使用记录页面 -->\n              <Usage\n                v-else-if=\"currentPage === 'usage'\"\n                :key=\"'usage'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 会员服务页面 -->\n              <Membership\n                v-else-if=\"currentPage === 'membership'\"\n                :key=\"'membership'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- <Referral\n                v-else-if=\"currentPage === 'referral'\"\n                :key=\"'referral'\"\n                @navigate=\"handleNavigate\"\n              /> -->\n\n              <!-- 系统通知页面 -->\n              <Notifications\n                v-else-if=\"currentPage === 'notifications'\"\n                :key=\"'notifications'\"\n                @navigate=\"handleNavigate\"\n                @notification-updated=\"handleNotificationUpdated\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 悬浮系统通知 -->\n    <FloatingNotifications\n      ref=\"floatingNotifications\"\n      @navigate-to-notifications=\"handleNavigateToNotifications\"\n    />\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport Sidebar from './components/Sidebar.vue'\nimport FloatingNotifications from './components/FloatingNotifications.vue'\nimport Overview from './views/Overview.vue'\nimport Profile from './views/Profile.vue'\nimport Credits from './views/Credits.vue'\nimport Orders from './views/Orders.vue'\nimport Usage from './views/Usage.vue'\n// 🚫 临时注释掉会员服务和推荐奖励组件\n// import Membership from './views/Membership.vue'\n// import Referral from './views/Referral.vue'\nimport Notifications from './pages/Notifications.vue'\nimport { getUserFullInfo } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { usercenterAnimations } from '@/animations/gsap/pages/usercenterAnimations.js'\nimport Vue from 'vue'\n\nexport default {\n  name: 'UserCenter',\n  components: {\n    WebsitePage,\n    Sidebar,\n    FloatingNotifications,\n    Overview,\n    Profile,\n    Credits,\n    Orders,\n    Usage,\n    // 🚫 临时注释掉会员服务和推荐奖励组件\n    // Membership,\n    // Referral,\n    Notifications\n  },\n  data() {\n    return {\n      loading: true,\n      currentPage: 'overview',\n      userInfo: {\n        nickname: '',\n        email: '',\n        avatar: '',\n        phone: '',\n        accountBalance: 0,\n        currentRole: '普通用户',\n        totalConsumption: 0,\n        totalRecharge: 0,\n        memberExpireTime: null,\n        apiKey: '',\n        createTime: null,\n        // 🔑 关键：添加密码修改状态字段\n        passwordChanged: 0\n      },\n      pageMap: {\n        overview: '概览',\n        profile: '账户设置',\n        credits: '账户管理',\n        orders: '订单记录',\n        usage: '使用记录',\n        notifications: '系统通知'\n        // 🚫 临时注释掉会员服务和推荐奖励页面\n        // membership: '会员服务',\n        // referral: '推荐奖励'\n      }\n    }\n  },\n  computed: {\n    currentPageTitle() {\n      return this.pageMap[this.currentPage] || ''\n    }\n  },\n  async mounted() {\n    // 检查登录状态\n    if (!this.checkLoginStatus()) {\n      return\n    }\n\n    await this.loadUserInfo()\n    this.initAnimations()\n\n    // 处理从官网传递过来的查询参数\n    this.handleQueryParams()\n  },\n  methods: {\n    /**\n     * 检查登录状态 - 使用与路由守卫相同的TOKEN检查方法\n     */\n    checkLoginStatus() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        console.log('🔍 UserCenter: 未登录，重定向到登录页')\n        this.$message.warning('请先登录')\n        this.$router.push({\n          path: '/login',\n          query: { redirect: this.$route.fullPath }\n        })\n        return false\n      }\n      console.log('🔍 UserCenter: 已登录，TOKEN存在')\n      return true\n    },\n\n    async loadUserInfo() {\n      try {\n        this.loading = true\n\n        const response = await getUserFullInfo()\n        console.log('🔍 UserCenter: 完整的响应对象:', response)\n        console.log('🔍 UserCenter: response.success:', response.success)\n        console.log('🔍 UserCenter: response.data:', response.data)\n        console.log('🔍 UserCenter: response.result:', response.result)\n        console.log('🔍 UserCenter: response.message:', response.message)\n\n        if (response.success) {\n          // 使用正确的字段：response.result 而不是 response.data\n          const rawData = response.result || response.data || {}\n          console.log('🔍 UserCenter: 后端返回的原始数据:', rawData)\n\n          // 字段名映射：后端下划线 -> 前端驼峰命名\n          const mappedData = {\n            nickname: rawData.nickname || '',\n            email: rawData.email || '',\n            avatar: rawData.avatar || '',\n            phone: rawData.phone || '',\n            accountBalance: rawData.account_balance || 0,\n            totalConsumption: rawData.total_consumption || 0,\n            totalRecharge: rawData.total_recharge || 0,\n            currentRole: rawData.current_role || '普通用户',\n            apiKey: rawData.api_key || '',\n            createTime: rawData.user_create_time || null,\n            username: rawData.username || '',\n            realname: rawData.realname || '',\n            // 🔑 关键：添加密码修改状态字段\n            passwordChanged: rawData.password_changed || 0\n          }\n\n          console.log('🔍 UserCenter: 映射后的数据:', mappedData)\n\n          // 使用Object.assign确保响应式更新\n          Object.assign(this.userInfo, mappedData)\n\n          console.log('🔍 UserCenter: 最终的userInfo:', this.userInfo)\n\n          // 强制触发视图更新\n          this.$forceUpdate()\n        } else {\n          console.error('🔍 UserCenter: 获取用户信息失败')\n          console.error('🔍 UserCenter: response.code:', response.code)\n          console.error('🔍 UserCenter: response.message:', response.message)\n          console.error('🔍 UserCenter: 完整响应:', response)\n\n          // 检查是否是认证失败\n          if (response.code === 401 || (response.message && response.message.includes('Token')) || (response.message && response.message.includes('登录'))) {\n            this.$message.warning('登录已过期，请重新登录')\n            this.$router.push({\n              path: '/login',\n              query: { redirect: this.$route.fullPath }\n            })\n            return\n          }\n          this.$message.error(`获取用户信息失败: ${response.message || '未知错误'}`)\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        // 检查是否是认证相关错误\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n          this.$router.push({\n            path: '/login',\n            query: { redirect: this.$route.fullPath }\n          })\n          return\n        }\n        this.$message.error('加载用户信息失败，请刷新重试')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 刷新用户信息的方法（供子组件调用）\n    async getUserInfo() {\n      console.log('🔍 UserCenter: 收到刷新用户信息请求')\n      await this.loadUserInfo()\n    },\n\n    initAnimations() {\n      this.$nextTick(() => {\n        // 初始化GSAP动画\n        if (this.$animationManager) {\n          usercenterAnimations.init(this.$animationManager)\n        }\n      })\n    },\n\n    handleMenuChange(page) {\n      if (this.currentPage !== page) {\n        // 页面切换动画\n        const fromPage = `.page-${this.currentPage}`\n        const toPage = `.page-${page}`\n\n        if (this.$animationManager) {\n          usercenterAnimations.switchPage(fromPage, toPage)\n        }\n\n        this.currentPage = page\n\n        // 更新URL（可选）\n        this.$router.replace({\n          path: '/usercenter',\n          query: { page }\n        })\n      }\n    },\n\n    handleNavigate(page) {\n      this.handleMenuChange(page)\n    },\n\n    handleSidebarAction(action) {\n      switch (action) {\n        case 'recharge':\n          this.handleMenuChange('credits')\n          break\n        case 'upgrade':\n          this.handleMenuChange('membership')\n          break\n        default:\n          console.log('未知操作:', action)\n      }\n    },\n\n    handleNavigateToNotifications() {\n      // 导航到系统通知页面\n      this.handleMenuChange('notifications')\n    },\n\n    // 处理从官网传递过来的查询参数\n    handleQueryParams() {\n      const { page, planId, planName, price } = this.$route.query\n\n      // 如果有page参数，切换到对应页面\n      if (page && this.pageMap[page]) {\n        console.log('🎯 UserCenter: 从官网跳转到页面:', page)\n        this.currentPage = page\n\n        // 如果是会员页面且有套餐信息，可以在这里处理\n        if (page === 'membership' && planId) {\n          console.log('🎯 UserCenter: 选择的套餐信息:', { planId, planName, price })\n          // 可以将套餐信息传递给会员组件\n          this.$nextTick(() => {\n            // 通过事件总线或其他方式通知会员组件\n            this.$bus && this.$bus.$emit('select-membership-plan', {\n              planId: parseInt(planId),\n              planName,\n              price: parseFloat(price)\n            })\n          })\n        }\n      }\n    },\n\n    handleNotificationUpdated() {\n      // 通知更新时，刷新悬浮通知组件的数据\n      if (this.$refs.floatingNotifications) {\n        this.$refs.floatingNotifications.loadNotifications()\n      }\n\n      // 同时通知Sidebar组件更新未读通知数量\n      if (this.$refs.sidebar) {\n        this.$refs.sidebar.loadUnreadNotificationCount()\n      }\n    },\n\n    handleAvatarUpdated(newAvatar) {\n      // 头像更新时，更新用户信息\n      console.log('🔍 UserCenter: 头像更新事件，新头像:', newAvatar)\n      this.userInfo.avatar = newAvatar\n      this.$forceUpdate()\n    },\n\n    handleInfoUpdated(updatedInfo) {\n      // 基本信息更新时，更新用户信息\n      console.log('🔍 UserCenter: 基本信息更新事件:', updatedInfo)\n      Object.assign(this.userInfo, updatedInfo)\n      this.$forceUpdate()\n    },\n\n    handleApiKeyUpdated(newApiKey) {\n      // API Key更新时，保存当前滚动位置\n      const currentScrollY = window.pageYOffset || document.documentElement.scrollTop\n      console.log('🔍 UserCenter: API Key更新事件，当前滚动位置:', currentScrollY)\n\n      // 使用Vue.set确保响应式更新\n      this.$set(this.userInfo, 'apiKey', newApiKey)\n\n      // 在下一个tick恢复滚动位置\n      this.$nextTick(() => {\n        window.scrollTo(0, currentScrollY)\n        console.log('🔍 UserCenter: 已恢复滚动位置到:', currentScrollY)\n      })\n    },\n\n    // 🔑 新增：密码修改处理\n    handlePasswordChanged() {\n      console.log('🔍 UserCenter: 收到密码修改事件')\n      if (this.userInfo) {\n        this.userInfo.passwordChanged = 1\n        console.log('🔍 UserCenter: passwordChanged已更新为1')\n      }\n    }\n  },\n\n  // 路由守卫：根据URL参数设置当前页面\n  beforeRouteEnter(to, _from, next) {\n    next(vm => {\n      const page = to.query.page\n      if (page && vm.pageMap[page]) {\n        vm.currentPage = page\n      }\n    })\n  },\n\n  beforeRouteUpdate(to, _from, next) {\n    const page = to.query.page\n    if (page && this.pageMap[page]) {\n      this.currentPage = page\n    }\n    next()\n  }\n}\n</script>\n\n<style scoped>\n.usercenter-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n}\n\n\n\n.container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* 主要内容区域 */\n.usercenter-main {\n  padding: 2rem 0;\n}\n\n.usercenter-layout {\n  display: grid;\n  grid-template-columns: 260px 1fr;\n  gap: 1.5rem;\n  align-items: flex-start;\n  /* 取消左边距，改为正常的两列布局 */\n  /* margin-left: 320px; */\n}\n\n.usercenter-content {\n  min-height: 600px;\n  position: relative;\n}\n\n/* 页面切换动画 */\n.usercenter-content > * {\n  opacity: 0.85;\n  animation: fadeInUp 0.6s ease-out forwards;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .usercenter-layout {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .usercenter-main {\n    padding: 1rem 0;\n  }\n\n  .usercenter-layout {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n\n\n</style>\n"], "sourceRoot": "src/views/website/usercenter"}]}