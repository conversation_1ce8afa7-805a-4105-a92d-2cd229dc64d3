{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=style&index=0&id=7dc25498&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753820476608}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.quick-recharge-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 16px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.quick-recharge-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.balance-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-avatar {\n  position: relative;\n}\n\n.user-avatar .ant-avatar {\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.user-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.user-name {\n  color: white;\n  font-size: 1.1rem;\n  font-weight: 600;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.balance-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.balance-label {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.85rem;\n  margin-bottom: 0.1rem;\n}\n\n.balance-amount {\n  color: white;\n  font-size: 1.6rem;\n  font-weight: bold;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.balance-actions {\n  position: relative;\n  z-index: 1;\n}\n\n.recharge-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n}\n\n.recharge-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n.recharge-btn i {\n  font-size: 1.1rem;\n}\n\n/* 充值弹窗样式 */\n.recharge-modal-content {\n  padding: 1rem 0;\n}\n\n.recharge-modal-content h4 {\n  margin-bottom: 1rem;\n  color: #1f2937;\n  font-weight: 600;\n}\n\n.options-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.recharge-option {\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 1rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f9fafb;\n}\n\n.recharge-option:hover {\n  border-color: #3b82f6;\n  background: #eff6ff;\n}\n\n.recharge-option.selected {\n  border-color: #3b82f6;\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n}\n\n.option-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  margin-bottom: 0.25rem;\n}\n\n.option-label {\n  font-size: 0.8rem;\n  opacity: 0.8;\n}\n\n.custom-amount {\n  margin-bottom: 2rem;\n}\n\n.custom-input {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.currency {\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.recharge-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.amount-summary {\n  font-size: 1rem;\n  color: #374151;\n}\n\n.final-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-left: 0.5rem;\n}\n\n/* 二维码支付样式 */\n.qr-payment-content {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code img {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.qr-loading {\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.qr-info {\n  margin-bottom: 1.5rem;\n}\n\n.qr-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-bottom: 0.5rem;\n}\n\n.qr-tip {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .quick-recharge-card {\n    padding: 1.5rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .balance-section {\n    flex-direction: column;\n    gap: 1.5rem;\n    text-align: center;\n  }\n\n  .user-info {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .user-details {\n    align-items: center;\n  }\n\n  .balance-amount {\n    font-size: 1.4rem;\n  }\n\n  .options-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .recharge-actions {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n", {"version": 3, "sources": ["QuickRecharge.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "QuickRecharge.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"quick-recharge-card\">\n    <!-- 余额显示区域 -->\n    <div class=\"balance-section\">\n      <div class=\"user-info\">\n        <div class=\"user-avatar\">\n          <a-avatar\n            :size=\"60\"\n            :src=\"avatarUrl\"\n            icon=\"user\"\n            :style=\"{ backgroundColor: '#87d068' }\"\n          >\n            {{ userNickname ? userNickname.charAt(0) : 'U' }}\n          </a-avatar>\n        </div>\n        <div class=\"user-details\">\n          <div class=\"user-name\">{{ userNickname || '智界用户' }}</div>\n          <div class=\"balance-info\">\n            <span class=\"balance-label\">账户余额</span>\n            <span class=\"balance-amount\">¥{{ formatBalance(userBalance) }}</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"balance-actions\">\n        <button class=\"recharge-btn\" @click=\"showRechargeModal = true\">\n          <i class=\"anticon anticon-plus-circle\"></i>\n          快速充值\n        </button>\n      </div>\n    </div>\n    \n    <!-- 充值弹窗 -->\n    <a-modal\n      title=\"账户充值\"\n      :visible=\"showRechargeModal\"\n      @cancel=\"showRechargeModal = false\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"recharge-modal-content\">\n        <!-- 充值选项 -->\n        <div class=\"recharge-options\">\n          <h4>选择充值金额</h4>\n          <div class=\"options-grid\">\n            <div \n              v-for=\"option in rechargeOptions\" \n              :key=\"option.amount\"\n              class=\"recharge-option\"\n              :class=\"{ selected: selectedAmount === option.amount }\"\n              @click=\"selectRechargeAmount(option.amount)\"\n            >\n              <div class=\"option-amount\">¥{{ option.amount }}</div>\n              <div class=\"option-label\">{{ option.label }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 自定义金额 -->\n        <div class=\"custom-amount\">\n          <h4>自定义金额</h4>\n          <div class=\"custom-input\">\n            <a-input-number\n              v-model=\"customAmount\"\n              :min=\"0.01\"\n              :max=\"10000\"\n              :step=\"0.01\"\n              placeholder=\"最低0.01元\"\n              size=\"large\"\n              style=\"width: 200px\"\n            />\n            <span class=\"currency\">元</span>\n          </div>\n        </div>\n        \n        <!-- 支付方式 -->\n        <div class=\"payment-methods\">\n          <h4>支付方式</h4>\n          <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n            <a-radio-button value=\"alipay-qr\">\n              <i class=\"anticon anticon-qrcode\"></i>\n              支付宝扫码\n            </a-radio-button>\n            <a-radio-button value=\"alipay-page\">\n              <i class=\"anticon anticon-alipay\"></i>\n              支付宝网页\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n        \n        <!-- 充值按钮 -->\n        <div class=\"recharge-actions\">\n          <div class=\"amount-summary\">\n            <span>充值金额：</span>\n            <span class=\"final-amount\">¥{{ finalRechargeAmount }}</span>\n          </div>\n          <a-button \n            type=\"primary\" \n            size=\"large\"\n            :loading=\"rechargeLoading\"\n            @click=\"handleRecharge\"\n            :disabled=\"!finalRechargeAmount || finalRechargeAmount < 0.01\"\n          >\n            确认充值\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n    \n    <!-- 支付二维码弹窗 -->\n    <a-modal\n      title=\"扫码支付\"\n      :visible=\"showQrModal\"\n      @cancel=\"closeQrModal\"\n      :footer=\"null\"\n      width=\"400px\"\n    >\n      <div class=\"qr-payment-content\">\n        <div class=\"qr-code-container\">\n          <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n            <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n          </div>\n          <div v-else class=\"qr-loading\">\n            <a-spin size=\"large\" />\n            <p>正在生成二维码...</p>\n          </div>\n        </div>\n        <div class=\"qr-info\">\n          <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n          <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n        </div>\n        <div class=\"qr-status\">\n          <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n            检查支付状态\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport {\n  getTransactionStats,\n  createRechargeOrder,\n  getUserProfile\n} from '@/api/usercenter'\nimport { getFileAccessHttpUrl } from '@/utils/util'\n\nexport default {\n  name: 'QuickRecharge',\n  data() {\n    return {\n      loading: false,\n      rechargeLoading: false,\n      checkingStatus: false,\n      \n      // 用户信息\n      userBalance: 0,\n      userNickname: '',\n      userAvatar: '',\n      defaultAvatar: '/default-avatar.png', // 本地默认头像作为降级方案\n      \n      // 充值弹窗\n      showRechargeModal: false,\n      \n      // 充值选项\n      rechargeOptions: [\n        { amount: 50, label: '体验套餐' },\n        { amount: 100, label: '基础套餐' },\n        { amount: 300, label: '进阶套餐' },\n        { amount: 500, label: '专业套餐' },\n        { amount: 1000, label: '企业套餐' }\n      ],\n      selectedAmount: 0,\n      customAmount: null,\n      \n      // 支付方式\n      selectedPaymentMethod: 'alipay-qr',\n      \n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null\n    }\n  },\n  \n  computed: {\n    finalRechargeAmount() {\n      return this.customAmount || this.selectedAmount || 0\n    },\n\n    // 头像URL处理（与个人中心逻辑一致）\n    avatarUrl() {\n      const avatar = this.userAvatar\n      if (!avatar) {\n        return this.defaultAvatar\n      }\n\n      // 如果是完整的URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是相对路径，使用getFileAccessHttpUrl转换\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar\n    }\n  },\n  \n  mounted() {\n    this.loadUserInfo()\n    this.loadDefaultAvatar()\n  },\n  \n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n  \n  methods: {\n    // 加载用户信息（包含余额）\n    async loadUserInfo() {\n      try {\n        this.loading = true\n\n        // 加载余额信息\n        const statsResponse = await getTransactionStats()\n        if (statsResponse.success) {\n          const stats = statsResponse.result || {}\n          this.userBalance = stats.accountBalance || 0\n        }\n\n        // 加载用户基本信息\n        const profileResponse = await getUserProfile()\n        if (profileResponse.success) {\n          const profile = profileResponse.result || {}\n          this.userNickname = profile.nickname || profile.realname || ''\n          this.userAvatar = profile.avatar || ''\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载TOS默认头像URL\n    async loadDefaultAvatar() {\n      try {\n        const response = await this.$http.get('/sys/common/default-avatar-url')\n        if (response && response.success && response.result) {\n          this.defaultAvatar = response.result\n          console.log('🎯 QuickRecharge: 已加载TOS默认头像:', this.defaultAvatar)\n        }\n      } catch (error) {\n        console.warn('⚠️ QuickRecharge: 获取TOS默认头像失败，使用本地降级:', error)\n        // 保持本地默认头像作为降级方案\n      }\n    },\n\n    // 头像URL处理方法（与个人中心逻辑一致）\n    getFileAccessHttpUrl(avatar) {\n      if (!avatar) return ''\n\n      // 如果已经是完整URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是TOS文件，使用全局方法\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar\n      }\n\n      // 本地文件，使用静态域名\n      return this.$store.state.app.staticDomainURL + '/' + avatar\n    },\n    \n    // 格式化余额显示\n    formatBalance(balance) {\n      return parseFloat(balance || 0).toFixed(2)\n    },\n    \n    // 选择充值金额\n    selectRechargeAmount(amount) {\n      this.selectedAmount = amount\n      this.customAmount = null\n    },\n    \n    // 处理充值\n    async handleRecharge() {\n      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {\n        this.$message.warning('请选择或输入充值金额，最低0.01元')\n        return\n      }\n\n      try {\n        this.rechargeLoading = true\n\n        const orderData = {\n          amount: this.finalRechargeAmount,\n          paymentMethod: this.selectedPaymentMethod\n        }\n\n        const response = await createRechargeOrder(orderData)\n        if (response.success) {\n          const result = response.result\n\n          if (this.selectedPaymentMethod === 'alipay-page') {\n            await this.handleAlipayPagePayment(result.orderId, result.amount)\n          } else if (this.selectedPaymentMethod === 'alipay-qr') {\n            await this.handleAlipayQrPayment(result.orderId, result.amount)\n          }\n\n          this.showRechargeModal = false\n        } else {\n          this.$message.error(response.message || '创建充值订单失败')\n        }\n      } catch (error) {\n        console.error('创建充值订单失败:', error)\n        this.$message.error('充值失败，请重试')\n      } finally {\n        this.rechargeLoading = false\n      }\n    },\n    \n    // 处理支付宝网页支付\n    async handleAlipayPagePayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        // 调用支付宝支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n    \n    // 处理支付宝扫码支付\n    async handleAlipayQrPayment(orderId, amount) {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        // 调用支付宝扫码支付接口\n        const paymentData = {\n          orderId: orderId,\n          amount: amount,\n          subject: '智界Aigc账户充值',\n          body: `充值金额：¥${amount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode, orderId, amount)\n\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n    \n    // 显示二维码弹窗\n    showQrCodeModal(qrCode, orderId, amount) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`\n      this.currentOrderId = orderId\n      this.currentOrderAmount = amount\n      this.showQrModal = true\n\n      console.log('🔍 显示二维码弹窗 - 订单号:', orderId, '金额:', amount)\n      console.log('🔍 二维码URL:', this.qrCodeUrl)\n\n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n    \n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n      this.currentOrderId = ''\n      this.currentOrderAmount = 0\n      \n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n    \n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n      \n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n    \n    // 检查支付状态\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        this.checkingStatus = true\n        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        console.log('🔍 支付状态查询响应:', response)\n\n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          console.log('🔍 支付成功！')\n          this.$message.success('支付成功！')\n          this.closeQrModal()\n          this.loadUserInfo() // 刷新用户信息和余额\n          this.$emit('recharge-success') // 通知父组件\n        } else {\n          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.quick-recharge-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 16px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.quick-recharge-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.balance-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-avatar {\n  position: relative;\n}\n\n.user-avatar .ant-avatar {\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.user-details {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.user-name {\n  color: white;\n  font-size: 1.1rem;\n  font-weight: 600;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.balance-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.balance-label {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.85rem;\n  margin-bottom: 0.1rem;\n}\n\n.balance-amount {\n  color: white;\n  font-size: 1.6rem;\n  font-weight: bold;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.balance-actions {\n  position: relative;\n  z-index: 1;\n}\n\n.recharge-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n}\n\n.recharge-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\n}\n\n.recharge-btn i {\n  font-size: 1.1rem;\n}\n\n/* 充值弹窗样式 */\n.recharge-modal-content {\n  padding: 1rem 0;\n}\n\n.recharge-modal-content h4 {\n  margin-bottom: 1rem;\n  color: #1f2937;\n  font-weight: 600;\n}\n\n.options-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.recharge-option {\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  padding: 1rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f9fafb;\n}\n\n.recharge-option:hover {\n  border-color: #3b82f6;\n  background: #eff6ff;\n}\n\n.recharge-option.selected {\n  border-color: #3b82f6;\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n}\n\n.option-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  margin-bottom: 0.25rem;\n}\n\n.option-label {\n  font-size: 0.8rem;\n  opacity: 0.8;\n}\n\n.custom-amount {\n  margin-bottom: 2rem;\n}\n\n.custom-input {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.currency {\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.payment-methods {\n  margin-bottom: 2rem;\n}\n\n.recharge-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.amount-summary {\n  font-size: 1rem;\n  color: #374151;\n}\n\n.final-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-left: 0.5rem;\n}\n\n/* 二维码支付样式 */\n.qr-payment-content {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code img {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.qr-loading {\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.qr-info {\n  margin-bottom: 1.5rem;\n}\n\n.qr-amount {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #3b82f6;\n  margin-bottom: 0.5rem;\n}\n\n.qr-tip {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .quick-recharge-card {\n    padding: 1.5rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .balance-section {\n    flex-direction: column;\n    gap: 1.5rem;\n    text-align: center;\n  }\n\n  .user-info {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .user-details {\n    align-items: center;\n  }\n\n  .balance-amount {\n    font-size: 1.4rem;\n  }\n\n  .options-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .recharge-actions {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n</style>\n"]}]}