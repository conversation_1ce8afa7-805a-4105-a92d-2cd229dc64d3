{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=template&id=4569dbde&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753808450477}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"WebsitePage\",\n    [\n      _c(\"div\", { staticClass: \"usercenter-container\" }, [\n        _c(\"div\", { staticClass: \"usercenter-main\" }, [\n          _c(\"div\", { staticClass: \"container\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"usercenter-layout\" },\n              [\n                _c(\"Sidebar\", {\n                  ref: \"sidebar\",\n                  attrs: {\n                    \"current-page\": _vm.currentPage,\n                    \"user-info\": _vm.userInfo\n                  },\n                  on: {\n                    \"menu-change\": _vm.handleMenuChange,\n                    action: _vm.handleSidebarAction\n                  }\n                }),\n                _c(\n                  \"div\",\n                  { staticClass: \"usercenter-content\" },\n                  [\n                    _vm.currentPage === \"overview\"\n                      ? _c(\"Overview\", {\n                          key: \"overview\",\n                          attrs: { \"user-info\": _vm.userInfo },\n                          on: { navigate: _vm.handleNavigate }\n                        })\n                      : _vm.currentPage === \"profile\"\n                      ? _c(\"Profile\", {\n                          key: \"profile\",\n                          attrs: { \"user-info\": _vm.userInfo },\n                          on: {\n                            navigate: _vm.handleNavigate,\n                            \"avatar-updated\": _vm.handleAvatarUpdated,\n                            \"info-updated\": _vm.handleInfoUpdated,\n                            \"api-key-updated\": _vm.handleApiKeyUpdated,\n                            \"password-changed\": _vm.handlePasswordChanged,\n                            \"refresh-user-info\": _vm.getUserInfo\n                          }\n                        })\n                      : _vm.currentPage === \"credits\"\n                      ? _c(\"Credits\", {\n                          key: \"credits\",\n                          on: { navigate: _vm.handleNavigate }\n                        })\n                      : _vm.currentPage === \"orders\"\n                      ? _c(\"Orders\", {\n                          key: \"orders\",\n                          on: { navigate: _vm.handleNavigate }\n                        })\n                      : _vm.currentPage === \"usage\"\n                      ? _c(\"Usage\", {\n                          key: \"usage\",\n                          on: { navigate: _vm.handleNavigate }\n                        })\n                      : _vm.currentPage === \"notifications\"\n                      ? _c(\"Notifications\", {\n                          key: \"notifications\",\n                          on: {\n                            navigate: _vm.handleNavigate,\n                            \"notification-updated\":\n                              _vm.handleNotificationUpdated\n                          }\n                        })\n                      : _vm._e()\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ])\n        ])\n      ]),\n      _c(\"FloatingNotifications\", {\n        ref: \"floatingNotifications\",\n        on: { \"navigate-to-notifications\": _vm.handleNavigateToNotifications }\n      })\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}