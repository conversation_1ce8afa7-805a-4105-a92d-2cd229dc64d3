{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753823636081}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport QuickRecharge from '@/components/QuickRecharge.vue'\nimport { createMembershipOrder, payOrder } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { getCurrentUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage,\n    QuickRecharge\n  },\n  data() {\n    return {\n      // 支付相关\n      paymentLoading: false,\n      showPaymentModal: false,\n      selectedPlan: null,\n      selectedPaymentMethod: 'alipay-qr',\n\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null,\n      checkingStatus: false,\n\n      plans: [\n        {\n          id: 1,\n          name: 'VIP月卡',\n          price: '29',\n          originalPrice: '39',\n          discountText: '限时7.4折',\n          saveAmount: '10',\n          period: '月',\n          description: '适合体验用户的基础功能',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 2,\n          name: 'VIP年卡',\n          price: '298',\n          originalPrice: '468',\n          discountText: '限时6.4折',\n          saveAmount: '170',\n          period: '年',\n          description: '适合长期使用的优惠套餐',\n          features: [\n            { text: '解锁VIP课程', disabled: false },\n            { text: '插件基础折扣', disabled: false },\n            { text: '邀请奖励35%基础比例', disabled: false },\n            { text: '调用工作流基础折扣', disabled: false },\n            { text: '复制所有工作流', disabled: true },\n            { text: '解锁流媒体转换', disabled: true },\n            { text: '部分插件免费', disabled: true }\n          ],\n          buttonText: '立即购买',\n          featured: false\n        },\n        {\n          id: 3,\n          name: 'SVIP年卡',\n          price: '489',\n          originalPrice: '999',\n          discountText: '限时4.9折',\n          saveAmount: '510',\n          period: '年',\n          description: '适合专业用户的全功能套餐',\n          features: [\n            { text: '解锁<strong class=\"highlight\">全部课程</strong>', disabled: false, exclusive: true },\n            { text: '插件<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '邀请<strong class=\"highlight\">奖励50%</strong>', disabled: false, exclusive: true },\n            { text: '调用工作流<strong class=\"highlight\">最高折扣</strong>', disabled: false, exclusive: true },\n            { text: '复制<strong class=\"highlight\">所有工作流</strong>', disabled: false, exclusive: true },\n            { text: '<strong class=\"highlight\">解锁流媒体转换</strong>', disabled: false, exclusive: true },\n            { text: '部分<strong class=\"highlight\">插件免费</strong>', disabled: false, exclusive: true }\n          ],\n          buttonText: '立即购买',\n          featured: true\n        }\n      ]\n    }\n  },\n\n  beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer)\n    }\n  },\n\n  mounted() {\n    console.log('Membership页面已挂载')\n    this.checkPaymentSuccessFromUrl()\n  },\n\n  methods: {\n    // 获取按钮文本（续费/升级/购买）\n    getButtonText(plan) {\n      const userRole = this.getUserRole() // 获取用户当前角色\n      const planRole = this.getPlanRole(plan.id) // 获取套餐对应角色\n\n      // 管理员显示\"立即购买\"（管理员不需要购买会员，但可以测试）\n      if (userRole === 'admin') {\n        return '立即购买'\n      }\n\n      // 相同等级显示\"续费会员\"\n      if (userRole === planRole) {\n        return '续费会员'\n      }\n      // 普通用户购买任何会员都显示\"立即购买\"\n      else if (userRole === 'user') {\n        return '立即购买'\n      }\n      // 会员用户购买更高等级显示\"升级会员\"\n      else if (this.isUpgrade(userRole, planRole)) {\n        return '升级会员'\n      }\n      // 其他情况显示\"立即购买\"\n      else {\n        return '立即购买'\n      }\n    },\n\n    // 获取套餐对应的角色\n    getPlanRole(planId) {\n      const roleMap = {\n        1: 'VIP',     // VIP月卡\n        2: 'VIP',     // VIP年卡\n        3: 'SVIP'     // SVIP年卡\n      }\n      return roleMap[planId] || 'user'\n    },\n\n    // 获取会员等级（用于后端处理）\n    getMembershipLevel(planId) {\n      const levelMap = {\n        1: 2,  // VIP月卡 → membershipLevel = 2 (VIP)\n        2: 2,  // VIP年卡 → membershipLevel = 2 (VIP)\n        3: 3   // SVIP年卡 → membershipLevel = 3 (SVIP)\n      }\n      return levelMap[planId] || 2 // 默认VIP等级\n    },\n\n    // 判断是否为升级\n    isUpgrade(currentRole, targetRole) {\n      const roleLevel = {\n        'user': 1,\n        'VIP': 2,\n        'SVIP': 3,\n        'admin': 999  // 管理员级别最高\n      }\n      return roleLevel[targetRole] > (roleLevel[currentRole] || 0)\n    },\n\n    // 是否显示升级提示\n    showUpgradeNotice(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      // 只有VIP升级到SVIP时显示特殊提示\n      return userRole === 'VIP' && planRole === 'SVIP'\n    },\n\n    // 获取升级提示文本\n    getUpgradeNoticeText(plan) {\n      const userRole = this.getUserRole()\n      const planRole = this.getPlanRole(plan.id)\n\n      if (userRole === 'VIP' && planRole === 'SVIP') {\n        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~'\n      }\n\n      return ''\n    },\n\n    // 获取用户当前角色（统一角色获取逻辑）\n    getUserRole() {\n      try {\n        // 使用roleUtils获取角色\n        let role = getCurrentUserRole()\n\n        console.log('🔍 Membership: getUserRole - 从roleUtils获取角色:', role)\n\n        // 角色映射：将中文角色名转换为英文角色代码\n        const roleMap = {\n          // 中文角色名映射\n          '普通用户': 'user',\n          'VIP会员': 'VIP',\n          'SVIP会员': 'SVIP',\n          '管理员': 'admin',\n          // 英文角色代码（保持不变）\n          'user': 'user',\n          'VIP': 'VIP',\n          'SVIP': 'SVIP',\n          'admin': 'admin',\n          // 大小写兼容\n          'USER': 'user',\n          'vip': 'VIP',\n          'svip': 'SVIP',\n          'ADMIN': 'admin'\n        }\n\n        // 映射角色\n        const mappedRole = roleMap[role] || 'user'\n\n        console.log('🔍 Membership: getUserRole - 映射后的角色:', mappedRole)\n        return mappedRole\n\n      } catch (error) {\n        console.warn('🔍 Membership: 获取用户角色失败:', error)\n        return 'user'  // 出错时默认为普通用户\n      }\n    },\n\n    async handleSubscribe(plan) {\n      console.log('选择购买套餐:', plan)\n\n      // 检查用户登录状态\n      const token = this.$ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$message.warning('请先登录后再购买会员')\n        this.$router.push('/user/login')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        // 准备会员订单数据（与充值订单结构保持一致）\n        const orderData = {\n          membershipLevel: this.getMembershipLevel(plan.id),\n          duration: plan.name.includes('月') ? 1 : 12, // 根据套餐名称判断时长\n          amount: parseFloat(plan.price),\n          planName: plan.name,\n          paymentMethod: 'alipay' // 默认支付宝，与充值保持一致\n        }\n\n        console.log('🎯 创建会员订单:', orderData)\n        this.$message.loading('正在创建订单...', 0)\n\n        // 创建会员订单（使用与充值相同的交易记录系统）\n        const response = await createMembershipOrder(orderData)\n        this.$message.destroy()\n\n        if (response.success) {\n          const orderResult = response.result\n          console.log('🎯 会员订单创建成功:', orderResult)\n\n          // 显示支付选择弹窗\n          this.selectedPlan = plan\n          this.currentOrderId = orderResult.orderId\n          this.currentOrderAmount = orderResult.amount\n          this.showPaymentModal = true\n\n        } else {\n          this.$message.error(response.message || '创建订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('创建会员订单失败:', error)\n        this.$message.error('创建订单失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付\n    async handlePayment() {\n      if (!this.currentOrderId) {\n        this.$message.error('订单信息错误')\n        return\n      }\n\n      try {\n        this.paymentLoading = true\n\n        if (this.selectedPaymentMethod === 'alipay-page') {\n          await this.handleAlipayPagePayment()\n        } else if (this.selectedPaymentMethod === 'alipay-qr') {\n          await this.handleAlipayQrPayment()\n        }\n\n        this.showPaymentModal = false\n      } catch (error) {\n        console.error('支付处理失败:', error)\n        this.$message.error('支付失败，请重试')\n      } finally {\n        this.paymentLoading = false\n      }\n    },\n\n    // 处理支付宝网页支付（与充值功能完全一致）\n    async handleAlipayPagePayment() {\n      try {\n        console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在跳转到支付宝支付...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)\n        console.log('🔍 支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const payForm = payResponse.result.payForm\n          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')\n\n          if (!payForm) {\n            this.$message.error('支付表单为空')\n            return\n          }\n\n          // 创建表单并提交到支付宝\n          const div = document.createElement('div')\n          div.innerHTML = payForm\n          document.body.appendChild(div)\n\n          const form = div.querySelector('form')\n          if (form) {\n            console.log('🔍 找到支付表单，准备提交')\n            form.submit()\n          } else {\n            console.error('🔍 未找到支付表单')\n            this.$message.error('支付表单创建失败')\n          }\n\n          // 清理DOM\n          setTimeout(() => {\n            if (document.body.contains(div)) {\n              document.body.removeChild(div)\n            }\n          }, 1000)\n        } else {\n          console.error('🔍 支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝网页支付失败:', error)\n        this.$message.error('支付宝支付失败，请重试')\n      }\n    },\n\n    // 处理支付宝扫码支付（与充值功能完全一致）\n    async handleAlipayQrPayment() {\n      try {\n        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId)\n        this.$message.loading('正在生成支付二维码...', 0)\n\n        const paymentData = {\n          orderId: this.currentOrderId,\n          amount: this.currentOrderAmount,\n          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,\n          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`\n        }\n\n        console.log('🔍 发送扫码支付请求数据:', paymentData)\n        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)\n        console.log('🔍 扫码支付响应:', payResponse)\n        this.$message.destroy()\n\n        if (payResponse.success) {\n          const qrCode = payResponse.result.qrCode\n          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')\n\n          if (!qrCode) {\n            this.$message.error('支付二维码生成失败')\n            return\n          }\n\n          // 显示二维码支付弹窗\n          this.showQrCodeModal(qrCode)\n        } else {\n          console.error('🔍 扫码支付请求失败:', payResponse.message)\n          this.$message.error(payResponse.message || '创建扫码支付订单失败')\n        }\n      } catch (error) {\n        this.$message.destroy()\n        console.error('支付宝扫码支付失败:', error)\n        this.$message.error('支付宝扫码支付失败，请重试')\n      }\n    },\n\n    // 显示二维码弹窗\n    showQrCodeModal(qrCode) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`\n      this.showQrModal = true\n\n      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount)\n      console.log('🔍 二维码URL:', this.qrCodeUrl)\n\n      // 开始轮询支付状态\n      this.startPaymentStatusCheck()\n    },\n\n    // 关闭二维码弹窗\n    closeQrModal() {\n      this.showQrModal = false\n      this.qrCodeUrl = ''\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n        this.paymentCheckTimer = null\n      }\n    },\n\n    // 开始支付状态检查\n    startPaymentStatusCheck() {\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer)\n      }\n\n      this.paymentCheckTimer = setInterval(() => {\n        this.checkPaymentStatus()\n      }, 3000) // 每3秒检查一次\n    },\n\n    // 检查支付状态（与充值功能完全一致）\n    async checkPaymentStatus() {\n      if (!this.currentOrderId) return\n\n      try {\n        this.checkingStatus = true\n        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)\n        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)\n        console.log('🔍 支付状态查询响应:', response)\n\n        if (response.success && response.result.status === 'TRADE_SUCCESS') {\n          console.log('🔍 会员购买支付成功！')\n          this.$message.success('支付成功！会员权益已生效')\n          this.closeQrModal()\n\n          // 留在当前membership页面，刷新用户状态\n          setTimeout(() => {\n            this.$message.success('会员购买成功！页面即将刷新以更新会员状态')\n            // 刷新当前页面以更新会员状态和按钮显示\n            window.location.reload()\n          }, 2000)\n        } else {\n          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error)\n      } finally {\n        this.checkingStatus = false\n      }\n    },\n\n    // 处理充值成功事件\n    handleRechargeSuccess() {\n      this.$message.success('充值成功！您可以继续选择套餐')\n      // 可以在这里添加其他逻辑，比如刷新用户信息等\n    },\n\n    // 检查URL中的支付成功参数\n    checkPaymentSuccessFromUrl() {\n      const urlParams = new URLSearchParams(window.location.search)\n      const paymentSuccess = urlParams.get('paymentSuccess')\n      const orderId = urlParams.get('orderId')\n\n      if (paymentSuccess === 'true' && orderId) {\n        console.log('🎉 检测到支付成功回调 - 订单号:', orderId)\n\n        if (orderId.startsWith('RECHARGE_')) {\n          // 充值成功\n          this.$message.success('充值成功！您可以继续选择套餐')\n        } else if (orderId.startsWith('MEMBERSHIP_')) {\n          // 会员购买成功\n          this.$message.success('会员购买成功！权益已生效')\n          // 延迟刷新页面以更新会员状态\n          setTimeout(() => {\n            window.location.reload()\n          }, 2000)\n        }\n\n        // 清除URL参数，避免重复提示\n        const newUrl = window.location.pathname\n        window.history.replaceState({}, document.title, newUrl)\n      }\n    }\n  }\n}\n", null]}