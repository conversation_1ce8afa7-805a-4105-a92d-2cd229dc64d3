{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=template&id=45f1e304&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753825633368}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"quick-recharge-card\">\n  <!-- 余额显示区域 -->\n  <div class=\"balance-section\">\n    <div class=\"user-info\">\n      <div class=\"user-avatar\">\n        <a-avatar\n          :size=\"80\"\n          :src=\"avatarUrl\"\n          icon=\"user\"\n          :style=\"{ backgroundColor: '#87d068' }\"\n        >\n          {{ userNickname ? userNickname.charAt(0) : 'U' }}\n        </a-avatar>\n      </div>\n      <div class=\"user-details\">\n        <div class=\"user-name\">\n          {{ userNickname || '智界用户' }}\n          <span class=\"user-role\">{{ getUserRoleDisplayName() }}</span>\n          <span v-if=\"getMemberExpireInfo()\" class=\"expire-info\">{{ getMemberExpireInfo() }}</span>\n        </div>\n        <div class=\"balance-info\">\n          <span class=\"balance-label\">账户余额</span>\n          <span class=\"balance-amount\">¥{{ formatBalance(userBalance) }}</span>\n        </div>\n      </div>\n    </div>\n    <div class=\"balance-actions\">\n      <button class=\"recharge-btn\" @click=\"showRechargeModal = true\">\n        <i class=\"anticon anticon-plus-circle\"></i>\n        快速充值\n      </button>\n    </div>\n  </div>\n  \n  <!-- 充值弹窗 -->\n  <a-modal\n    title=\"账户充值\"\n    :visible=\"showRechargeModal\"\n    @cancel=\"showRechargeModal = false\"\n    :footer=\"null\"\n    width=\"500px\"\n  >\n    <div class=\"recharge-modal-content\">\n      <!-- 充值选项 -->\n      <div class=\"recharge-options\">\n        <h4>选择充值金额</h4>\n        <div class=\"options-grid\">\n          <div \n            v-for=\"option in rechargeOptions\" \n            :key=\"option.amount\"\n            class=\"recharge-option\"\n            :class=\"{ selected: selectedAmount === option.amount }\"\n            @click=\"selectRechargeAmount(option.amount)\"\n          >\n            <div class=\"option-amount\">¥{{ option.amount }}</div>\n            <div class=\"option-label\">{{ option.label }}</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 自定义金额 -->\n      <div class=\"custom-amount\">\n        <h4>自定义金额</h4>\n        <div class=\"custom-input\">\n          <a-input-number\n            v-model=\"customAmount\"\n            :min=\"0.01\"\n            :max=\"10000\"\n            :step=\"0.01\"\n            placeholder=\"最低0.01元\"\n            size=\"large\"\n            style=\"width: 200px\"\n          />\n          <span class=\"currency\">元</span>\n        </div>\n      </div>\n      \n      <!-- 支付方式 -->\n      <div class=\"payment-methods\">\n        <h4>支付方式</h4>\n        <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n          <a-radio-button value=\"alipay-qr\">\n            <i class=\"anticon anticon-qrcode\"></i>\n            支付宝扫码\n          </a-radio-button>\n          <a-radio-button value=\"alipay-page\">\n            <i class=\"anticon anticon-alipay\"></i>\n            支付宝网页\n          </a-radio-button>\n        </a-radio-group>\n      </div>\n      \n      <!-- 充值按钮 -->\n      <div class=\"recharge-actions\">\n        <div class=\"amount-summary\">\n          <span>充值金额：</span>\n          <span class=\"final-amount\">¥{{ finalRechargeAmount }}</span>\n        </div>\n        <a-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"rechargeLoading\"\n          @click=\"handleRecharge\"\n          :disabled=\"!finalRechargeAmount || finalRechargeAmount < 0.01\"\n        >\n          确认充值\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n  \n  <!-- 支付二维码弹窗 -->\n  <a-modal\n    title=\"扫码支付\"\n    :visible=\"showQrModal\"\n    @cancel=\"closeQrModal\"\n    :footer=\"null\"\n    width=\"400px\"\n  >\n    <div class=\"qr-payment-content\">\n      <div class=\"qr-code-container\">\n        <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n          <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n        </div>\n        <div v-else class=\"qr-loading\">\n          <a-spin size=\"large\" />\n          <p>正在生成二维码...</p>\n        </div>\n      </div>\n      <div class=\"qr-info\">\n        <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n        <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n      </div>\n      <div class=\"qr-status\">\n        <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n          检查支付状态\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n</div>\n", null]}