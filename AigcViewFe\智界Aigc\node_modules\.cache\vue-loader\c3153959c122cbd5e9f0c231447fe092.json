{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=template&id=381de7b9&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753832743911}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"quick-recharge-card\"},[_c('div',{staticClass:\"balance-section\"},[_c('div',{staticClass:\"user-info\"},[_c('div',{staticClass:\"user-avatar\"},[_c('a-avatar',{style:({ backgroundColor: '#87d068' }),attrs:{\"size\":80,\"src\":_vm.avatarUrl,\"icon\":\"user\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.userNickname ? _vm.userNickname.charAt(0) : 'U')+\"\\n        \")])],1),_c('div',{staticClass:\"user-details\"},[_c('div',{staticClass:\"user-name\"},[_vm._v(\"\\n          \"+_vm._s(_vm.userNickname || '智界用户')+\"\\n          \"),_c('span',{staticClass:\"user-role\"},[_vm._v(_vm._s(_vm.getUserRoleDisplayName()))]),(_vm.getMemberExpireInfo())?_c('span',{staticClass:\"expire-info\"},[_vm._v(_vm._s(_vm.getMemberExpireInfo()))]):_vm._e()]),_c('div',{staticClass:\"balance-info\"},[_c('span',{staticClass:\"balance-label\"},[_vm._v(\"账户余额\")]),_c('span',{staticClass:\"balance-amount\"},[_vm._v(\"¥\"+_vm._s(_vm.formatBalance(_vm.userBalance)))])])])]),_c('div',{staticClass:\"balance-actions\"},[_c('button',{staticClass:\"recharge-btn\",on:{\"click\":function($event){_vm.showRechargeModal = true}}},[_c('i',{staticClass:\"anticon anticon-plus-circle\"}),_vm._v(\"\\n        快速充值\\n      \")])])]),_c('a-modal',{attrs:{\"title\":\"账户充值\",\"visible\":_vm.showRechargeModal,\"footer\":null,\"width\":\"500px\"},on:{\"cancel\":function($event){_vm.showRechargeModal = false}}},[_c('div',{staticClass:\"recharge-modal-content\"},[_c('div',{staticClass:\"recharge-options\"},[_c('h4',[_vm._v(\"选择充值金额\")]),_c('div',{staticClass:\"options-grid\"},_vm._l((_vm.rechargeOptions),function(option){return _c('div',{key:option.amount,staticClass:\"recharge-option\",class:{ selected: _vm.selectedAmount === option.amount },on:{\"click\":function($event){return _vm.selectRechargeAmount(option.amount)}}},[_c('div',{staticClass:\"option-amount\"},[_vm._v(\"¥\"+_vm._s(option.amount))]),_c('div',{staticClass:\"option-label\"},[_vm._v(_vm._s(option.label))])])}),0)]),_c('div',{staticClass:\"custom-amount\"},[_c('h4',[_vm._v(\"自定义金额\")]),_c('div',{staticClass:\"custom-input\"},[_c('a-input-number',{staticStyle:{\"width\":\"200px\"},attrs:{\"min\":0.01,\"max\":10000,\"step\":0.01,\"placeholder\":\"最低0.01元\",\"size\":\"large\"},model:{value:(_vm.customAmount),callback:function ($$v) {_vm.customAmount=$$v},expression:\"customAmount\"}}),_c('span',{staticClass:\"currency\"},[_vm._v(\"元\")])],1)]),_c('div',{staticClass:\"payment-methods\"},[_c('h4',[_vm._v(\"支付方式\")]),_c('a-radio-group',{attrs:{\"size\":\"large\"},model:{value:(_vm.selectedPaymentMethod),callback:function ($$v) {_vm.selectedPaymentMethod=$$v},expression:\"selectedPaymentMethod\"}},[_c('a-radio-button',{attrs:{\"value\":\"alipay-qr\"}},[_c('i',{staticClass:\"anticon anticon-qrcode\"}),_vm._v(\"\\n            支付宝扫码\\n          \")]),_c('a-radio-button',{attrs:{\"value\":\"alipay-page\"}},[_c('i',{staticClass:\"anticon anticon-alipay\"}),_vm._v(\"\\n            支付宝网页\\n          \")])],1)],1),_c('div',{staticClass:\"recharge-actions\"},[_c('div',{staticClass:\"amount-summary\"},[_c('span',[_vm._v(\"充值金额：\")]),_c('span',{staticClass:\"final-amount\"},[_vm._v(\"¥\"+_vm._s(_vm.finalRechargeAmount))])]),_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"loading\":_vm.rechargeLoading,\"disabled\":!_vm.finalRechargeAmount || _vm.finalRechargeAmount < 0.01},on:{\"click\":_vm.handleRecharge}},[_vm._v(\"\\n          确认充值\\n        \")])],1)])]),_c('a-modal',{attrs:{\"title\":\"扫码支付\",\"visible\":_vm.showQrModal,\"footer\":null,\"width\":\"400px\"},on:{\"cancel\":_vm.closeQrModal}},[_c('div',{staticClass:\"qr-payment-content\"},[_c('div',{staticClass:\"qr-code-container\"},[(_vm.qrCodeUrl)?_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.qrCodeUrl,\"alt\":\"支付二维码\"}})]):_c('div',{staticClass:\"qr-loading\"},[_c('a-spin',{attrs:{\"size\":\"large\"}}),_c('p',[_vm._v(\"正在生成二维码...\")])],1)]),_c('div',{staticClass:\"qr-info\"},[_c('p',{staticClass:\"qr-amount\"},[_vm._v(\"支付金额：¥\"+_vm._s(_vm.currentOrderAmount))]),_c('p',{staticClass:\"qr-tip\"},[_vm._v(\"请使用支付宝扫码支付\")])]),_c('div',{staticClass:\"qr-status\"},[_c('a-button',{attrs:{\"loading\":_vm.checkingStatus},on:{\"click\":_vm.checkPaymentStatus}},[_vm._v(\"\\n          检查支付状态\\n        \")])],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}