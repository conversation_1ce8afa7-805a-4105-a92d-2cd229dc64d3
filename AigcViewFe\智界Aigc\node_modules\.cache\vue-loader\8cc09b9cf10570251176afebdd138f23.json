{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=template&id=17b9e6ca&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753823636081}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"membership-container\">\n    <!-- 简洁页面标题 -->\n    <div class=\"simple-header\">\n      <h1 class=\"simple-title\">订阅会员</h1>\n      <p class=\"simple-subtitle\">成为会员享受更多特权，解锁高级功能</p>\n    </div>\n\n    <!-- 快速充值模块 -->\n    <section class=\"recharge-section\">\n      <div class=\"container\">\n        <QuickRecharge @recharge-success=\"handleRechargeSuccess\" />\n      </div>\n    </section>\n\n    <!-- 会员套餐区域 -->\n    <section class=\"plans-section\">\n      <div class=\"container\">\n        <div class=\"plans-grid\">\n          <div \n            v-for=\"plan in plans\" \n            :key=\"plan.id\"\n            class=\"plan-card\"\n            :class=\"{ 'featured': plan.featured }\"\n          >\n            <div v-if=\"plan.featured\" class=\"plan-badge\">推荐</div>\n            <div class=\"plan-header\">\n              <h3 class=\"plan-name\">{{ plan.name }}</h3>\n              <div class=\"plan-price\">\n                <!-- 原价显示 -->\n                <div v-if=\"plan.originalPrice\" class=\"original-price\">\n                  <span class=\"original-price-text\">原价：¥{{ plan.originalPrice }}</span>\n                  <span class=\"discount-badge\">{{ plan.discountText }}</span>\n                </div>\n                <!-- 现价显示 -->\n                <div class=\"current-price\">\n                  <span class=\"price-symbol\">¥</span>\n                  <span class=\"price-amount\">{{ plan.price }}</span>\n                  <span class=\"price-period\">/{{ plan.period }}</span>\n                </div>\n                <!-- 立省金额 -->\n                <div v-if=\"plan.saveAmount\" class=\"save-amount\">\n                  立省¥{{ plan.saveAmount }}\n                </div>\n              </div>\n              <p class=\"plan-description\">{{ plan.description }}</p>\n            </div>\n            \n            <div class=\"plan-features\">\n              <div\n                v-for=\"feature in plan.features\"\n                :key=\"feature.text || feature\"\n                class=\"feature-item\"\n                :class=\"{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }\"\n              >\n                <a-icon\n                  :type=\"feature.disabled ? 'close' : 'check'\"\n                  :class=\"{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }\"\n                />\n                <span class=\"feature-text\" v-html=\"feature.text || feature\"></span>\n                <span v-if=\"feature.exclusive\" class=\"exclusive-badge\">专属</span>\n                <span v-if=\"feature.disabled\" class=\"disabled-text\">（SVIP专享）</span>\n              </div>\n            </div>\n            \n            <button\n              class=\"btn-subscribe\"\n              :class=\"{ 'featured': plan.featured }\"\n              @click=\"handleSubscribe(plan)\"\n            >\n              {{ getButtonText(plan) }}\n            </button>\n\n            <!-- VIP升级SVIP的特殊提示 -->\n            <div v-if=\"showUpgradeNotice(plan)\" class=\"upgrade-notice\">\n              <i class=\"icon-info\"></i>\n              <span>{{ getUpgradeNoticeText(plan) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n\n  <!-- 支付方式选择弹窗 -->\n  <a-modal\n    title=\"选择支付方式\"\n    :visible=\"showPaymentModal\"\n    @cancel=\"showPaymentModal = false\"\n    :footer=\"null\"\n    width=\"500px\"\n  >\n    <div class=\"payment-modal-content\">\n      <div class=\"order-info\">\n        <h4>订单信息</h4>\n        <div class=\"order-details\">\n          <div class=\"order-item\">\n            <span>套餐名称：</span>\n            <span>{{ selectedPlan ? selectedPlan.name : '' }}</span>\n          </div>\n          <div class=\"order-item\">\n            <span>支付金额：</span>\n            <span class=\"amount\">¥{{ currentOrderAmount }}</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"payment-methods\">\n        <h4>支付方式</h4>\n        <a-radio-group v-model=\"selectedPaymentMethod\" size=\"large\">\n          <a-radio-button value=\"alipay-qr\">\n            <i class=\"anticon anticon-qrcode\"></i>\n            支付宝扫码\n          </a-radio-button>\n          <a-radio-button value=\"alipay-page\">\n            <i class=\"anticon anticon-alipay\"></i>\n            支付宝网页\n          </a-radio-button>\n        </a-radio-group>\n      </div>\n\n      <div class=\"payment-actions\">\n        <a-button\n          type=\"primary\"\n          size=\"large\"\n          :loading=\"paymentLoading\"\n          @click=\"handlePayment\"\n          block\n        >\n          确认支付\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 支付二维码弹窗 -->\n  <a-modal\n    title=\"扫码支付\"\n    :visible=\"showQrModal\"\n    @cancel=\"closeQrModal\"\n    :footer=\"null\"\n    width=\"400px\"\n  >\n    <div class=\"qr-payment-content\">\n      <div class=\"qr-code-container\">\n        <div v-if=\"qrCodeUrl\" class=\"qr-code\">\n          <img :src=\"qrCodeUrl\" alt=\"支付二维码\" />\n        </div>\n        <div v-else class=\"qr-loading\">\n          <a-spin size=\"large\" />\n          <p>正在生成二维码...</p>\n        </div>\n      </div>\n      <div class=\"qr-info\">\n        <p class=\"qr-amount\">支付金额：¥{{ currentOrderAmount }}</p>\n        <p class=\"qr-tip\">请使用支付宝扫码支付</p>\n      </div>\n      <div class=\"qr-status\">\n        <a-button @click=\"checkPaymentStatus\" :loading=\"checkingStatus\">\n          检查支付状态\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n</WebsitePage>\n", null]}