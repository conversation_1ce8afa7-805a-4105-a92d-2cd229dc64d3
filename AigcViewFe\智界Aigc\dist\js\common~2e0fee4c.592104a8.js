(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~2e0fee4c"],{"0aa0":function(t,e,a){},1189:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stats-card",class:{loading:t.loading}},[a("div",{staticClass:"card-icon",style:{background:t.iconBackground}},[a("i",{class:t.icon})]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"stats-value"},[t.loading?a("span",{staticClass:"skeleton-loading"}):a("span",{ref:"valueNumber",staticClass:"value-number"},[t._v(t._s(t.displayValue))]),t.unit&&!t.loading?a("span",{staticClass:"value-unit"},[t._v(t._s(t.unit))]):t._e()]),a("div",{staticClass:"stats-label"},[t._v(t._s(t.label))]),t.trend&&!t.loading?a("div",{staticClass:"stats-trend",class:t.trendClass},[a("i",{class:t.trendIcon}),a("span",[t._v(t._s(t.trend.text))])]):t._e(),t.description?a("div",{staticClass:"stats-description"},[t._v("\n      "+t._s(t.description)+"\n    ")]):t._e()]),t.showProgress&&!t.loading?a("div",{staticClass:"progress-bar"},[a("div",{ref:"progressBar",staticClass:"progress-fill",style:{width:t.progressPercentage+"%"}})]):t._e()])},n=[],s=a("302c"),r={name:"StatsCard",props:{value:{type:[Number,String],default:0},unit:{type:String,default:""},label:{type:String,required:!0},description:{type:String,default:""},icon:{type:String,required:!0},iconColor:{type:String,default:"#7c8aed"},trend:{type:Object,default:null},showProgress:{type:Boolean,default:!1},progressValue:{type:Number,default:0},progressMax:{type:Number,default:100},loading:{type:Boolean,default:!1},animateValue:{type:Boolean,default:!1}},data:function(){return{animatedValue:0}},computed:{displayValue:function(){if(this.loading)return"0";var t=this.animateValue?this.animatedValue:this.value;if(null===t||void 0===t||""===t)return"0";if("number"===typeof t)return"元"===this.unit?parseFloat(t).toFixed(2):t>=1e4?(t/1e4).toFixed(1)+"万":t>=1e3?(t/1e3).toFixed(1)+"k":t.toLocaleString();if("string"===typeof t&&!isNaN(parseFloat(t))){var e=parseFloat(t);return"元"===this.unit?e.toFixed(2):e.toLocaleString()}return t||"0"},iconBackground:function(){return"linear-gradient(135deg, ".concat(this.iconColor,"20 0%, ").concat(this.iconColor,"10 100%)")},trendClass:function(){return this.trend?{"trend-up":"up"===this.trend.type,"trend-down":"down"===this.trend.type,"trend-stable":"stable"===this.trend.type}:""},trendIcon:function(){if(!this.trend)return"";var t={up:"anticon anticon-arrow-up",down:"anticon anticon-arrow-down",stable:"anticon anticon-minus"};return t[this.trend.type]||""},progressPercentage:function(){if(!this.showProgress)return 0;var t=this.progressValue||0,e=this.progressMax||100;return 0===e?0:Math.min(t/e*100,100)}},mounted:function(){var t=this;this.animateValue&&"number"===typeof this.value&&this.animateCounter(),this.showProgress&&this.$nextTick((function(){t.animateProgress()}))},watch:{value:function(t){this.animateValue&&"number"===typeof t&&this.animateCounter()},progressValue:function(){var t=this;this.showProgress&&this.$nextTick((function(){t.animateProgress()}))}},methods:{animateCounter:function(){var t=this;this.$refs.valueNumber&&s["a"].animateCounter(this.$refs.valueNumber,this.value,{duration:1.5,onUpdate:function(e){t.animatedValue=Math.round(e||t.value)}})},animateProgress:function(){this.$refs.progressBar&&s["a"].animateProgressBar(this.$refs.progressBar,this.progressPercentage)}}},o=r,c=(a("63ea"),a("2877")),l=Object(c["a"])(o,i,n,!1,null,"cb11b3ac",null);e["default"]=l.exports},"15f34":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"notifications-page"},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"header-content"},[t._m(0),a("div",{staticClass:"header-actions"},[a("a-button",{attrs:{type:"primary",loading:t.markingAllRead,disabled:!t.hasUnreadNotifications},on:{click:t.markAllAsRead}},[a("i",{staticClass:"anticon anticon-check"}),t._v("\n          全部标记已读\n        ")]),a("a-button",{attrs:{loading:t.loading},on:{click:t.refreshNotifications}},[a("i",{staticClass:"anticon anticon-reload"}),t._v("\n          刷新\n        ")])],1)])]),a("div",{staticClass:"stats-cards"},[a("div",{staticClass:"stat-card clickable",on:{click:function(e){return t.handleStatCardClick("all")}}},[t._m(1),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.globalStats.total))]),a("div",{staticClass:"stat-label"},[t._v("总通知")])])]),a("div",{staticClass:"stat-card clickable",on:{click:function(e){return t.handleStatCardClick("unread")}}},[t._m(2),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.unreadCount))]),a("div",{staticClass:"stat-label"},[t._v("未读通知")])])]),a("div",{staticClass:"stat-card clickable",on:{click:function(e){return t.handleStatCardClick("read")}}},[t._m(3),a("div",{staticClass:"stat-content"},[a("div",{staticClass:"stat-number"},[t._v(t._s(t.readCount))]),a("div",{staticClass:"stat-label"},[t._v("已读通知")])])])]),a("div",{staticClass:"filter-section"},[a("div",{staticClass:"filter-left"},[a("a-radio-group",{on:{change:t.handleFilterChange},model:{value:t.filterStatus,callback:function(e){t.filterStatus=e},expression:"filterStatus"}},[a("a-radio-button",{attrs:{value:"all"}},[t._v("全部")]),a("a-radio-button",{attrs:{value:"unread"}},[t._v("未读")]),a("a-radio-button",{attrs:{value:"read"}},[t._v("已读")])],1)],1),a("div",{staticClass:"filter-right"},[a("a-input-search",{staticStyle:{width:"300px"},attrs:{placeholder:"搜索通知内容..."},on:{search:t.handleSearch,change:t.handleSearchChange},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}})],1)]),a("div",{staticClass:"notifications-list"},[a("a-spin",{attrs:{spinning:t.loading}},[t.filteredNotifications.length>0?a("div",{staticClass:"notification-items"},t._l(t.filteredNotifications,(function(e){return a("div",{key:e.id,staticClass:"notification-item",class:{unread:!e.readFlag,read:e.readFlag},on:{click:function(a){return t.handleNotificationClick(e)}}},[a("div",{staticClass:"notification-indicator"},[e.readFlag?t._e():a("div",{staticClass:"unread-dot"})]),a("div",{staticClass:"notification-content"},[a("div",{staticClass:"notification-header"},[a("h3",{staticClass:"notification-title"},[t._v(t._s(e.titile||"系统通知"))]),a("div",{staticClass:"notification-meta"},[a("span",{staticClass:"notification-time"},[t._v(t._s(t.formatTime(e.sendTime)))]),a("span",{staticClass:"notification-type"},[t._v(t._s(t.getNotificationType(e.msgCategory)))])])]),a("div",{staticClass:"notification-body"},[a("p",{staticClass:"notification-message"},[t._v(t._s(t.getNotificationSummary(e.msgContent)))]),t.isContentTruncated(e.msgContent)?a("span",{staticClass:"read-more-hint"},[t._v("点击查看详情")]):t._e(),a("div",{staticClass:"notification-code"},[t._v("发送者: "+t._s(e.sender||"系统"))])])]),a("div",{staticClass:"notification-actions"},[e.readFlag?a("a-button",{staticClass:"mark-unread-btn",attrs:{type:"default",size:"small",loading:e.marking,icon:"undo"},on:{click:function(a){return a.stopPropagation(),t.markAsUnread(e)}}},[t._v("\n              标记未读\n            ")]):a("a-button",{staticClass:"mark-read-btn",attrs:{type:"primary",size:"small",loading:e.marking,icon:"check"},on:{click:function(a){return a.stopPropagation(),t.markAsRead(e)}}},[t._v("\n              标记已读\n            ")])],1)])})),0):t.loading?t._e():a("div",{staticClass:"empty-state"},[a("div",{staticClass:"empty-icon"},[a("i",{staticClass:"anticon anticon-bell"})]),a("h3",[t._v(t._s(t.getEmptyStateTitle()))]),a("p",[t._v(t._s(t.getEmptyStateDescription()))])])])],1),t.totalCount>0?a("div",{staticClass:"pagination-wrapper"},[a("a-pagination",{attrs:{total:t.totalCount,"page-size":t.pageSize,"show-size-changer":!0,"show-quick-jumper":!0,"show-total":function(t,e){return"第 "+e[0]+"-"+e[1]+" 条，共 "+t+" 条"}},on:{change:t.handlePageChange,showSizeChange:t.handlePageSizeChange},model:{value:t.currentPage,callback:function(e){t.currentPage=e},expression:"currentPage"}})],1):t._e(),a("a-modal",{attrs:{title:"通知详情",width:"900px",footer:null,bodyStyle:{maxHeight:"70vh",overflowY:"auto",padding:"24px"}},on:{cancel:t.handleDetailModalClose},model:{value:t.detailModalVisible,callback:function(e){t.detailModalVisible=e},expression:"detailModalVisible"}},[t.selectedNotification?a("div",{staticClass:"notification-detail"},[a("div",{staticClass:"detail-header"},[a("h3",{staticClass:"detail-title"},[t._v(t._s(t.selectedNotification.titile||"系统通知"))]),a("div",{staticClass:"detail-meta"},[a("span",{staticClass:"detail-time"},[t._v(t._s(t.formatTime(t.selectedNotification.sendTime)))]),a("span",{staticClass:"detail-type"},[t._v(t._s(t.getNotificationType(t.selectedNotification.msgCategory)))])])]),a("div",{staticClass:"detail-content"},[a("div",{staticClass:"detail-message",domProps:{innerHTML:t._s(t.selectedNotification.msgContent||"暂无内容")}})]),a("div",{staticClass:"detail-footer"},[a("div",{staticClass:"detail-sender"},[t._v("发送者: "+t._s(t.selectedNotification.sender||"系统"))]),a("div",{staticClass:"detail-status"},[t.selectedNotification.readFlag?a("span",{staticClass:"status-read"},[t._v("已读")]):a("span",{staticClass:"status-unread"},[t._v("未读")])])])]):t._e()])],1)},n=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"header-left"},[a("h1",{staticClass:"page-title"},[a("i",{staticClass:"anticon anticon-bell"}),t._v("\n          系统通知\n        ")]),a("p",{staticClass:"page-description"},[t._v("查看系统消息和重要通知")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stat-icon total"},[a("i",{staticClass:"anticon anticon-bell"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stat-icon unread"},[a("i",{staticClass:"anticon anticon-exclamation-circle"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stat-icon read"},[a("i",{staticClass:"anticon anticon-check-circle"})])}],s=a("a34a"),r=a.n(s),o=a("ff1f"),c=a("9fb0"),l=a("2b0e");function u(t,e,a,i,n,s,r){try{var o=t[s](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,n)}function d(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var s=t.apply(e,a);function r(t){u(s,i,n,r,o,"next",t)}function o(t){u(s,i,n,r,o,"throw",t)}r(void 0)}))}}var f={name:"Notifications",data:function(){return{loading:!1,markingAllRead:!1,notifications:[],currentPage:1,pageSize:10,totalCount:0,filterStatus:"all",searchKeyword:"",searchTimeout:null,detailModalVisible:!1,selectedNotification:null,globalStats:{total:0,unreadCount:0,readCount:0}}},computed:{unreadCount:function(){return this.globalStats.unreadCount},readCount:function(){return this.globalStats.readCount},hasUnreadNotifications:function(){return this.unreadCount>0},filteredNotifications:function(){return this.notifications},currentUserId:function(){var t=l["default"].ls.get(c["u"]);return t?t.id:null}},mounted:function(){var t=d(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Promise.all([this.loadNotifications(),this.loadGlobalStats()]);case 2:this.$bus.$on("notification-updated",this.handleNotificationUpdated);case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){this.$bus.$off("notification-updated",this.handleNotificationUpdated)},methods:{loadNotifications:function(){var t=d(r.a.mark((function t(){var e,a,i,n=this;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,e={pageNo:this.currentPage,pageSize:this.pageSize},"unread"===this.filterStatus?e.readFlag="0":"read"===this.filterStatus&&(e.readFlag="1"),this.searchKeyword&&this.searchKeyword.trim()&&(e.titile=this.searchKeyword.trim()),t.next=8,Object(o["b"])(e);case 8:a=t.sent,a.success?(i=a.result||{},this.notifications=i.records||[],this.totalCount=i.total||0,this.notifications.forEach((function(t){n.$set(t,"marking",!1)}))):this.$message.error(a.message||"获取通知失败"),t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](0),this.$message.error("获取通知失败，请重试");case 16:return t.prev=16,this.loading=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,this,[[0,12,16,19]])})));function e(){return t.apply(this,arguments)}return e}(),loadGlobalStats:function(){var t=d(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,Object(o["a"])();case 4:e=t.sent,e.success&&e.result&&(this.globalStats={total:e.result.total||0,unreadCount:e.result.unreadCount||0,readCount:e.result.readCount||0}),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0);case 12:case"end":return t.stop()}}),t,this,[[0,9]])})));function e(){return t.apply(this,arguments)}return e}(),markAsRead:function(){var t=d(r.a.mark((function t(e){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.marking=!0,t.next=4,Object(o["f"])(e.anntId||e.id);case 4:a=t.sent,a.success?(e.readFlag=!0,this.$message.success("标记已读成功"),this.loadGlobalStats(),this.$emit("notification-updated")):this.$message.error(a.message||"标记失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$message.error("标记失败，请重试");case 12:return t.prev=12,e.marking=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(e){return t.apply(this,arguments)}return e}(),markAsUnread:function(){var t=d(r.a.mark((function t(e){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.marking=!0,t.next=4,Object(o["g"])(e.anntId||e.id);case 4:a=t.sent,a.success?(e.readFlag=!1,this.$message.success("标记未读成功"),this.loadGlobalStats(),this.$emit("notification-updated")):this.$message.error(a.message||"标记失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$message.error("标记失败，请重试");case 12:return t.prev=12,e.marking=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(e){return t.apply(this,arguments)}return e}(),markAllAsRead:function(){var t=d(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.markingAllRead=!0,t.next=4,Object(o["e"])();case 4:e=t.sent,e.success?(this.notifications.forEach((function(t){t.readFlag=!0})),this.$message.success("全部标记已读成功"),this.loadNotifications(),this.loadGlobalStats()):this.$message.error(e.message||"批量标记失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$message.error("批量标记失败，请重试");case 12:return t.prev=12,this.markingAllRead=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),handleNotificationClick:function(t){this.showNotificationDetail(t)},showNotificationDetail:function(t){var e=this;this.selectedNotification=t,this.detailModalVisible=!0,t.readFlag||this.markAsRead(t),this.$nextTick((function(){e.fixImageSizes()}))},fixImageSizes:function(){setTimeout((function(){var t=document.querySelector(".ant-modal-body .notification-detail");if(t){var e=t.closest(".ant-modal-body"),a=e?e.offsetWidth:900,i=Math.max(a-10,200),n=t.querySelectorAll("img");n.forEach((function(t){t.removeAttribute("width"),t.removeAttribute("height");var e={width:"auto !important","max-width":"".concat(i,"px !important"),height:"auto !important","max-height":"none !important",display:"block !important",margin:"1rem auto !important","object-fit":"contain !important","border-radius":"8px !important","box-shadow":"0 2px 8px rgba(0, 0, 0, 0.1) !important",visibility:"visible !important",opacity:"1 !important",position:"static !important","z-index":"auto !important",transform:"none !important",cursor:"default !important",transition:"all 0.3s ease !important"};Object.keys(e).forEach((function(a){t.style.setProperty(a,e[a].replace(" !important",""),"important")})),t.addEventListener("mouseenter",(function(){t.style.setProperty("transform","scale(1.02)","important"),t.style.setProperty("box-shadow","0 4px 16px rgba(0, 0, 0, 0.2)","important")})),t.addEventListener("mouseleave",(function(){t.style.setProperty("transform","scale(1)","important"),t.style.setProperty("box-shadow","0 2px 8px rgba(0, 0, 0, 0.1)","important")}))}))}}),100)},handleDetailModalClose:function(){this.detailModalVisible=!1,this.selectedNotification=null},handleFilterChange:function(){this.currentPage=1,this.loadNotifications()},handleStatCardClick:function(t){this.searchKeyword="",this.filterStatus=t,this.currentPage=1,this.loadNotifications()},handleSearch:function(){this.currentPage=1,this.loadNotifications()},handleSearchChange:function(){var t=this;this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout((function(){t.handleSearch()}),500)},handlePageChange:function(t){this.currentPage=t,this.loadNotifications()},handlePageSizeChange:function(t,e){this.pageSize=e,this.currentPage=1,this.loadNotifications()},refreshNotifications:function(){this.loadNotifications()},handleNotificationUpdated:function(t){t&&"markAsRead"===t.type&&this.loadNotifications()},getNotificationText:function(t){if(!t)return"暂无内容";var e=t.replace(/<[^>]*>/g,"");return e},getNotificationSummary:function(t){if(!t)return"暂无内容";var e=t.replace(/<[^>]*>/g,""),a=50;return e.length<=a?e:e.substring(0,a)+"..."},isContentTruncated:function(t){if(!t)return!1;var e=t.replace(/<[^>]*>/g,"");return e.length>50},getNotificationType:function(t){var e={1:"系统消息",2:"流程催办",3:"超时提醒",4:"系统催办"};return e[t]||"系统通知"},formatTime:function(t){if(!t)return"";var e=new Date(t),a=new Date,i=a-e,n=Math.floor(i/6e4),s=Math.floor(i/36e5),r=Math.floor(i/864e5);return n<1?"刚刚":n<60?"".concat(n,"分钟前"):s<24?"".concat(s,"小时前"):r<7?"".concat(r,"天前"):e.toLocaleString("zh-CN")},getEmptyStateTitle:function(){return"unread"===this.filterStatus?"暂无未读通知":"read"===this.filterStatus?"暂无已读通知":this.searchKeyword.trim()?"未找到相关通知":"暂无系统通知"},getEmptyStateDescription:function(){return"unread"===this.filterStatus?"所有通知都已阅读":"read"===this.filterStatus?"还没有已读的通知":this.searchKeyword.trim()?"尝试使用其他关键词搜索":"系统会在这里显示重要消息和通知"}}},p=f,h=(a("ecf7"),a("2877")),v=Object(h["a"])(p,i,n,!1,null,"a684106a",null);e["default"]=v.exports},"195b":function(t,e,a){"use strict";var i=a("2fe8"),n=a.n(i);n.a},"2fe8":function(t,e,a){},"3c24":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"user-info-card"},[a("div",{staticClass:"card-header"},[a("h3",{staticClass:"card-title"},[t._v("个人信息")]),a("button",{staticClass:"edit-btn",on:{click:t.handleEdit}},[a("i",{staticClass:"anticon anticon-edit"}),a("span",[t._v("编辑")])])]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"user-avatar-section"},[a("div",{staticClass:"avatar-container"},[a("img",{staticClass:"user-avatar",attrs:{src:t.avatarUrl,alt:t.userInfo&&t.userInfo.nickname||"用户头像"}})])]),a("div",{staticClass:"user-details"},[a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("昵称")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.userInfo&&t.userInfo.nickname||"未设置"))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("邮箱")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.userInfo&&t.userInfo.email||"未绑定"))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("手机号")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.userInfo&&t.userInfo.phone||"未绑定"))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("注册时间")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.formatDate(t.userInfo&&t.userInfo.createTime||null)))])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("会员等级")]),a("span",{staticClass:"detail-value"},[a("span",{staticClass:"member-badge",class:t.memberLevelClass},[t._v("\n            "+t._s(t.memberLevelText)+"\n          ")])])]),a("div",{staticClass:"detail-row"},[a("span",{staticClass:"detail-label"},[t._v("API Key")]),a("span",{staticClass:"detail-value"},[a("span",{staticClass:"api-key"},[t._v(t._s(t.maskedApiKey))]),a("div",{staticClass:"api-key-actions"},[a("button",{staticClass:"copy-btn",attrs:{disabled:!(t.userInfo&&t.userInfo.apiKey),title:"复制API Key"},on:{click:t.handleCopyApiKey}},[a("i",{staticClass:"anticon anticon-copy"}),a("span",[t._v("复制")])]),a("button",{staticClass:"regenerate-btn",attrs:{disabled:t.regenerating,title:"重置API Key"},on:{click:t.showResetConfirm}},[a("i",{staticClass:"anticon anticon-reload",class:{spinning:t.regenerating}}),a("span",[t._v(t._s(t.regenerating?"重置中":"重置"))])])])])])])]),a("ConfirmDialog",{attrs:{visible:t.showConfirmDialog,title:"确认重置API Key",type:"warning",loading:t.regenerating,"confirm-text":"确定重置","loading-text":"重置中..."},on:{"update:visible":function(e){t.showConfirmDialog=e},confirm:t.handleResetApiKey,cancel:t.hideResetConfirm}},[a("p",[a("strong",[t._v("API Key是您访问系统的唯一凭证，具有唯一性，请妥善保管。")])]),a("p",[t._v("重置后原有的API Key将立即失效，所有使用旧API Key的应用将无法正常工作。")]),a("p",{staticStyle:{color:"#f59e0b","font-weight":"600","margin-top":"1rem"}},[t._v("确定要重置吗？")])])],1)},n=[],s=a("a34a"),r=a.n(s),o=a("77ea"),c=a("0fea"),l=a("1741");function u(t,e,a,i,n,s,r){try{var o=t[s](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,n)}function d(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var s=t.apply(e,a);function r(t){u(s,i,n,r,o,"next",t)}function o(t){u(s,i,n,r,o,"throw",t)}r(void 0)}))}}var f={name:"UserInfoCard",components:{ConfirmDialog:l["default"]},props:{userInfo:{type:Object,default:function(){return{}}},loading:{type:Boolean,default:!1}},data:function(){return{defaultAvatar:"/default-avatar.png",regenerating:!1,showConfirmDialog:!1}},computed:{memberLevelClass:function(){var t=this.userInfo&&this.userInfo.currentRole||"普通用户";return{"level-user":"普通用户"===t,"level-vip":"VIP"===t,"level-svip":"SVIP"===t,"level-admin":"管理员"===t}},memberLevelText:function(){var t=this.userInfo&&this.userInfo.currentRole||"普通用户";return t},maskedApiKey:function(){var t=this.userInfo&&this.userInfo.apiKey;return t?t.length>8?"".concat(t.substring(0,4)).concat("*".repeat(t.length-8)).concat(t.substring(t.length-4)):t:"未生成"},avatarUrl:function(){var t=this.userInfo&&this.userInfo.avatar;return t?t.startsWith("http://")||t.startsWith("https://")?t:Object(c["d"])(t)||this.defaultAvatar:this.defaultAvatar}},created:function(){this.loadDefaultAvatar()},methods:{loadDefaultAvatar:function(){var t=d(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/sys/common/default-avatar-url");case 3:e=t.sent,e&&e.success&&e.result&&(this.defaultAvatar=e.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),handleEdit:function(){this.$emit("edit")},handleCopyApiKey:function(){var t=this,e=this.userInfo&&this.userInfo.apiKey;e?navigator.clipboard?navigator.clipboard.writeText(e).then((function(){t.$notification.success({message:"API Key已复制",description:"API Key已成功复制到剪贴板，可以粘贴使用了。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})})).catch((function(){t.fallbackCopyTextToClipboard(e)})):this.fallbackCopyTextToClipboard(e):this.$notification.warning({message:"API Key未生成",description:"请先生成API Key后再进行复制操作。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})},fallbackCopyTextToClipboard:function(t){var e=document.createElement("textarea");e.value=t,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),this.$notification.success({message:"API Key已复制",description:"API Key已成功复制到剪贴板，可以粘贴使用了。",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})}catch(a){this.$notification.error({message:"复制失败",description:"无法自动复制到剪贴板，请手动选择并复制API Key。",placement:"topRight",duration:4,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})}document.body.removeChild(e)},showResetConfirm:function(){this.showConfirmDialog=!0},hideResetConfirm:function(){this.showConfirmDialog=!1},handleResetApiKey:function(){var t=d(r.a.mark((function t(){var e,a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.regenerating=!0,t.next=4,Object(o["B"])();case 4:e=t.sent,e.success?(a=e.result||e.data,this.$emit("api-key-regenerated",a),this.$notification.success({message:"API Key重置成功",description:"新的API Key已生成，请妥善保管。旧的API Key已失效。",placement:"topRight",duration:4.5,style:{width:"400px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),this.hideResetConfirm()):this.$notification.error({message:"API Key重置失败",description:e.message||"重置操作未能完成，请稍后重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$notification.error({message:"重置API Key失败",description:"网络连接异常或服务器错误，请检查网络后重试。",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}});case 12:return t.prev=12,this.regenerating=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),formatDate:function(t){if(!t)return"未知";try{var e=new Date(t);return e.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch(a){return"未知"}}}},p=f,h=(a("6a12"),a("2877")),v=Object(h["a"])(p,i,n,!1,null,"d9f57ba2",null);e["default"]=v.exports},"4d78":function(t,e,a){"use strict";var i=a("a7c8"),n=a.n(i);n.a},"4dbe":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"data-table"},[t.showHeader?a("div",{staticClass:"table-header"},[a("h3",{staticClass:"table-title"},[t._v(t._s(t.title))]),a("div",{staticClass:"table-actions"},[t.showFilters?a("div",{staticClass:"filter-group"},[t.typeOptions.length>0?a("a-select",{staticStyle:{width:"140px"},attrs:{placeholder:t.typeFilterPlaceholder},on:{change:t.handleFilterChange},model:{value:t.filters.type,callback:function(e){t.$set(t.filters,"type",e)},expression:"filters.type"}},[a("a-select-option",{attrs:{value:""}},[t._v(t._s(t.typeFilterDefaultText))]),t._l(t.typeOptions,(function(e){return a("a-select-option",{key:e.value,attrs:{value:e.value}},[t._v("\n            "+t._s(e.label)+"\n          ")])}))],2):t._e(),t.statusOptions.length>0?a("a-select",{staticStyle:{width:"140px"},attrs:{placeholder:t.statusFilterPlaceholder},on:{change:t.handleFilterChange},model:{value:t.filters.status,callback:function(e){t.$set(t.filters,"status",e)},expression:"filters.status"}},[a("a-select-option",{attrs:{value:""}},[t._v(t._s(t.statusFilterDefaultText))]),t._l(t.statusOptions,(function(e){return a("a-select-option",{key:e.value,attrs:{value:e.value}},[t._v("\n            "+t._s(e.label)+"\n          ")])}))],2):t._e(),t.showDateFilter?a("a-range-picker",{staticStyle:{width:"220px"},attrs:{placeholder:t.dateFilterPlaceholder},on:{change:t.handleFilterChange},model:{value:t.filters.dateRange,callback:function(e){t.$set(t.filters,"dateRange",e)},expression:"filters.dateRange"}}):t._e(),t.showOrderSearch?a("a-input",{staticStyle:{width:"160px"},attrs:{placeholder:t.orderSearchPlaceholder},on:{change:t.handleFilterChange},model:{value:t.filters.orderKeyword,callback:function(e){t.$set(t.filters,"orderKeyword",e)},expression:"filters.orderKeyword"}},[a("a-icon",{attrs:{slot:"suffix",type:"search"},slot:"suffix"})],1):t._e(),t.showProductSearch?a("a-input",{staticStyle:{width:"160px"},attrs:{placeholder:t.productSearchPlaceholder},on:{change:t.handleFilterChange},model:{value:t.filters.productKeyword,callback:function(e){t.$set(t.filters,"productKeyword",e)},expression:"filters.productKeyword"}},[a("a-icon",{attrs:{slot:"suffix",type:"search"},slot:"suffix"})],1):t._e()],1):t._e(),a("div",{staticClass:"action-buttons"},[t._t("actions")],2)])]):t._e(),a("div",{staticClass:"table-container"},[a("a-table",{attrs:{columns:t.tableColumns,"data-source":t.dataSource,loading:t.loading,pagination:t.paginationConfig,scroll:{x:t.scrollX},"row-key":t.rowKey},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"action",fn:function(e){var i=e.record,n=e.index;return[a("div",{staticClass:"table-actions"},[t._t("actions-column",[a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handleViewDetail(i)}}},[t._v("\n              查看详情\n            ")])],{record:i,index:n})],2)]}}],null,!0)})],1)])},n=[];function s(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function r(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?s(Object(a),!0).forEach((function(e){o(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function o(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function c(t){return f(t)||d(t)||u(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return p(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function d(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function f(t){if(Array.isArray(t))return p(t)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}var h={name:"DataTable",props:{title:{type:String,default:"数据列表"},dataSource:{type:Array,default:function(){return[]}},columns:{type:Array,required:!0},loading:{type:Boolean,default:!1},pagination:{type:Object,default:function(){return{current:1,pageSize:10,total:0}}},rowKey:{type:String,default:"id"},scrollX:{type:Number,default:1200},showHeader:{type:Boolean,default:!0},showFilters:{type:Boolean,default:!0},showDateFilter:{type:Boolean,default:!0},showSearch:{type:Boolean,default:!0},showOrderSearch:{type:Boolean,default:!1},showProductSearch:{type:Boolean,default:!1},typeOptions:{type:Array,default:function(){return[]}},statusOptions:{type:Array,default:function(){return[]}},typeFilterPlaceholder:{type:String,default:"类型"},statusFilterPlaceholder:{type:String,default:"状态"},typeFilterDefaultText:{type:String,default:"全部类型"},statusFilterDefaultText:{type:String,default:"全部状态"},searchPlaceholder:{type:String,default:"搜索关键词"},orderSearchPlaceholder:{type:String,default:"搜索订单号"},productSearchPlaceholder:{type:String,default:"搜索商品名称"},dateFilterPlaceholder:{type:Array,default:function(){return["开始时间","结束时间"]}},showActionColumn:{type:Boolean,default:!0}},data:function(){return{filters:{type:"",status:"",dateRange:[],keyword:"",orderKeyword:"",productKeyword:""}}},computed:{tableColumns:function(){if(!this.showActionColumn)return this.columns;var t=this.columns[this.columns.length-1];return t&&"action"===t.key?this.columns:[].concat(c(this.columns),[{title:"操作",key:"action",width:200,align:"center",scopedSlots:{customRender:"action"}}])},paginationConfig:function(){return r(r({},this.pagination),{},{showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")},pageSizeOptions:["10","20","50","100"]})}},methods:{handleFilterChange:function(){this.$emit("filter-change",this.filters)},resetFilters:function(){this.filters={type:"",status:"",dateRange:[],keyword:"",orderKeyword:"",productKeyword:""},this.handleFilterChange()},handleTableChange:function(t,e,a){this.$emit("table-change",{pagination:t,filters:e,sorter:a})},handleViewDetail:function(t){this.$emit("view-detail",t)},formatDateTime:function(t){if(!t)return"-";try{var e;return e=new Date(t),e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}},formatNumber:function(t){return t?parseFloat(t).toFixed(2):"0.00"}}},v=h,g=(a("51a7"),a("2877")),m=Object(g["a"])(v,i,n,!1,null,"821a32de",null);e["default"]=m.exports},"51a7":function(t,e,a){"use strict";var i=a("ba12"),n=a.n(i);n.a},6205:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"usercenter-sidebar",style:{top:t.sidebarTop+"px"}},[a("div",{staticClass:"user-info-card"},[a("div",{staticClass:"user-avatar"},[a("img",{attrs:{src:t.avatarUrl,alt:t.userInfo&&t.userInfo.nickname||"用户头像"}}),a("div",{staticClass:"avatar-status",class:{online:t.isOnline}})]),a("div",{staticClass:"user-details"},[a("h3",{staticClass:"user-name"},[t._v(t._s(t.userInfo&&t.userInfo.nickname||"智界用户"))]),a("div",{staticClass:"user-level"},[a("span",{staticClass:"level-badge",class:t.memberLevelClass},[t._v("\n          "+t._s(t.memberLevelText)+"\n        ")])]),a("div",{staticClass:"user-balance"},[a("div",{staticClass:"balance-label"},[t._v("账户余额")]),a("div",{staticClass:"balance-amount"},[t._v("¥"+t._s(t.formatBalance(t.userInfo&&t.userInfo.accountBalance||0)))]),a("button",{staticClass:"balance-btn-mini",on:{click:t.handleRecharge}},[t._v("充值")])])])]),a("nav",{staticClass:"sidebar-nav"},[a("ul",{staticClass:"nav-menu"},t._l(t.menuItems,(function(e){return a("li",{key:e.key,staticClass:"nav-item"},[a("a",{staticClass:"nav-link sidebar-menu-item",class:{active:t.currentPage===e.key},attrs:{href:"#"},on:{click:function(a){return a.preventDefault(),t.handleMenuClick(e)}}},[a("i",{staticClass:"nav-icon",class:e.icon}),a("span",{staticClass:"nav-text"},[t._v(t._s(e.title))]),e.badge?a("span",{staticClass:"nav-badge"},[t._v(t._s(e.badge))]):t._e()])])})),0)])])},n=[],s=a("a34a"),r=a.n(s),o=a("0fea"),c=a("ff1f");function l(t,e,a,i,n,s,r){try{var o=t[s](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,n)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var s=t.apply(e,a);function r(t){l(s,i,n,r,o,"next",t)}function o(t){l(s,i,n,r,o,"throw",t)}r(void 0)}))}}var d={name:"UserCenterSidebar",props:{currentPage:{type:String,default:"overview"},userInfo:{type:Object,default:function(){return{}}}},data:function(){return{isOnline:!0,defaultAvatar:"/default-avatar.png",unreadNotificationCount:0,sidebarTop:140,footerSafeDistance:150}},computed:{menuItems:function(){return[{key:"overview",title:"概览",icon:"anticon anticon-dashboard",description:"查看账户概况和统计数据"},{key:"profile",title:"账户设置",icon:"anticon anticon-setting",description:"管理个人信息和安全设置"},{key:"credits",title:"账户管理",icon:"anticon anticon-wallet",description:"查看余额和交易记录"},{key:"orders",title:"订单记录",icon:"anticon anticon-shopping",description:"查看购买历史和订单状态"},{key:"usage",title:"使用记录",icon:"anticon anticon-bar-chart",description:"查看API调用和插件使用"},{key:"notifications",title:"系统通知",icon:"anticon anticon-bell",description:"查看系统消息和通知",badge:this.unreadNotificationCount>0?this.unreadNotificationCount>99?"99+":this.unreadNotificationCount.toString():null}]},memberLevelClass:function(){var t=this.userInfo&&this.userInfo.currentRole||"user";return{"level-user":"user"===t||"USER"===t||"普通用户"===t,"level-vip":"vip"===t||"VIP"===t||"VIP会员"===t,"level-svip":"svip"===t||"SVIP"===t||"SVIP会员"===t,"level-admin":"admin"===t||"ADMIN"===t||"管理员"===t}},memberLevelText:function(){var t=this.userInfo&&this.userInfo.currentRole||"user",e={user:"普通用户",vip:"VIP会员",svip:"SVIP会员",admin:"管理员",USER:"普通用户",VIP:"VIP会员",SVIP:"SVIP会员",ADMIN:"管理员","普通用户":"普通用户","VIP会员":"VIP会员","SVIP会员":"SVIP会员","管理员":"管理员"};return e[t]||"普通用户"},avatarUrl:function(){var t=this.userInfo&&this.userInfo.avatar;return t?t.startsWith("http://")||t.startsWith("https://")?t:Object(o["d"])(t)||this.defaultAvatar:this.defaultAvatar}},mounted:function(){var t=u(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=3,this.loadUnreadNotificationCount();case 3:this.loadDefaultAvatar(),this.$bus.$on("notification-updated",this.handleNotificationUpdated),this.initScrollListener();case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){this.$bus.$off("notification-updated",this.handleNotificationUpdated),window.removeEventListener("scroll",this.handleScroll)},methods:{loadDefaultAvatar:function(){var t=u(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/sys/common/default-avatar-url");case 3:e=t.sent,e&&e.success&&e.result&&(this.defaultAvatar=e.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),handleMenuClick:function(t){this.$emit("menu-change",t.key)},handleRecharge:function(){this.$emit("action","recharge")},handleUpgrade:function(){this.$emit("action","upgrade")},formatBalance:function(t){return t?parseFloat(t).toFixed(2):"0.00"},loadUnreadNotificationCount:function(){var t=u(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,Object(c["c"])();case 4:e=t.sent,e.success&&e.result&&(this.unreadNotificationCount=e.result.unreadCount||0),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0);case 11:case"end":return t.stop()}}),t,this,[[0,8]])})));function e(){return t.apply(this,arguments)}return e}(),handleNotificationUpdated:function(t){this.loadUnreadNotificationCount()},initScrollListener:function(){this.handleScroll=this.throttle(this.calculateSidebarPosition,16),window.addEventListener("scroll",this.handleScroll,{passive:!0}),this.calculateSidebarPosition()},calculateSidebarPosition:function(){if(!(window.innerWidth<=1200)){var t=document.querySelector(".website-footer");if(t){var e=this.$el;if(e){var a=t.getBoundingClientRect(),i=e.getBoundingClientRect(),n=window.innerHeight,s=a.top,r=i.height+this.footerSafeDistance;if(s<n&&s<r+140){var o=Math.max(20,s-i.height-this.footerSafeDistance);this.sidebarTop=o}else 140!==this.sidebarTop&&(this.sidebarTop=140)}}}},throttle:function(t,e){var a,i=0;return function(){for(var n=this,s=arguments.length,r=new Array(s),o=0;o<s;o++)r[o]=arguments[o];var c=Date.now();c-i>e?(t.apply(this,r),i=c):(clearTimeout(a),a=setTimeout((function(){t.apply(n,r),i=Date.now()}),e-(c-i)))}}}},f=d,p=(a("195b"),a("2877")),h=Object(p["a"])(f,i,n,!1,null,"1e174e22",null);e["default"]=h.exports},"63ea":function(t,e,a){"use strict";var i=a("9985"),n=a.n(i);n.a},"6a12":function(t,e,a){"use strict";var i=a("0aa0"),n=a.n(i);n.a},"7c35":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"transaction-table"},[a("div",{staticClass:"table-header"},[a("h3",{staticClass:"table-title"},[t._v(t._s(t.title))]),a("div",{staticClass:"table-actions"},[a("div",{staticClass:"filter-group"},[a("a-select",{staticStyle:{width:"120px"},attrs:{placeholder:"交易类型"},on:{change:t.handleFilterChange},model:{value:t.filters.type,callback:function(e){t.$set(t.filters,"type",e)},expression:"filters.type"}},[a("a-select-option",{attrs:{value:""}},[t._v("全部")]),a("a-select-option",{attrs:{value:"1"}},[t._v("充值")]),a("a-select-option",{attrs:{value:"2"}},[t._v("消费")]),a("a-select-option",{attrs:{value:"3"}},[t._v("退款")]),a("a-select-option",{attrs:{value:"4"}},[t._v("奖励")])],1),a("a-range-picker",{staticStyle:{width:"240px"},on:{change:t.handleFilterChange},model:{value:t.filters.dateRange,callback:function(e){t.$set(t.filters,"dateRange",e)},expression:"filters.dateRange"}})],1),a("button",{staticClass:"refresh-btn",attrs:{disabled:t.loading},on:{click:t.handleRefresh}},[a("i",{staticClass:"anticon anticon-reload",class:{spinning:t.loading}})])])]),a("div",{staticClass:"table-container"},[a("a-table",{attrs:{columns:t.columns,"data-source":t.dataSource,loading:t.loading,pagination:t.paginationConfig,scroll:{x:800},"row-key":"id"},on:{change:t.handleTableChange},scopedSlots:t._u([{key:"action",fn:function(e){var i=e.record;return[a("a-button",{attrs:{type:"link",size:"small"},on:{click:function(e){return t.handleViewDetail(i)}}},[t._v("\n          查看详情\n        ")])]}}])})],1)])},n=[];function s(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function r(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?s(Object(a),!0).forEach((function(e){o(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function o(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var c={name:"TransactionTable",props:{title:{type:String,default:"交易记录"},dataSource:{type:Array,default:function(){return[]}},loading:{type:Boolean,default:!1},pagination:{type:Object,default:function(){return{current:1,pageSize:10,total:0}}}},data:function(){var t=this;return{filters:{type:"",dateRange:[]},columns:[{title:"交易类型",dataIndex:"transactionType",key:"transactionType",width:120,align:"center",customRender:function(t){var e={1:"充值",2:"消费",3:"退款",4:"奖励"};return e[t]||"未知"}},{title:"交易单号",dataIndex:"id",key:"id",width:200,align:"center",customRender:function(t){return t||"-"}},{title:"金额",dataIndex:"amount",key:"amount",width:120,align:"right",customRender:function(t,e){var a=[1,3,4].includes(e.transactionType)?"+":"-";return"".concat(a,"¥").concat(parseFloat(Math.abs(t)||0).toFixed(2))}},{title:"余额",dataIndex:"balanceAfter",key:"balanceAfter",width:120,align:"right",customRender:function(t){return"¥".concat(parseFloat(t||0).toFixed(2))}},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"交易时间",dataIndex:"transactionTime",key:"transactionTime",width:150,align:"center",customRender:function(e){return t.formatDateTime(e)}},{title:"操作",key:"action",width:100,align:"center",scopedSlots:{customRender:"action"}}]}},computed:{paginationConfig:function(){return r(r({},this.pagination),{},{showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")},pageSizeOptions:["10","20","50","100"]})}},methods:{handleFilterChange:function(){this.$emit("filter-change",this.filters)},handleRefresh:function(){this.$emit("refresh")},handleTableChange:function(t,e,a){this.$emit("table-change",{pagination:t,filters:e,sorter:a})},handleViewDetail:function(t){this.$emit("view-detail",t)},getTypeClass:function(t){var e={1:"type-recharge",2:"type-consume",3:"type-refund",4:"type-reward"};return e[t]||""},getTypeIcon:function(t){var e={1:"anticon anticon-plus-circle",2:"anticon anticon-minus-circle",3:"anticon anticon-undo",4:"anticon anticon-gift"};return e[t]||"anticon anticon-question-circle"},getTypeText:function(t){var e={1:"充值",2:"消费",3:"退款",4:"奖励"};return e[t]||"未知"},getAmountClass:function(t){return{"amount-positive":[1,3,4].includes(t),"amount-negative":2===t}},formatAmount:function(t,e){var a=[1,3,4].includes(e)?"+":"-";return"".concat(a,"¥").concat(this.formatNumber(Math.abs(t)))},formatNumber:function(t){return t?parseFloat(t).toFixed(2):"0.00"},formatDateTime:function(t){if(!t)return"-";try{var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}},getStatusClass:function(t){var e={1:"status-success",2:"status-pending",3:"status-failed"};return e[t]||""},getStatusText:function(t){var e={1:"成功",2:"处理中",3:"失败"};return e[t]||"未知"}}},l=c,u=(a("b56a"),a("2877")),d=Object(u["a"])(l,i,n,!1,null,"7dbfea38",null);e["default"]=d.exports},"8a06":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"floating-notifications",class:{expanded:t.isExpanded,minimized:t.isMinimized}},[t.isMinimized?t._e():a("div",{staticClass:"notification-trigger",on:{click:t.toggleExpanded}},[a("div",{staticClass:"trigger-icon"},[a("i",{staticClass:"anticon anticon-bell"}),t.unreadCount>0?a("span",{staticClass:"notification-badge"},[t._v(t._s(t.unreadCount>99?"99+":t.unreadCount))]):t._e()]),a("div",{staticClass:"trigger-text"},[a("span",{staticClass:"trigger-title"},[t._v("系统通知")]),t.isExpanded?a("span",{staticClass:"trigger-subtitle"},[t._v("点击收起通知面板")]):a("span",{staticClass:"trigger-subtitle"},[t._v(t._s(t.unreadCount>0?t.unreadCount+"条未读":"暂无新消息"))])]),a("div",{staticClass:"trigger-dropdown",attrs:{title:"展开/收起通知"},on:{click:function(e){return e.stopPropagation(),t.toggleExpanded(e)}}},[a("span",{staticClass:"dropdown-arrow",class:{"arrow-up":t.isExpanded,"arrow-down":!t.isExpanded}})])]),t.isMinimized?a("div",{staticClass:"minimized-trigger",on:{click:t.toggleMinimized}},[a("i",{staticClass:"anticon anticon-bell"}),t.unreadCount>0?a("span",{staticClass:"notification-badge"},[t._v(t._s(t.unreadCount>99?"99+":t.unreadCount))]):t._e()]):t._e(),t.isExpanded&&!t.isMinimized?a("div",{staticClass:"notification-panel"},[a("div",{staticClass:"panel-header"},[a("h4",[t._v("系统通知")]),a("div",{staticClass:"panel-actions"},[a("button",{staticClass:"action-btn",attrs:{title:"查看全部"},on:{click:t.handleViewAll}},[a("i",{staticClass:"anticon anticon-eye"})]),a("button",{staticClass:"action-btn",attrs:{title:"最小化"},on:{click:t.toggleMinimized}},[a("i",{staticClass:"anticon anticon-minus"})]),a("button",{staticClass:"action-btn",attrs:{title:"收起"},on:{click:t.toggleExpanded}},[a("i",{staticClass:"anticon anticon-up"})])])]),t.notifications&&t.notifications.length?a("div",{staticClass:"notification-list"},t._l(t.notifications,(function(e){return a("div",{key:e.id,staticClass:"notification-item",class:{unread:!e.readFlag},on:{click:function(a){return t.handleNotificationClick(e)}}},[e.readFlag?t._e():a("div",{staticClass:"notification-dot"}),a("div",{staticClass:"notification-content"},[a("div",{staticClass:"notification-title"},[t._v(t._s(e.titile||"系统通知"))]),a("div",{staticClass:"notification-message"},[t._v(t._s(t.getNotificationSummary(e.msgContent)))]),a("div",{staticClass:"notification-time"},[t._v(t._s(t.formatRelativeTime(e.sendTime)))])]),e.readFlag?t._e():a("button",{staticClass:"mark-read-btn",attrs:{title:"标记已读"},on:{click:function(a){return a.stopPropagation(),t.handleMarkAsRead(e)}}},[a("i",{staticClass:"anticon anticon-check"})])])})),0):t.loading?a("div",{staticClass:"notification-loading"},t._l(3,(function(e){return a("div",{key:e,staticClass:"notification-skeleton"},[t._m(0,!0)])})),0):a("div",{staticClass:"notification-empty"},[a("i",{staticClass:"anticon anticon-bell"}),a("p",[t._v("暂无系统通知")])])]):t._e(),a("a-modal",{attrs:{title:"通知详情",width:"900px",footer:null,bodyStyle:{maxHeight:"70vh",overflowY:"auto",padding:"24px"}},on:{cancel:t.handleDetailModalClose},model:{value:t.detailModalVisible,callback:function(e){t.detailModalVisible=e},expression:"detailModalVisible"}},[t.selectedNotification?a("div",{staticClass:"notification-detail"},[a("div",{staticClass:"detail-header"},[a("h3",{staticClass:"detail-title"},[t._v(t._s(t.selectedNotification.titile||"系统通知"))]),a("div",{staticClass:"detail-meta"},[a("span",{staticClass:"detail-time"},[t._v(t._s(t.formatTime(t.selectedNotification.sendTime)))]),a("span",{staticClass:"detail-type"},[t._v(t._s(t.getNotificationType(t.selectedNotification.msgCategory)))])])]),a("div",{staticClass:"detail-content"},[a("div",{staticClass:"detail-message",domProps:{innerHTML:t._s(t.selectedNotification.msgContent||"暂无内容")}})]),a("div",{staticClass:"detail-footer"},[a("div",{staticClass:"detail-sender"},[t._v("发送者: "+t._s(t.selectedNotification.sender||"系统"))]),a("div",{staticClass:"detail-status"},[t.selectedNotification.readFlag?a("span",{staticClass:"status-read"},[t._v("已读")]):a("span",{staticClass:"status-unread"},[t._v("未读")])])])]):t._e()])],1)},n=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"skeleton-content"},[a("div",{staticClass:"skeleton-line"}),a("div",{staticClass:"skeleton-line short"})])}],s=a("a34a"),r=a.n(s),o=a("ff1f"),c=a("9fb0"),l=a("2b0e");function u(t,e,a,i,n,s,r){try{var o=t[s](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,n)}function d(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var s=t.apply(e,a);function r(t){u(s,i,n,r,o,"next",t)}function o(t){u(s,i,n,r,o,"throw",t)}r(void 0)}))}}var f={name:"FloatingNotifications",data:function(){return{isExpanded:!1,isMinimized:!1,loading:!1,notifications:[],refreshInterval:null,detailModalVisible:!1,selectedNotification:null}},computed:{unreadCount:function(){return this.notifications&&Array.isArray(this.notifications)?this.notifications.length:0},currentUserId:function(){var t=l["default"].ls.get(c["u"]);return t?t.id:null},dropdownIconClass:function(){return this.isExpanded?"anticon anticon-up":"anticon anticon-down"}},mounted:function(){var t=d(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=3,this.loadNotifications();case 3:this.startAutoRefresh();case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){this.stopAutoRefresh()},methods:{loadNotifications:function(){var t=d(r.a.mark((function t(){var e,a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=5,Object(o["d"])();case 5:e=t.sent,e.success?(a=e.result||[],this.notifications=Array.isArray(a)?a:[],this.notifications.forEach((function(t){t.readFlag=!1,t.title&&!t.titile&&(t.titile=t.title),t.content&&!t.msgContent&&(t.msgContent=t.content),t.createTime&&!t.sendTime&&(t.sendTime=t.createTime)}))):this.notifications=[],t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](0),this.notifications=[];case 14:return t.prev=14,this.loading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[0,10,14,17]])})));function e(){return t.apply(this,arguments)}return e}(),toggleExpanded:function(){this.isExpanded=!this.isExpanded,this.isExpanded&&0===this.notifications.length&&this.loadNotifications()},toggleMinimized:function(){this.isMinimized=!this.isMinimized,this.isMinimized||(this.isExpanded=!1)},handleNotificationClick:function(t){this.showNotificationDetail(t),this.isExpanded=!1},showNotificationDetail:function(t){this.selectedNotification=t,this.detailModalVisible=!0,t.readFlag||this.handleMarkAsRead(t)},handleDetailModalClose:function(){this.detailModalVisible=!1,this.selectedNotification=null},getNotificationText:function(t){if(!t)return"点击查看详情";var e=t.replace(/<[^>]*>/g,"");return e.length>50?e.substring(0,50)+"...":e},getNotificationSummary:function(t){if(!t)return"点击查看详情";var e=t.replace(/<[^>]*>/g,""),a=25;return e.length<=a?e:e.substring(0,a)+"..."},handleMarkAsRead:function(){var t=d(r.a.mark((function t(e){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(o["f"])(e.anntId||e.id);case 3:a=t.sent,a.success?(e.readFlag=!0,e.isRead=1,this.$message.success("标记已读成功"),this.loadNotifications(),this.$bus.$emit("notification-updated",{type:"markAsRead",notificationId:e.anntId||e.id})):this.$message.error(a.message||"标记失败"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.$message.error("标记失败，请重试");case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(e){return t.apply(this,arguments)}return e}(),handleViewAll:function(){this.$router.push("/usercenter?page=notifications"),this.isExpanded=!1},formatRelativeTime:function(t){if(!t)return"";var e=new Date,a=new Date(t),i=e-a,n=Math.floor(i/6e4),s=Math.floor(i/36e5),r=Math.floor(i/864e5);return n<1?"刚刚":n<60?"".concat(n,"分钟前"):s<24?"".concat(s,"小时前"):r<7?"".concat(r,"天前"):a.toLocaleDateString("zh-CN")},startAutoRefresh:function(){},stopAutoRefresh:function(){this.refreshInterval&&(clearInterval(this.refreshInterval),this.refreshInterval=null)},formatTime:function(t){if(!t)return"";var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},getNotificationType:function(t){var e={1:"系统消息",2:"业务消息",3:"安全消息",4:"营销消息"};return e[t]||"系统消息"}}},p=f,h=(a("4d78"),a("2877")),v=Object(h["a"])(p,i,n,!1,null,"817eb63e",null);e["default"]=v.exports},"8f57":function(t,e,a){},9316:function(t,e,a){},9985:function(t,e,a){},a7c8:function(t,e,a){},b56a:function(t,e,a){"use strict";var i=a("8f57"),n=a.n(i);n.a},ba12:function(t,e,a){},ecf7:function(t,e,a){"use strict";var i=a("9316"),n=a.n(i);n.a}}]);