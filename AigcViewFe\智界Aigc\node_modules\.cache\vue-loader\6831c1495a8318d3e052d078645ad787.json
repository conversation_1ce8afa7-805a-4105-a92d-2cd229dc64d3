{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue?vue&type=template&id=38b897e5&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\user\\register\\Register.vue", "mtime": 1753835250707}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"main user-layout-register\">\n  <h3><span>注册</span></h3>\n  <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\">\n    <a-form-model-item prop=\"username\">\n      <a-input v-model=\"model.username\" size=\"large\" type=\"text\" autocomplete=\"false\" placeholder=\"请输入用户名\"/>\n    </a-form-model-item>\n\n    <a-popover placement=\"rightTop\" trigger=\"click\" :visible=\"state.passwordLevelChecked\">\n      <template slot=\"content\">\n        <div :style=\"{ width: '240px' }\">\n          <div :class=\"['user-register', passwordLevelClass]\">强度：<span>{{ passwordLevelName }}</span></div>\n          <a-progress :percent=\"state.percent\" :showInfo=\"false\" :strokeColor=\" passwordLevelColor \"/>\n          <div style=\"margin-top: 10px;\">\n            <span>请至少输入 8 个字符。请不要使用容易被猜到的密码。</span>\n          </div>\n        </div>\n      </template>\n      <a-form-model-item prop=\"password\">\n        <a-input\n          v-model=\"model.password\"\n          size=\"large\"\n          type=\"password\"\n          @click=\"handlePasswordInputClick\"\n          autocomplete=\"false\"\n          placeholder=\"至少8位密码，区分大小写\">\n        </a-input>\n      </a-form-model-item>\n    </a-popover>\n\n    <a-form-model-item prop=\"password2\">\n      <a-input v-model=\"model.password2\" size=\"large\" type=\"password\" autocomplete=\"false\" placeholder=\"确认密码\"></a-input>\n    </a-form-model-item>\n\n    <a-form-model-item prop=\"mobile\">\n      <a-input v-model=\"model.mobile\" size=\"large\" placeholder=\"11 位手机号\">\n        <a-select slot=\"addonBefore\" size=\"large\" defaultValue=\"+86\">\n          <a-select-option value=\"+86\">+86</a-select-option>\n          <a-select-option value=\"+87\">+87</a-select-option>\n        </a-select>\n      </a-input>\n    </a-form-model-item>\n    <!--<a-input-group size=\"large\" compact>\n          <a-select style=\"width: 20%\" size=\"large\" defaultValue=\"+86\">\n            <a-select-option value=\"+86\">+86</a-select-option>\n            <a-select-option value=\"+87\">+87</a-select-option>\n          </a-select>\n          <a-input style=\"width: 80%\" size=\"large\" placeholder=\"11 位手机号\"></a-input>\n        </a-input-group>-->\n\n    <a-row :gutter=\"16\">\n      <a-col class=\"gutter-row\" :span=\"16\">\n        <a-form-model-item prop=\"captcha\">\n          <a-input v-model=\"model.captcha\" size=\"large\" type=\"text\" placeholder=\"验证码\">\n            <a-icon slot=\"prefix\" type=\"mail\" :style=\"{ color: 'rgba(0,0,0,.25)' }\"/>\n          </a-input>\n        </a-form-model-item>\n      </a-col>\n      <a-col class=\"gutter-row\" :span=\"8\">\n        <a-button\n          class=\"getCaptcha\"\n          size=\"large\"\n          :disabled=\"state.smsSendBtn\"\n          @click.stop.prevent=\"getCaptcha\"\n          v-text=\"!state.smsSendBtn && '获取验证码'||(state.time+' s')\"></a-button>\n      </a-col>\n    </a-row>\n\n    <a-form-model-item>\n      <a-button\n        size=\"large\"\n        type=\"primary\"\n        htmlType=\"submit\"\n        class=\"register-button\"\n        :loading=\"registerBtn\"\n        @click.stop.prevent=\"handleSubmit\"\n        :disabled=\"registerBtn\">注册\n      </a-button>\n      <router-link class=\"login\" :to=\"{ name: 'login' }\">使用已有账户登录</router-link>\n    </a-form-model-item>\n\n  </a-form-model>\n</div>\n", null]}