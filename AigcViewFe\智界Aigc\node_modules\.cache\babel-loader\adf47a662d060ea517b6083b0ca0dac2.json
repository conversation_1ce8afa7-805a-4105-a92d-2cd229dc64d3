{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753834252523}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport QuickRecharge from '@/components/QuickRecharge.vue';\nimport { createMembershipOrder, payOrder, getUserRole } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'Membership',\n  components: {\n    WebsitePage: WebsitePage,\n    QuickRecharge: QuickRecharge\n  },\n  data: function data() {\n    return {\n      // 用户角色（实时获取）\n      userRole: 'user',\n      // 支付相关\n      paymentLoading: false,\n      showPaymentModal: false,\n      selectedPlan: null,\n      selectedPaymentMethod: 'alipay-qr',\n      // 二维码支付\n      showQrModal: false,\n      qrCodeUrl: '',\n      currentOrderId: '',\n      currentOrderAmount: 0,\n      paymentCheckTimer: null,\n      checkingStatus: false,\n      plans: [{\n        id: 1,\n        name: 'VIP月卡',\n        price: '29',\n        originalPrice: '39',\n        discountText: '限时7.4折',\n        saveAmount: '10',\n        period: '月',\n        description: '适合体验用户的基础功能',\n        features: [{\n          text: '解锁VIP课程',\n          disabled: false\n        }, {\n          text: '插件基础折扣',\n          disabled: false\n        }, {\n          text: '邀请奖励35%基础比例',\n          disabled: false\n        }, {\n          text: '调用工作流基础折扣',\n          disabled: false\n        }, {\n          text: '复制所有工作流',\n          disabled: true\n        }, {\n          text: '解锁流媒体转换',\n          disabled: true\n        }, {\n          text: '部分插件免费',\n          disabled: true\n        }],\n        buttonText: '立即购买',\n        featured: false\n      }, {\n        id: 2,\n        name: 'VIP年卡',\n        price: '298',\n        originalPrice: '468',\n        discountText: '限时6.4折',\n        saveAmount: '170',\n        period: '年',\n        description: '适合长期使用的优惠套餐',\n        features: [{\n          text: '解锁VIP课程',\n          disabled: false\n        }, {\n          text: '插件基础折扣',\n          disabled: false\n        }, {\n          text: '邀请奖励35%基础比例',\n          disabled: false\n        }, {\n          text: '调用工作流基础折扣',\n          disabled: false\n        }, {\n          text: '复制所有工作流',\n          disabled: true\n        }, {\n          text: '解锁流媒体转换',\n          disabled: true\n        }, {\n          text: '部分插件免费',\n          disabled: true\n        }],\n        buttonText: '立即购买',\n        featured: false\n      }, {\n        id: 3,\n        name: 'SVIP年卡',\n        price: '489',\n        originalPrice: '999',\n        discountText: '限时4.9折',\n        saveAmount: '510',\n        period: '年',\n        description: '适合专业用户的全功能套餐',\n        features: [{\n          text: '解锁<strong class=\"highlight\">全部课程</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '插件<strong class=\"highlight\">最高折扣</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '邀请<strong class=\"highlight\">奖励50%</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '调用工作流<strong class=\"highlight\">最高折扣</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '复制<strong class=\"highlight\">所有工作流</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '<strong class=\"highlight\">解锁流媒体转换</strong>',\n          disabled: false,\n          exclusive: true\n        }, {\n          text: '部分<strong class=\"highlight\">插件免费</strong>',\n          disabled: false,\n          exclusive: true\n        }],\n        buttonText: '立即购买',\n        featured: true\n      }]\n    };\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.paymentCheckTimer) {\n      clearInterval(this.paymentCheckTimer);\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              console.log('Membership页面已挂载'); // 检查登录状态\n\n              if (this.checkLoginStatus()) {\n                _context.next = 3;\n                break;\n              }\n\n              return _context.abrupt(\"return\");\n\n            case 3:\n              _context.next = 5;\n              return this.loadUserRole();\n\n            case 5:\n              this.checkPaymentSuccessFromUrl();\n\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    /**\n     * 检查登录状态 - 使用与个人中心相同的TOKEN检查方法\n     */\n    checkLoginStatus: function checkLoginStatus() {\n      var token = Vue.ls.get(ACCESS_TOKEN);\n\n      if (!token) {\n        console.log('🔍 Membership: 未登录，重定向到登录页');\n        this.$router.push({\n          path: '/login',\n          query: {\n            redirect: this.$route.fullPath\n          }\n        });\n        return false;\n      }\n\n      console.log('🔍 Membership: 已登录，TOKEN存在');\n      return true;\n    },\n    // 获取按钮文本（续费/升级/购买）\n    getButtonText: function getButtonText(plan) {\n      var userRole = this.getUserRole(); // 获取用户当前角色\n\n      var planRole = this.getPlanRole(plan.id); // 获取套餐对应角色\n      // 管理员显示\"立即购买\"（管理员不需要购买会员，但可以测试）\n\n      if (userRole === 'admin') {\n        return '立即购买';\n      } // 相同等级显示\"续费会员\"\n\n\n      if (userRole === planRole) {\n        return '续费会员';\n      } // 普通用户购买任何会员都显示\"立即购买\"\n      else if (userRole === 'user') {\n          return '立即购买';\n        } // 会员用户购买更高等级显示\"升级会员\"\n        else if (this.isUpgrade(userRole, planRole)) {\n            return '升级会员';\n          } // 其他情况显示\"立即购买\"\n          else {\n              return '立即购买';\n            }\n    },\n    // 获取套餐对应的角色\n    getPlanRole: function getPlanRole(planId) {\n      var roleMap = {\n        1: 'VIP',\n        // VIP月卡\n        2: 'VIP',\n        // VIP年卡\n        3: 'SVIP' // SVIP年卡\n\n      };\n      return roleMap[planId] || 'user';\n    },\n    // 获取会员等级（用于后端处理）\n    getMembershipLevel: function getMembershipLevel(planId) {\n      var levelMap = {\n        1: 2,\n        // VIP月卡 → membershipLevel = 2 (VIP)\n        2: 2,\n        // VIP年卡 → membershipLevel = 2 (VIP)\n        3: 3 // SVIP年卡 → membershipLevel = 3 (SVIP)\n\n      };\n      return levelMap[planId] || 2; // 默认VIP等级\n    },\n    // 判断是否为升级\n    isUpgrade: function isUpgrade(currentRole, targetRole) {\n      var roleLevel = {\n        'user': 1,\n        'VIP': 2,\n        'SVIP': 3,\n        'admin': 999 // 管理员级别最高\n\n      };\n      return roleLevel[targetRole] > (roleLevel[currentRole] || 0);\n    },\n    // 是否显示升级提示\n    showUpgradeNotice: function showUpgradeNotice(plan) {\n      var userRole = this.getUserRole();\n      var planRole = this.getPlanRole(plan.id); // 只有VIP升级到SVIP时显示特殊提示\n\n      return userRole === 'VIP' && planRole === 'SVIP';\n    },\n    // 获取升级提示文本\n    getUpgradeNoticeText: function getUpgradeNoticeText(plan) {\n      var userRole = this.getUserRole();\n      var planRole = this.getPlanRole(plan.id);\n\n      if (userRole === 'VIP' && planRole === 'SVIP') {\n        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~';\n      }\n\n      return '';\n    },\n    // 获取用户当前角色（使用实时数据）\n    getUserRole: function getUserRole() {\n      // 直接返回实时获取的角色数据\n      return this.userRole || 'user';\n    },\n    // 加载用户角色（实时API获取）\n    loadUserRole: function () {\n      var _loadUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                console.log('🔍 Membership: 开始加载用户角色');\n                _context2.next = 4;\n                return getUserRole();\n\n              case 4:\n                response = _context2.sent;\n\n                if (response.success) {\n                  this.userRole = response.result.role_code || 'user';\n                  console.log('🔍 Membership: 实时获取角色成功:', this.userRole);\n                } else {\n                  this.userRole = 'user';\n                  console.warn('🔍 Membership: 获取角色失败，使用默认值');\n                }\n\n                _context2.next = 12;\n                break;\n\n              case 8:\n                _context2.prev = 8;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('🔍 Membership: 获取用户角色异常:', _context2.t0);\n                this.userRole = 'user';\n\n              case 12:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 8]]);\n      }));\n\n      function loadUserRole() {\n        return _loadUserRole.apply(this, arguments);\n      }\n\n      return loadUserRole;\n    }(),\n    handleSubscribe: function () {\n      var _handleSubscribe = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3(plan) {\n        var token, orderData, response, orderResult;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                console.log('选择购买套餐:', plan); // 检查用户登录状态\n\n                token = this.$ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context3.next = 6;\n                  break;\n                }\n\n                this.$message.warning('请先登录后再购买会员');\n                this.$router.push('/user/login');\n                return _context3.abrupt(\"return\");\n\n              case 6:\n                _context3.prev = 6;\n                this.paymentLoading = true; // 准备会员订单数据（与充值订单结构保持一致）\n\n                orderData = {\n                  membershipLevel: this.getMembershipLevel(plan.id),\n                  duration: plan.name.includes('月') ? 1 : 12,\n                  // 根据套餐名称判断时长\n                  amount: parseFloat(plan.price),\n                  planName: plan.name,\n                  paymentMethod: 'alipay',\n                  // 默认支付宝，与充值保持一致\n                  source: 'membership' // 标识来源为membership页面\n\n                };\n                console.log('🎯 创建会员订单:', orderData);\n                this.$message.loading('正在创建订单...', 0); // 创建会员订单（使用与充值相同的交易记录系统）\n\n                _context3.next = 13;\n                return createMembershipOrder(orderData);\n\n              case 13:\n                response = _context3.sent;\n                this.$message.destroy();\n\n                if (response.success) {\n                  orderResult = response.result;\n                  console.log('🎯 会员订单创建成功:', orderResult); // 显示支付选择弹窗\n\n                  this.selectedPlan = plan;\n                  this.currentOrderId = orderResult.orderId;\n                  this.currentOrderAmount = orderResult.amount;\n                  this.showPaymentModal = true;\n                } else {\n                  this.$message.error(response.message || '创建订单失败');\n                }\n\n                _context3.next = 23;\n                break;\n\n              case 18:\n                _context3.prev = 18;\n                _context3.t0 = _context3[\"catch\"](6);\n                this.$message.destroy();\n                console.error('创建会员订单失败:', _context3.t0);\n                this.$message.error('创建订单失败，请重试');\n\n              case 23:\n                _context3.prev = 23;\n                this.paymentLoading = false;\n                return _context3.finish(23);\n\n              case 26:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[6, 18, 23, 26]]);\n      }));\n\n      function handleSubscribe(_x) {\n        return _handleSubscribe.apply(this, arguments);\n      }\n\n      return handleSubscribe;\n    }(),\n    // 处理支付\n    handlePayment: function () {\n      var _handlePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                if (this.currentOrderId) {\n                  _context4.next = 3;\n                  break;\n                }\n\n                this.$message.error('订单信息错误');\n                return _context4.abrupt(\"return\");\n\n              case 3:\n                _context4.prev = 3;\n                this.paymentLoading = true;\n\n                if (!(this.selectedPaymentMethod === 'alipay-page')) {\n                  _context4.next = 10;\n                  break;\n                }\n\n                _context4.next = 8;\n                return this.handleAlipayPagePayment();\n\n              case 8:\n                _context4.next = 13;\n                break;\n\n              case 10:\n                if (!(this.selectedPaymentMethod === 'alipay-qr')) {\n                  _context4.next = 13;\n                  break;\n                }\n\n                _context4.next = 13;\n                return this.handleAlipayQrPayment();\n\n              case 13:\n                this.showPaymentModal = false;\n                _context4.next = 20;\n                break;\n\n              case 16:\n                _context4.prev = 16;\n                _context4.t0 = _context4[\"catch\"](3);\n                console.error('支付处理失败:', _context4.t0);\n                this.$message.error('支付失败，请重试');\n\n              case 20:\n                _context4.prev = 20;\n                this.paymentLoading = false;\n                return _context4.finish(20);\n\n              case 23:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[3, 16, 20, 23]]);\n      }));\n\n      function handlePayment() {\n        return _handlePayment.apply(this, arguments);\n      }\n\n      return handlePayment;\n    }(),\n    // 处理支付宝网页支付（与充值功能完全一致）\n    handleAlipayPagePayment: function () {\n      var _handleAlipayPagePayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var paymentData, payResponse, payForm, div, form;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId);\n                this.$message.loading('正在跳转到支付宝支付...', 0);\n                paymentData = {\n                  orderId: this.currentOrderId,\n                  amount: this.currentOrderAmount,\n                  subject: \"\\u667A\\u754CAigc\\u4F1A\\u5458 - \".concat(this.selectedPlan.name),\n                  body: \"\\u8D2D\\u4E70\".concat(this.selectedPlan.name, \"\\uFF0C\\u91D1\\u989D\\uFF1A\\xA5\").concat(this.currentOrderAmount)\n                };\n                console.log('🔍 发送支付请求数据:', paymentData);\n                _context5.next = 7;\n                return this.$http.post('/api/alipay/createOrder', paymentData);\n\n              case 7:\n                payResponse = _context5.sent;\n                console.log('🔍 支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context5.next = 24;\n                  break;\n                }\n\n                payForm = payResponse.result.payForm;\n                console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空');\n\n                if (payForm) {\n                  _context5.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付表单为空');\n                return _context5.abrupt(\"return\");\n\n              case 16:\n                // 创建表单并提交到支付宝\n                div = document.createElement('div');\n                div.innerHTML = payForm;\n                document.body.appendChild(div);\n                form = div.querySelector('form');\n\n                if (form) {\n                  console.log('🔍 找到支付表单，准备提交');\n                  form.submit();\n                } else {\n                  console.error('🔍 未找到支付表单');\n                  this.$message.error('支付表单创建失败');\n                } // 清理DOM\n\n\n                setTimeout(function () {\n                  if (document.body.contains(div)) {\n                    document.body.removeChild(div);\n                  }\n                }, 1000);\n                _context5.next = 26;\n                break;\n\n              case 24:\n                console.error('🔍 支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建支付订单失败');\n\n              case 26:\n                _context5.next = 33;\n                break;\n\n              case 28:\n                _context5.prev = 28;\n                _context5.t0 = _context5[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝网页支付失败:', _context5.t0);\n                this.$message.error('支付宝支付失败，请重试');\n\n              case 33:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 28]]);\n      }));\n\n      function handleAlipayPagePayment() {\n        return _handleAlipayPagePayment.apply(this, arguments);\n      }\n\n      return handleAlipayPagePayment;\n    }(),\n    // 处理支付宝扫码支付（与充值功能完全一致）\n    handleAlipayQrPayment: function () {\n      var _handleAlipayQrPayment = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var paymentData, payResponse, qrCode;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId);\n                this.$message.loading('正在生成支付二维码...', 0);\n                paymentData = {\n                  orderId: this.currentOrderId,\n                  amount: this.currentOrderAmount,\n                  subject: \"\\u667A\\u754CAigc\\u4F1A\\u5458 - \".concat(this.selectedPlan.name),\n                  body: \"\\u8D2D\\u4E70\".concat(this.selectedPlan.name, \"\\uFF0C\\u91D1\\u989D\\uFF1A\\xA5\").concat(this.currentOrderAmount)\n                };\n                console.log('🔍 发送扫码支付请求数据:', paymentData);\n                _context6.next = 7;\n                return this.$http.post('/api/alipay/createQrOrder', paymentData);\n\n              case 7:\n                payResponse = _context6.sent;\n                console.log('🔍 扫码支付响应:', payResponse);\n                this.$message.destroy();\n\n                if (!payResponse.success) {\n                  _context6.next = 19;\n                  break;\n                }\n\n                qrCode = payResponse.result.qrCode;\n                console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空');\n\n                if (qrCode) {\n                  _context6.next = 16;\n                  break;\n                }\n\n                this.$message.error('支付二维码生成失败');\n                return _context6.abrupt(\"return\");\n\n              case 16:\n                // 显示二维码支付弹窗\n                this.showQrCodeModal(qrCode);\n                _context6.next = 21;\n                break;\n\n              case 19:\n                console.error('🔍 扫码支付请求失败:', payResponse.message);\n                this.$message.error(payResponse.message || '创建扫码支付订单失败');\n\n              case 21:\n                _context6.next = 28;\n                break;\n\n              case 23:\n                _context6.prev = 23;\n                _context6.t0 = _context6[\"catch\"](0);\n                this.$message.destroy();\n                console.error('支付宝扫码支付失败:', _context6.t0);\n                this.$message.error('支付宝扫码支付失败，请重试');\n\n              case 28:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 23]]);\n      }));\n\n      function handleAlipayQrPayment() {\n        return _handleAlipayQrPayment.apply(this, arguments);\n      }\n\n      return handleAlipayQrPayment;\n    }(),\n    // 显示二维码弹窗\n    showQrCodeModal: function showQrCodeModal(qrCode) {\n      // 生成二维码图片URL\n      this.qrCodeUrl = \"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=\".concat(encodeURIComponent(qrCode));\n      this.showQrModal = true;\n      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount);\n      console.log('🔍 二维码URL:', this.qrCodeUrl); // 开始轮询支付状态\n\n      this.startPaymentStatusCheck();\n    },\n    // 关闭二维码弹窗\n    closeQrModal: function closeQrModal() {\n      this.showQrModal = false;\n      this.qrCodeUrl = '';\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n        this.paymentCheckTimer = null;\n      }\n    },\n    // 开始支付状态检查\n    startPaymentStatusCheck: function startPaymentStatusCheck() {\n      var _this = this;\n\n      if (this.paymentCheckTimer) {\n        clearInterval(this.paymentCheckTimer);\n      }\n\n      this.paymentCheckTimer = setInterval(function () {\n        _this.checkPaymentStatus();\n      }, 3000); // 每3秒检查一次\n    },\n    // 检查支付状态（与充值功能完全一致）\n    checkPaymentStatus: function () {\n      var _checkPaymentStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n        var _this2 = this;\n\n        var response;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                if (this.currentOrderId) {\n                  _context7.next = 2;\n                  break;\n                }\n\n                return _context7.abrupt(\"return\");\n\n              case 2:\n                _context7.prev = 2;\n                this.checkingStatus = true;\n                console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId);\n                _context7.next = 7;\n                return this.$http.get(\"/api/alipay/queryOrder/\".concat(this.currentOrderId));\n\n              case 7:\n                response = _context7.sent;\n                console.log('🔍 支付状态查询响应:', response);\n\n                if (response.success && response.result.status === 'TRADE_SUCCESS') {\n                  console.log('🔍 会员购买支付成功！');\n                  this.$message.success('支付成功！会员权益已生效');\n                  this.closeQrModal(); // 清除localStorage中的角色缓存，强制重新获取\n\n                  localStorage.removeItem('userRole');\n                  localStorage.removeItem('roleCode'); // 立即刷新QuickRecharge组件的用户信息\n\n                  if (this.$refs.quickRecharge && this.$refs.quickRecharge.loadUserInfo) {\n                    console.log('🔄 立即刷新QuickRecharge组件用户信息');\n                    this.$refs.quickRecharge.loadUserInfo();\n                  } // 强制刷新当前组件的用户角色（用于按钮文本更新）\n\n\n                  this.forceRefreshUserRole(); // 留在当前membership页面，刷新用户状态\n\n                  setTimeout(function () {\n                    _this2.$message.success('会员购买成功！页面即将刷新以更新会员状态'); // 刷新当前页面以更新会员状态和按钮显示\n\n\n                    window.location.reload();\n                  }, 2000);\n                } else {\n                  console.log('🔍 支付状态:', response.result && response.result.status || '未知');\n                }\n\n                _context7.next = 15;\n                break;\n\n              case 12:\n                _context7.prev = 12;\n                _context7.t0 = _context7[\"catch\"](2);\n                console.error('检查支付状态失败:', _context7.t0);\n\n              case 15:\n                _context7.prev = 15;\n                this.checkingStatus = false;\n                return _context7.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[2, 12, 15, 18]]);\n      }));\n\n      function checkPaymentStatus() {\n        return _checkPaymentStatus.apply(this, arguments);\n      }\n\n      return checkPaymentStatus;\n    }(),\n    // 处理充值成功事件\n    handleRechargeSuccess: function handleRechargeSuccess() {\n      this.$message.success('充值成功！您可以继续选择套餐'); // 可以在这里添加其他逻辑，比如刷新用户信息等\n    },\n    // 查询订单类型\n    getOrderType: function () {\n      var _getOrderType = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8(orderId) {\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n\n                if (!orderId.startsWith('RECHARGE_')) {\n                  _context8.next = 5;\n                  break;\n                }\n\n                return _context8.abrupt(\"return\", {\n                  orderType: 'recharge'\n                });\n\n              case 5:\n                if (!orderId.startsWith('ORDER_')) {\n                  _context8.next = 9;\n                  break;\n                }\n\n                return _context8.abrupt(\"return\", {\n                  orderType: 'membership'\n                });\n\n              case 9:\n                return _context8.abrupt(\"return\", {\n                  orderType: 'unknown'\n                });\n\n              case 10:\n                _context8.next = 16;\n                break;\n\n              case 12:\n                _context8.prev = 12;\n                _context8.t0 = _context8[\"catch\"](0);\n                console.error('查询订单类型失败:', _context8.t0);\n                throw _context8.t0;\n\n              case 16:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, null, [[0, 12]]);\n      }));\n\n      function getOrderType(_x2) {\n        return _getOrderType.apply(this, arguments);\n      }\n\n      return getOrderType;\n    }(),\n    // 强制刷新用户角色（用于立即更新按钮文本）\n    forceRefreshUserRole: function () {\n      var _forceRefreshUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                _context9.prev = 0;\n                console.log('🔄 强制刷新用户角色信息'); // 清除所有角色相关的缓存\n\n                localStorage.removeItem('userRole');\n                localStorage.removeItem('roleCode'); // 等待一小段时间，确保后端数据已更新\n\n                _context9.next = 6;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 1000);\n                });\n\n              case 6:\n                _context9.next = 8;\n                return this.loadUserRole();\n\n              case 8:\n                console.log('🔄 用户角色刷新完成，新角色:', this.userRole);\n                _context9.next = 14;\n                break;\n\n              case 11:\n                _context9.prev = 11;\n                _context9.t0 = _context9[\"catch\"](0);\n                console.error('强制刷新用户角色失败:', _context9.t0);\n\n              case 14:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this, [[0, 11]]);\n      }));\n\n      function forceRefreshUserRole() {\n        return _forceRefreshUserRole.apply(this, arguments);\n      }\n\n      return forceRefreshUserRole;\n    }(),\n    // 检查URL中的支付成功参数\n    checkPaymentSuccessFromUrl: function () {\n      var _checkPaymentSuccessFromUrl = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n        var urlParams, paymentSuccess, orderId, orderResponse, orderType, newUrl;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                urlParams = new URLSearchParams(window.location.search);\n                paymentSuccess = urlParams.get('paymentSuccess');\n                orderId = urlParams.get('orderId');\n\n                if (!(paymentSuccess === 'true' && orderId)) {\n                  _context10.next = 19;\n                  break;\n                }\n\n                console.log('🎉 检测到支付成功回调 - 订单号:', orderId);\n                _context10.prev = 5;\n                _context10.next = 8;\n                return this.getOrderType(orderId);\n\n              case 8:\n                orderResponse = _context10.sent;\n                orderType = orderResponse.orderType;\n\n                if (orderType === 'recharge') {\n                  // 充值成功\n                  this.$message.success('充值成功！您可以继续选择套餐');\n                } else if (orderType === 'membership') {\n                  // 会员购买成功\n                  this.$message.success('会员购买成功！权益已生效'); // 清除localStorage中的角色缓存，强制重新获取\n\n                  localStorage.removeItem('userRole');\n                  localStorage.removeItem('roleCode'); // 立即刷新QuickRecharge组件的用户信息\n\n                  if (this.$refs.quickRecharge && this.$refs.quickRecharge.loadUserInfo) {\n                    this.$refs.quickRecharge.loadUserInfo();\n                  } // 强制刷新当前组件的用户角色（用于按钮文本更新）\n\n\n                  this.forceRefreshUserRole();\n                } else {\n                  // 其他类型，根据订单号前缀判断（兜底逻辑）\n                  if (orderId.startsWith('RECHARGE_')) {\n                    this.$message.success('充值成功！您可以继续选择套餐');\n                  } else {\n                    this.$message.success('支付成功！');\n                  }\n                }\n\n                _context10.next = 17;\n                break;\n\n              case 13:\n                _context10.prev = 13;\n                _context10.t0 = _context10[\"catch\"](5);\n                console.error('查询订单类型失败:', _context10.t0); // 兜底逻辑：根据订单号前缀判断\n\n                if (orderId.startsWith('RECHARGE_')) {\n                  this.$message.success('充值成功！您可以继续选择套餐');\n                } else {\n                  this.$message.success('支付成功！');\n                }\n\n              case 17:\n                // 清除URL参数，避免重复提示\n                newUrl = window.location.pathname;\n                window.history.replaceState({}, document.title, newUrl);\n\n              case 19:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[5, 13]]);\n      }));\n\n      function checkPaymentSuccessFromUrl() {\n        return _checkPaymentSuccessFromUrl.apply(this, arguments);\n      }\n\n      return checkPaymentSuccessFromUrl;\n    }()\n  }\n};", null]}