/**
 * 邀请人管理工具
 * 使用sessionStorage存储，关闭浏览器会清除，刷新页面不会清除
 */

const REFERRAL_KEY = 'aigc_referral_code'

/**
 * 设置邀请人
 * @param {string} referralCode 邀请码
 */
export function setReferralCode(referralCode) {
  if (referralCode && referralCode.trim()) {
    const code = referralCode.trim()
    try {
      sessionStorage.setItem(REFERRAL_KEY, code)
      console.log('🔗 设置邀请人:', code)
    } catch (error) {
      console.error('🔗 设置邀请人失败:', error)
    }
  }
}

/**
 * 获取邀请人
 * @returns {string|null} 邀请码
 */
export function getReferralCode() {
  try {
    const code = sessionStorage.getItem(REFERRAL_KEY)
    console.log('🔗 获取邀请人:', code)
    return code
  } catch (error) {
    console.error('🔗 获取邀请人失败:', error)
    return null
  }
}

/**
 * 清除邀请人
 */
export function clearReferralCode() {
  sessionStorage.removeItem(REFERRAL_KEY)
  console.log('🔗 清除邀请人')
}

/**
 * 从URL中提取ref参数并设置邀请人
 * @param {string} url 当前URL
 */
export function handleReferralFromUrl(url) {
  try {
    const urlObj = new URL(url)
    const refParam = urlObj.searchParams.get('ref')

    if (refParam) {
      setReferralCode(refParam)
      console.log('🔗 从URL提取邀请码:', refParam)
      return refParam
    } else {
      // 如果URL中没有ref参数，检查sessionStorage中是否有邀请码
      const existingCode = getReferralCode()
      if (existingCode) {
        console.log('🔗 URL中无ref参数，使用已存储的邀请码:', existingCode)
        return existingCode
      }
    }
  } catch (error) {
    console.warn('🔗 解析URL邀请参数失败:', error)
  }

  return null
}

/**
 * 检查是否有有效的邀请人
 * @returns {boolean}
 */
export function hasReferralCode() {
  const code = getReferralCode()
  return !!(code && code.trim())
}


