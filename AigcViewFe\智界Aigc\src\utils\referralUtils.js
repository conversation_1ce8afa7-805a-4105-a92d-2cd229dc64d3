/**
 * 邀请人管理工具
 * 使用sessionStorage存储，关闭浏览器会清除，刷新页面不会清除
 */

const REFERRAL_KEY = 'aigc_referral_code'

/**
 * 设置邀请人
 * @param {string} referralCode 邀请码
 */
export function setReferralCode(referralCode) {
  if (referralCode && referralCode.trim()) {
    const code = referralCode.trim()
    try {
      sessionStorage.setItem(REFERRAL_KEY, code)
      // 添加时间戳，用于调试
      sessionStorage.setItem(REFERRAL_KEY + '_time', new Date().toISOString())
      console.log('🔗 设置邀请人:', code, '时间:', new Date().toLocaleString())
    } catch (error) {
      console.error('🔗 设置邀请人失败:', error)
    }
  }
}

/**
 * 获取邀请人
 * @returns {string|null} 邀请码
 */
export function getReferralCode() {
  try {
    const code = sessionStorage.getItem(REFERRAL_KEY)
    const time = sessionStorage.getItem(REFERRAL_KEY + '_time')
    console.log('🔗 获取邀请人:', code, '设置时间:', time)
    return code
  } catch (error) {
    console.error('🔗 获取邀请人失败:', error)
    return null
  }
}

/**
 * 清除邀请人
 */
export function clearReferralCode() {
  sessionStorage.removeItem(REFERRAL_KEY)
  console.log('🔗 清除邀请人')
}

/**
 * 从URL中提取ref参数并设置邀请人
 * @param {string} url 当前URL
 */
export function handleReferralFromUrl(url) {
  try {
    const urlObj = new URL(url)
    const refParam = urlObj.searchParams.get('ref')

    if (refParam) {
      setReferralCode(refParam)
      console.log('🔗 从URL提取邀请码:', refParam)
      return refParam
    } else {
      // 如果URL中没有ref参数，检查sessionStorage中是否有邀请码
      const existingCode = getReferralCode()
      if (existingCode) {
        console.log('🔗 URL中无ref参数，使用已存储的邀请码:', existingCode)
        return existingCode
      }
    }
  } catch (error) {
    console.warn('🔗 解析URL邀请参数失败:', error)
  }

  return null
}

/**
 * 检查是否有有效的邀请人
 * @returns {boolean}
 */
export function hasReferralCode() {
  const code = getReferralCode()
  return !!(code && code.trim())
}

/**
 * 调试函数：显示当前邀请人状态
 */
export function debugReferralStatus() {
  const code = getReferralCode()
  const time = sessionStorage.getItem(REFERRAL_KEY + '_time')
  const currentUrl = window.location.href

  console.log('🔗 邀请人调试信息:')
  console.log('  - 当前URL:', currentUrl)
  console.log('  - 存储的邀请码:', code)
  console.log('  - 设置时间:', time)
  console.log('  - sessionStorage支持:', typeof(Storage) !== "undefined")

  return {
    code,
    time,
    currentUrl,
    hasStorage: typeof(Storage) !== "undefined"
  }
}
