-- =============================================
-- 智界Aigc 会员订阅订单支持数据库脚本
-- 创建时间：2025-06-20
-- 版本：V1.0
-- 说明：扩展aicg_user_transaction表支持会员订阅订单
-- =============================================

-- 1. 扩展交易类型支持会员订阅
ALTER TABLE `aicg_user_transaction`
MODIFY COLUMN `transaction_type` tinyint(2) NOT NULL
COMMENT '交易类型：1-消费，2-充值，3-退款，4-兑换，5-会员订阅';

-- 2. 添加订单状态字段（用于区分订单状态）
ALTER TABLE `aicg_user_transaction`
ADD COLUMN `order_status` tinyint(2) DEFAULT 1
COMMENT '订单状态：1-待支付，2-已支付，3-已完成，4-已取消，5-已退款'
AFTER `description`;

-- 3. 添加订单类型字段（用于前端显示）
ALTER TABLE `aicg_user_transaction`
ADD COLUMN `order_type` varchar(20) DEFAULT NULL
COMMENT '订单类型：plugin-插件购买，membership-会员订阅，recharge-账户充值'
AFTER `order_status`;

-- 4. 添加产品信息字段（存储商品详情）
ALTER TABLE `aicg_user_transaction` 
ADD COLUMN `product_info` text 
COMMENT '产品信息JSON：存储商品详细信息' 
AFTER `order_type`;

-- 5. 为新字段添加索引
ALTER TABLE `aicg_user_transaction` 
ADD INDEX `idx_order_status` (`order_status`);

ALTER TABLE `aicg_user_transaction` 
ADD INDEX `idx_order_type` (`order_type`);

-- 6. 插入测试数据（会员订阅订单示例）
INSERT INTO `aicg_user_transaction` (
    `id`, 
    `user_id`, 
    `transaction_type`, 
    `amount`, 
    `balance_before`, 
    `balance_after`, 
    `description`, 
    `order_status`, 
    `order_type`, 
    `product_info`, 
    `related_order_id`, 
    `transaction_time`, 
    `create_time`
) VALUES (
    REPLACE(UUID(), '-', ''),
    'test_user_001',
    5,
    99.00,
    100.00,
    100.00,
    'VIP会员月卡订阅',
    1,
    'membership',
    '{"membershipLevel": 2, "duration": 1, "planName": "VIP会员月卡", "features": ["高级AI服务", "每日100次调用", "优先客服"], "originalPrice": 99.00, "discountPrice": 99.00}',
    CONCAT('ORDER_', DATE_FORMAT(NOW(), '%Y%m%d'), '_', LPAD(FLOOR(RAND() * 10000), 4, '0')),
    NOW(),
    NOW()
);

-- 7. 查询验证数据
SELECT 
    id,
    user_id,
    transaction_type,
    amount,
    order_status,
    order_type,
    description,
    product_info,
    related_order_id,
    transaction_time
FROM aicg_user_transaction 
WHERE transaction_type = 5 
ORDER BY transaction_time DESC 
LIMIT 5;

-- 8. 统计各类型订单数量
SELECT 
    order_type,
    order_status,
    COUNT(*) as count,
    SUM(amount) as total_amount
FROM aicg_user_transaction 
WHERE order_type IS NOT NULL
GROUP BY order_type, order_status
ORDER BY order_type, order_status;
