-- =============================================
-- 智界Aigc 会员订阅订单支持数据库脚本
-- 创建时间：2025-06-20
-- 版本：V1.0
-- 说明：扩展aicg_user_transaction表支持会员订阅订单
-- =============================================

-- 1. 扩展交易类型支持会员订阅
ALTER TABLE `aicg_user_transaction`
MODIFY COLUMN `transaction_type` tinyint(2) NOT NULL
COMMENT '交易类型：1-消费，2-充值，3-退款，4-兑换，5-会员订阅';

-- 2. 添加订单状态字段（用于区分订单状态）
ALTER TABLE `aicg_user_transaction`
ADD COLUMN `order_status` tinyint(2) DEFAULT 1
COMMENT '订单状态：1-待支付，2-已支付，3-已完成，4-已取消，5-已退款'
AFTER `description`;

-- 3. 添加订单类型字段（用于前端显示）
ALTER TABLE `aicg_user_transaction`
ADD COLUMN `order_type` varchar(20) DEFAULT NULL
COMMENT '订单类型：plugin-插件购买，membership-会员订阅，recharge-账户充值'
AFTER `order_status`;

-- 4. 添加产品信息字段（存储商品详情）
ALTER TABLE `aicg_user_transaction`
ADD COLUMN `product_info` text
COMMENT '产品信息JSON：存储商品详细信息'
AFTER `order_type`;

-- 5. 为新字段添加索引
ALTER TABLE `aicg_user_transaction`
ADD INDEX `idx_order_status` (`order_status`);

ALTER TABLE `aicg_user_transaction`
ADD INDEX `idx_order_type` (`order_type`);
