{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=template&id=6a30e1a0&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753825911222}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('WebsitePage',[_c('div',{staticClass:\"membership-container\"},[_c('div',{staticClass:\"simple-header\"},[_c('h1',{staticClass:\"simple-title\"},[_vm._v(\"订阅会员\")]),_c('p',{staticClass:\"simple-subtitle\"},[_vm._v(\"成为会员享受更多特权，解锁高级功能\")])]),_c('section',{staticClass:\"recharge-section\"},[_c('div',{staticClass:\"container\"},[_c('QuickRecharge',{on:{\"recharge-success\":_vm.handleRechargeSuccess}})],1)]),_c('section',{staticClass:\"plans-section\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"plans-grid\"},_vm._l((_vm.plans),function(plan){return _c('div',{key:plan.id,staticClass:\"plan-card\",class:{ 'featured': plan.featured }},[(plan.featured)?_c('div',{staticClass:\"plan-badge\"},[_vm._v(\"推荐\")]):_vm._e(),_c('div',{staticClass:\"plan-header\"},[_c('h3',{staticClass:\"plan-name\"},[_vm._v(_vm._s(plan.name))]),_c('div',{staticClass:\"plan-price\"},[(plan.originalPrice)?_c('div',{staticClass:\"original-price\"},[_c('span',{staticClass:\"original-price-text\"},[_vm._v(\"原价：¥\"+_vm._s(plan.originalPrice))]),_c('span',{staticClass:\"discount-badge\"},[_vm._v(_vm._s(plan.discountText))])]):_vm._e(),_c('div',{staticClass:\"current-price\"},[_c('span',{staticClass:\"price-symbol\"},[_vm._v(\"¥\")]),_c('span',{staticClass:\"price-amount\"},[_vm._v(_vm._s(plan.price))]),_c('span',{staticClass:\"price-period\"},[_vm._v(\"/\"+_vm._s(plan.period))])]),(plan.saveAmount)?_c('div',{staticClass:\"save-amount\"},[_vm._v(\"\\n                  立省¥\"+_vm._s(plan.saveAmount)+\"\\n                \")]):_vm._e()]),_c('p',{staticClass:\"plan-description\"},[_vm._v(_vm._s(plan.description))])]),_c('div',{staticClass:\"plan-features\"},_vm._l((plan.features),function(feature){return _c('div',{key:feature.text || feature,staticClass:\"feature-item\",class:{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }},[_c('a-icon',{class:{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive },attrs:{\"type\":feature.disabled ? 'close' : 'check'}}),_c('span',{staticClass:\"feature-text\",domProps:{\"innerHTML\":_vm._s(feature.text || feature)}}),(feature.exclusive)?_c('span',{staticClass:\"exclusive-badge\"},[_vm._v(\"专属\")]):_vm._e(),(feature.disabled)?_c('span',{staticClass:\"disabled-text\"},[_vm._v(\"（SVIP专享）\")]):_vm._e()],1)}),0),_c('button',{staticClass:\"btn-subscribe\",class:{ 'featured': plan.featured },on:{\"click\":function($event){return _vm.handleSubscribe(plan)}}},[_vm._v(\"\\n              \"+_vm._s(_vm.getButtonText(plan))+\"\\n            \")]),(_vm.showUpgradeNotice(plan))?_c('div',{staticClass:\"upgrade-notice\"},[_c('i',{staticClass:\"icon-info\"}),_c('span',[_vm._v(_vm._s(_vm.getUpgradeNoticeText(plan)))])]):_vm._e()])}),0)])])]),_c('a-modal',{attrs:{\"title\":\"选择支付方式\",\"visible\":_vm.showPaymentModal,\"footer\":null,\"width\":\"500px\"},on:{\"cancel\":function($event){_vm.showPaymentModal = false}}},[_c('div',{staticClass:\"payment-modal-content\"},[_c('div',{staticClass:\"order-info\"},[_c('h4',[_vm._v(\"订单信息\")]),_c('div',{staticClass:\"order-details\"},[_c('div',{staticClass:\"order-item\"},[_c('span',[_vm._v(\"套餐名称：\")]),_c('span',[_vm._v(_vm._s(_vm.selectedPlan ? _vm.selectedPlan.name : ''))])]),_c('div',{staticClass:\"order-item\"},[_c('span',[_vm._v(\"支付金额：\")]),_c('span',{staticClass:\"amount\"},[_vm._v(\"¥\"+_vm._s(_vm.currentOrderAmount))])])])]),_c('div',{staticClass:\"payment-methods\"},[_c('h4',[_vm._v(\"支付方式\")]),_c('a-radio-group',{attrs:{\"size\":\"large\"},model:{value:(_vm.selectedPaymentMethod),callback:function ($$v) {_vm.selectedPaymentMethod=$$v},expression:\"selectedPaymentMethod\"}},[_c('a-radio-button',{attrs:{\"value\":\"alipay-qr\"}},[_c('i',{staticClass:\"anticon anticon-qrcode\"}),_vm._v(\"\\n            支付宝扫码\\n          \")]),_c('a-radio-button',{attrs:{\"value\":\"alipay-page\"}},[_c('i',{staticClass:\"anticon anticon-alipay\"}),_vm._v(\"\\n            支付宝网页\\n          \")])],1)],1),_c('div',{staticClass:\"payment-actions\"},[_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"loading\":_vm.paymentLoading,\"block\":\"\"},on:{\"click\":_vm.handlePayment}},[_vm._v(\"\\n          确认支付\\n        \")])],1)])]),_c('a-modal',{attrs:{\"title\":\"扫码支付\",\"visible\":_vm.showQrModal,\"footer\":null,\"width\":\"400px\"},on:{\"cancel\":_vm.closeQrModal}},[_c('div',{staticClass:\"qr-payment-content\"},[_c('div',{staticClass:\"qr-code-container\"},[(_vm.qrCodeUrl)?_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.qrCodeUrl,\"alt\":\"支付二维码\"}})]):_c('div',{staticClass:\"qr-loading\"},[_c('a-spin',{attrs:{\"size\":\"large\"}}),_c('p',[_vm._v(\"正在生成二维码...\")])],1)]),_c('div',{staticClass:\"qr-info\"},[_c('p',{staticClass:\"qr-amount\"},[_vm._v(\"支付金额：¥\"+_vm._s(_vm.currentOrderAmount))]),_c('p',{staticClass:\"qr-tip\"},[_vm._v(\"请使用支付宝扫码支付\")])]),_c('div',{staticClass:\"qr-status\"},[_c('a-button',{attrs:{\"loading\":_vm.checkingStatus},on:{\"click\":_vm.checkPaymentStatus}},[_vm._v(\"\\n          检查支付状态\\n        \")])],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}