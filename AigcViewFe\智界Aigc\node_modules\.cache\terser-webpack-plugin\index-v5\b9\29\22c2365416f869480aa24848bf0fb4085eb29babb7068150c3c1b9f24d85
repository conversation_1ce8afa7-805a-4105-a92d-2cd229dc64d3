
3313ab5c95b17c302e34650c99dec8aa0bc736d6	{"key":"{\"terser\":\"4.8.0\",\"node_version\":\"v14.18.0\",\"terser-webpack-plugin\":\"1.4.4\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true,\"drop_console\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"6b52b63e5369b10131629e070c98d3ca\"}","integrity":"sha512-ntBMYOUDbNI9Iwko8l6MnD2tOAohw3ABIZKcTk9lafvOJu0hmuc0IwbO8YoMsnRh1mm+YZ2LJMPe0Z0u4xBFxw==","time":1753833283535,"size":157871}