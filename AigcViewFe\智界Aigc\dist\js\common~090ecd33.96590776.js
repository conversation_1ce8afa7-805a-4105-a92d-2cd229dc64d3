(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~090ecd33"],{"013f":function(t,e,a){},"0759":function(t,e,a){},"1d6a":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"main"},[n("a-form",{staticStyle:{"max-width":"500px",margin:"40px auto 0"},attrs:{form:t.form},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.nextStep(e)}}},[n("a-form-item",[n("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["username",t.validatorRules.username],expression:"['username',validatorRules.username]"}],attrs:{size:"large",type:"text",autocomplete:"false",placeholder:"请输入用户账号或手机号"}},[n("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),n("a-row",{attrs:{gutter:0}},[n("a-col",{attrs:{span:14}},[n("a-form-item",[n("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["inputCode",t.validatorRules.inputCode],expression:"['inputCode',validatorRules.inputCode]"}],attrs:{size:"large",type:"text",placeholder:"请输入验证码"},on:{change:t.inputCodeChange}},[t.inputCodeContent==t.verifiedCode?n("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"smile"},slot:"prefix"}):n("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"frown"},slot:"prefix"})],1)],1)],1),n("a-col",{staticStyle:{"text-align":"right"},attrs:{span:10}},[t.requestCodeSuccess?n("img",{staticStyle:{"margin-top":"2px"},attrs:{src:t.randCodeImage},on:{click:t.handleChangeCheckCode}}):n("img",{staticStyle:{"margin-top":"2px"},attrs:{src:a("d5ac")},on:{click:t.handleChangeCheckCode}})])],1),n("a-form-item",{attrs:{wrapperCol:{span:19,offset:5}}},[n("router-link",{staticStyle:{float:"left","line-height":"40px"},attrs:{to:{name:"login"}}},[t._v("使用已有账户登录")]),n("a-button",{attrs:{type:"primary"},on:{click:t.nextStep}},[t._v("下一步")])],1)],1)],1)},s=[],i=a("0fea"),r=a("4ec3"),o={name:"Step1",data:function(){return{form:this.$form.createForm(this),inputCodeContent:"",inputCodeNull:!0,verifiedCode:"",validatorRules:{username:{rules:[{required:!1},{validator:this.validateInputUsername}]},inputCode:{rules:[{required:!0,message:"请输入验证码!"}]}},randCodeImage:"",requestCodeSuccess:!0,currdatetime:""}},created:function(){this.handleChangeCheckCode()},methods:{handleChangeCheckCode:function(){var t=this;this.currdatetime=(new Date).getTime(),Object(i["c"])("/sys/randomImage/".concat(this.currdatetime)).then((function(e){e.success?(t.randCodeImage=e.result,t.requestCodeSuccess=!0):(t.$message.error(e.message),t.requestCodeSuccess=!1)})).catch((function(){t.requestCodeSuccess=!1}))},nextStep:function(){var t=this;this.form.validateFields((function(e,a){if(!e){var n=!1,s={},r=/^[1-9]\d*$|^0$/,o=a.username;1==r.test(o)?(s.phone=o,n=!0):s.username=o,t.validateInputCode().then((function(){Object(i["c"])("/sys/user/querySysUser",s).then((function(e){if(e.success){var a={username:e.result.username,phone:e.result.phone,isPhone:n};setTimeout((function(){t.$emit("nextStep",a)}))}}))}))}}))},validateInputCode:function(){var t=this;return new Promise((function(e,a){Object(i["i"])("/sys/checkCaptcha",{captcha:t.inputCodeContent,checkKey:t.currdatetime}).then((function(n){n.success?e():(t.$message.error(n.message),a())}))}))},inputCodeChange:function(t){this.inputCodeContent=t.target.value,t.target.value&&0!=t.target.value?(this.inputCodeContent=this.inputCodeContent.toLowerCase(),this.inputCodeNull=!1):this.inputCodeNull=!0},generateCode:function(t){this.verifiedCode=t.toLowerCase()},validateInputUsername:function(t,e,a){var n=/^[0-9]+.?[0-9]*/;if(e||a("请输入用户名和手机号！"),n.test(e)){var s={phone:e};Object(r["h"])(s).then((function(t){t.success?a("用户名不存在!"):a()}))}else{s={username:e};Object(r["h"])(s).then((function(t){t.success?a("用户名不存在!"):a()}))}}}},c=o,l=a("2877"),u=Object(l["a"])(c,n,s,!1,null,"5a355e54",null);e["default"]=u.exports},"2ea5":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-modal",{attrs:{title:t.title,width:450,visible:t.visible,closable:!1,maskClosable:!1}},[a("template",{slot:"footer"},[a("a-button",{attrs:{type:"primary"},on:{click:t.selectOk}},[t._v("确认")])],1),a("a-form-model",[t.isMultiTenant?a("a-form-model-item",{staticStyle:{"margin-bottom":"10px"},attrs:{labelCol:{span:4},wrapperCol:{span:20},"validate-status":t.validate_status1}},[a("a-tooltip",{attrs:{placement:"topLeft"}},[a("template",{slot:"title"},[a("span",[t._v("您有多个租户，请选择登录租户")])]),a("a-avatar",{staticStyle:{backgroundColor:"#87d068"},attrs:{icon:"gold"}})],2),a("a-select",{class:{"valid-error":"error"==t.validate_status1},staticStyle:{"margin-left":"10px",width:"80%"},attrs:{placeholder:"请选择登录租户"},on:{change:t.handleTenantChange}},[a("a-icon",{attrs:{slot:"suffixIcon",type:"gold"},slot:"suffixIcon"}),t._l(t.tenantList,(function(e){return a("a-select-option",{key:e.id,attrs:{value:e.id}},[t._v("\n          "+t._s(e.name)+"\n        ")])}))],2)],1):t._e(),t.isMultiDepart?a("a-form-model-item",{staticStyle:{"margin-bottom":"10px"},attrs:{labelCol:{span:4},wrapperCol:{span:20},"validate-status":t.validate_status2}},[a("a-tooltip",{attrs:{placement:"topLeft"}},[a("template",{slot:"title"},[a("span",[t._v("您有多个部门，请选择登录部门")])]),a("a-avatar",{staticStyle:{backgroundColor:"rgb(104, 208, 203)"},attrs:{icon:"gold"}})],2),a("a-select",{class:{"valid-error":"error"==t.validate_status2},staticStyle:{"margin-left":"10px",width:"80%"},attrs:{placeholder:"请选择登录部门"},on:{change:t.handleDepartChange}},[a("a-icon",{attrs:{slot:"suffixIcon",type:"gold"},slot:"suffixIcon"}),t._l(t.departList,(function(e){return a("a-select-option",{key:e.id,attrs:{value:e.orgCode}},[t._v("\n          "+t._s(e.departName)+"\n        ")])}))],2)],1):t._e()],1)],2)},s=[],i=a("2b0e"),r=a("0fea"),o=a("9fb0"),c={name:"LoginSelectTenant",data:function(){return{visible:!1,isMultiDepart:!1,departList:[],isMultiTenant:!1,tenantList:[],username:"",orgCode:"",tenant_id:"",validate_status1:"",validate_status2:""}},computed:{title:function(){return this.isMultiDepart&&this.isMultiTenant?"请选择租户和部门":this.isMultiDepart&&!this.isMultiTenant?"请选择部门":!this.isMultiDepart&&this.isMultiTenant?"请选择租户":void 0}},methods:{clear:function(){this.departList=[],this.tenantList=[],this.visible=!1,this.validate_status1="",this.validate_status2=""},bizDepart:function(t){var e=t.multi_depart;0==e?(this.$notification.warn({message:"提示",description:"您尚未归属部门,请确认账号信息",duration:3}),this.isMultiDepart=!1):2==e?(this.visible=!0,this.isMultiDepart=!0,this.departList=t.departs):this.isMultiDepart=!1},bizTenantList:function(t){var e=t.tenantList;Array.isArray(e)&&(0===e.length?this.isMultiTenant=!1:1===e.length?(this.tenant_id=e[0].id,this.isMultiTenant=!1):(this.visible=!0,this.isMultiTenant=!0,this.tenantList=e))},show:function(t){this.clear(),this.bizDepart(t);var e=i["default"].ls.get(o["u"]);this.username=e.username,this.bizTenantList(t),!1===this.visible&&(this.$store.dispatch("saveTenant",this.tenant_id),this.$emit("success"))},requestFailed:function(t){this.$notification["error"]({message:"登录失败",description:((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试",duration:4}),this.loginBtn=!1},departResolve:function(){var t=this;return new Promise((function(e,a){if(!1===t.isMultiDepart)e();else{var n={orgCode:t.orgCode,username:t.username};Object(r["j"])("/sys/selectDepart",n).then((function(n){if(n.success){var s=n.result.userInfo;i["default"].ls.set(o["u"],s,6048e5),t.$store.commit("SET_INFO",s),e()}else t.requestFailed(n),t.$store.dispatch("Logout"),a()}))}}))},selectOk:function(){var t=this;return this.isMultiTenant&&!this.tenant_id?(this.validate_status1="error",!1):this.isMultiDepart&&!this.orgCode?(this.validate_status2="error",!1):void this.departResolve().then((function(){t.$store.dispatch("saveTenant",t.tenant_id),t.isMultiTenant,t.$emit("success")})).catch((function(){}))},handleTenantChange:function(t){this.validate_status1="",this.tenant_id=t},handleDepartChange:function(t){this.validate_status2="",this.orgCode=t}}},l=c,u=a("2877"),d=Object(u["a"])(l,n,s,!1,null,"dab4d038",null);e["default"]=d.exports},3507:function(t,e,a){},5193:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{attrs:{id:"loader-wrapper"}},[a("div",{attrs:{id:"loader"}}),a("div",{staticClass:"loader-section section-left"}),a("div",{staticClass:"loader-section section-right"}),a("div",{staticClass:"load_title"},[t._v("正在登录 智界AIGC，请耐心等待")])])])}],i=a("2f62"),r=a("ca00"),o=a("9fb0");function c(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function l(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?c(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var d={name:"OAuth2Login",data:function(){return{env:{thirdApp:!1,wxWork:!1,dingtalk:!1}}},beforeCreate:function(){Object(r["h"])()||this.$router.replace({path:"/user/login"})},created:function(){this.checkEnv(),this.doOAuth2Login()},methods:l(l({},Object(i["b"])(["ThirdLogin"])),{},{checkEnv:function(){/wxwork/i.test(navigator.userAgent)&&(this.env.thirdApp=!0,this.env.wxWork=!0),/dingtalk/i.test(navigator.userAgent)&&(this.env.thirdApp=!0,this.env.dingtalk=!0)},doOAuth2Login:function(){if(this.env.thirdApp)if(this.$route.query.oauth2LoginToken){this.thirdType=this.$route.query.thirdType;var t=this.$route.query.oauth2LoginToken;this.doThirdLogin(t)}else this.env.wxWork?this.doWechatEnterpriseOAuth2Login():this.env.dingtalk&&this.doDingTalkOAuth2Login()},doThirdLogin:function(t){var e=this,a={};a.thirdType=this.thirdType,a.token=t,this.ThirdLogin(a).then((function(t){t.success?e.loginSuccess():e.requestFailed(t)}))},loginSuccess:function(){this.$router.replace({path:o["n"]}),this.$notification.success({message:"欢迎",description:"".concat(Object(r["o"])(),"，欢迎回来")})},requestFailed:function(t){this.$error({title:"登录失败",content:((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试",okText:"重新登陆",onOk:function(){window.location.reload()},onCancel:function(){window.location.reload()}})},doWechatEnterpriseOAuth2Login:function(){this.sysOAuth2Login("wechat_enterprise")},doDingTalkOAuth2Login:function(){this.sysOAuth2Login("dingtalk")},sysOAuth2Login:function(t){var e="".concat(window._CONFIG["domianURL"],"/sys/thirdLogin/oauth2/").concat(t,"/login");e+="?state=".concat(encodeURIComponent(window.location.origin)),window.location.href=e}})},p=d,f=a("2877"),m=Object(f["a"])(p,n,s,!1,null,"00fca87c",null);e["default"]=m.exports},"5cfa":function(t,e,a){},6928:function(t,e,a){},7103:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form-model",{ref:"form",attrs:{model:t.model,rules:t.validatorRules}},[a("a-form-model-item",{attrs:{required:"",prop:"mobile"}},[a("a-input",{attrs:{size:"large",type:"text",placeholder:"请输入手机号"},model:{value:t.model.mobile,callback:function(e){t.$set(t.model,"mobile",e)},expression:"model.mobile"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mobile"},slot:"prefix"})],1)],1),a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:16}},[a("a-form-model-item",{attrs:{required:"",prop:"captcha"}},[a("a-input",{attrs:{size:"large",type:"text",placeholder:"请输入验证码"},model:{value:t.model.captcha,callback:function(e){t.$set(t.model,"captcha",e)},expression:"model.captcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{staticClass:"getCaptcha",attrs:{tabindex:"-1",disabled:t.state.smsSendBtn},domProps:{textContent:t._s(t.state.smsSendBtn?t.state.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getCaptcha(e)}}})],1)],1)],1)],1)},s=[],i=a("0fea"),r=a("2f62");function o(t,e){var a;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(a=c(t))||e&&t&&"number"===typeof t.length){a&&(t=a);var n=0,s=function(){};return{s:s,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,r=!0,o=!1;return{s:function(){a=t[Symbol.iterator]()},n:function(){var t=a.next();return r=t.done,t},e:function(t){o=!0,i=t},f:function(){try{r||null==a.return||a.return()}finally{if(o)throw i}}}}function c(t,e){if(t){if("string"===typeof t)return l(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?l(t,e):void 0}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function d(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach((function(e){p(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function p(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var f={name:"LoginPhone",data:function(){return{model:{mobile:"",captcha:""},state:{time:60,smsSendBtn:!1},validatorRules:{mobile:[{required:!0,message:"请输入手机号码!"},{validator:this.validateMobile}],captcha:[{required:!0,message:"请输入验证码!"}]}}},methods:d(d({},Object(r["b"])(["PhoneLogin"])),{},{handleLogin:function(t){var e=this;this.validateFields(["mobile","captcha"],(function(a){if(a)e.$emit("validateFail");else{var n={mobile:e.model.mobile,captcha:e.model.captcha,remember_me:t};e.PhoneLogin(n).then((function(t){e.$emit("success",t.result)})).catch((function(t){e.$emit("fail",t)}))}}))},validateMobile:function(t,e,a){!e||new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(e)?a():a("您的手机号码格式不正确!")},getCaptcha:function(t){t.preventDefault();var e=this;e.validateFields(["mobile"],(function(t){if(!t){e.state.smsSendBtn=!0;var a=window.setInterval((function(){e.state.time--<=0&&(e.state.time=60,e.state.smsSendBtn=!1,window.clearInterval(a))}),1e3),n=e.$message.loading("验证码发送中..",0),s={};s.mobile=e.model.mobile,s.smsmode="0",Object(i["i"])("/sys/sms",s).then((function(t){t.success||(setTimeout(n,0),e.cmsFailed(t.message)),setTimeout(n,500)})).catch((function(t){setTimeout(n,1),clearInterval(a),e.state.time=60,e.state.smsSendBtn=!1,e.requestFailed(t)}))}}))},cmsFailed:function(t){this.$notification["error"]({message:"获取验证码失败",description:t,duration:4})},validateFields:function(t,e){var a,n=this,s=[],i=o(t);try{var r=function(){var t=a.value,e=new Promise((function(e,a){n.$refs["form"].validateField(t,(function(t){t?a(t):e()}))}));s.push(e)};for(i.s();!(a=i.n()).done;)r()}catch(c){i.e(c)}finally{i.f()}Promise.all(s).then((function(){e()})).catch((function(t){e(t)}))}})},m=f,h=(a("97ad"),a("2877")),g=Object(h["a"])(m,n,s,!1,null,"42b877c5",null);e["default"]=g.exports},8859:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{staticStyle:{width:"130%","text-align":"center","margin-left":"-10%"},attrs:{bordered:!1}},[a("a-steps",{staticClass:"steps",attrs:{current:t.currentTab}},[a("a-step",{attrs:{title:"手机验证"}}),a("a-step",{attrs:{title:"更改密码"}}),a("a-step",{attrs:{title:"完成"}})],1),a("div",{staticClass:"content"},[0===t.currentTab?a("step2",{on:{nextStep:t.nextStep}}):t._e(),1===t.currentTab?a("step3",{attrs:{userList:t.userList},on:{nextStep:t.nextStep,prevStep:t.prevStep}}):t._e(),2===t.currentTab?a("step4",{attrs:{userList:t.userList},on:{prevStep:t.prevStep,finish:t.finish}}):t._e()],1)],1)},s=[],i=a("a73d"),r=a("977f"),o=a("b23d"),c={name:"Alteration",components:{Step2:i["default"],Step3:r["default"],Step4:o["default"]},data:function(){return{description:"将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。",currentTab:0,userList:{},form:null}},methods:{nextStep:function(t){this.userList=t,this.currentTab<4&&(this.currentTab+=1)},prevStep:function(t){this.userList=t,this.currentTab>0&&(this.currentTab-=1)},finish:function(){this.currentTab=0}}},l=c,u=(a("c4f8"),a("2877")),d=Object(u["a"])(l,n,s,!1,null,"8733cc9e",null);e["default"]=d.exports},"8ef0":function(t,e,a){"use strict";var n=a("013f"),s=a.n(n);s.a},"977f":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form-model",{ref:"form",staticClass:"password-retrieval-form",attrs:{model:t.model,rules:t.validatorRules}},[a("a-form-model-item",t._b({attrs:{label:"账号名"}},"a-form-model-item",t.layout,!1),[a("a-input",{attrs:{type:"text",value:t.accountName,disabled:""}})],1),a("a-form-model-item",t._b({staticClass:"stepFormText",attrs:{prop:"password",label:"新密码"}},"a-form-model-item",t.layout,!1),[a("a-input",{attrs:{type:"password",autocomplete:"false"},model:{value:t.model.password,callback:function(e){t.$set(t.model,"password",e)},expression:"model.password"}})],1),a("a-form-model-item",t._b({staticClass:"stepFormText",attrs:{prop:"confirmPassword",label:"确认密码"}},"a-form-model-item",t.layout,!1),[a("a-input",{attrs:{type:"password",autocomplete:"false"},model:{value:t.model.confirmPassword,callback:function(e){t.$set(t.model,"confirmPassword",e)},expression:"model.confirmPassword"}})],1),a("a-form-model-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:t.prevStep}},[t._v("上一步")]),a("a-button",{staticStyle:{"margin-left":"20px"},attrs:{loading:t.loading,type:"primary"},on:{click:t.nextStep}},[t._v("提交")])],1)],1)],1)},s=[],i=a("0fea"),r={name:"Step3",props:["userList"],data:function(){return{model:{},layout:{labelCol:{span:5},wrapperCol:{span:19}},loading:!1,accountName:this.userList.username,validatorRules:{password:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!!"}],confirmPassword:[{required:!0,message:"密码不能为空!"},{validator:this.handlePasswordCheck}]}}},methods:{nextStep:function(){var t=this;t.loading=!0,t.$refs["form"].validate((function(e){if(!0===e){var a={username:t.userList.username,password:t.model.password,smscode:t.userList.smscode,phone:t.userList.phone};Object(i["c"])("/sys/user/passwordChange",a).then((function(e){if(e.success){var a={username:t.userList.username};setTimeout((function(){t.$emit("nextStep",a)}),1500)}else t.passwordFailed(e.message),t.loading=!1}))}else t.loading=!1}))},prevStep:function(){this.$emit("prevStep",this.userList)},handlePasswordCheck:function(t,e,a){var n=this.model["password"];e&&n&&e.trim()!==n.trim()&&a(new Error("两次密码不一致")),a()},passwordFailed:function(t){this.$notification["error"]({message:"更改密码失败",description:t,duration:4})}}},o=r,c=(a("f55e"),a("2877")),l=Object(c["a"])(o,n,s,!1,null,"3c0160d7",null);e["default"]=l.exports},"97ad":function(t,e,a){"use strict";var n=a("6928"),s=a.n(n);s.a},a175:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("a-form-model",{ref:"form",attrs:{model:t.model,rules:t.validatorRules}},[n("a-form-model-item",{attrs:{required:"",prop:"username"}},[n("a-input",{attrs:{size:"large",placeholder:"请输入帐户名"},model:{value:t.model.username,callback:function(e){t.$set(t.model,"username",e)},expression:"model.username"}},[n("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"user"},slot:"prefix"})],1)],1),n("a-form-model-item",{attrs:{required:"",prop:"password"}},[n("a-input",{attrs:{size:"large",type:"password",autocomplete:"false",placeholder:"请输入密码"},model:{value:t.model.password,callback:function(e){t.$set(t.model,"password",e)},expression:"model.password"}},[n("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),n("a-row",{attrs:{gutter:0}},[n("a-col",{attrs:{span:16}},[n("a-form-model-item",{attrs:{required:"",prop:"inputCode"}},[n("a-input",{attrs:{size:"large",type:"text",placeholder:"请输入验证码"},model:{value:t.model.inputCode,callback:function(e){t.$set(t.model,"inputCode",e)},expression:"model.inputCode"}},[n("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"smile"},slot:"prefix"})],1)],1)],1),n("a-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[t.requestCodeSuccess?n("img",{staticStyle:{"margin-top":"2px"},attrs:{src:t.randCodeImage},on:{click:t.handleChangeCheckCode}}):n("img",{staticStyle:{"margin-top":"2px"},attrs:{src:a("d5ac")},on:{click:t.handleChangeCheckCode}})])],1)],1)],1)},s=[],i=a("0fea"),r=a("2b0e"),o=a("2f62"),c=a("9da4"),l=a("9fb0");function u(t,e){var a;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(a=d(t))||e&&t&&"number"===typeof t.length){a&&(t=a);var n=0,s=function(){};return{s:s,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,r=!0,o=!1;return{s:function(){a=t[Symbol.iterator]()},n:function(){var t=a.next();return r=t.done,t},e:function(t){o=!0,i=t},f:function(){try{r||null==a.return||a.return()}finally{if(o)throw i}}}}function d(t,e){if(t){if("string"===typeof t)return p(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}function f(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function m(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?f(Object(a),!0).forEach((function(e){h(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):f(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function h(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var g={name:"LoginAccount",data:function(){return{requestCodeSuccess:!1,randCodeImage:"",currdatetime:"",loginType:0,model:{username:"",password:"",inputCode:""},validatorRules:{username:[{required:!0,message:"请输入用户名!"},{validator:this.handleUsernameOrEmail}],password:[{required:!0,message:"请输入密码!",validator:"click"}],inputCode:[{required:!0,message:"请输入验证码!"}]},encryptedString:{key:"",iv:""}}},created:function(){this.handleChangeCheckCode(),this.getEncrypte()},methods:m(m({},Object(o["b"])(["Login"])),{},{getEncrypte:function(){var t=this,e=r["default"].ls.get(l["l"]);null==e?Object(c["b"])().then((function(e){t.encryptedString=e})):this.encryptedString=e},handleChangeCheckCode:function(){var t=this;this.currdatetime=(new Date).getTime(),this.model.inputCode="",Object(i["c"])("/sys/randomImage/".concat(this.currdatetime)).then((function(e){e.success?(t.randCodeImage=e.result,t.requestCodeSuccess=!0):(t.$message.error(e.message),t.requestCodeSuccess=!1)})).catch((function(){t.requestCodeSuccess=!1}))},handleUsernameOrEmail:function(t,e,a){var n=/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;n.test(e)?this.loginType=0:this.loginType=1,a()},validateFields:function(t,e){var a,n=this,s=[],i=u(t);try{var r=function(){var t=a.value,e=new Promise((function(e,a){n.$refs["form"].validateField(t,(function(t){t?a(t):e()}))}));s.push(e)};for(i.s();!(a=i.n()).done;)r()}catch(o){i.e(o)}finally{i.f()}Promise.all(s).then((function(){e()})).catch((function(t){e(t)}))},acceptUsername:function(t){this.model["username"]=t},handleLogin:function(t){var e=this;this.validateFields(["username","password","inputCode"],(function(a){if(a)e.$emit("validateFail");else{var n=Object(c["a"])(e.model.username,e.encryptedString.key,e.encryptedString.iv),s=Object(c["a"])(e.model.password,e.encryptedString.key,e.encryptedString.iv),i={username:n,password:s,captcha:e.model.inputCode,checkKey:e.currdatetime,remember_me:t,loginType:"admin"};e.Login(i).then((function(t){localStorage.setItem("username",e.model.username),e.$emit("success",t.result)})).catch((function(t){e.$emit("fail",t)}))}}))}})},v=g,b=a("2877"),y=Object(b["a"])(v,n,s,!1,null,"ad6b0cfa",null);e["default"]=y.exports},a2cc:function(t,e,a){},a73d:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form-model",{ref:"form",staticClass:"password-retrieval-form",attrs:{model:t.model,rules:t.validatorRules},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.nextStep(e)}}},[a("a-form-model-item",{attrs:{label:"手机",required:"",prop:"phone",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:20}},[a("a-input",{attrs:{type:"text",autocomplete:"false",placeholder:"请输入手机号"},model:{value:t.model.phone,callback:function(e){t.$set(t.model,"phone",e)},expression:"model.phone"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"phone"},slot:"prefix"})],1)],1)],1)],1),t.show?a("a-form-model-item",{attrs:{required:"",prop:"captcha",label:"验证码",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:12}},[a("a-input",{attrs:{type:"text",placeholder:"手机短信验证码"},model:{value:t.model.captcha,callback:function(e){t.$set(t.model,"captcha",e)},expression:"model.captcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"code"},slot:"prefix"})],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{attrs:{tabindex:"-1",size:"default",disabled:t.state.smsSendBtn},domProps:{textContent:t._s(t.state.smsSendBtn?t.state.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getCaptcha(e)}}})],1)],1)],1):t._e(),a("a-form-model-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("router-link",{staticStyle:{float:"left","line-height":"40px"},attrs:{to:{name:"login"}}},[t._v("使用已有账户登录")]),a("a-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:t.nextStep}},[t._v("下一步")])],1)],1)],1)},s=[],i=a("0fea"),r={name:"Step2",props:["userList"],data:function(){return{model:{},loading:!1,dropList:"0",captcha:"",show:!0,state:{time:60,smsSendBtn:!1},formLogin:{captcha:"",mobile:""},validatorRules:{phone:[{required:!0,message:"请输入手机号码!"},{validator:this.validatePhone}],captcha:[{required:!0,message:"请输入短信验证码!"}]}}},computed:{},methods:{nextStep:function(){var t=this,e=this;e.loading=!0,this.$refs["form"].validate((function(a){if(1==a){var n={phone:t.model.phone,smscode:t.model.captcha};Object(i["i"])("/sys/user/phoneVerification",n).then((function(a){if(a.success){var s={username:a.result.username,phone:n.phone,smscode:a.result.smscode};setTimeout((function(){e.$emit("nextStep",s)}),0)}else t.cmsFailed(a.message)}))}}))},getCaptcha:function(t){t.preventDefault();var e=this;e.$refs["form"].validateField("phone",(function(t){if(t)e.cmsFailed(t);else{e.state.smsSendBtn=!0;var a=window.setInterval((function(){e.state.time--<=0&&(e.state.time=60,e.state.smsSendBtn=!1,window.clearInterval(a))}),1e3),n=e.$message.loading("验证码发送中..",0),s={mobile:e.model.phone,smsmode:"2"};Object(i["i"])("/sys/sms",s).then((function(t){t.success||(setTimeout(n,1),e.cmsFailed(t.message)),setTimeout(n,500)}))}}))},cmsFailed:function(t){this.$notification["error"]({message:"验证错误",description:t,duration:4})},handleChangeSelect:function(t){var e=this;0==t?(e.dropList="0",e.show=!0):(e.dropList="1",e.show=!1)},validatePhone:function(t,e,a){if(e){var n=/^[1][3,4,5,7,8][0-9]{9}$/;n.test(e)?a():a("请输入正确的手机号")}else a()}}},o=r,c=(a("8ef0"),a("2877")),l=Object(c["a"])(o,n,s,!1,null,"14343278",null);e["default"]=l.exports},aa79:function(t,e,a){},aa95:function(t,e,a){"use strict";var n=a("3507"),s=a.n(n);s.a},ac2a:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tech-login"},[a("div",{staticClass:"dynamic-background"},[a("div",{staticClass:"grid-background"}),a("div",{staticClass:"particles-container"},t._l(t.particleCount,(function(e){return a("div",{key:e,staticClass:"particle",style:t.getParticleStyle(e)})})),0)]),a("div",{staticClass:"login-container"},[a("div",{ref:"loginCard",staticClass:"login-card"},[a("div",{staticClass:"login-header"},[a("div",{staticClass:"logo-section"},[a("div",{staticClass:"logo-icon"},[a("a-icon",{attrs:{type:"thunderbolt"}})],1),a("h1",{staticClass:"system-title"},[t._v("智界AIGC")]),a("p",{staticClass:"system-subtitle"},[t._v("后台管理系统")])])]),a("div",{staticClass:"login-form-section"},[t._m(0),a("a-form",{staticClass:"login-form",attrs:{form:t.form},on:{submit:t.handleSubmit}},[a("login-account",{ref:"alogin",on:{validateFail:t.validateFail,success:t.requestSuccess,fail:t.requestFailed},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleSubmit(e)}}}),a("a-form-item",{staticClass:"login-button-item"},[a("a-button",{staticClass:"login-submit-button",attrs:{size:"large",type:"primary",htmlType:"submit",loading:t.loginBtn,disabled:t.loginBtn,block:""},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[t.loginBtn?a("span",[a("a-icon",{staticStyle:{"margin-right":"8px"},attrs:{type:"loading"}}),t._v("\n                验证中...\n              ")],1):a("span",[a("a-icon",{staticStyle:{"margin-right":"8px"},attrs:{type:"login"}}),t._v("\n                登录系统\n              ")],1)])],1)],1),a("div",{staticClass:"website-button-section"},[a("a-button",{staticClass:"website-button",attrs:{size:"large",type:"default",block:""},on:{click:t.goToWebsite}},[a("a-icon",{attrs:{type:"home"}}),t._v("\n            探索智界AIGC官网\n          ")],1)],1),a("div",{staticClass:"login-footer"},[a("div",{staticClass:"footer-links"},[a("a",{staticClass:"footer-link",attrs:{href:"#"}},[a("a-icon",{attrs:{type:"question-circle"}}),t._v("\n              忘记密码\n            ")],1),a("a",{staticClass:"footer-link",attrs:{href:"#"}},[a("a-icon",{attrs:{type:"customer-service"}}),t._v("\n              技术支持\n            ")],1)])])],1)])]),t.requiredTwoStepCaptcha?a("two-step-captcha",{attrs:{visible:t.stepCaptchaVisible},on:{success:t.stepCaptchaSuccess,cancel:t.stepCaptchaCancel}}):t._e(),a("login-select-tenant",{ref:"loginSelect",on:{success:t.loginSelectOk}})],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"form-header"},[a("h2",{staticClass:"form-title"},[t._v("登录账户")]),a("p",{staticClass:"form-subtitle"},[t._v("请输入您的账号密码")])])}],i=a("a34a"),r=a.n(i),o=a("2b0e"),c=a("9fb0"),l=a("2914"),u=a("2ea5"),d=a("2ca2"),p=a("9da4"),f=a("ca00"),m=a("0fea"),h=a("a175"),g=a("7103");function v(t,e,a,n,s,i,r){try{var o=t[i](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(n,s)}function b(t){return function(){var e=this,a=arguments;return new Promise((function(n,s){var i=t.apply(e,a);function r(t){v(i,n,s,r,o,"next",t)}function o(t){v(i,n,s,r,o,"throw",t)}r(void 0)}))}}var y={components:{LoginSelectTenant:u["default"],TwoStepCaptcha:d["default"],ThirdLogin:l["default"],LoginAccount:h["default"],LoginPhone:g["default"]},data:function(){return{customActiveKey:"tab1",rememberMe:!0,loginBtn:!1,requiredTwoStepCaptcha:!1,stepCaptchaVisible:!1,encryptedString:{key:"",iv:""},aas:"",paths:"/dashboard/analysis",form:this.$form.createForm(this),particleCount:this.isMobile()?20:40,features:[{icon:"dashboard",title:"智能仪表板",description:"实时监控系统状态和业务数据"},{icon:"api",title:"API管理",description:"统一管理所有API接口和调用"},{icon:"team",title:"用户管理",description:"完善的用户权限和角色管理"},{icon:"setting",title:"系统配置",description:"灵活的系统参数和功能配置"}]}},created:function(){o["default"].ls.remove(c["a"]),this.getRouterData(),this.rememberMe=!0,localStorage.clear()},mounted:function(){var t=this;this.initPageAnimations();var e=this.$route.query.name;if(void 0!=e&&"zszxsq"==e.slice(0,6)){var a=e.slice(6),n=a.split("iwoaksqdsja")[0],s={username:n};this.axios.post("/sys/mmlogin",s).then((function(a){if("200"==a.code){var n=a.result,s=n.userInfo;o["default"].ls.set(c["a"],n.token,6048e5),o["default"].ls.set(c["v"],s.username,6048e5),o["default"].ls.set(c["u"],s,6048e5),o["default"].ls.set(c["s"],n.sysAllDictItems,6048e5),t.aas=s.realname,localStorage.setItem("realname",n.role),localStorage.setItem("city",n.city),localStorage.setItem("county",n.county),"zszxsq"==e.slice(0,6)&&(t.paths="/dashboard/analysis",t.intos())}}))}},methods:{isMobile:function(){return window.innerWidth<=768},getParticleStyle:function(t){var e=4*Math.random()+2,a=15*Math.random()+10,n=20*Math.random(),s=100*Math.random(),i=100*Math.random(),r=.3*Math.random()+.3;return{width:"".concat(e,"px"),height:"".concat(e,"px"),left:"".concat(s,"%"),top:"".concat(i,"%"),animationDuration:"".concat(a,"s"),animationDelay:"".concat(n,"s"),opacity:r}},initPageAnimations:function(){var t=this;this.$nextTick((function(){t.$refs.loginBrand&&(t.$refs.loginBrand.style.opacity="0",t.$refs.loginBrand.style.transform="translateX(-50px)",setTimeout((function(){t.$refs.loginBrand.style.transition="all 0.8s ease",t.$refs.loginBrand.style.opacity="1",t.$refs.loginBrand.style.transform="translateX(0)"}),200)),t.$refs.loginContainer&&(t.$refs.loginContainer.style.opacity="0",t.$refs.loginContainer.style.transform="translateX(50px)",setTimeout((function(){t.$refs.loginContainer.style.transition="all 0.8s ease",t.$refs.loginContainer.style.opacity="1",t.$refs.loginContainer.style.transform="translateX(0)"}),400))}))},handleTabClick:function(t){this.customActiveKey=t},handleRememberMeChange:function(t){this.rememberMe=t.target.checked},getRouterData:function(){var t=this;this.$nextTick((function(){var e=t.$route.params.username||t.$route.query.username||"";e&&t.$refs.alogin.acceptUsername(e)}))},handleSubmit:function(){this.loginBtn=!0,"tab1"===this.customActiveKey?this.$refs.alogin.handleLogin(this.rememberMe):this.$refs.plogin.handleLogin(this.rememberMe)},validateFail:function(){this.loginBtn=!1},requestSuccess:function(t){this.aas=t.userInfo.realname,this.$refs.loginSelect.show(t);var e="/sys/user/getCurrentUserDeparts";Object(m["c"])(e).then((function(t){if(t.success){var e=t.result.role;localStorage.setItem("userRole",e);var a=t.result.departId;localStorage.setItem("departId",a)}}))},intos:function(){this.$router.push({path:this.paths}).catch((function(){})),this.$notification.success({message:"欢迎",description:"".concat(Object(f["o"])(),"，欢迎回来")})},requestFailed:function(t){var e=((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试";"ACCESS_DENIED"!==e?(this.$notification["error"]({message:"登录失败",description:e,duration:4}),"tab1"===this.customActiveKey&&e.indexOf("密码错误")>0&&this.$refs.alogin.handleChangeCheckCode(),this.loginBtn=!1):this.$router.push("/404")},loginSelectOk:function(){this.loginSuccess()},loginSuccess:function(){var t=b(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=1,t.next=5,new Promise((function(t){return setTimeout(t,500)}));case 5:e=localStorage.getItem("userRole"),"admin"===e?this.$router.push({path:"/"}).catch((function(){})):this.$router.push({path:"/home"}).catch((function(){})),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](1),this.$router.push({path:"/home"}).catch((function(){}));case 14:this.$notification.success({message:"欢迎",description:"".concat(Object(f["o"])(),"，欢迎回来")});case 15:case"end":return t.stop()}}),t,this,[[1,10]])})));function e(){return t.apply(this,arguments)}return e}(),stepCaptchaSuccess:function(){this.loginSuccess()},stepCaptchaCancel:function(){var t=this;this.Logout().then((function(){t.loginBtn=!1,t.stepCaptchaVisible=!1}))},getEncrypte:function(){var t=this,e=o["default"].ls.get(c["l"]);null==e?Object(p["b"])().then((function(e){t.encryptedString=e})):this.encryptedString=e},goToWebsite:function(){this.$router.push({path:"/home"}).catch((function(){}))}}},C=y,w=(a("b161"),a("aa95"),a("2877")),S=Object(w["a"])(C,n,s,!1,null,"9b1f0b90",null);e["default"]=S.exports},b161:function(t,e,a){"use strict";var n=a("0759"),s=a.n(n);s.a},b23d:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form",{staticStyle:{margin:"40px auto 0"}},[a("result",{attrs:{title:"更改密码成功","is-success":!0}},[a("div",{staticClass:"toLogin"},[a("h3",[t._v("将在"),a("span",[t._v(t._s(t.time))]),t._v("秒后返回登录页面.")])])])],1)],1)},s=[],i=a("9a3d"),r={name:"Step4",props:["userList"],components:{Result:i["default"]},data:function(){return{loading:!1,time:0}},methods:{countDown:function(){var t=this;t.time--}},mounted:function(){var t=this;t.time=5,setInterval(t.countDown,1e3)},watch:{time:function(t,e){if(0==t){var a={username:this.userList.username};this.$router.push({name:"login",params:a})}}}},o=r,c=(a("d8b8"),a("2877")),l=Object(c["a"])(o,n,s,!1,null,"0ac9a29e",null);e["default"]=l.exports},c4f8:function(t,e,a){"use strict";var n=a("aa79"),s=a.n(n);s.a},d8b8:function(t,e,a){"use strict";var n=a("a2cc"),s=a.n(n);s.a},f55e:function(t,e,a){"use strict";var n=a("5cfa"),s=a.n(n);s.a}}]);