2025-07-30 00:57:41.360 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-30 00:57:41.367 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-30 00:57:41.367 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutting down.
2025-07-30 00:57:41.367 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 paused.
2025-07-30 00:57:41.370 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutdown complete.
2025-07-30 00:57:41.374 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-30 00:57:41.378 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-30 00:57:41.382 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-30 00:57:41.382 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-30 00:57:52.536 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-30 00:57:52.563 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 4292 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-30 00:57:52.564 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-30 00:57:52.911 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-30 00:57:54.095 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 00:57:54.097 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 00:57:54.236 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 131ms. Found 0 Redis repository interfaces.
2025-07-30 00:57:54.413 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-30 00:57:54.414 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-30 00:57:54.414 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-30 00:57:54.494 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-30 00:57:54.495 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-30 00:57:54.690 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.693 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.694 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.696 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.699 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.701 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.702 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.704 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.705 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.705 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.706 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.708 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.710 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.729 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.733 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.794 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$13d46adb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.888 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.312 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-30 00:57:55.313 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-30 00:57:55.315 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.318 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.498 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.505 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1e2693b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.511 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.521 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$31367b0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.554 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$c7b10092] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.558 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.795 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-30 00:57:55.802 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 00:57:55.803 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-30 00:57:55.803 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-30 00:57:55.967 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-30 00:57:55.968 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3370 ms
2025-07-30 00:57:56.597 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-30 00:57:56.598 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-30 00:57:56.598 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-30 00:57:57.986 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-30 00:57:58.795 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-30 00:57:58.795 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-30 00:57:58.908 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-30 00:57:58.911 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-30 00:57:58.911 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-30 00:57:58.911 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-30 00:57:59.727 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-30 00:57:59.727 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-30 00:57:59.730 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-30 00:57:59.732 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-30 00:57:59.732 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-30 00:57:59.732 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-30 00:57:59.908 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-30 00:58:00.135 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-30 00:58:00.557 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-30 00:58:00.566 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-30 00:58:00.597 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-30 00:58:00.639 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-30 00:58:00.646 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-30 00:58:00.977 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-30 00:58:00.978 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-30 00:58:00.985 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 00:58:00.985 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 00:58:00.989 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-30 00:58:00.990 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-30 00:58:00.990 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753808280978'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-30 00:58:00.991 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-30 00:58:00.991 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-30 00:58:00.991 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4e68aede
2025-07-30 00:58:03.693 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-30 00:58:05.121 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-30 00:58:05.360 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-30 00:58:05.412 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 00:58:05.589 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-30 00:58:05.590 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-30 00:58:05.595 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-30 00:58:07.427 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 00:58:07.466 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-30 00:58:07.468 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-30 00:58:07.472 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-30 00:58:07.732 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-30 00:58:07.945 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-30 00:58:07.959 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-30 00:58:07.970 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-30 00:58:07.989 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-30 00:58:07.993 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-30 00:58:07.995 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-30 00:58:07.996 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-30 00:58:07.998 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-30 00:58:08.002 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-30 00:58:08.008 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-30 00:58:08.021 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-30 00:58:08.024 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-30 00:58:08.027 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-30 00:58:08.029 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-30 00:58:08.031 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-30 00:58:08.038 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-30 00:58:08.045 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-30 00:58:08.068 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-30 00:58:08.071 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-30 00:58:08.073 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-30 00:58:08.076 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-30 00:58:08.080 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-30 00:58:08.088 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-30 00:58:08.103 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-30 00:58:08.104 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-30 00:58:08.106 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-30 00:58:08.108 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-30 00:58:08.109 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-30 00:58:08.114 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-30 00:58:08.125 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-30 00:58:08.128 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-30 00:58:08.129 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-30 00:58:08.132 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-30 00:58:08.140 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-30 00:58:08.147 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-30 00:58:08.160 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-30 00:58:08.166 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-30 00:58:08.167 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-30 00:58:08.169 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-30 00:58:08.171 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-30 00:58:08.175 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-30 00:58:08.181 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-30 00:58:08.195 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-30 00:58:08.210 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-30 00:58:08.212 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-30 00:58:08.214 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-30 00:58:08.216 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-30 00:58:08.217 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-30 00:58:08.222 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-30 00:58:08.230 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-30 00:58:08.231 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-30 00:58:08.233 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-30 00:58:08.235 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-30 00:58:08.236 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-30 00:58:08.241 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-30 00:58:08.257 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-30 00:58:08.263 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-30 00:58:08.265 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-30 00:58:08.266 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-30 00:58:08.271 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-30 00:58:08.277 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-30 00:58:08.296 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-30 00:58:08.302 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-30 00:58:08.304 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-30 00:58:08.306 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-30 00:58:08.315 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-30 00:58:08.320 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-30 00:58:08.330 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-30 00:58:08.331 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-30 00:58:08.332 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-30 00:58:08.334 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-30 00:58:08.335 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-30 00:58:08.341 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-30 00:58:08.350 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-30 00:58:08.351 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-30 00:58:08.354 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-30 00:58:08.356 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-30 00:58:08.357 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-30 00:58:08.362 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-30 00:58:08.371 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-30 00:58:08.373 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-30 00:58:08.374 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-30 00:58:08.376 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-30 00:58:08.378 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-30 00:58:08.388 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-30 00:58:08.396 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-30 00:58:08.397 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-30 00:58:08.399 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-30 00:58:08.401 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-30 00:58:08.405 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-30 00:58:08.410 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-30 00:58:08.563 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-30 00:58:08.570 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-30 00:58:08.573 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-30 00:58:08.577 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-30 00:58:08.580 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-30 00:58:08.596 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-30 00:58:08.685 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-30 00:58:08.688 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-30 00:58:08.690 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-30 00:58:08.692 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-30 00:58:08.695 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-30 00:58:08.700 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-30 00:58:08.706 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-30 00:58:08.708 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-30 00:58:08.710 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-30 00:58:08.711 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-30 00:58:08.714 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-30 00:58:08.719 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-30 00:58:08.724 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-30 00:58:08.725 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-30 00:58:08.727 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-30 00:58:08.729 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-30 00:58:08.730 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-30 00:58:08.735 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-30 00:58:08.740 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-30 00:58:08.742 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-30 00:58:08.743 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-30 00:58:08.745 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-30 00:58:08.747 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-30 00:58:08.751 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-30 00:58:08.762 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-30 00:58:08.763 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-30 00:58:08.764 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-30 00:58:08.765 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-30 00:58:08.766 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-30 00:58:08.773 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-30 00:58:08.784 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-30 00:58:08.785 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-30 00:58:08.792 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-30 00:58:08.794 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-30 00:58:08.795 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-30 00:58:08.799 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-30 00:58:08.816 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-30 00:58:08.819 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-30 00:58:08.822 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-30 00:58:08.824 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-30 00:58:08.825 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-30 00:58:08.843 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-30 00:58:08.897 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-30 00:58:08.937 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-30 00:58:08.939 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-30 00:58:08.941 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-30 00:58:11.330 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 19.307 seconds (JVM running for 20.345)
2025-07-30 00:58:11.345 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-30 00:58:11.836 [RMI TCP Connection(2)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 00:58:11.836 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 00:58:11.865 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 29 ms
2025-07-30 00:58:41.193 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:41.194 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:58:41.195 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:58:41.194 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:58:41.241 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:58:41.801 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:58:41.810 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:58:41.811 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:58:41.824 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:41.824 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.824 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:41.825 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.825 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.824 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.866 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:41.869 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 00:58:41.871 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:58:41.884 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:58:41.894 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.898 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.914 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 00:58:41.916 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 00:58:41.919 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.919 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.919 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.919 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.925 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.925 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.337 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.337 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.350 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.351 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.348 [http-nio-8080-exec-5] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 00:58:43.352 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 00:58:43.353 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 00:58:43.358 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.360 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.360 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.361 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.363 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 00:58:43.364 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.429 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 00:58:43.429 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.473 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 00:58:43.474 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 00:58:43.641 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 00:58:43.641 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 00:58:43.643 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.195 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:58:44.197 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:44.197 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:58:44.205 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:58:44.211 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:58:44.213 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:58:44.219 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:44.222 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:44.223 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:44.224 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.224 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:58:44.221 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.225 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:58:44.231 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:44.232 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.232 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:58:44.235 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:44.236 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.246 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:58:44.248 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:44.249 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.836 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:48.838 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:58:48.840 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:58:48.847 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:58:48.851 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:58:48.853 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:58:48.854 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:58:48.857 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:48.858 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.858 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:48.858 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.859 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:48.859 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.863 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:58:48.865 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:48.865 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:58:48.866 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:48.866 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.870 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:58:48.872 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:48.872 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.797 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:59:51.801 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:59:51.801 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:59:51.807 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:59:51.831 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:59:51.837 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:59:51.837 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.843 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:59:51.852 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:59:51.852 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:59:51.855 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:59:51.859 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:59:51.859 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:59:51.859 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.862 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:59:51.863 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:59:51.863 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.867 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:59:51.869 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.876 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:59:51.877 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.609 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 01:00:02.608 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 01:00:02.611 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 01:00:02.610 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 01:00:02.619 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 01:00:02.622 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 01:00:02.623 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.626 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 01:00:02.631 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:00:02.631 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 01:00:02.633 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.640 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 01:00:02.641 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 01:00:02.640 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:00:02.642 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.643 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 01:00:02.643 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.643 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 01:00:02.651 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 01:00:02.654 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:00:02.654 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.131 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.131 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.146 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:55.147 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.151 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.152 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.154 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.156 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.157 [http-nio-8080-exec-10] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 01:02:55.158 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 01:02:55.158 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:55.158 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:55.159 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.162 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.165 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.247 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:55.248 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:55.322 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:55.323 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 01:02:55.323 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.706 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.706 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.719 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.720 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.720 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:58.721 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.721 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.721 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.724 [http-nio-8080-exec-1] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 01:02:58.725 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 01:02:58.725 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:58.726 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:58.726 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.727 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.727 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.800 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:58.800 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:58.829 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:58.830 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 01:02:58.830 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.321 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.322 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.334 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.335 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.336 [http-nio-8080-exec-6] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 01:38:13.336 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 01:38:13.336 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:38:13.336 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.336 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.337 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:38:13.337 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.338 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:38:13.338 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.345 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.345 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.438 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:38:13.439 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:38:13.539 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:38:13.540 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 01:38:13.540 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
