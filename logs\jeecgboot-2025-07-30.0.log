2025-07-30 00:57:41.360 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-30 00:57:41.367 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-30 00:57:41.367 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutting down.
2025-07-30 00:57:41.367 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 paused.
2025-07-30 00:57:41.370 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753786040354 shutdown complete.
2025-07-30 00:57:41.374 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-30 00:57:41.378 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-30 00:57:41.382 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-30 00:57:41.382 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-30 00:57:52.536 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-30 00:57:52.563 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 4292 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-30 00:57:52.564 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-30 00:57:52.911 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-30 00:57:54.095 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 00:57:54.097 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 00:57:54.236 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 131ms. Found 0 Redis repository interfaces.
2025-07-30 00:57:54.413 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-30 00:57:54.414 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-30 00:57:54.414 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-30 00:57:54.494 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-30 00:57:54.495 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-30 00:57:54.496 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-30 00:57:54.690 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.693 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.694 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.696 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.699 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.701 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.702 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.704 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.705 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.705 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.706 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.708 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#285a4fe3#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.710 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.729 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.733 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.794 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$13d46adb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:54.888 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.312 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-30 00:57:55.313 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-30 00:57:55.315 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.318 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.498 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.505 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1e2693b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.511 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.521 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$31367b0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.554 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$c7b10092] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.558 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 00:57:55.795 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-30 00:57:55.802 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 00:57:55.803 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-30 00:57:55.803 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-30 00:57:55.967 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-30 00:57:55.968 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3370 ms
2025-07-30 00:57:56.597 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-30 00:57:56.598 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-30 00:57:56.598 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-30 00:57:57.986 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-30 00:57:58.795 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-30 00:57:58.795 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-30 00:57:58.908 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-30 00:57:58.911 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-30 00:57:58.911 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-30 00:57:58.911 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-30 00:57:59.727 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-30 00:57:59.727 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-30 00:57:59.730 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-30 00:57:59.732 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-30 00:57:59.732 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-30 00:57:59.732 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-30 00:57:59.908 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-30 00:58:00.135 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-30 00:58:00.557 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-30 00:58:00.566 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-30 00:58:00.597 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-30 00:58:00.639 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-30 00:58:00.646 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-30 00:58:00.977 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-30 00:58:00.978 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-30 00:58:00.985 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 00:58:00.985 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 00:58:00.989 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-30 00:58:00.990 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-30 00:58:00.990 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753808280978'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-30 00:58:00.991 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-30 00:58:00.991 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-30 00:58:00.991 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4e68aede
2025-07-30 00:58:03.693 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-30 00:58:05.121 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-30 00:58:05.360 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-30 00:58:05.412 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 00:58:05.589 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-30 00:58:05.590 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-30 00:58:05.595 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-30 00:58:07.427 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 00:58:07.466 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-30 00:58:07.468 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-30 00:58:07.472 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-30 00:58:07.732 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-30 00:58:07.945 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-30 00:58:07.959 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-30 00:58:07.970 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-30 00:58:07.989 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-30 00:58:07.993 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-30 00:58:07.995 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-30 00:58:07.996 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-30 00:58:07.998 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-30 00:58:08.002 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-30 00:58:08.008 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-30 00:58:08.021 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-30 00:58:08.024 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-30 00:58:08.027 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-30 00:58:08.029 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-30 00:58:08.031 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-30 00:58:08.038 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-30 00:58:08.045 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-30 00:58:08.068 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-30 00:58:08.071 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-30 00:58:08.073 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-30 00:58:08.076 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-30 00:58:08.080 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-30 00:58:08.088 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-30 00:58:08.103 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-30 00:58:08.104 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-30 00:58:08.106 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-30 00:58:08.108 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-30 00:58:08.109 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-30 00:58:08.114 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-30 00:58:08.125 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-30 00:58:08.128 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-30 00:58:08.129 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-30 00:58:08.132 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-30 00:58:08.140 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-30 00:58:08.147 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-30 00:58:08.160 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-30 00:58:08.166 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-30 00:58:08.167 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-30 00:58:08.169 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-30 00:58:08.171 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-30 00:58:08.175 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-30 00:58:08.181 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-30 00:58:08.195 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-30 00:58:08.210 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-30 00:58:08.212 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-30 00:58:08.214 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-30 00:58:08.216 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-30 00:58:08.217 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-30 00:58:08.222 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-30 00:58:08.230 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-30 00:58:08.231 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-30 00:58:08.233 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-30 00:58:08.235 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-30 00:58:08.236 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-30 00:58:08.241 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-30 00:58:08.257 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-30 00:58:08.263 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-30 00:58:08.265 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-30 00:58:08.266 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-30 00:58:08.271 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-30 00:58:08.277 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-30 00:58:08.296 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-30 00:58:08.302 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-30 00:58:08.304 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-30 00:58:08.306 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-30 00:58:08.315 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-30 00:58:08.320 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-30 00:58:08.330 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-30 00:58:08.331 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-30 00:58:08.332 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-30 00:58:08.334 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-30 00:58:08.335 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-30 00:58:08.341 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-30 00:58:08.350 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-30 00:58:08.351 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-30 00:58:08.354 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-30 00:58:08.356 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-30 00:58:08.357 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-30 00:58:08.362 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-30 00:58:08.371 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-30 00:58:08.373 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-30 00:58:08.374 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-30 00:58:08.376 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-30 00:58:08.378 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-30 00:58:08.388 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-30 00:58:08.396 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-30 00:58:08.397 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-30 00:58:08.399 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-30 00:58:08.401 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-30 00:58:08.405 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-30 00:58:08.410 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-30 00:58:08.563 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-30 00:58:08.570 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-30 00:58:08.573 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-30 00:58:08.577 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-30 00:58:08.580 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-30 00:58:08.596 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-30 00:58:08.685 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-30 00:58:08.688 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-30 00:58:08.690 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-30 00:58:08.692 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-30 00:58:08.695 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-30 00:58:08.700 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-30 00:58:08.706 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-30 00:58:08.708 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-30 00:58:08.710 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-30 00:58:08.711 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-30 00:58:08.714 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-30 00:58:08.719 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-30 00:58:08.724 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-30 00:58:08.725 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-30 00:58:08.727 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-30 00:58:08.729 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-30 00:58:08.730 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-30 00:58:08.735 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-30 00:58:08.740 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-30 00:58:08.742 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-30 00:58:08.743 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-30 00:58:08.745 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-30 00:58:08.747 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-30 00:58:08.751 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-30 00:58:08.762 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-30 00:58:08.763 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-30 00:58:08.764 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-30 00:58:08.765 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-30 00:58:08.766 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-30 00:58:08.773 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-30 00:58:08.784 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-30 00:58:08.785 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-30 00:58:08.792 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-30 00:58:08.794 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-30 00:58:08.795 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-30 00:58:08.799 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-30 00:58:08.816 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-30 00:58:08.819 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-30 00:58:08.822 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-30 00:58:08.824 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-30 00:58:08.825 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-30 00:58:08.843 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-30 00:58:08.897 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-30 00:58:08.937 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-30 00:58:08.939 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-30 00:58:08.941 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-30 00:58:11.330 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 19.307 seconds (JVM running for 20.345)
2025-07-30 00:58:11.345 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-30 00:58:11.836 [RMI TCP Connection(2)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 00:58:11.836 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 00:58:11.865 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 29 ms
2025-07-30 00:58:41.193 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:41.194 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:58:41.195 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:58:41.194 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:58:41.241 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:58:41.801 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:58:41.810 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:58:41.811 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:58:41.824 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:41.824 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.824 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:41.825 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.825 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.824 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.866 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:41.869 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 00:58:41.871 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:58:41.884 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:58:41.894 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.898 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.914 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 00:58:41.916 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 00:58:41.919 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.919 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.919 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.919 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:41.925 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:41.925 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.337 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.337 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.350 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.351 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.348 [http-nio-8080-exec-5] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 00:58:43.352 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 00:58:43.353 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 00:58:43.358 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.360 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.360 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:43.361 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.363 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 00:58:43.364 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.429 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 00:58:43.429 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:43.473 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 00:58:43.474 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 00:58:43.641 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 00:58:43.641 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 00:58:43.643 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.195 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:58:44.197 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:44.197 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:58:44.205 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:58:44.211 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:58:44.213 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:58:44.219 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:44.222 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:44.223 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:44.224 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.224 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:58:44.221 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.225 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:58:44.231 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:44.232 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.232 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:58:44.235 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:44.236 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:44.246 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:58:44.248 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:44.249 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.836 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:48.838 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:58:48.840 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:58:48.847 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:58:48.851 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:58:48.853 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:58:48.854 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:58:48.857 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:48.858 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.858 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:48.858 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.859 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:48.859 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.863 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:58:48.865 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:58:48.865 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:58:48.866 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:58:48.866 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:58:48.870 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:58:48.872 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:58:48.872 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.797 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:59:51.801 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 00:59:51.801 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 00:59:51.807 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 00:59:51.831 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 00:59:51.837 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:59:51.837 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.843 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 00:59:51.852 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 00:59:51.852 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 00:59:51.855 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 00:59:51.859 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 00:59:51.859 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:59:51.859 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.862 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 00:59:51.863 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 00:59:51.863 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.867 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:59:51.869 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 00:59:51.876 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 00:59:51.877 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.609 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 01:00:02.608 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 01:00:02.611 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 01:00:02.610 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 01:00:02.619 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 01:00:02.622 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 01:00:02.623 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.626 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 01:00:02.631 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:00:02.631 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 01:00:02.633 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.640 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 01:00:02.641 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 01:00:02.640 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:00:02.642 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.643 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 01:00:02.643 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:00:02.643 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 01:00:02.651 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 01:00:02.654 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:00:02.654 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.131 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.131 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.146 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:55.147 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.151 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.152 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.154 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.156 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.157 [http-nio-8080-exec-10] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 01:02:55.158 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 01:02:55.158 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:55.158 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:55.159 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.162 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:55.165 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:55.247 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:55.248 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:55.322 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:55.323 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 01:02:55.323 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.706 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.706 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.719 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.720 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.720 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:58.721 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.721 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.721 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.724 [http-nio-8080-exec-1] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 01:02:58.725 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 01:02:58.725 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:58.726 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:02:58.726 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.727 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:02:58.727 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:02:58.800 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:02:58.800 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:58.829 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:02:58.830 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 01:02:58.830 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.321 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.322 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.334 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.335 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.336 [http-nio-8080-exec-6] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 01:38:13.336 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 01:38:13.336 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:38:13.336 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.336 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.337 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:38:13.337 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.338 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 01:38:13.338 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.345 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 01:38:13.345 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 01:38:13.438 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 01:38:13.439 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:38:13.539 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 01:38:13.540 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 01:38:13.540 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:47.125 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:41:47.126 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 02:41:47.127 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 02:41:47.128 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 02:41:47.140 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 02:41:47.145 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:41:47.146 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:47.147 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 02:41:47.151 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 02:41:47.153 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:41:47.154 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:47.154 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:41:47.154 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:47.286 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:41:47.287 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 02:41:47.291 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 02:41:47.296 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 02:41:47.296 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:41:47.296 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:47.298 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:41:47.300 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:48.814 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 02:41:48.822 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 02:41:48.823 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:41:48.824 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:41:48.825 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:41:48.825 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:41:48.825 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:42:15.767 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:42:15.770 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 02:42:15.773 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 02:42:15.775 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 02:42:15.778 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:42:15.779 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 02:42:15.781 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 02:42:15.781 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 02:42:15.782 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 02:42:15.783 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 02:42:15.785 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:42:15.785 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:42:15.786 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:42:15.786 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:42:15.786 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:42:15.787 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:42:15.795 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:42:15.797 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:42:15.801 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 02:42:15.803 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:42:15.804 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:48:30.207 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:79 -  LogContent length : 21
2025-07-30 02:48:30.208 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:80 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:48:30.209 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.LoginController:216 -  用户名:  智界用户qO6NG6,退出成功！ 
2025-07-30 02:48:30.209 [http-nio-8080-exec-1] INFO  o.j.modules.system.service.UserCacheCleanupService:41 - 🧹 开始清理用户登录缓存，用户：***********
2025-07-30 02:48:30.211 [http-nio-8080-exec-1] INFO  o.j.modules.system.service.UserCacheCleanupService:49 - ✅ 用户 *********** 登录缓存清理完成
2025-07-30 02:48:31.720 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:48:31.723 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:48:31.724 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:48:32.346 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:49:02.192 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:49:02.192 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:49:02.194 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:49:02.195 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:53:29.109 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:53:29.112 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:53:29.112 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:54:57.789 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:54:57.848 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:54:57.851 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:54:57.851 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:12.399 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:55:12.423 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:55:12.426 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:12.427 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:22.395 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:55:22.414 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:55:22.416 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:22.417 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:36.427 [http-nio-8080-exec-1] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:55:36.456 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:55:36.459 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:36.459 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:40.048 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:55:40.050 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 02:55:40.053 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 02:55:40.056 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 02:55:40.074 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 02:55:40.081 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 02:55:40.086 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 02:55:40.089 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 02:55:40.089 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:55:40.090 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:40.090 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:40.090 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:55:40.090 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:40.090 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:40.096 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:40.096 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:40.099 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:55:40.099 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 02:55:40.103 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 02:55:40.106 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:40.106 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:41.346 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:55:41.349 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:41.350 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:42.299 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:55:42.300 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 02:55:42.301 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 02:55:42.302 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 02:55:42.309 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 02:55:42.311 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 02:55:42.317 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:55:42.317 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:42.318 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=18, 当前页数据量=0
2025-07-30 02:55:42.316 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 02:55:42.319 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 02:55:42.319 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 02:55:42.320 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:42.320 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:42.323 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 02:55:42.324 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:42.331 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 02:55:42.330 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:42.335 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:42.334 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:42.336 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.888 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:43.889 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.890 [http-nio-8080-exec-5] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 02:55:43.890 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 02:55:43.890 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 02:55:43.888 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:43.891 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:55:43.891 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.891 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.891 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:43.899 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.902 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:55:43.902 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.912 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:43.912 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:43.980 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 02:55:43.980 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 02:55:44.085 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 02:55:44.086 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 02:55:44.086 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:55:46.047 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:55:46.049 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:55:46.049 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:56:12.713 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:56:12.715 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:56:12.745 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:56:12.748 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:56:12.748 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:56:12.769 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:56:12.770 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:56:24.816 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:56:24.820 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:56:24.821 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:56:24.838 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:56:24.838 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:56:25.024 [http-nio-8080-exec-1] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:56:25.024 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:57:05.653 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1, 支付方式: alipay-page
2025-07-30 02:57:05.681 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815425657_e9ca23d6
2025-07-30 02:57:05.681 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:57:05.682 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:05.698 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815425657_e9ca23d6, 金额: 1
2025-07-30 02:57:05.698 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815425657_e9ca23d6, 金额: 1
2025-07-30 02:57:05.702 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:46 - 🔍 私钥调试信息:
2025-07-30 02:57:05.703 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:47 - 🔍 私钥是否为空: false
2025-07-30 02:57:05.703 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:49 - 🔍 私钥长度: 1624
2025-07-30 02:57:05.703 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:50 - 🔍 私钥前50字符: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQ
2025-07-30 02:57:05.704 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:51 - 🔍 私钥后50字符: XORYARFPfrcb5nvSh+GXn2TGujhV/JNnmeSI/gIpXbmN5MdLo=
2025-07-30 02:57:05.704 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:52 - 🔍 私钥是否包含换行符: false
2025-07-30 02:57:05.704 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:53 - 🔍 私钥是否包含回车符: false
2025-07-30 02:57:05.704 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:54 - 🔍 私钥是否包含空格: false
2025-07-30 02:57:05.704 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:55 - 🔍 私钥是否包含制表符: false
2025-07-30 02:57:05.705 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:59 - 🔍 清理后私钥长度: 1624
2025-07-30 02:57:05.769 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815425657_e9ca23d6
2025-07-30 02:57:05.769 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1304
2025-07-30 02:57:05.769 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 02:57:05.770 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753815425657_e9ca23d6, amount=1, payForm长度=1304
2025-07-30 02:57:05.770 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 02:57:05.770 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:29.484 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:57:29.488 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:57:29.488 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:29.550 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:57:29.551 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:32.215 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-qr
2025-07-30 02:57:32.223 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815452219_e9ca23d6
2025-07-30 02:57:32.224 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:57:32.224 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:32.239 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:99 - 💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753815452219_e9ca23d6, 金额: 1000
2025-07-30 02:57:32.239 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:139 - 💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753815452219_e9ca23d6, 金额: 1000
2025-07-30 02:57:33.911 [http-nio-8080-exec-1] INFO  sdk.biz.info:372 - Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&method=alipay.trade.precreate&sign=v0Nk/hR0dAJZIvmGtTLUVpAOCGAcZPxa3v3xWv7Rjh7bQ8G+ORjryrysp14mjrrE9ft40ie50LrTtOQl8snQOx5QwdlXMN953M2zRMOQnEyWNVr99bwcopl3nY0wcF0gONq22lzEcHQ7zvFrRgZSgW6jJ0cnUNPz9b7nAg84LVT83lNO73JpJw6zIaaMJjcmnLhqD8ipsARoIcS0Cw0hh92VfUeu6HSzLapqqCzj4XgbHqYo4k0d/TplM9rylfmMI5CbCsD3V7iBJXPXpy8m1iUYin6TMQ3QgKhCJ+bRc8w/rvFcbACHlPXQCOQ7Ra6N3SXZtd2/SwRLqL6f3tqvXw==&notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&version=1.0&app_id=9021000150681157&sign_type=RSA2&timestamp=2025-07-30 02:57:32^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&format=json^_^ApplicationParams:biz_content={"out_trade_no":"RECHARGE_1753815452219_e9ca23d6","total_amount":"1000","subject":"智界Aigc账户充值","timeout_express":"15m","goods_type":"0","body":"充值金额：¥1000"}^_^15ms,1609ms,44ms^_^trace_id:0601ac3e175381545447274644924
2025-07-30 02:57:33.912 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:164 - 💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753815452219_e9ca23d6, 二维码: https://qr.alipay.com/bax096049brtnriphkr40018
2025-07-30 02:57:33.912 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:103 - 🔍 扫码支付二维码URL: https://qr.alipay.com/bax096049brtnriphkr40018
2025-07-30 02:57:33.913 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:110 - 🔍 返回扫码支付结果: orderId=RECHARGE_1753815452219_e9ca23d6, amount=1000, qrCode=已生成
2025-07-30 02:57:33.913 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:57:33.913 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:38.769 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page
2025-07-30 02:57:38.776 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815458772_e9ca23d6
2025-07-30 02:57:38.777 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:57:38.777 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:57:38.789 [http-nio-8080-exec-5] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815458772_e9ca23d6, 金额: 1000
2025-07-30 02:57:38.789 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815458772_e9ca23d6, 金额: 1000
2025-07-30 02:57:38.805 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815458772_e9ca23d6
2025-07-30 02:57:38.805 [http-nio-8080-exec-5] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1304
2025-07-30 02:57:38.805 [http-nio-8080-exec-5] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 02:57:38.805 [http-nio-8080-exec-5] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753815458772_e9ca23d6, amount=1000, payForm长度=1304
2025-07-30 02:57:38.805 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 02:57:38.806 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:58:27.088 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page
2025-07-30 02:58:27.093 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815507091_e9ca23d6
2025-07-30 02:58:27.094 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:58:27.094 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:58:27.108 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815507091_e9ca23d6, 金额: 1000
2025-07-30 02:58:27.108 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815507091_e9ca23d6, 金额: 1000
2025-07-30 02:58:27.122 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815507091_e9ca23d6
2025-07-30 02:58:27.123 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1298
2025-07-30 02:58:27.123 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 02:58:27.124 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753815507091_e9ca23d6, amount=1000, payForm长度=1298
2025-07-30 02:58:27.124 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 02:58:27.124 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:58:28.994 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page
2025-07-30 02:58:29.001 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815508997_e9ca23d6
2025-07-30 02:58:29.001 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:58:29.002 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:58:29.010 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815508997_e9ca23d6, 金额: 1000
2025-07-30 02:58:29.011 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815508997_e9ca23d6, 金额: 1000
2025-07-30 02:58:29.026 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815508997_e9ca23d6
2025-07-30 02:58:29.026 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1304
2025-07-30 02:58:29.026 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 02:58:29.027 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753815508997_e9ca23d6, amount=1000, payForm长度=1304
2025-07-30 02:58:29.027 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 02:58:29.028 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:58:44.691 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:58:44.691 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:58:44.840 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:58:44.843 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:58:44.843 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:58:44.860 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:58:44.860 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:10.873 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:59:10.873 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:59:11.078 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:59:11.081 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:59:11.081 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:11.099 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:59:11.099 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:26.744 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:59:26.744 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 02:59:27.029 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:59:27.033 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:59:27.033 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:27.049 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:59:27.050 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:38.335 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 02:59:38.338 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 02:59:38.338 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:38.386 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:59:38.387 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:40.850 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-qr
2025-07-30 02:59:40.855 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815580853_e9ca23d6
2025-07-30 02:59:40.856 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 02:59:40.856 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 02:59:40.870 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:99 - 💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753815580853_e9ca23d6, 金额: 1000
2025-07-30 02:59:40.871 [http-nio-8080-exec-8] INFO  org.jeecg.modules.system.service.AlipayService:139 - 💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753815580853_e9ca23d6, 金额: 1000
2025-07-30 02:59:41.737 [http-nio-8080-exec-8] INFO  sdk.biz.info:372 - Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&method=alipay.trade.precreate&sign=ZcgWuEVQEmEfCqyYDrGIIVTUmbxIY5nZ2orMvslAlYiaXKk0qQgiOWYoiAnWW11V+095jHq3bJzBQ4WGi4dKomTOqT3QJ3KNCO5QaqNBlx32JRElR3Ul9UAjbtlTMnn7o8l+6C7RHBBARB/xFHK9R96uhGdd3Ze8cO1pxd8sXjWE0N+P/iacvh7BV27WWQaz0zH/cd7ajF/CauAC9myFkE16BdtHJqNw2Y7g/zHI6iKNeutxukhx5E0rs/cx7RV6vGD9Bbo5psJjQYekDvpKSBAFMB8UomjXNVFkflUakNH3qQgb9yotQhVNuhjJ4aQ04kdD8E8uFgioGzrRWvFoRA==&notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&version=1.0&app_id=9021000150681157&sign_type=RSA2&timestamp=2025-07-30 02:59:40^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&format=json^_^ApplicationParams:biz_content={"out_trade_no":"RECHARGE_1753815580853_e9ca23d6","total_amount":"1000","subject":"智界Aigc账户充值","timeout_express":"15m","goods_type":"0","body":"充值金额：¥1000"}^_^13ms,834ms,19ms^_^trace_id:0601ac3e175381558309980264924
2025-07-30 02:59:41.737 [http-nio-8080-exec-8] INFO  org.jeecg.modules.system.service.AlipayService:164 - 💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753815580853_e9ca23d6, 二维码: https://qr.alipay.com/bax06504ap84dfwtnibo0027
2025-07-30 02:59:41.738 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:103 - 🔍 扫码支付二维码URL: https://qr.alipay.com/bax06504ap84dfwtnibo0027
2025-07-30 02:59:41.738 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:110 - 🔍 返回扫码支付结果: orderId=RECHARGE_1753815580853_e9ca23d6, amount=1000, qrCode=已生成
2025-07-30 02:59:41.738 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 02:59:41.738 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:25.804 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:00:25.805 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:00:28.814 [http-nio-8080-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:58 - 【websocket消息】有新的连接，总数为:1
2025-07-30 03:00:28.857 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.util.RoleChecker:39 - === 开始检查用户admin角色 ===
2025-07-30 03:00:28.858 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.util.RoleChecker:40 - 用户ID：e9ca23d68d884d4ebb19d07889727dae
2025-07-30 03:00:28.872 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.util.RoleChecker:47 - 用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1
2025-07-30 03:00:28.878 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.util.RoleChecker:58 - 用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)
2025-07-30 03:00:28.878 [http-nio-8080-exec-5] INFO  org.jeecg.modules.system.util.RoleChecker:60 - *** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***
2025-07-30 03:00:28.882 [http-nio-8080-exec-10] INFO  org.jeecg.modules.api.controller.AigcApiController:716 - 计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00
2025-07-30 03:00:28.882 [http-nio-8080-exec-10] INFO  org.jeecg.modules.api.controller.AigcApiController:656 - admin用户查看系统总收入: 0.00
2025-07-30 03:00:30.850 [http-nio-8080-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 03:00:30.851 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 03:00:30.851 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 03:00:30.851 [http-nio-8080-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 03:00:30.851 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 03:00:30.851 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 03:00:30.888 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-30 03:00:30.889 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:30.890 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 03:00:30.890 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:30.896 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 03:00:30.897 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:33.222 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:00:33.225 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:00:33.226 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:33.259 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:00:33.259 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:35.700 [http-nio-8080-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:58 - 【websocket消息】有新的连接，总数为:2
2025-07-30 03:00:37.140 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-qr
2025-07-30 03:00:37.147 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815637143_e9ca23d6
2025-07-30 03:00:37.149 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 03:00:37.149 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:37.163 [http-nio-8080-exec-6] INFO  o.jeecg.modules.system.controller.AlipayController:99 - 💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753815637143_e9ca23d6, 金额: 1000
2025-07-30 03:00:37.163 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.service.AlipayService:139 - 💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753815637143_e9ca23d6, 金额: 1000
2025-07-30 03:00:38.191 [http-nio-8080-exec-6] INFO  sdk.biz.info:372 - Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&method=alipay.trade.precreate&sign=NORTi15KyUgYFo6IxYaA3s9w/a2jrM0Klyh1QzWZyluUvZX28Cdp7M6mZegENRvwLSYDT5kvU3SFQ+fXe47iAqifWFnoJahq64ID4CSkJK5LFNYcdOFOSiQ/VVaC522VlJzjzilFa7W4kJ9flETxvsBdVUxVJ2GIzshTGlq60aM+x/0fHd+iLEznuM8OQajXsrap2TAcFbQNvNmdOfzHPnq97aSYQJ2ZNddazxcLsUswDTYYhRKVoO2oZUnA8e2okryAufYLVjwRbHMJd9RwSc/4AONuJ8LjhqVmP3xSy5xCO4WrnxC4fleHaaQ76po9ttjnpQI12ocfU1ZFqVm0aQ==&notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&version=1.0&app_id=9021000150681157&sign_type=RSA2&timestamp=2025-07-30 03:00:37^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&format=json^_^ApplicationParams:biz_content={"out_trade_no":"RECHARGE_1753815637143_e9ca23d6","total_amount":"1000","subject":"智界Aigc账户充值","timeout_express":"15m","goods_type":"0","body":"充值金额：¥1000"}^_^15ms,997ms,16ms^_^trace_id:060d2b35175381563937879295993
2025-07-30 03:00:38.192 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.service.AlipayService:164 - 💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753815637143_e9ca23d6, 二维码: https://qr.alipay.com/bax09168vu7u5twelspg006f
2025-07-30 03:00:38.192 [http-nio-8080-exec-6] INFO  o.jeecg.modules.system.controller.AlipayController:103 - 🔍 扫码支付二维码URL: https://qr.alipay.com/bax09168vu7u5twelspg006f
2025-07-30 03:00:38.192 [http-nio-8080-exec-6] INFO  o.jeecg.modules.system.controller.AlipayController:110 - 🔍 返回扫码支付结果: orderId=RECHARGE_1753815637143_e9ca23d6, amount=1000, qrCode=已生成
2025-07-30 03:00:38.193 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:00:38.193 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:45.611 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1000, 支付方式: alipay-page
2025-07-30 03:00:45.618 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815645615_e9ca23d6
2025-07-30 03:00:45.618 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 03:00:45.618 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:45.631 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815645615_e9ca23d6, 金额: 1000
2025-07-30 03:00:45.631 [http-nio-8080-exec-3] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815645615_e9ca23d6, 金额: 1000
2025-07-30 03:00:45.645 [http-nio-8080-exec-3] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815645615_e9ca23d6
2025-07-30 03:00:45.646 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1312
2025-07-30 03:00:45.646 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 03:00:45.646 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753815645615_e9ca23d6, amount=1000, payForm长度=1312
2025-07-30 03:00:45.647 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 03:00:45.648 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:48.460 [http-nio-8080-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:0
2025-07-30 03:00:48.460 [http-nio-8080-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:1
2025-07-30 03:00:55.046 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:00:55.049 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:00:55.050 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:55.100 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:00:55.100 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:57.368 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 03:00:57.368 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:00:57.369 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 03:00:57.370 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 03:00:57.379 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 03:00:57.379 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=26, 当前页数据量=0
2025-07-30 03:00:57.382 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 03:00:57.382 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:00:57.382 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:57.384 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:00:57.384 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:00:57.384 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:57.385 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 03:00:57.385 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:00:57.386 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:57.389 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 03:00:57.392 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:00:57.393 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:00:57.406 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 03:00:57.408 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:00:57.409 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:02.134 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 03:01:02.140 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 03:01:02.143 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:02.143 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:02.150 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:01:02.153 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:02.153 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:30.686 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 100, 支付方式: alipay-page
2025-07-30 03:01:30.699 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753815690690_e9ca23d6
2025-07-30 03:01:30.700 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 03:01:30.700 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:30.713 [http-nio-8080-exec-10] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753815690690_e9ca23d6, 金额: 100
2025-07-30 03:01:30.713 [http-nio-8080-exec-10] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753815690690_e9ca23d6, 金额: 100
2025-07-30 03:01:30.726 [http-nio-8080-exec-10] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753815690690_e9ca23d6
2025-07-30 03:01:30.727 [http-nio-8080-exec-10] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1300
2025-07-30 03:01:30.727 [http-nio-8080-exec-10] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 03:01:30.728 [http-nio-8080-exec-10] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753815690690_e9ca23d6, amount=100, payForm长度=1300
2025-07-30 03:01:30.728 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 03:01:30.728 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.100 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:01:42.101 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 03:01:42.101 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 03:01:42.103 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 03:01:42.105 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 03:01:42.107 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 03:01:42.108 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 03:01:42.109 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=27, 当前页数据量=0
2025-07-30 03:01:42.109 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:01:42.113 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:42.114 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.112 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:01:42.117 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.118 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:42.118 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.110 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.123 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:01:42.123 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 03:01:42.124 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 03:01:42.127 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 03:01:42.132 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 03:01:42.132 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:42.134 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.135 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:01:42.136 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:42.136 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:42.138 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:42.138 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:44.659 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:01:44.662 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:01:44.662 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:01:44.685 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:01:44.686 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:02:45.454 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:02:45.454 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:02:45.466 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:02:45.469 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:02:45.470 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:02:45.502 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:02:45.504 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:03:02.861 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:03:02.861 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:03:02.883 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:03:02.886 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:03:02.886 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:03:02.911 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:03:02.912 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:03:14.803 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:03:14.803 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:03:14.977 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:03:14.979 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:03:14.980 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:03:15.013 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:03:15.013 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:03:26.298 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:03:26.299 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:03:26.546 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:03:26.549 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:03:26.549 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:03:26.590 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:03:26.590 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:14.805 [http-nio-8080-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:58 - 【websocket消息】有新的连接，总数为:1
2025-07-30 03:04:14.913 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.util.RoleChecker:39 - === 开始检查用户admin角色 ===
2025-07-30 03:04:14.914 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.util.RoleChecker:40 - 用户ID：e9ca23d68d884d4ebb19d07889727dae
2025-07-30 03:04:14.926 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.util.RoleChecker:47 - 用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1
2025-07-30 03:04:14.931 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.util.RoleChecker:58 - 用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)
2025-07-30 03:04:14.931 [http-nio-8080-exec-6] INFO  org.jeecg.modules.system.util.RoleChecker:60 - *** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***
2025-07-30 03:04:14.936 [http-nio-8080-exec-2] INFO  org.jeecg.modules.api.controller.AigcApiController:716 - 计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00
2025-07-30 03:04:14.937 [http-nio-8080-exec-2] INFO  org.jeecg.modules.api.controller.AigcApiController:656 - admin用户查看系统总收入: 0.00
2025-07-30 03:04:19.855 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 03:04:19.857 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 03:04:19.858 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 03:04:19.859 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 03:04:19.859 [http-nio-8080-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 03:04:19.860 [http-nio-8080-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 03:04:19.889 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 03:04:19.890 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:19.890 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-30 03:04:19.890 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:19.898 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 03:04:19.899 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:21.481 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:04:21.484 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:04:21.484 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:21.514 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:04:21.514 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:24.807 [http-nio-8080-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:58 - 【websocket消息】有新的连接，总数为:2
2025-07-30 03:04:47.773 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:04:47.773 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:04:47.785 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:04:47.787 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:04:47.788 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:04:47.813 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:04:47.813 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:08.406 [http-nio-8080-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:1
2025-07-30 03:05:08.406 [http-nio-8080-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:0
2025-07-30 03:05:09.817 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:09.817 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:11.554 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:05:11.557 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:11.557 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:11.609 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:05:11.610 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:16.932 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 03:05:16.934 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:05:16.936 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 03:05:16.934 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 03:05:16.942 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 03:05:16.947 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=27, 当前页数据量=0
2025-07-30 03:05:16.953 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 03:05:16.954 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:16.955 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:16.956 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:16.956 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:05:16.957 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:16.957 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:16.969 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:05:16.970 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 03:05:16.971 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 03:05:16.973 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:05:16.974 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 03:05:16.976 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:16.978 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:16.978 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:18.220 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 03:05:18.224 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:05:18.226 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 03:05:18.227 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:18.227 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:18.229 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:18.229 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:25.528 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:25.528 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:30.826 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:05:30.854 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:30.854 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:30.876 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:05:30.876 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:35.145 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:35.145 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:35.164 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:05:35.167 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:35.168 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:35.206 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:05:35.206 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:58.453 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:58.453 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:05:59.877 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:05:59.880 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:05:59.881 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:05:59.943 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:05:59.944 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:07:16.410 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:07:16.410 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:07:18.955 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:07:18.958 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:07:18.959 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:07:19.006 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:07:19.007 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:14:03.777 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:14:03.780 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:14:03.780 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:14:03.822 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:14:03.823 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:14:09.472 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=3, duration=12, amount=489, planName=SVIP年卡, features=[解锁全部课程, 插件最高折扣, 邀请奖励50%, 调用工作流最高折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}
2025-07-30 03:14:09.491 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=489.0, orderId=ORDER_1753816449475_254, createTime=Wed Jul 30 03:14:09 CST 2025, planName=SVIP年卡, transactionId=406fb536bf914262869679e757f3c57b, status=pending}
2025-07-30 03:14:09.491 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:14:09.492 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:14:13.327 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=2, duration=12, amount=298, planName=VIP年卡, features=[解锁VIP课程, 插件基础折扣, 邀请奖励35%基础比例, 调用工作流基础折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}
2025-07-30 03:14:13.334 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=298.0, orderId=ORDER_1753816453330_687, createTime=Wed Jul 30 03:14:13 CST 2025, planName=VIP年卡, transactionId=6bc63a841cff4b84b578d88bd04c222a, status=pending}
2025-07-30 03:14:13.334 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:14:13.334 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:14:15.621 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=1, duration=1, amount=29, planName=VIP月卡, features=[解锁VIP课程, 插件基础折扣, 邀请奖励35%基础比例, 调用工作流基础折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}
2025-07-30 03:14:15.627 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=29.0, orderId=ORDER_1753816455624_790, createTime=Wed Jul 30 03:14:15 CST 2025, planName=VIP月卡, transactionId=72e0492041214f48b376cdc9b12a92b5, status=pending}
2025-07-30 03:14:15.628 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:14:15.628 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:14:17.479 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=3, duration=12, amount=489, planName=SVIP年卡, features=[解锁全部课程, 插件最高折扣, 邀请奖励50%, 调用工作流最高折扣, 复制所有工作流, 解锁流媒体转换, 部分插件免费]}
2025-07-30 03:14:17.488 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=489.0, orderId=ORDER_1753816457482_308, createTime=Wed Jul 30 03:14:17 CST 2025, planName=SVIP年卡, transactionId=ccb9a52e7784465e902b80e3776424ed, status=pending}
2025-07-30 03:14:17.488 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:14:17.489 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:16:48.505 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:16:48.505 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:16:48.893 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:16:48.895 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:16:48.896 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:16:48.924 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:16:48.925 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:17:11.612 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:17:11.612 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:17:11.855 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:17:11.858 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:17:11.858 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:17:11.886 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:17:11.887 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:17:32.789 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:17:32.789 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:17:33.100 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:17:33.102 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:17:33.103 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:17:33.127 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:17:33.128 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:18:52.021 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:18:52.021 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:18:52.428 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:18:52.431 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:18:52.431 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:18:52.468 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:18:52.469 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:19:08.383 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:19:08.383 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 03:19:08.563 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 03:19:08.567 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:19:08.568 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:19:08.639 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 03:19:08.640 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:39:39.860 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:39:39.862 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 03:39:39.863 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 03:39:39.861 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 03:39:39.880 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:39:39.880 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:39:39.885 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 03:39:39.888 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 03:39:39.890 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 03:39:39.890 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:39:39.890 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 03:39:39.890 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:39:39.896 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 03:39:39.896 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 03:39:39.897 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 03:39:39.899 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:39:39.899 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 03:39:39.899 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:39:39.899 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 03:39:39.901 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 03:39:39.901 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:02:40.714 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:02:40.719 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:02:40.719 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:02:40.734 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:02:40.734 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:12:57.863 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:12:57.863 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:12:57.974 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:12:57.976 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:12:57.976 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:12:58.007 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:12:58.008 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:19:27.301 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:19:27.304 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:19:27.306 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:19:27.343 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:19:27.343 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:48.383 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:20:48.383 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:20:48.388 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:20:48.389 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:20:48.416 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:20:48.418 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 04:20:48.422 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:20:48.422 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:48.423 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:20:48.423 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:48.419 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 04:20:48.423 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:20:48.423 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:48.424 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:20:48.424 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:48.425 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:48.427 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:20:48.427 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:48.429 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:20:48.432 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:48.432 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:50.798 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 04:20:50.804 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:20:50.808 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:50.808 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:50.809 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-30 04:20:50.812 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:50.812 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:52.503 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:20:52.507 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:52.507 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:52.530 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:20:52.530 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:56.384 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:20:56.387 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:20:56.388 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:20:56.388 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:20:56.392 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 04:20:56.399 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:20:56.399 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:20:56.400 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 04:20:56.405 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:20:56.406 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:20:56.406 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:56.409 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:56.410 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:56.410 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:56.411 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:56.411 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:56.413 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:20:56.413 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:20:56.418 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:20:56.421 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:56.422 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:58.096 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:20:58.099 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:20:58.100 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:20:58.117 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:20:58.117 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:31.532 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:21:31.533 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:21:31.538 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:21:31.540 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:21:31.549 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 04:21:31.551 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:21:31.555 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 04:21:31.559 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:21:31.559 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:31.563 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:21:31.564 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:31.565 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:31.564 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:21:31.569 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:21:31.570 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:31.571 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:31.572 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:21:31.573 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:31.586 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:21:31.593 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:31.593 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:38.450 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:21:38.450 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:21:38.451 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:21:38.451 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:21:38.455 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 04:21:38.464 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:21:38.464 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:21:38.464 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 04:21:38.468 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:21:38.468 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:38.468 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:21:38.469 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:38.473 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:38.474 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:38.478 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:21:38.479 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:21:38.479 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:38.479 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:38.482 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:21:38.486 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:38.486 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.419 [http-nio-8080-exec-3] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 04:21:43.420 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 04:21:43.421 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 04:21:43.422 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.423 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.424 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:21:43.425 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.425 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.426 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.426 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.426 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.432 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:21:43.432 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.433 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.434 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.521 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 04:21:43.522 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 04:21:43.629 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 04:21:43.630 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 04:21:43.630 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.900 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:21:43.900 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:21:43.900 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:21:43.902 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:21:43.906 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-30 04:21:43.906 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 04:21:43.909 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=154, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:21:43.910 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:21:43.914 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:21:43.915 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.915 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.916 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:21:43.916 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.916 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.920 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:21:43.921 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.921 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:21:43.921 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:21:43.924 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:21:43.927 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:21:43.927 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:22:00.600 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:22:00.603 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:22:00.603 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:22:00.633 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:22:00.633 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:23:56.732 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:23:56.733 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:23:56.733 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:23:56.733 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:23:56.758 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Wed Jul 30 04:23:19 CST 2025, sysOrgCode=A01)
2025-07-30 04:23:56.762 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:23:56.764 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:23:56.766 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:23:56.767 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:23:56.770 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:23:56.775 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:23:56.778 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:23:56.779 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:23:56.778 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=null, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:23:56.779 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=31, 当前页数据量=0
2025-07-30 04:23:56.781 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:23:56.781 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:23:56.781 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:23:56.781 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:23:56.782 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:23:56.782 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.533 [http-nio-8080-exec-7] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 e9ca23d68d884d4ebb19d07889727dae 已有邀请码: ZJ19385F
2025-07-30 04:24:04.534 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 admin 使用新服务获取/生成邀请码: ZJ19385F
2025-07-30 04:24:04.537 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 04:24:04.537 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:24:04.538 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.538 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:24:04.538 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.540 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:24:04.540 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:24:04.540 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.540 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.541 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:24:04.542 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.551 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:24:04.552 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:04.655 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ19385F
2025-07-30 04:24:04.655 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403811852.png
2025-07-30 04:24:04.738 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403811852.png
2025-07-30 04:24:04.738 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 04:24:04.738 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:05.071 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:24:05.072 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:24:05.073 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:05.096 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:24:05.096 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:09.521 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:24:09.523 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:24:09.523 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:24:09.574 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:24:09.575 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:25:58.659 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:25:58.659 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:25:58.682 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:25:58.685 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:25:58.685 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:04.190 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:26:04.193 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:26:04.193 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:04.241 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:26:04.241 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:17.376 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:26:17.376 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:26:17.616 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:26:17.620 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:26:17.620 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:17.688 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:26:17.689 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:32.869 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:26:32.872 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:26:32.872 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:32.907 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:26:32.908 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:26:33.033 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:26:33.033 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:30:48.816 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:30:48.816 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:30:49.044 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:30:49.046 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:30:49.047 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:30:49.066 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:30:49.066 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:32:23.346 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:32:23.348 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:32:23.348 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:32:23.385 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:32:23.385 [http-nio-8080-exec-4] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:32:23.386 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:32:23.386 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:32:38.566 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:32:38.566 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:32:38.598 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:32:38.600 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:32:38.601 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:32:38.645 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:32:38.646 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:33:14.533 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:33:14.533 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:33:14.562 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:33:14.564 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:33:14.564 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:33:14.606 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:33:14.606 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:34:06.706 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:34:06.706 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:34:06.727 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:34:06.729 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:34:06.730 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:34:06.764 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:34:06.765 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:34:21.271 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:34:21.271 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:34:21.297 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:34:21.299 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:34:21.300 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:34:21.335 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:34:21.335 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:34:54.462 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=3, duration=12, amount=489, planName=SVIP年卡, paymentMethod=alipay}
2025-07-30 04:34:54.468 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=489.0, orderId=ORDER_1753821294464_203, createTime=Wed Jul 30 04:34:54 CST 2025, planName=SVIP年卡, transactionId=2db000a6a33d43a2841ca5f966252c43, status=pending}
2025-07-30 04:34:54.468 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:34:54.468 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:34:56.770 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: admin, 请求数据: {membershipLevel=2, duration=12, amount=298, planName=VIP年卡, paymentMethod=alipay}
2025-07-30 04:34:56.775 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=298.0, orderId=ORDER_1753821296772_980, createTime=Wed Jul 30 04:34:56 CST 2025, planName=VIP年卡, transactionId=215015aab71e47aab2fbeb6f50123be0, status=pending}
2025-07-30 04:34:56.775 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:34:56.776 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:38:29.696 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:38:29.696 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:38:29.710 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:38:29.712 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:38:29.713 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:38:29.746 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:38:29.746 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:23.164 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-30 04:39:23.166 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:39:23.166 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:23.218 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:39:23.218 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:26.822 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:39:26.822 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-30 04:39:26.823 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-30 04:39:26.823 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-30 04:39:26.828 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:39:26.830 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:39:26.830 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:26.831 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Wed Jul 30 04:23:19 CST 2025, sysOrgCode=A01)
2025-07-30 04:39:26.833 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=100000.00, totalRecharge=0.00}
2025-07-30 04:39:26.836 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:39:26.836 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:26.839 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=33, 当前页数据量=0
2025-07-30 04:39:26.841 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:39:26.841 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:39:26.841 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:26.841 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-30 04:39:26.842 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:26.842 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-30 04:39:26.845 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=null, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-30 04:39:26.847 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:39:26.847 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:30.601 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:79 -  LogContent length : 14
2025-07-30 04:39:30.601 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:80 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:30.601 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.LoginController:216 -  用户名:  管理员,退出成功！ 
2025-07-30 04:39:30.601 [http-nio-8080-exec-8] INFO  o.j.modules.system.service.UserCacheCleanupService:41 - 🧹 开始清理用户登录缓存，用户：admin
2025-07-30 04:39:30.602 [http-nio-8080-exec-8] INFO  o.j.modules.system.service.UserCacheCleanupService:49 - ✅ 用户 admin 登录缓存清理完成
2025-07-30 04:39:30.618 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:79 -  LogContent length : 14
2025-07-30 04:39:30.618 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:80 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:30.619 [http-nio-8080-exec-6] INFO  o.jeecg.modules.system.controller.LoginController:216 -  用户名:  管理员,退出成功！ 
2025-07-30 04:39:30.619 [http-nio-8080-exec-6] INFO  o.j.modules.system.service.UserCacheCleanupService:41 - 🧹 开始清理用户登录缓存，用户：admin
2025-07-30 04:39:30.620 [http-nio-8080-exec-6] INFO  o.j.modules.system.service.UserCacheCleanupService:49 - ✅ 用户 admin 登录缓存清理完成
2025-07-30 04:39:34.502 [http-nio-8080-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:39:34.504 [http-nio-8080-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:39:34.503 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:39:34.504 [http-nio-8080-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:39:34.504 [http-nio-8080-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:39:34.504 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:39:34.515 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-30 04:39:34.515 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 04:39:34.515 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:34.516 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:34.516 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 04:39:34.516 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:39:39.004 [http-nio-8080-exec-3] INFO  o.j.m.s.service.impl.AicgVerifyCodeServiceImpl:63 - 发送短信验证码，手机号：***********，场景：register
2025-07-30 04:39:40.518 [http-nio-8080-exec-3] INFO  org.jeecg.common.util.DySmsHelper:97 - 短信接口返回的数据----------------
2025-07-30 04:39:40.519 [http-nio-8080-exec-3] INFO  org.jeecg.common.util.DySmsHelper:98 - {Code:OK,Message:OK,RequestId:BD425F65-B572-5523-96EE-D2D88EBEF45F,BizId:424224853821582393^0}
2025-07-30 04:39:40.544 [http-nio-8080-exec-3] INFO  o.j.m.s.service.impl.AicgVerifyCodeServiceImpl:113 - 短信验证码发送成功，手机号：***********
2025-07-30 04:40:10.053 [http-nio-8080-exec-7] INFO  o.j.m.s.service.impl.AicgVerifyCodeServiceImpl:200 - login场景未找到验证码，尝试查找register场景，目标：***********，类型：sms
2025-07-30 04:40:10.064 [http-nio-8080-exec-7] INFO  o.j.m.s.service.impl.AicgVerifyCodeServiceImpl:222 - 验证码验证成功，目标：***********，类型：sms，实际场景：register
2025-07-30 04:40:10.073 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:493 - 官网用户通过手机号登录: ***********, 登录类型: website
2025-07-30 04:40:10.073 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:785 - === 开始统一登录处理 ===
2025-07-30 04:40:10.073 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:786 - 用户：***********，登录类型：website
2025-07-30 04:40:10.073 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:39 - === 开始检查用户admin角色 ===
2025-07-30 04:40:10.074 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:40 - 用户ID：19437**************
2025-07-30 04:40:10.083 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:47 - 用户 19437************** 的角色数量：1
2025-07-30 04:40:10.090 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:58 - 用户 19437************** 具有角色：普通用户 (role_code: user)
2025-07-30 04:40:10.091 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:68 - 用户 19437************** 不具有admin角色
2025-07-30 04:40:10.241 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:944 - 开始记录用户在线状态 - 用户ID: 19437**************, 会话ID: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:10.249 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:948 - 设置用户 19437************** 的其他会话为离线，影响行数: 2
2025-07-30 04:40:10.249 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:960 - 准备插入在线用户记录: AicgOnlineUsers(id=null, userId=19437**************, sessionId=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY, loginTime=Wed Jul 30 04:40:10 CST 2025, lastActiveTime=Wed Jul 30 04:40:10 CST 2025, ipAddress=null, userAgent=null, status=true, createTime=Wed Jul 30 04:40:10 CST 2025, updateTime=Wed Jul 30 04:40:10 CST 2025)
2025-07-30 04:40:10.260 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:966 - 用户 19437************** 在线状态记录成功，插入行数: 1, 生成ID: 1950295305593319425
2025-07-30 04:40:10.262 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:39 - === 开始检查用户admin角色 ===
2025-07-30 04:40:10.262 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:40 - 用户ID：19437**************
2025-07-30 04:40:10.265 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:47 - 用户 19437************** 的角色数量：1
2025-07-30 04:40:10.267 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:58 - 用户 19437************** 具有角色：普通用户 (role_code: user)
2025-07-30 04:40:10.267 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.RoleChecker:68 - 用户 19437************** 不具有admin角色
2025-07-30 04:40:10.268 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.SingleLoginManager:63 - 检查Redis Key：current_user_token_19437**************，强制登录：false
2025-07-30 04:40:10.269 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.SingleLoginManager:65 - 当前Token对象：不存在
2025-07-30 04:40:10.269 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.SingleLoginManager:114 - 非admin用户 *********** 首次登录或无现有Token
2025-07-30 04:40:10.269 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.SingleLoginManager:118 - 记录新Token到Redis：current_user_token_19437**************
2025-07-30 04:40:10.270 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.util.SingleLoginManager:121 - 新Token记录完成（原子操作）
2025-07-30 04:40:10.270 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:821 - 已通过SingleLoginManager处理用户登录状态：***********，强制登录：false
2025-07-30 04:40:10.271 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:79 -  LogContent length : 24
2025-07-30 04:40:10.272 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:80 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:10.303 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:10.303 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:40:10.315 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:40:10.317 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:10.318 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:10.776 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:10.777 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:40:10.781 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:10.781 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:40:10.781 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:40:10.782 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:40:10.784 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:10.785 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:10.785 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:40:10.785 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:40:10.795 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:10.796 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:40:10.801 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:10.802 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:10.802 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:10.802 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:10.802 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:10.802 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:10.812 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:10.814 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:10.814 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:18.534 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:40:18.536 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:18.536 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:18.564 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:40:18.564 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:20.940 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:20.941 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:40:20.945 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:40:20.945 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:40:20.945 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:40:20.947 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:40:20.952 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:20.953 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:20.954 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:20.955 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:20.958 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:20.958 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:20.958 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:20.958 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:20.958 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:20.958 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:20.958 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:20.959 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:40:20.961 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:40:20.963 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:20.964 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:22.038 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:40:22.040 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:22.040 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:22.064 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:40:22.064 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.060 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:23.060 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:40:23.061 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:40:23.061 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:40:23.063 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:40:23.065 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:23.065 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:23.065 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:40:23.067 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:23.066 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:23.067 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:23.067 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.067 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.067 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.067 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:23.067 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:23.068 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:40:23.068 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.069 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:40:23.071 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:23.071 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.861 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:40:23.863 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:23.863 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:23.884 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:40:23.885 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:56.561 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:56.561 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:40:56.564 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:40:56.564 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:40:56.565 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:40:56.569 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:40:56.572 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:56.572 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:56.572 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:56.572 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:56.574 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:56.574 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:56.575 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:56.575 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:56.575 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:56.575 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:56.700 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:56.701 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:40:56.706 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:40:56.708 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:56.709 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:57.706 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:40:57.709 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:57.709 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:57.744 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:40:57.744 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:58.586 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:58.586 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:40:58.588 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:40:58.588 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:40:58.588 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:40:58.590 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:40:58.591 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:58.591 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:40:58.593 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:58.593 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:58.593 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:58.593 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:58.594 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:40:58.594 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:58.594 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:58.595 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:40:58.599 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:40:58.600 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:40:58.603 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:40:58.605 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:40:58.606 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:00.046 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:41:00.047 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:00.047 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:00.073 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:41:00.073 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:04.325 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:41:04.326 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:41:04.326 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:41:04.327 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:41:04.328 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:41:04.329 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:41:04.331 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:41:04.330 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:41:04.332 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:04.332 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:04.332 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:04.333 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:04.334 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:41:04.334 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:04.334 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:41:04.335 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:04.342 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:41:04.344 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:41:04.346 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:41:04.349 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:04.349 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:19.569 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: 
2025-07-30 04:41:19.570 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:622 - 🎯 getOrderStats - 用户: ***********
2025-07-30 04:41:19.572 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:41:19.573 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:19.573 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:19.578 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:674 - 🎯 getOrderStats - 统计结果: {totalAmount=0, monthCancelledOrders=0, pluginOrders=0, monthAmount=0, totalCancelledOrders=0, pendingOrders=0, rechargeOrders=0, totalCompletedOrders=0, monthCompletedOrders=0, membershipOrders=0}
2025-07-30 04:41:19.581 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:19.581 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:23.692 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: ***********, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-30 04:41:23.695 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: ***********, 总数: 0, 当前页: 1
2025-07-30 04:41:23.696 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:41:23.698 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:23.698 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:23.699 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:23.699 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:30.149 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:41:30.149 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:41:30.150 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:41:30.149 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:41:30.152 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:41:30.155 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:41:30.156 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:41:30.156 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:41:30.157 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:41:30.157 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:30.157 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:41:30.158 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:30.158 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:30.158 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:30.158 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:30.158 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:33.010 [http-nio-8080-exec-1] INFO  o.j.config.sign.interceptor.SignAuthInterceptor:35 - request URI = /jeecg-boot/sys/dict/getDictItems/plugin_category
2025-07-30 04:41:33.030 [http-nio-8080-exec-1] INFO  org.jeecg.config.sign.util.SignUtil:48 - Param paramsJsonStr : {}
2025-07-30 04:41:33.033 [http-nio-8080-exec-1] INFO  org.jeecg.config.sign.util.SignUtil:35 - Param Sign : E19D6243CB1945AB4F7202A1B00F77D5
2025-07-30 04:41:33.056 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:41:33.056 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:41:33.074 [http-nio-8080-exec-1] INFO  o.j.modules.system.controller.SysDictController:157 -  dictCode : plugin_category
2025-07-30 04:41:33.169 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:41:33.170 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:34.755 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:41:34.755 [http-nio-8080-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:41:34.756 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 04:41:34.756 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:41:34.756 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:41:34.756 [http-nio-8080-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 04:41:34.762 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-30 04:41:34.763 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:34.765 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 04:41:34.766 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:34.767 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 04:41:34.768 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.176 [http-nio-8080-exec-1] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:41:36.176 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:41:36.225 [http-nio-8080-exec-1] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 19437************** 已有邀请码: ZJ194EB9
2025-07-30 04:41:36.226 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-30 04:41:36.226 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:36.226 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:41:36.226 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.226 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:41:36.226 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.232 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:36.233 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.236 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:41:36.236 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.237 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:36.238 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.244 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:36.244 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.366 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:41:36.367 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:41:36.455 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:41:36.456 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 04:41:36.456 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.559 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:41:36.562 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:36.562 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:36.587 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:41:36.587 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:53.479 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:41:53.480 [http-nio-8080-exec-1] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:41:53.484 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:41:53.486 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:41:53.486 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:41:53.514 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:41:53.514 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:42:22.173 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:42:22.175 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:42:22.175 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:42:22.240 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:42:22.240 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:42:43.101 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:42:43.101 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:42:43.121 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:42:43.123 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:42:43.123 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:42:43.147 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:42:43.147 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:42:52.302 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:42:52.304 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:42:52.304 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:42:52.348 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:42:52.349 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:45:12.464 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:45:12.464 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:45:12.493 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:45:12.496 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:45:12.496 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:45:12.540 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:45:12.540 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:45:29.200 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:45:29.200 [http-nio-8080-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:45:29.227 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:45:29.231 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:45:29.231 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:45:29.267 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:45:29.267 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:15.983 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:46:15.986 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:46:15.986 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:16.038 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:46:16.038 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.254 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:46:21.255 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.260 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:46:21.260 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.261 [http-nio-8080-exec-3] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 19437************** 已有邀请码: ZJ194EB9
2025-07-30 04:46:21.261 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-30 04:46:21.261 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:46:21.261 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:46:21.261 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.263 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:46:21.264 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.274 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:46:21.274 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.282 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:46:21.282 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:21.404 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:46:21.404 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:46:21.489 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:46:21.490 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 04:46:21.491 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:22.834 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:46:22.836 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:46:22.837 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:46:22.863 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:46:22.863 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:48:16.524 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:48:16.526 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:48:16.526 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:48:16.565 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:48:16.565 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:48:17.912 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:48:17.912 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:50:36.388 [http-nio-8080-exec-1] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 19437************** 已有邀请码: ZJ194EB9
2025-07-30 04:50:36.389 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-30 04:50:36.392 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:50:36.396 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:50:36.396 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:36.402 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:50:36.402 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:36.403 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:36.403 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:36.406 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:36.406 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:36.407 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:36.407 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:36.418 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:36.418 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:36.606 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:50:36.606 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:50:36.696 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:50:36.697 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 04:50:36.698 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:44.333 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:50:44.333 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:50:44.335 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:50:44.336 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:50:44.336 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:50:44.345 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:50:44.347 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:50:44.347 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:50:44.348 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:50:44.348 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:44.348 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:44.348 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:44.349 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:50:44.350 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:44.350 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:44.351 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:44.353 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:50:44.354 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:50:44.357 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:50:44.359 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:50:44.359 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:50:53.985 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:50:53.985 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:51:15.413 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:51:15.413 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:51:49.942 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:51:49.943 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:51:51.955 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:51:51.958 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:51:51.959 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:51:51.992 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:51:51.992 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:05.029 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:52:05.031 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:05.032 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:05.096 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:52:05.097 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:37.957 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:52:37.957 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:52:37.957 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:52:37.957 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:52:37.973 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:52:37.973 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:52:37.974 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:52:37.976 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:37.976 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:52:37.977 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:37.977 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:52:37.979 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:37.979 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:52:37.979 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:37.979 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:37.980 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:37.980 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:52:37.980 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:37.984 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:52:37.986 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:52:37.987 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:41.504 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:52:41.506 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:41.507 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:41.529 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:52:41.529 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:43.350 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:52:43.353 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-30 04:52:43.354 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-30 04:52:43.356 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-30 04:52:43.356 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=19437**************, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-30 04:52:43.359 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-30 04:52:43.360 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:52:43.362 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:52:43.362 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:43.363 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:43.363 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:43.364 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:43.364 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:43.364 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-30 04:52:43.368 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-30 04:52:43.368 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:52:43.368 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************.sui71cUwOwcotYlw99K6QEWju4AK9ndOCRyLoHpSaqY
2025-07-30 04:52:43.368 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-30 04:52:43.375 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=19437**************, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-30 04:52:43.377 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:52:43.378 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:53:47.852 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:53:47.855 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:53:47.856 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:53:47.884 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:53:47.884 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:54:43.721 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:54:43.721 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:54:44.768 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:54:44.770 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:54:44.770 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:54:44.799 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:54:44.799 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:54:52.905 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:54:52.904 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:54:52.928 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:54:52.930 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:54:52.930 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:54:52.957 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:54:52.958 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:36.647 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:55:36.647 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:55:36.937 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:55:36.938 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:36.939 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:36.949 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:55:36.949 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.058 [http-nio-8080-exec-6] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:55:46.058 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:55:46.081 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:55:46.083 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:46.083 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.105 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:55:46.105 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.291 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:46.292 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.293 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:46.294 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.295 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:46.296 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.296 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:55:46.297 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.299 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:46.299 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.300 [http-nio-8080-exec-7] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 19437************** 已有邀请码: ZJ194EB9
2025-07-30 04:55:46.300 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-30 04:55:46.301 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:55:46.301 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 04:55:46.301 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.426 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3725 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-30 04:55:46.426 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3744 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:55:46.512 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:3756 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-30 04:55:46.513 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 04:55:46.513 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.665 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:55:46.667 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:46.667 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:46.687 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:55:46.688 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:59.933 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:55:59.935 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:55:59.936 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:55:59.947 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:55:59.947 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:56:00.141 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:56:00.141 [http-nio-8080-exec-10] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:56:47.360 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:56:47.362 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:56:47.362 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:56:47.405 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:56:47.405 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:57:04.953 [http-nio-8080-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:57:04.953 [http-nio-8080-exec-7] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 04:57:04.967 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 04:57:04.969 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 04:57:04.970 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 04:57:04.993 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 04:57:04.994 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:00:41.799 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:3389 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 05:00:41.801 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:00:41.801 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:00:41.858 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:00:41.858 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:01:41.392 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:2377 - 🎯 createMembershipOrder - 用户: ***********, 请求数据: {membershipLevel=1, duration=1, amount=29, planName=VIP月卡, paymentMethod=alipay}
2025-07-30 05:01:41.397 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:2452 - 🎯 createMembershipOrder - 订单创建成功: {amount=29.0, orderId=ORDER_1753822901393_152, createTime=Wed Jul 30 05:01:41 CST 2025, planName=VIP月卡, transactionId=5899c3abfbc345f5968ad97d98f67917, status=pending}
2025-07-30 05:01:41.397 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:01:41.398 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:01:48.180 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: ***********, 金额: 50, 支付方式: alipay-qr
2025-07-30 05:01:48.185 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753822908183_19437477
2025-07-30 05:01:48.186 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 05:01:48.186 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:01:48.199 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:99 - 💰 创建支付宝扫码支付订单请求 - 订单号: RECHARGE_1753822908183_19437477, 金额: 50
2025-07-30 05:01:48.199 [http-nio-8080-exec-8] INFO  org.jeecg.modules.system.service.AlipayService:139 - 💰 创建支付宝扫码支付订单 - 订单号: RECHARGE_1753822908183_19437477, 金额: 50
2025-07-30 05:01:49.743 [http-nio-8080-exec-8] INFO  sdk.biz.info:372 - Summary^_^10000^_^null^_^ProtocalMustParams:charset=UTF-8&method=alipay.trade.precreate&sign=OKU1kS9POBYtWCqgAdMAqzDyoEL8mgx/Om5+DTh53I5LkhbojScjX2OESLgXoSeXBtO8kK2mmTdfCb1VybNnOkJMLM6xlwnjdCeuAQJ8BX5MzZpczoj76h0e5Np7Q3kA9CbNquEc0w3rymaIKfYt+FdoWcx/iug8jZHs0mAoX9dR69r+wGwLlBfICDi09O2SyHorXgIa83/WgvLi5KJxap6YCAEwKOmN44fSyt8vLiflrDoT8U3IZSBTgQQ8bLnFvxStiIIorjgdqdcfhwsWwu5L/pg7zc9WdlXHKni7b/+v9cYHXellp29NCNocyS/Wf47SR1Bg2mpct49rHCyIDQ==&notify_url=http://localhost:8080/jeecg-boot/api/alipay/notify&version=1.0&app_id=9021000150681157&sign_type=RSA2&timestamp=2025-07-30 05:01:48^_^ProtocalOptParams:alipay_sdk=alipay-sdk-java-4.38.200.ALL&format=json^_^ApplicationParams:biz_content={"out_trade_no":"RECHARGE_1753822908183_19437477","total_amount":"50","subject":"智界Aigc账户充值","timeout_express":"15m","goods_type":"0","body":"充值金额：¥50"}^_^15ms,1513ms,15ms^_^trace_id:0601ac3e175382291073015424924
2025-07-30 05:01:49.744 [http-nio-8080-exec-8] INFO  org.jeecg.modules.system.service.AlipayService:164 - 💰 支付宝扫码支付订单创建成功 - 订单号: RECHARGE_1753822908183_19437477, 二维码: https://qr.alipay.com/bax00125l4zjb6gylxez0044
2025-07-30 05:01:49.744 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:103 - 🔍 扫码支付二维码URL: https://qr.alipay.com/bax00125l4zjb6gylxez0044
2025-07-30 05:01:49.744 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:110 - 🔍 返回扫码支付结果: orderId=RECHARGE_1753822908183_19437477, amount=50, qrCode=已生成
2025-07-30 05:01:49.745 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:01:49.745 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:01:53.394 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: ***********, 金额: 50, 支付方式: alipay-page
2025-07-30 05:01:53.400 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:1833 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753822913397_19437477
2025-07-30 05:01:53.400 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 05:01:53.401 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:01:53.412 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:65 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753822913397_19437477, 金额: 50
2025-07-30 05:01:53.413 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753822913397_19437477, 金额: 50
2025-07-30 05:01:53.427 [http-nio-8080-exec-7] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753822913397_19437477
2025-07-30 05:01:53.427 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:69 - 🔍 支付表单内容长度: 1298
2025-07-30 05:01:53.428 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:70 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 05:01:53.428 [http-nio-8080-exec-7] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 返回结果: orderId=RECHARGE_1753822913397_19437477, amount=50, payForm长度=1298
2025-07-30 05:01:53.428 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 05:01:53.429 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:02:26.615 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:205 - 💰 收到支付宝同步返回
2025-07-30 05:02:26.616 [http-nio-8080-exec-3] INFO  org.jeecg.modules.system.service.AlipayService:240 - 💰 支付宝异步通知签名验证结果: true
2025-07-30 05:02:26.617 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:219 - 💰 支付宝同步返回参数 - 订单号: RECHARGE_1753822913397_19437477, 交易号: 2025073022001476680506759714, 金额: 50.00
2025-07-30 05:02:26.618 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:234 - 💰 动态获取前端地址: http://localhost:8080 (scheme: http, serverName: localhost, port: 8080)
2025-07-30 05:02:26.618 [http-nio-8080-exec-3] INFO  o.jeecg.modules.system.controller.AlipayController:238 - 💰 重定向到前端页面: http://localhost:8080/usercenter?page=credits&paymentSuccess=true&orderId=RECHARGE_1753822913397_19437477
2025-07-30 05:02:26.631 [http-nio-8080-exec-3] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:80 - Method [updateUserMembershipStatus] was discovered in the .class file but cannot be resolved in the class object
java.lang.IllegalStateException: Method [updateUserMembershipStatus] was discovered in the .class file but cannot be resolved in the class object
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:242)
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:225)
	at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1498)
	at org.springframework.asm.ClassReader.accept(ClassReader.java:718)
	at org.springframework.asm.ClassReader.accept(ClassReader.java:401)
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:107)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84)
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72)
	at org.jeecg.common.aspect.AutoLogAspect.getReqestParams(AutoLogAspect.java:187)
	at org.jeecg.common.aspect.AutoLogAspect.saveSysLog(AutoLogAspect.java:98)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:62)
	at sun.reflect.GeneratedMethodAccessor276.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at org.jeecg.modules.system.controller.AlipayController$$EnhancerBySpringCGLIB$$4a1ec1ac.handleReturn(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoSuchMethodException: org.jeecg.modules.system.controller.AlipayController.updateUserMembershipStatus(java.lang.String, java.lang.Integer, java.lang.Integer)
	at java.lang.Class.getDeclaredMethod(Class.java:2130)
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:239)
	... 107 common frames omitted
2025-07-30 05:03:46.582 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-30 05:03:46.588 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-30 05:03:46.588 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753808280978 shutting down.
2025-07-30 05:03:46.589 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753808280978 paused.
2025-07-30 05:03:46.591 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753808280978 shutdown complete.
2025-07-30 05:03:46.601 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-30 05:03:46.605 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-30 05:03:46.607 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-30 05:03:46.607 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-30 05:03:56.371 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-30 05:03:56.406 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 36844 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-30 05:03:56.406 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-30 05:03:56.866 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-30 05:03:58.087 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 05:03:58.089 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 05:03:58.232 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 134ms. Found 0 Redis repository interfaces.
2025-07-30 05:03:58.357 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-30 05:03:58.357 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-30 05:03:58.358 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-30 05:03:58.441 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-30 05:03:58.442 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-30 05:03:58.442 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-30 05:03:58.442 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-30 05:03:58.442 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-30 05:03:58.442 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-30 05:03:58.443 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-30 05:03:58.443 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-30 05:03:58.444 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-30 05:03:58.444 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-30 05:03:58.611 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.614 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.615 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.615 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.616 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.616 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.617 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.617 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.618 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.619 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.619 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.621 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.621 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.623 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.623 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.624 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.624 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#7558633#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.625 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.642 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.645 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.751 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$ded408e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:58.784 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.181 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-30 05:03:59.182 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-30 05:03:59.185 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.187 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.219 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.358 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.363 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$183f6966] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.370 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.379 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$2b4f50be] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.417 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$c1c9d645] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.421 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 05:03:59.670 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-30 05:03:59.679 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 05:03:59.679 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-30 05:03:59.680 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-30 05:03:59.826 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-30 05:03:59.826 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3377 ms
2025-07-30 05:04:00.402 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-30 05:04:00.403 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-30 05:04:00.404 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-30 05:04:01.700 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-30 05:04:02.480 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-30 05:04:02.480 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-30 05:04:02.581 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-30 05:04:02.583 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-30 05:04:02.584 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-30 05:04:02.584 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-30 05:04:03.181 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-30 05:04:03.181 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-30 05:04:03.182 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-30 05:04:03.184 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-30 05:04:03.184 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-30 05:04:03.184 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-30 05:04:03.446 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-30 05:04:03.663 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-30 05:04:04.094 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-30 05:04:04.103 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-30 05:04:04.132 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-30 05:04:04.161 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-30 05:04:04.166 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-30 05:04:04.565 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-30 05:04:04.567 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-30 05:04:04.574 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 05:04:04.574 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 05:04:04.577 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-30 05:04:04.579 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-30 05:04:04.579 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753823044566'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-30 05:04:04.579 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-30 05:04:04.580 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-30 05:04:04.580 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6f12b637
2025-07-30 05:04:06.631 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-30 05:04:07.312 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-30 05:04:07.401 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-30 05:04:07.418 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 05:04:07.473 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-30 05:04:07.474 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-30 05:04:07.475 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-30 05:04:08.248 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 05:04:08.272 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-30 05:04:08.273 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-30 05:04:08.275 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-30 05:04:08.387 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-30 05:04:08.518 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-30 05:04:08.523 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-30 05:04:08.527 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-30 05:04:08.539 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-30 05:04:08.540 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-30 05:04:08.543 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-30 05:04:08.544 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-30 05:04:08.545 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-30 05:04:08.546 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-30 05:04:08.550 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-30 05:04:08.557 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-30 05:04:08.561 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-30 05:04:08.562 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-30 05:04:08.563 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-30 05:04:08.565 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-30 05:04:08.569 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-30 05:04:08.573 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-30 05:04:08.588 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-30 05:04:08.590 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-30 05:04:08.591 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-30 05:04:08.592 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-30 05:04:08.596 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-30 05:04:08.601 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-30 05:04:08.611 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-30 05:04:08.612 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-30 05:04:08.613 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-30 05:04:08.614 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-30 05:04:08.614 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-30 05:04:08.617 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-30 05:04:08.622 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-30 05:04:08.624 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-30 05:04:08.625 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-30 05:04:08.627 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-30 05:04:08.630 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-30 05:04:08.632 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-30 05:04:08.638 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-30 05:04:08.640 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-30 05:04:08.641 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-30 05:04:08.642 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-30 05:04:08.643 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-30 05:04:08.645 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-30 05:04:08.648 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-30 05:04:08.656 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-30 05:04:08.666 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-30 05:04:08.667 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-30 05:04:08.668 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-30 05:04:08.668 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-30 05:04:08.669 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-30 05:04:08.672 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-30 05:04:08.678 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-30 05:04:08.679 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-30 05:04:08.680 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-30 05:04:08.681 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-30 05:04:08.682 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-30 05:04:08.686 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-30 05:04:08.696 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-30 05:04:08.699 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-30 05:04:08.699 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-30 05:04:08.700 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-30 05:04:08.705 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-30 05:04:08.709 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-30 05:04:08.718 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-30 05:04:08.722 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-30 05:04:08.722 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-30 05:04:08.723 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-30 05:04:08.729 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-30 05:04:08.733 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-30 05:04:08.738 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-30 05:04:08.739 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-30 05:04:08.740 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-30 05:04:08.740 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-30 05:04:08.741 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-30 05:04:08.744 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-30 05:04:08.749 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-30 05:04:08.750 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-30 05:04:08.751 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-30 05:04:08.752 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-30 05:04:08.753 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-30 05:04:08.756 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-30 05:04:08.760 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-30 05:04:08.761 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-30 05:04:08.762 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-30 05:04:08.763 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-30 05:04:08.764 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-30 05:04:08.768 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-30 05:04:08.772 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-30 05:04:08.773 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-30 05:04:08.773 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-30 05:04:08.774 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-30 05:04:08.775 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-30 05:04:08.778 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-30 05:04:08.854 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-30 05:04:08.857 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-30 05:04:08.858 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-30 05:04:08.860 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-30 05:04:08.862 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-30 05:04:08.869 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-30 05:04:08.903 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-30 05:04:08.905 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-30 05:04:08.905 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-30 05:04:08.906 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-30 05:04:08.907 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-30 05:04:08.910 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-30 05:04:08.913 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-30 05:04:08.913 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-30 05:04:08.914 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-30 05:04:08.915 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-30 05:04:08.915 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-30 05:04:08.917 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-30 05:04:08.920 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-30 05:04:08.920 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-30 05:04:08.921 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-30 05:04:08.922 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-30 05:04:08.922 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-30 05:04:08.925 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-30 05:04:08.928 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-30 05:04:08.929 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-30 05:04:08.930 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-30 05:04:08.931 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-30 05:04:08.931 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-30 05:04:08.934 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-30 05:04:08.939 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-30 05:04:08.940 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-30 05:04:08.942 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-30 05:04:08.943 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-30 05:04:08.944 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-30 05:04:08.946 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-30 05:04:08.954 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-30 05:04:08.955 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-30 05:04:08.958 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-30 05:04:08.959 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-30 05:04:08.961 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-30 05:04:08.963 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-30 05:04:08.972 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-30 05:04:08.975 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-30 05:04:08.976 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-30 05:04:08.977 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-30 05:04:08.978 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-30 05:04:08.986 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-30 05:04:09.013 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-30 05:04:09.032 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-30 05:04:09.033 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-30 05:04:09.035 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-30 05:04:09.960 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 14.137 seconds (JVM running for 15.308)
2025-07-30 05:04:09.967 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-30 05:04:10.836 [RMI TCP Connection(4)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 05:04:10.836 [RMI TCP Connection(4)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 05:04:10.845 [RMI TCP Connection(4)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 8 ms
2025-07-30 05:04:21.700 [http-nio-8080-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:04:21.700 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:04:21.700 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:04:21.705 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:04:21.705 [http-nio-8080-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:04:21.705 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:04:22.143 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-30 05:04:22.143 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 05:04:22.143 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:22.143 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:22.147 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 05:04:22.147 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:23.507 [http-nio-8080-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:04:23.507 [http-nio-8080-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:04:23.646 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:04:23.647 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:29.705 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3390 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 05:04:29.708 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:04:29.708 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:29.747 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:04:29.747 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:31.490 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:2378 - 🎯 createMembershipOrder - 用户: ***********, 请求数据: {membershipLevel=1, duration=1, amount=29, planName=VIP月卡, paymentMethod=alipay}
2025-07-30 05:04:31.531 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:2453 - 🎯 createMembershipOrder - 订单创建成功: {amount=29.0, orderId=ORDER_1753823071497_788, createTime=Wed Jul 30 05:04:31 CST 2025, planName=VIP月卡, transactionId=1acbf453007747feaefceb7a516cf5be, status=pending}
2025-07-30 05:04:31.534 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:04:31.534 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:37.714 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1776 - 💰 创建充值订单 - 用户: ***********, 金额: 50, 支付方式: alipay-page
2025-07-30 05:04:37.723 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1834 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753823077720_19437477
2025-07-30 05:04:37.723 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-30 05:04:37.723 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:04:37.744 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:72 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753823077720_19437477, 金额: 50
2025-07-30 05:04:37.744 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:89 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753823077720_19437477, 金额: 50
2025-07-30 05:04:37.746 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:46 - 🔍 私钥调试信息:
2025-07-30 05:04:37.747 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:47 - 🔍 私钥是否为空: false
2025-07-30 05:04:37.747 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:49 - 🔍 私钥长度: 1624
2025-07-30 05:04:37.747 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:50 - 🔍 私钥前50字符: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQ
2025-07-30 05:04:37.747 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:51 - 🔍 私钥后50字符: XORYARFPfrcb5nvSh+GXn2TGujhV/JNnmeSI/gIpXbmN5MdLo=
2025-07-30 05:04:37.747 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:52 - 🔍 私钥是否包含换行符: false
2025-07-30 05:04:37.748 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:53 - 🔍 私钥是否包含回车符: false
2025-07-30 05:04:37.748 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:54 - 🔍 私钥是否包含空格: false
2025-07-30 05:04:37.748 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:55 - 🔍 私钥是否包含制表符: false
2025-07-30 05:04:37.749 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:59 - 🔍 清理后私钥长度: 1624
2025-07-30 05:04:37.786 [http-nio-8080-exec-1] INFO  org.jeecg.modules.system.service.AlipayService:116 - 💰 支付宝支付订单创建成功 - 订单号: RECHARGE_1753823077720_19437477
2025-07-30 05:04:37.786 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:76 - 🔍 支付表单内容长度: 1318
2025-07-30 05:04:37.787 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:77 - 🔍 支付表单前100字符: <form name="punchout_form" method="post" action="https://openapi-sandbox.dl.alipaydev.com/gateway.do
2025-07-30 05:04:37.787 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.AlipayController:84 - 🔍 返回结果: orderId=RECHARGE_1753823077720_19437477, amount=50, payForm长度=1318
2025-07-30 05:04:37.787 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-30 05:04:37.788 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:05:11.724 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:212 - 💰 收到支付宝同步返回
2025-07-30 05:05:11.726 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.service.AlipayService:240 - 💰 支付宝异步通知签名验证结果: true
2025-07-30 05:05:11.727 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:226 - 💰 支付宝同步返回参数 - 订单号: RECHARGE_1753823077720_19437477, 交易号: 2025073022001476680506763147, 金额: 50.00
2025-07-30 05:05:11.727 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:241 - 💰 动态获取前端地址: http://localhost:8080 (scheme: http, serverName: localhost, port: 8080)
2025-07-30 05:05:11.727 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.AlipayController:245 - 💰 重定向到前端页面: http://localhost:8080/usercenter?page=credits&paymentSuccess=true&orderId=RECHARGE_1753823077720_19437477
2025-07-30 05:05:11.728 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-30 05:05:11.728 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:05:51.856 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:05:51.856 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:05:51.856 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-30 05:05:51.857 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:05:51.857 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:05:51.857 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-30 05:05:51.880 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 05:05:51.880 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:05:51.890 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-30 05:05:51.890 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:05:51.891 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-30 05:05:51.892 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:05:53.995 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3390 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 05:05:53.997 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:05:53.998 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:05:54.052 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:05:54.052 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:07:26.230 [http-nio-8080-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 05:07:26.230 [http-nio-8080-exec-8] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 05:07:26.263 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3390 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 05:07:26.264 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:07:26.265 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:07:31.420 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3390 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 05:07:31.421 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:07:31.422 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:07:31.478 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:07:31.479 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:07:46.756 [http-nio-8080-exec-5] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 05:07:46.756 [http-nio-8080-exec-1] INFO  org.jeecg.config.shiro.ShiroRealm:99 - ————————身份认证失败——————————IP地址:  0:0:0:0:0:0:0:1
2025-07-30 05:07:48.422 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:3390 - 💰 计算用户本月消费 - 用户ID: 19437**************, 本月消费: 0.00
2025-07-30 05:07:48.425 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-30 05:07:48.425 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-30 05:07:48.499 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-30 05:07:48.499 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
