{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\App.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\App.vue", "mtime": 1753835199423}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n\nimport zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'\nimport enquireScreen from '@/utils/device'\nimport { handleReferralFromUrl } from '@/utils/referralUtils'\n\nexport default {\n  data () {\n    return {\n      locale: zhCN,\n    }\n  },\n  created () {\n    // 处理邀请链接参数\n    this.handleReferralCode()\n  },\n  mounted () {\n    let that = this\n    enquireScreen(deviceType => {\n      // tablet\n      if (deviceType === 0) {\n        that.$store.commit('TOGGLE_DEVICE', 'mobile')\n        that.$store.dispatch('setSidebar', false)\n      }\n      // mobile\n      else if (deviceType === 1) {\n        that.$store.commit('TOGGLE_DEVICE', 'mobile')\n        that.$store.dispatch('setSidebar', false)\n      }\n      else {\n        that.$store.commit('TOGGLE_DEVICE', 'desktop')\n        that.$store.dispatch('setSidebar', true)\n      }\n\n    })\n  },\n  watch: {\n    // 监听路由变化，处理邀请链接\n    '$route'(to, from) {\n      this.handleReferralCode()\n    }\n  },\n  methods: {\n    // 处理邀请码\n    handleReferralCode() {\n      try {\n        const currentUrl = window.location.href\n        handleReferralFromUrl(currentUrl)\n      } catch (error) {\n        console.warn('处理邀请码失败:', error)\n      }\n    }\n  }\n}\n", null]}