{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\utils\\referralUtils.js", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\utils\\referralUtils.js", "mtime": 1753836591475}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["/**\n * 邀请人管理工具\n * 使用sessionStorage存储，关闭浏览器会清除，刷新页面不会清除\n */\nvar REFERRAL_KEY = 'aigc_referral_code';\n/**\n * 设置邀请人\n * @param {string} referralCode 邀请码\n */\n\nexport function setReferralCode(referralCode) {\n  if (referralCode && referralCode.trim()) {\n    var code = referralCode.trim();\n\n    try {\n      sessionStorage.setItem(REFERRAL_KEY, code);\n      console.log('🔗 设置邀请人:', code);\n    } catch (error) {\n      console.error('🔗 设置邀请人失败:', error);\n    }\n  }\n}\n/**\n * 获取邀请人\n * @returns {string|null} 邀请码\n */\n\nexport function getReferralCode() {\n  try {\n    var code = sessionStorage.getItem(REFERRAL_KEY);\n    console.log('🔗 获取邀请人:', code);\n    return code;\n  } catch (error) {\n    console.error('🔗 获取邀请人失败:', error);\n    return null;\n  }\n}\n/**\n * 清除邀请人\n */\n\nexport function clearReferralCode() {\n  sessionStorage.removeItem(REFERRAL_KEY);\n  console.log('🔗 清除邀请人');\n}\n/**\n * 从URL中提取ref参数并设置邀请人\n * @param {string} url 当前URL\n */\n\nexport function handleReferralFromUrl(url) {\n  try {\n    var urlObj = new URL(url);\n    var refParam = urlObj.searchParams.get('ref');\n\n    if (refParam) {\n      setReferralCode(refParam);\n      console.log('🔗 从URL提取邀请码:', refParam);\n      return refParam;\n    } else {\n      // 如果URL中没有ref参数，检查sessionStorage中是否有邀请码\n      var existingCode = getReferralCode();\n\n      if (existingCode) {\n        console.log('🔗 URL中无ref参数，使用已存储的邀请码:', existingCode);\n        return existingCode;\n      }\n    }\n  } catch (error) {\n    console.warn('🔗 解析URL邀请参数失败:', error);\n  }\n\n  return null;\n}\n/**\n * 检查是否有有效的邀请人\n * @returns {boolean}\n */\n\nexport function hasReferralCode() {\n  var code = getReferralCode();\n  return !!(code && code.trim());\n}", null]}