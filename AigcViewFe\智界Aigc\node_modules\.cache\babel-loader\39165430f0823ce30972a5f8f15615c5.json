{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\utils\\referralUtils.js", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\utils\\referralUtils.js", "mtime": 1753835140589}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["/**\n * 邀请人管理工具\n * 使用sessionStorage存储，关闭浏览器会清除，刷新页面不会清除\n */\nvar REFERRAL_KEY = 'aigc_referral_code';\n/**\n * 设置邀请人\n * @param {string} referralCode 邀请码\n */\n\nexport function setReferralCode(referralCode) {\n  if (referralCode && referralCode.trim()) {\n    sessionStorage.setItem(REFERRAL_KEY, referralCode.trim());\n    console.log('🔗 设置邀请人:', referralCode.trim());\n  }\n}\n/**\n * 获取邀请人\n * @returns {string|null} 邀请码\n */\n\nexport function getReferralCode() {\n  var code = sessionStorage.getItem(REFERRAL_KEY);\n  console.log('🔗 获取邀请人:', code);\n  return code;\n}\n/**\n * 清除邀请人\n */\n\nexport function clearReferralCode() {\n  sessionStorage.removeItem(REFERRAL_KEY);\n  console.log('🔗 清除邀请人');\n}\n/**\n * 从URL中提取ref参数并设置邀请人\n * @param {string} url 当前URL\n */\n\nexport function handleReferralFromUrl(url) {\n  try {\n    var urlObj = new URL(url);\n    var refParam = urlObj.searchParams.get('ref');\n\n    if (refParam) {\n      setReferralCode(refParam);\n      console.log('🔗 从URL提取邀请码:', refParam); // 清除URL中的ref参数，避免用户看到\n\n      urlObj.searchParams.delete('ref');\n      var cleanUrl = urlObj.toString(); // 使用replaceState更新URL，不会触发页面刷新\n\n      if (window.history && window.history.replaceState) {\n        window.history.replaceState(null, '', cleanUrl);\n      }\n\n      return refParam;\n    }\n  } catch (error) {\n    console.warn('🔗 解析URL邀请参数失败:', error);\n  }\n\n  return null;\n}\n/**\n * 检查是否有有效的邀请人\n * @returns {boolean}\n */\n\nexport function hasReferralCode() {\n  var code = getReferralCode();\n  return !!(code && code.trim());\n}", null]}