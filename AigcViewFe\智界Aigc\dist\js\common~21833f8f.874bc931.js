(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~21833f8f"],{"0096":function(t,e,n){"use strict";n.r(e);for(var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 0 32px 32px"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{attrs:{height:"254",data:t.datasource,forceFit:!0,padding:["auto","auto","40","50"]}},[n("v-tooltip"),n("v-axis"),n("v-bar",{attrs:{position:"x*y"}})],1)],1)},i=[],r=[],s=0;s<12;s+=1)r.push({x:"".concat(s+1,"月"),y:Math.floor(1e3*Math.random())+200});var o=["x*y",function(t,e){return{name:t,value:e}}],l=[{dataKey:"x",min:2},{dataKey:"y",title:"时间",min:1,max:22}],c={name:"Bar",props:{title:{type:String,default:""}},mounted:function(){this.datasource=r},data:function(){return{datasource:[],scale:l,tooltip:o}}},u=c,d=n("2877"),p=Object(d["a"])(u,a,i,!1,null,null,null);e["default"]=p.exports},"0411":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"quick-recharge-card"},[n("div",{staticClass:"balance-section"},[n("div",{staticClass:"user-info"},[n("div",{staticClass:"user-avatar"},[n("a-avatar",{style:{backgroundColor:"#87d068"},attrs:{size:80,src:t.avatarUrl,icon:"user"}},[t._v("\n          "+t._s(t.userNickname?t.userNickname.charAt(0):"U")+"\n        ")])],1),n("div",{staticClass:"user-details"},[n("div",{staticClass:"user-name"},[t._v("\n          "+t._s(t.userNickname||"智界用户")+"\n          "),n("span",{staticClass:"user-role"},[t._v(t._s(t.getUserRoleDisplayName()))]),t.getMemberExpireInfo()?n("span",{staticClass:"expire-info"},[t._v(t._s(t.getMemberExpireInfo()))]):t._e()]),n("div",{staticClass:"balance-info"},[n("span",{staticClass:"balance-label"},[t._v("账户余额")]),n("span",{staticClass:"balance-amount"},[t._v("¥"+t._s(t.formatBalance(t.userBalance)))])])])]),n("div",{staticClass:"balance-actions"},[n("button",{staticClass:"recharge-btn",on:{click:function(e){t.showRechargeModal=!0}}},[n("i",{staticClass:"anticon anticon-plus-circle"}),t._v("\n        快速充值\n      ")])])]),n("a-modal",{attrs:{title:"账户充值",visible:t.showRechargeModal,footer:null,width:"500px"},on:{cancel:function(e){t.showRechargeModal=!1}}},[n("div",{staticClass:"recharge-modal-content"},[n("div",{staticClass:"recharge-options"},[n("h4",[t._v("选择充值金额")]),n("div",{staticClass:"options-grid"},t._l(t.rechargeOptions,(function(e){return n("div",{key:e.amount,staticClass:"recharge-option",class:{selected:t.selectedAmount===e.amount},on:{click:function(n){return t.selectRechargeAmount(e.amount)}}},[n("div",{staticClass:"option-amount"},[t._v("¥"+t._s(e.amount))]),n("div",{staticClass:"option-label"},[t._v(t._s(e.label))])])})),0)]),n("div",{staticClass:"custom-amount"},[n("h4",[t._v("自定义金额")]),n("div",{staticClass:"custom-input"},[n("a-input-number",{staticStyle:{width:"200px"},attrs:{min:.01,max:1e4,step:.01,placeholder:"最低0.01元",size:"large"},model:{value:t.customAmount,callback:function(e){t.customAmount=e},expression:"customAmount"}}),n("span",{staticClass:"currency"},[t._v("元")])],1)]),n("div",{staticClass:"payment-methods"},[n("h4",[t._v("支付方式")]),n("a-radio-group",{attrs:{size:"large"},model:{value:t.selectedPaymentMethod,callback:function(e){t.selectedPaymentMethod=e},expression:"selectedPaymentMethod"}},[n("a-radio-button",{attrs:{value:"alipay-qr"}},[n("i",{staticClass:"anticon anticon-qrcode"}),t._v("\n            支付宝扫码\n          ")]),n("a-radio-button",{attrs:{value:"alipay-page"}},[n("i",{staticClass:"anticon anticon-alipay"}),t._v("\n            支付宝网页\n          ")])],1)],1),n("div",{staticClass:"recharge-actions"},[n("div",{staticClass:"amount-summary"},[n("span",[t._v("充值金额：")]),n("span",{staticClass:"final-amount"},[t._v("¥"+t._s(t.finalRechargeAmount))])]),n("a-button",{attrs:{type:"primary",size:"large",loading:t.rechargeLoading,disabled:!t.finalRechargeAmount||t.finalRechargeAmount<.01},on:{click:t.handleRecharge}},[t._v("\n          确认充值\n        ")])],1)])]),n("a-modal",{attrs:{title:"扫码支付",visible:t.showQrModal,footer:null,width:"400px"},on:{cancel:t.closeQrModal}},[n("div",{staticClass:"qr-payment-content"},[n("div",{staticClass:"qr-code-container"},[t.qrCodeUrl?n("div",{staticClass:"qr-code"},[n("img",{attrs:{src:t.qrCodeUrl,alt:"支付二维码"}})]):n("div",{staticClass:"qr-loading"},[n("a-spin",{attrs:{size:"large"}}),n("p",[t._v("正在生成二维码...")])],1)]),n("div",{staticClass:"qr-info"},[n("p",{staticClass:"qr-amount"},[t._v("支付金额：¥"+t._s(t.currentOrderAmount))]),n("p",{staticClass:"qr-tip"},[t._v("请使用支付宝扫码支付")])]),n("div",{staticClass:"qr-status"},[n("a-button",{attrs:{loading:t.checkingStatus},on:{click:t.checkPaymentStatus}},[t._v("\n          检查支付状态\n        ")])],1)])])],1)},i=[],r=n("a34a"),s=n.n(r),o=n("77ea"),l=(n("ca00"),n("9fb0")),c=n("2b0e");function u(t,e,n,a,i,r,s){try{var o=t[r](s),l=o.value}catch(c){return void n(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function d(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){u(r,a,i,s,o,"next",t)}function o(t){u(r,a,i,s,o,"throw",t)}s(void 0)}))}}var p={name:"QuickRecharge",data:function(){return{loading:!1,rechargeLoading:!1,checkingStatus:!1,userBalance:0,userNickname:"",userAvatar:"",userMemberExpireTime:null,userRole:"user",defaultAvatar:"/default-avatar.png",showRechargeModal:!1,rechargeOptions:[{amount:50,label:"体验套餐"},{amount:100,label:"基础套餐"},{amount:300,label:"进阶套餐"},{amount:500,label:"专业套餐"},{amount:1e3,label:"企业套餐"}],selectedAmount:0,customAmount:null,selectedPaymentMethod:"alipay-qr",showQrModal:!1,qrCodeUrl:"",currentOrderId:"",currentOrderAmount:0,paymentCheckTimer:null}},computed:{finalRechargeAmount:function(){return this.customAmount||this.selectedAmount||0},avatarUrl:function(){var t=this.userAvatar;return t?t.startsWith("http://")||t.startsWith("https://")?t:this.getFileAccessHttpUrl(t)||this.defaultAvatar:this.defaultAvatar}},mounted:function(){this.checkLoginStatus()&&(this.loadUserInfo(),this.loadDefaultAvatar())},beforeDestroy:function(){this.paymentCheckTimer&&clearInterval(this.paymentCheckTimer)},methods:{checkLoginStatus:function(){var t=c["default"].ls.get(l["a"]);return!!t},loadUserInfo:function(){var t=d(s.a.mark((function t(){var e,n,a,i,r;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Object(o["q"])();case 4:return e=t.sent,e.success&&(n=e.result||{},this.userBalance=n.accountBalance||0),t.next=8,Object(o["v"])();case 8:return a=t.sent,a.success&&(i=a.result||{},this.userNickname=i.nickname||i.realname||"",this.userAvatar=i.avatar||"",this.userMemberExpireTime=i.member_expire_time||null),t.next=12,Object(o["w"])();case 12:r=t.sent,r.success?this.userRole=r.result.role_code||"user":this.userRole="user",t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](0);case 19:return t.prev=19,this.loading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[0,16,19,22]])})));function e(){return t.apply(this,arguments)}return e}(),loadDefaultAvatar:function(){var t=d(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/sys/common/default-avatar-url");case 3:e=t.sent,e&&e.success&&e.result&&(this.defaultAvatar=e.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),getFileAccessHttpUrl:function(t){return t?t.startsWith("http://")||t.startsWith("https://")?t:t.startsWith("uploads/")?window.getFileAccessHttpUrl?window.getFileAccessHttpUrl(t):t:this.$store.state.app.staticDomainURL+"/"+t:""},formatBalance:function(t){return parseFloat(t||0).toFixed(2)},selectRechargeAmount:function(t){this.selectedAmount=t,this.customAmount=null},handleRecharge:function(){var t=d(s.a.mark((function t(){var e,n,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.finalRechargeAmount&&!(this.finalRechargeAmount<.01)){t.next=3;break}return this.$message.warning("请选择或输入充值金额，最低0.01元"),t.abrupt("return");case 3:return t.prev=3,this.rechargeLoading=!0,e={amount:this.finalRechargeAmount,paymentMethod:this.selectedPaymentMethod},t.next=8,Object(o["c"])(e);case 8:if(n=t.sent,!n.success){t.next=22;break}if(a=n.result,"alipay-page"!==this.selectedPaymentMethod){t.next=16;break}return t.next=14,this.handleAlipayPagePayment(a.orderId,a.amount);case 14:t.next=19;break;case 16:if("alipay-qr"!==this.selectedPaymentMethod){t.next=19;break}return t.next=19,this.handleAlipayQrPayment(a.orderId,a.amount);case 19:this.showRechargeModal=!1,t.next=23;break;case 22:this.$message.error(n.message||"创建充值订单失败");case 23:t.next=29;break;case 25:t.prev=25,t.t0=t["catch"](3),this.$message.error("充值失败，请重试");case 29:return t.prev=29,this.rechargeLoading=!1,t.finish(29);case 32:case"end":return t.stop()}}),t,this,[[3,25,29,32]])})));function e(){return t.apply(this,arguments)}return e}(),getUserRoleDisplayName:function(){try{var t=this.userRole,e={"普通用户":"普通用户","VIP会员":"VIP会员","SVIP会员":"SVIP会员","管理员":"管理员",user:"普通用户",VIP:"VIP会员",SVIP:"SVIP会员",admin:"管理员",USER:"普通用户",vip:"VIP会员",svip:"SVIP会员",ADMIN:"管理员"};return e[t]||"普通用户"}catch(n){return"普通用户"}},getMemberExpireInfo:function(){try{var t=this.userRole;if(!t||!["VIP","SVIP","VIP会员","SVIP会员"].includes(t))return null;if(!this.userMemberExpireTime)return null;var e=new Date(this.userMemberExpireTime),n=new Date;if(e<=n)return"已过期";var a=e.getTime()-n.getTime(),i=Math.ceil(a/864e5);if(i<=7)return"".concat(i,"天后到期");var r=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),o=String(e.getDate()).padStart(2,"0");return"".concat(r,"-").concat(s,"-").concat(o,"到期")}catch(l){return null}},handleAlipayPagePayment:function(){var t=d(s.a.mark((function t(e,n){var a,i,r,o,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在跳转到支付宝支付...",0),a={orderId:e,amount:n,subject:"智界Aigc账户充值",body:"充值金额：¥".concat(n)},t.next=7,this.$http.post("/api/alipay/createOrder",a);case 7:if(i=t.sent,this.$message.destroy(),!i.success){t.next=24;break}if(r=i.result.payForm,r){t.next=16;break}return this.$message.error("支付表单为空"),t.abrupt("return");case 16:o=document.createElement("div"),o.innerHTML=r,document.body.appendChild(o),l=o.querySelector("form"),l?l.submit():this.$message.error("支付表单创建失败"),setTimeout((function(){document.body.contains(o)&&document.body.removeChild(o)}),1e3),t.next=26;break;case 24:this.$message.error(i.message||"创建支付订单失败");case 26:t.next=33;break;case 28:t.prev=28,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("支付宝支付失败，请重试");case 33:case"end":return t.stop()}}),t,this,[[0,28]])})));function e(e,n){return t.apply(this,arguments)}return e}(),handleAlipayQrPayment:function(){var t=d(s.a.mark((function t(e,n){var a,i,r;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在生成支付二维码...",0),a={orderId:e,amount:n,subject:"智界Aigc账户充值",body:"充值金额：¥".concat(n)},t.next=7,this.$http.post("/api/alipay/createQrOrder",a);case 7:if(i=t.sent,this.$message.destroy(),!i.success){t.next=19;break}if(r=i.result.qrCode,r){t.next=16;break}return this.$message.error("支付二维码生成失败"),t.abrupt("return");case 16:this.showQrCodeModal(r,e,n),t.next=21;break;case 19:this.$message.error(i.message||"创建扫码支付订单失败");case 21:t.next=28;break;case 23:t.prev=23,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("支付宝扫码支付失败，请重试");case 28:case"end":return t.stop()}}),t,this,[[0,23]])})));function e(e,n){return t.apply(this,arguments)}return e}(),showQrCodeModal:function(t,e,n){this.qrCodeUrl="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(t)),this.currentOrderId=e,this.currentOrderAmount=n,this.showQrModal=!0,this.startPaymentStatusCheck()},closeQrModal:function(){this.showQrModal=!1,this.qrCodeUrl="",this.currentOrderId="",this.currentOrderAmount=0,this.paymentCheckTimer&&(clearInterval(this.paymentCheckTimer),this.paymentCheckTimer=null)},startPaymentStatusCheck:function(){var t=this;this.paymentCheckTimer&&clearInterval(this.paymentCheckTimer),this.paymentCheckTimer=setInterval((function(){t.checkPaymentStatus()}),3e3)},checkPaymentStatus:function(){var t=d(s.a.mark((function t(){var e;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.currentOrderId){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,this.checkingStatus=!0,t.next=7,this.$http.get("/api/alipay/queryOrder/".concat(this.currentOrderId));case 7:e=t.sent,e.success&&"TRADE_SUCCESS"===e.result.status&&(this.$message.success("支付成功！"),this.closeQrModal(),this.loadUserInfo(),this.$emit("recharge-success")),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2);case 15:return t.prev=15,this.checkingStatus=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[2,12,15,18]])})));function e(){return t.apply(this,arguments)}return e}()}},h=p,f=(n("3499"),n("2877")),m=Object(f["a"])(h,a,i,!1,null,"4eaacdc8",null);e["default"]=m.exports},"0591":function(t,e,n){"use strict";var a=n("09d8"),i=n.n(a);i.a},"05ed":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("a-card",{attrs:{loading:t.loading,"body-style":{padding:"20px 24px 8px"},bordered:!1}},[n("div",{staticClass:"chart-card-header"},[n("div",{staticClass:"meta"},[n("span",{staticClass:"chart-card-title"},[t._v(t._s(t.title))]),n("span",{staticClass:"chart-card-action"},[t._t("action")],2)]),n("div",{staticClass:"total"},[n("span",[t._v(t._s(t.total))])])]),n("div",{staticClass:"chart-card-content"},[n("div",{staticClass:"content-fix"},[t._t("default")],2)]),n("div",{staticClass:"chart-card-footer"},[n("div",{staticClass:"field"},[t._t("footer")],2)])])},i=[],r={name:"ChartCard",props:{title:{type:String,default:""},total:{type:String,default:""},loading:{type:Boolean,default:!1}}},s=r,o=(n("7227"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"bc1c776e",null);e["default"]=l.exports},"0923":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"rank"},[n("h4",{staticClass:"title"},[t._v(t._s(t.title))]),n("ul",{staticClass:"list",style:{height:t.height?t.height+"px":"auto",overflow:"auto"}},t._l(t.list,(function(e,a){return n("li",{key:a},[n("span",{class:a<3?"active":null},[t._v(t._s(a+1))]),n("span",[t._v(t._s(e.name))]),n("span",[t._v(t._s(e.total))])])})),0)])},i=[],r={name:"RankList",props:{title:{type:String,default:""},list:{type:Array,default:null},height:{type:Number,default:null}}},s=r,o=(n("cd1e"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"83c80048",null);e["default"]=l.exports},"09d8":function(t,e,n){},"0d34":function(t,e,n){"use strict";n.r(e),n.d(e,"loadEnabledTypes",(function(){return m}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.syncToApp||t.syncToLocal?n("span",[t.enabledTypes.wechatEnterprise?n("j-third-app-dropdown",t._g(t._b({attrs:{type:"wechatEnterprise",name:"企微"}},"j-third-app-dropdown",t.bindAttrs,!1),t.bindEvents)):t._e(),t.enabledTypes.dingtalk?n("j-third-app-dropdown",t._g(t._b({attrs:{type:"dingtalk",name:"钉钉"}},"j-third-app-dropdown",t.bindAttrs,!1),t.bindEvents)):t._e()],1):n("span",[t._v("未设置任何同步方向")])},i=[],r=n("a34a"),s=n.n(r),o=n("0fea"),l=n("ca00"),c=n("fe28");function u(t,e,n,a,i,r,s){try{var o=t[r](s),l=o.value}catch(c){return void n(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function d(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){u(r,a,i,s,o,"next",t)}function o(t){u(r,a,i,s,o,"throw",t)}s(void 0)}))}}var p={getEnabledType:"/sys/thirdApp/getEnabledType",wechatEnterprise:{user:"/sys/thirdApp/sync/wechatEnterprise/user",depart:"/sys/thirdApp/sync/wechatEnterprise/depart"},dingtalk:{user:"/sys/thirdApp/sync/dingtalk/user",depart:"/sys/thirdApp/sync/dingtalk/depart"}},h={name:"JThirdAppButton",components:{JThirdAppDropdown:c["default"]},props:{bizType:{type:String,required:!0},syncToApp:Boolean,syncToLocal:Boolean,selectedRowKeys:Array},data:function(){return{enabledTypes:{},attrs:{dingtalk:{}}}},computed:{bindAttrs:function(){return{syncToApp:this.syncToApp,syncToLocal:this.syncToLocal}},bindEvents:function(){return{"to-app":this.onToApp,"to-local":this.onToLocal}}},created:function(){this.loadEnabledTypes()},methods:{handleMenuClick:function(){},onToApp:function(t){this.doSync(t.type,"/toApp")},onToLocal:function(t){this.doSync(t.type,"/toLocal")},loadEnabledTypes:function(){var t=d(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,m();case 2:this.enabledTypes=t.sent;case 3:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),doSync:function(t,e){var n=this,a=p[t];if(a&&a[this.bizType]){var i=a[this.bizType]+e,r=this.selectedRowKeys,s="确定要开始同步全部数据吗？可能花费较长时间！";return Array.isArray(r)&&r.length>0?s="确定要开始同步这 ".concat(r.length," 项吗？"):r=[],new Promise((function(a,l){var c=n.$confirm({title:"同步",content:s,onOk:function(){return c.update({keyboard:!1,okText:"同步中…",cancelButtonProps:{props:{disabled:!0}}}),Object(o["c"])(i,{ids:r.join(",")}).then((function(t){var e=null;t.result&&(e={width:600,title:t.message,content:function(e){var a,i=["成功信息如下：",n.renderTextarea(e,t.result.successInfo.map((function(t,e){return"".concat(e+1,". ").concat(t)})).join("\n"))];return a=t.success?[].concat(i,[e("br"),"无失败信息！"]):["失败信息如下：",n.renderTextarea(e,t.result.failInfo.map((function(t,e){return"".concat(e+1,". ").concat(t)})).join("\n")),e("br")].concat(i),a}}),t.success?(null!=e?n.$success(e):n.$message.success(t.message),n.$emit("sync-ok")):(null!=e?n.$warning(e):n.$message.warning(t.message),n.$emit("sync-error"))})).catch((function(){return c.destroy()})).finally((function(){a(),n.$emit("sync-finally",{type:t,direction:e,isToApp:"/toApp"===e,isToLocal:"/toLocal"===e})}))},onCancel:function(){a()}})}))}},renderTextarea:function(t,e){return t("a-textarea",{props:{value:e,readOnly:!0,autosize:{minRows:5,maxRows:10}},style:{whiteSpace:"pre",overflow:"auto"}})}}},f=null;function m(){return g.apply(this,arguments)}function g(){return g=d(s.a.mark((function t(){var e,n,a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null==f){t.next=4;break}return t.abrupt("return",Object(l["b"])(f));case 4:return t.next=6,Object(o["c"])(p.getEnabledType);case 6:if(e=t.sent,n=e.success,a=e.result,!n){t.next=14;break}return f=Object(l["b"])(a),t.abrupt("return",a);case 14:case 15:return t.abrupt("return",{});case 16:case"end":return t.stop()}}),t)}))),g.apply(this,arguments)}var v=h,y=n("2877"),b=Object(y["a"])(v,a,i,!1,null,"0a891898",null);e["default"]=b.exports},1337:function(t,e,n){"use strict";var a=n("add5"),i=n.n(a);i.a},1397:function(t,e,n){},1521:function(t,e,n){},1741:function(t,e,n){"use strict";n.r(e);var a,i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div")},r=[];function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var o=(a={name:"ConfirmDialog",props:{visible:{type:Boolean,default:!1},title:{type:String,default:"确认操作"},content:{type:String,default:""},type:{type:String,default:"warning",validator:function(t){return["warning","danger","info","success"].includes(t)}},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确定"},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"处理中..."},closeOnClickOverlay:{type:Boolean,default:!0}},computed:{iconClass:function(){return{"icon-warning":"warning"===this.type,"icon-danger":"danger"===this.type,"icon-info":"info"===this.type,"icon-success":"success"===this.type}},iconType:function(){var t={warning:"anticon anticon-exclamation-circle",danger:"anticon anticon-close-circle",info:"anticon anticon-info-circle",success:"anticon anticon-check-circle"};return t[this.type]},warningIconType:function(){var t={warning:"anticon anticon-warning",danger:"anticon anticon-close-circle",info:"anticon anticon-info-circle",success:"anticon anticon-check-circle"};return t[this.type]},contentClass:function(){return{"content-warning":"warning"===this.type,"content-danger":"danger"===this.type,"content-info":"info"===this.type,"content-success":"success"===this.type}},confirmButtonClass:function(){return{"btn-warning":"warning"===this.type,"btn-danger":"danger"===this.type,"btn-info":"info"===this.type,"btn-success":"success"===this.type}},confirmIconType:function(){var t={warning:"anticon anticon-check",danger:"anticon anticon-check",info:"anticon anticon-check",success:"anticon anticon-check"};return t[this.type]}},data:function(){return{portalElement:null}},watch:{visible:{handler:function(t){t?this.createPortal():this.removePortal()},immediate:!0}},beforeDestroy:function(){this.removePortal()}},s(a,"computed",{iconClass:function(){var t={warning:"icon-warning",danger:"icon-danger",info:"icon-info",success:"icon-success"};return t[this.type]||"icon-warning"},contentClass:function(){var t={warning:"content-warning",danger:"content-danger",info:"content-info",success:"content-success"};return t[this.type]||"content-warning"},confirmButtonClass:function(){var t={warning:"btn-warning",danger:"btn-danger",info:"btn-info",success:"btn-success"};return t[this.type]||"btn-warning"}}),s(a,"methods",{createPortal:function(){if(this.removePortal(),!this.portalElement){this.savedScrollY=window.pageYOffset||document.documentElement.scrollTop,document.body.style.overflow="hidden",document.documentElement.style.overflow="hidden",this.portalElement=document.createElement("div"),this.portalElement.style.cssText="\n        position: fixed !important;\n        top: 0 !important;\n        left: 0 !important;\n        right: 0 !important;\n        bottom: 0 !important;\n        width: 100vw !important;\n        height: 100vh !important;\n        background: rgba(0, 0, 0, 0.85) !important;\n        backdrop-filter: blur(20px) !important;\n        -webkit-backdrop-filter: blur(20px) !important;\n        display: flex !important;\n        align-items: center !important;\n        justify-content: center !important;\n        z-index: 1000 !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        overflow: hidden !important;\n        pointer-events: all !important;\n      ",this.portalElement.innerHTML=this.generateDialogHTML(),this.portalElement.addEventListener("click",this.handleOverlayClick);var t=this.portalElement.querySelector(".cancel-btn"),e=this.portalElement.querySelector(".confirm-btn"),n=this.portalElement.querySelector(".confirm-dialog");t&&t.addEventListener("click",this.handleCancel),e&&e.addEventListener("click",this.handleConfirm),n&&n.addEventListener("click",(function(t){return t.stopPropagation()})),document.body.appendChild(this.portalElement)}},removePortal:function(){if(this.portalElement){var t=this.savedScrollY||0;document.body.style.overflow="",document.documentElement.style.overflow="",window.scrollTo(0,t),setTimeout((function(){window.scrollTo(0,t)}),10),requestAnimationFrame((function(){window.scrollTo(0,t)})),this.portalElement.removeEventListener("click",this.handleOverlayClick),this.portalElement.parentNode&&this.portalElement.parentNode.removeChild(this.portalElement),this.portalElement=null,this.savedScrollY=null}},generateDialogHTML:function(){return'\n        <div class="confirm-dialog-portal" style="\n          background: rgba(255, 255, 255, 0.98) !important;\n          border-radius: 24px !important;\n          padding: 4rem !important;\n          margin: 1rem !important;\n          max-width: 700px !important;\n          width: 98% !important;\n          max-height: 90vh !important;\n          overflow-y: auto !important;\n          box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;\n          backdrop-filter: blur(20px) !important;\n          -webkit-backdrop-filter: blur(20px) !important;\n          border: 1px solid rgba(124, 138, 237, 0.15) !important;\n          z-index: 1000 !important;\n          position: relative !important;\n        " onclick="event.stopPropagation()">\n          <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">\n            <div style="\n              width: 48px; height: 48px; border-radius: 50%; display: flex;\n              align-items: center; justify-content: center; color: white; font-size: 24px;\n              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n            ">\n              <i class="'.concat(this.iconType,'"></i>\n            </div>\n            <h3 style="font-size: 1.5rem; font-weight: 600; color: #334155; margin: 0;">\n              ').concat(this.title,'\n            </h3>\n          </div>\n\n          <div style="margin-bottom: 2rem;">\n            <div style="\n              display: flex; gap: 1rem; padding: 1.5rem; border-radius: 16px; border: 1px solid;\n              background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);\n              border-color: rgba(245, 158, 11, 0.2);\n            ">\n              <div style="flex-shrink: 0; width: 24px; height: 24px; font-size: 20px; color: #f59e0b;">\n                <i class="').concat(this.warningIconType,'"></i>\n              </div>\n              <div style="flex: 1; color: #64748b; line-height: 1.6;">\n                <p style="margin: 0 0 0.75rem 0;"><strong style="color: #334155; font-weight: 600;">API Key是您访问系统的唯一凭证，具有唯一性，请妥善保管。</strong></p>\n                <p style="margin: 0 0 0.75rem 0;">重置后原有的API Key将立即失效，所有使用旧API Key的应用将无法正常工作。</p>\n                <p style="color: #f59e0b; font-weight: 600; margin: 1rem 0 0 0;">确定要重置吗？</p>\n              </div>\n            </div>\n          </div>\n\n          <div style="display: flex; gap: 1rem; justify-content: flex-end;">\n            ').concat(this.showCancel?'\n              <button class="cancel-btn" style="\n                display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;\n                border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;\n                border: 1px solid rgba(148, 163, 184, 0.2); font-size: 14px; min-width: 120px;\n                justify-content: center; background: rgba(148, 163, 184, 0.1); color: #64748b;\n              ">\n                <i class="anticon anticon-close"></i>\n                <span>'.concat(this.cancelText,"</span>\n              </button>\n            "):"",'\n            <button class="confirm-btn" style="\n              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;\n              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;\n              border: none; font-size: 14px; min-width: 120px; justify-content: center;\n              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;\n              box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);\n            " ').concat(this.loading?"disabled":"",'>\n              <i class="anticon ').concat(this.loading?"anticon-loading spinning":this.confirmIconType,'"></i>\n              <span>').concat(this.loading?this.loadingText:this.confirmText,"</span>\n            </button>\n          </div>\n        </div>\n      ")},handleOverlayClick:function(t){if(this.closeOnClickOverlay){var e=this.portalElement.querySelector(".confirm-dialog-portal");if(e){var n=e.getBoundingClientRect(),a=50,i={left:n.left-a,right:n.right+a,top:n.top-a,bottom:n.bottom+a},r=t.clientX,s=t.clientY,o=r<i.left||r>i.right||s<i.top||s>i.bottom;o&&this.handleCancel()}else this.handleCancel()}},handleCancel:function(){this.$emit("cancel"),this.$emit("update:visible",!1)},handleConfirm:function(){this.$emit("confirm")}}),a),l=o,c=(n("ee3b"),n("2877")),u=Object(c["a"])(l,i,r,!1,null,"0f1386d8",null);e["default"]=u.exports},1963:function(t,e,n){"use strict";var a=n("6ded"),i=n.n(a);i.a},"1cf5":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 0 32px 32px"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{attrs:{height:t.height,data:t.data,scale:t.scale,forceFit:!0,padding:["auto","auto","40","50"]}},[n("v-tooltip"),n("v-axis"),n("v-bar",{attrs:{position:"x*y"}})],1)],1)},i=[],r={name:"Bar",props:{title:{type:String,default:""},x:{type:String,default:"x"},y:{type:String,default:"y"},data:{type:Array,default:function(){return[]}},height:{type:Number,default:254}},data:function(){return{}},computed:{scale:function(){return[{dataKey:"x",title:this.x,alias:this.x},{dataKey:"y",title:this.y,alias:this.y}]}},created:function(){},methods:{}},s=r,o=n("2877"),l=Object(o["a"])(s,a,i,!1,null,null,null);e["default"]=l.exports},"1d43":function(t,e,n){"use strict";n.r(e);for(var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"antv-chart-mini"},[n("div",{staticClass:"chart-wrapper",style:{height:46}},[n("v-chart",{attrs:{"force-fit":!0,height:t.height,data:t.data,scale:t.scale,padding:[36,0,18,0]}},[n("v-tooltip"),n("v-smooth-area",{attrs:{position:"x*y"}})],1)],1)])},i=[],r=n("5a0c"),s=n.n(r),o=[],l=(new Date).getTime(),c=0;c<10;c++)o.push({x:s()(new Date(l+864e5*c)).format("YYYY-MM-DD"),y:Math.round(10*Math.random())});var u={name:"MiniArea",props:{dataSource:{type:Array,default:function(){return[]}},x:{type:String,default:"x"},y:{type:String,default:"y"}},data:function(){return{data:[],height:100}},computed:{scale:function(){return[{dataKey:"x",title:this.x,alias:this.x},{dataKey:"y",title:this.y,alias:this.y}]}},created:function(){0===this.dataSource.length?this.data=o:this.data=this.dataSource}},d=u,p=(n("9815"),n("2877")),h=Object(p["a"])(d,a,i,!1,null,"1efcadbe",null);e["default"]=h.exports},"1d81":function(t,e,n){"use strict";var a=n("3365"),i=n.n(a);i.a},"1f70e":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{ref:"carousel",staticClass:"tencent-carousel",class:{loaded:t.isLoaded},on:{mouseenter:t.showControls,mouseleave:t.hideControls}},[n("div",{staticClass:"carousel-container"},[n("div",{ref:"wrapper",staticClass:"carousel-wrapper"},t._l(t.slides,(function(e,a){return n("div",{key:a,staticClass:"carousel-slide",class:{active:a===t.currentSlide}},[n("div",{staticClass:"slide-background"},[n("img",{attrs:{src:e.image,alt:e.title}}),n("div",{staticClass:"slide-overlay"}),n("div",{staticClass:"slide-gradient"})]),n("div",{staticClass:"slide-content"},[n("div",{staticClass:"content-wrapper"},[e.badge?n("div",{staticClass:"slide-badge"},[t._v("\n              "+t._s(e.badge)+"\n            ")]):t._e(),n("h2",{staticClass:"slide-title"},[t._v(t._s(e.title))]),n("p",{staticClass:"slide-description"},[t._v(t._s(e.description))]),e.primaryAction?n("div",{staticClass:"slide-actions"},[n("button",{staticClass:"btn-primary",on:{click:function(n){return t.handleSlideAction(e)}}},[t._v("\n                "+t._s(e.primaryAction.text)+"\n                "),n("a-icon",{attrs:{type:"arrow-right"}})],1)]):t._e()])])])})),0),n("div",{staticClass:"carousel-controls"},[n("div",{staticClass:"controls-panel",class:{visible:t.controlsVisible}},[n("button",{staticClass:"control-btn prev-btn",attrs:{disabled:t.isTransitioning},on:{click:t.prevSlideHandler}},[n("a-icon",{attrs:{type:"left"}})],1),n("div",{staticClass:"modern-indicators"},t._l(t.slides,(function(e,a){return n("div",{key:a,staticClass:"modern-indicator",class:{active:a===t.currentSlide},on:{click:function(e){return t.goToSlide(a)}}},[a===t.currentSlide?n("div",{staticClass:"indicator-progress"}):t._e()])})),0),n("button",{staticClass:"control-btn next-btn",attrs:{disabled:t.isTransitioning},on:{click:t.nextSlideHandler}},[n("a-icon",{attrs:{type:"right"}})],1)]),n("div",{staticClass:"play-control",class:{visible:t.controlsVisible}},[n("button",{staticClass:"play-pause-btn",on:{click:t.toggleAutoPlay}},[n("a-icon",{attrs:{type:t.isPlaying?"pause":"play-circle"}})],1)])])])])},i=[],r={name:"TencentCarousel",props:{slides:{type:Array,required:!0},autoPlay:{type:Boolean,default:!0},interval:{type:Number,default:5e3},transitionDuration:{type:Number,default:600}},data:function(){return{currentSlide:0,isTransitioning:!1,isPlaying:this.autoPlay,autoPlayTimer:null,controlsVisible:!1,progressTimer:null,progressStartTime:null,isLoaded:!1}},computed:{prevSlide:function(){return 0===this.currentSlide?this.slides.length-1:this.currentSlide-1},nextSlide:function(){return this.currentSlide===this.slides.length-1?0:this.currentSlide+1}},mounted:function(){var t=this;this.initCarousel(),this.slides&&0!==this.slides.length&&setTimeout((function(){t.isLoaded=!0,setTimeout((function(){t.isPlaying&&t.startAutoPlay(),t.showInitialHint()}),500)}),300)},beforeDestroy:function(){this.stopAutoPlay(),this.stopProgress(),this.clearAllTimers()},methods:{initCarousel:function(){},goToSlide:function(t){var e=this;t===this.currentSlide||this.isTransitioning||(this.isTransitioning=!0,this.stopProgress(),this.currentSlide=t,this.isPlaying&&this.startProgress(),setTimeout((function(){e.isTransitioning=!1}),600))},nextSlideHandler:function(){var t=this.currentSlide===this.slides.length-1?0:this.currentSlide+1;this.goToSlide(t)},prevSlideHandler:function(){var t=0===this.currentSlide?this.slides.length-1:this.currentSlide-1;this.goToSlide(t)},startAutoPlay:function(){var t=this;this.stopAutoPlay(),this.autoPlayTimer=setInterval((function(){t.nextSlideHandler()}),this.interval),this.startProgress()},stopAutoPlay:function(){this.autoPlayTimer&&(clearInterval(this.autoPlayTimer),this.autoPlayTimer=null),this.stopProgress()},showControls:function(){this.controlsVisible=!0},hideControls:function(){this.controlsVisible=!1},toggleAutoPlay:function(){this.isPlaying?(this.stopAutoPlay(),this.isPlaying=!1):(this.startAutoPlay(),this.isPlaying=!0)},showInitialHint:function(){var t=this;setTimeout((function(){t.controlsVisible=!0,setTimeout((function(){t.controlsVisible=!1}),3e3)}),2e3)},startProgress:function(){var t=this;this.stopProgress(),this.progressStartTime=Date.now(),this.$nextTick((function(){var e=t.$el.querySelector(".indicator-progress");e&&(e.style.animation="none",e.offsetHeight,e.style.animation="progressBar ".concat(t.interval,"ms linear"))}))},stopProgress:function(){if(this.progressTimer&&(clearTimeout(this.progressTimer),this.progressTimer=null),this.$el){var t=this.$el.querySelector(".indicator-progress");t&&(t.style.animation="none")}},clearAllTimers:function(){this.autoPlayTimer&&(clearInterval(this.autoPlayTimer),this.autoPlayTimer=null),this.progressTimer&&(clearTimeout(this.progressTimer),this.progressTimer=null)},handleSlideAction:function(t){this.$emit("slide-action",{slide:t,action:t.primaryAction}),t.primaryAction&&t.primaryAction.link&&(t.primaryAction.link.startsWith("/")?this.$router.push(t.primaryAction.link):window.open(t.primaryAction.link,"_blank"))}}},s=r,o=(n("e58a"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"5f5a5709",null);e["default"]=l.exports},2257:function(t,e,n){},"23fe":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{class:[t.prefixCls]},[t._t("subtitle",[n("div",{class:[t.prefixCls+"-subtitle"]},[t._v(t._s("string"===typeof t.subTitle?t.subTitle:t.subTitle()))])]),n("div",{staticClass:"number-info-value"},[n("span",[t._v(t._s(t.total))]),n("span",{staticClass:"sub-total"},[t._v("\n      "+t._s(t.subTotal)+"\n      "),n("icon",{attrs:{type:"caret-"+t.status}})],1)])],2)},i=[],r=n("0c63"),s={name:"NumberInfo",props:{prefixCls:{type:String,default:"ant-pro-number-info"},total:{type:Number,required:!0},subTotal:{type:Number,required:!0},subTitle:{type:[String,Function],default:""},status:{type:String,default:"up"}},components:{Icon:r["a"]},data:function(){return{}}},o=s,l=(n("7506"),n("2877")),c=Object(l["a"])(o,a,i,!1,null,"fca2c294",null);e["default"]=c.exports},"2ca2":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=this,n=e.$createElement,a=e._self._c||n;return a("a-modal",{attrs:{centered:"",maskClosable:!1},on:{cancel:e.handleCancel},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[a("div",{style:{textAlign:"center"},attrs:{slot:"title"},slot:"title"},[e._v("两步验证")]),a("template",{slot:"footer"},[a("div",{style:{textAlign:"center"}},[a("a-button",{key:"back",on:{click:e.handleCancel}},[e._v("返回")]),a("a-button",{key:"submit",attrs:{type:"primary",loading:e.stepLoading},on:{click:e.handleStepOk}},[e._v("\n        继续\n      ")])],1)]),a("a-spin",{attrs:{spinning:e.stepLoading}},[a("a-form",{attrs:{layout:"vertical","auto-form-create":function(e){t.form=e}}},[a("div",{staticClass:"step-form-wrapper"},[e.stepLoading?a("p",{staticStyle:{"text-align":"center"}},[e._v("正在验证.."),a("br"),e._v("请稍后")]):a("p",{staticStyle:{"text-align":"center"}},[e._v("请在手机中打开 Google Authenticator 或两步验证 APP"),a("br"),e._v("输入 6 位动态码")]),a("a-form-item",{style:{textAlign:"center"},attrs:{hasFeedback:"",fieldDecoratorId:"stepCode",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入 6 位动态码!",pattern:/^\d{6}$/,len:6}]}}},[a("a-input",{style:{textAlign:"center"},attrs:{placeholder:"000000"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleStepOk(t)}}})],1),a("p",{staticStyle:{"text-align":"center"}},[a("a",{on:{click:e.onForgeStepCode}},[e._v("遗失手机?")])])],1)])],1)],2)},i=[],r={props:{visible:{type:Boolean,default:!1}},data:function(){return{stepLoading:!1,form:null}},methods:{handleStepOk:function(){var t=this,e=this;this.stepLoading=!0,this.form.validateFields((function(n,a){n?(t.stepLoading=!1,t.$emit("error",{err:n})):setTimeout((function(){e.stepLoading=!1,e.$emit("success",{values:a})}),2e3)}))},handleCancel:function(){this.visible=!1,this.$emit("cancel")},onForgeStepCode:function(){}}},s=r,o=(n("3aca"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"ab1e4d58",null);e["default"]=l.exports},"2ce4":function(t,e,n){"use strict";var a=n("23fe");e["a"]=a["default"]},"2dd4":function(t,e,n){},3365:function(t,e,n){},3499:function(t,e,n){"use strict";var a=n("f8f4"),i=n.n(a);i.a},"35b1":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("footer",{staticClass:"website-footer"},[n("div",{staticClass:"footer-container"},[n("div",{staticClass:"footer-main"},[n("div",{staticClass:"footer-brand"},[n("LogoImage",{attrs:{size:"medium",hover:!1,"container-class":"footer-logo-container","image-class":"footer-logo-image","fallback-class":"footer-logo-fallback"}}),t._m(0)],1),n("div",{staticClass:"footer-links"},[n("div",{staticClass:"link-group"},[n("h4",{staticClass:"group-title"},[t._v("产品功能")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/features/xiaohongshu-text"}},[t._v("小红书图文")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/features/xiaohongshu-video"}},[t._v("小红书视频")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/features/auto-publish"}},[t._v("自动发布")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/features/jianying-assistant"}},[t._v("剪映小助手")])],1),n("div",{staticClass:"link-group"},[n("h4",{staticClass:"group-title"},[t._v("服务支持")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/tutorials"}},[t._v("教程中心")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/cases"}},[t._v("客户案例")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/help"}},[t._v("帮助中心")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/contact"}},[t._v("联系我们")])],1),n("div",{staticClass:"link-group"},[n("h4",{staticClass:"group-title"},[t._v("商业服务")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/market"}},[t._v("插件商城")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/membership"}},[t._v("订阅会员")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/affiliate"}},[t._v("分销推广")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/enterprise"}},[t._v("企业服务")])],1),n("div",{staticClass:"link-group"},[n("h4",{staticClass:"group-title"},[t._v("关于我们")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/about"}},[t._v("公司介绍")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/news"}},[t._v("新闻动态")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/careers"}},[t._v("加入我们")]),n("router-link",{staticClass:"footer-link",attrs:{to:"/privacy"}},[t._v("隐私政策")])],1)]),n("div",{staticClass:"footer-contact"},[n("h4",{staticClass:"contact-title"},[t._v("联系我们")]),n("div",{staticClass:"contact-item"},[n("a-icon",{attrs:{type:"mail"}}),n("span",[t._v("<EMAIL>")])],1),n("div",{staticClass:"social-links"},[n("a",{staticClass:"social-link",attrs:{href:"#",title:"微信公众号"}},[n("a-icon",{attrs:{type:"wechat"}})],1),n("a",{staticClass:"social-link",attrs:{href:"#",title:"新浪微博"}},[n("a-icon",{attrs:{type:"weibo"}})],1),n("a",{staticClass:"social-link",attrs:{href:"#",title:"QQ群"}},[n("a-icon",{attrs:{type:"qq"}})],1),n("a",{staticClass:"social-link",attrs:{href:"#",title:"GitHub"}},[n("a-icon",{attrs:{type:"github"}})],1)])])]),n("div",{staticClass:"footer-bottom"},[t._m(1),n("div",{staticClass:"footer-badges"},[n("div",{staticClass:"badge-item"},[n("a-icon",{attrs:{type:"safety-certificate"}}),n("span",[t._v("安全认证")])],1),n("div",{staticClass:"badge-item"},[n("a-icon",{attrs:{type:"shield"}}),n("span",[t._v("数据保护")])],1)])])])])},i=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"brand-info"},[n("h3",{staticClass:"brand-name"},[t._v("智界AIGC")]),n("p",{staticClass:"brand-slogan"},[t._v("AI驱动的内容创作平台")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"copyright"},[n("p",[t._v("© 2024 智界AIGC. 保留所有权利.")]),n("p",[n("a",{staticClass:"legal-link",attrs:{href:"/terms"}},[t._v("服务条款")]),n("span",{staticClass:"separator"},[t._v("|")]),n("a",{staticClass:"legal-link",attrs:{href:"/privacy"}},[t._v("隐私政策")]),n("span",{staticClass:"separator"},[t._v("|")]),n("a",{staticClass:"legal-link",attrs:{href:"/icp"}},[t._v("ICP备案号")])])])}],r=n("8bd7"),s={name:"WebsiteFooter",components:{LogoImage:r["default"]}},o=s,l=(n("1d81"),n("2877")),c=Object(l["a"])(o,a,i,!1,null,"83179902",null);e["default"]=c.exports},"36d5f":function(t,e,n){"use strict";n.d(e,"a",(function(){return d}));var a=n("a34a"),i=n.n(a),r=n("5f87"),s=n("7ded"),o=n("2b0e"),l=n("9fb0");function c(t,e,n,a,i,r,s){try{var o=t[r](s),l=o.value}catch(c){return void n(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function u(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){c(r,a,i,s,o,"next",t)}function o(t){c(r,a,i,s,o,"throw",t)}s(void 0)}))}}var d={methods:{checkLoginStatus:function(){var t=Object(r["a"])();return!!t||(this.$message.warning("请先登录"),this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),!1)},handleApiError:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"操作失败";return!t.success&&(401===t.code||t.message&&t.message.includes("Token")||t.message&&t.message.includes("登录")||t.message&&t.message.includes("认证")||t.message&&t.message.includes("权限")?(this.handleTokenExpired(),!0):(this.$message.error(t.message||e),!1))},handleApiException:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"请求失败，请重试";return t.response&&401===t.response.status?(this.handleTokenExpired(),!0):"ERR_NETWORK"===t.code||t.message&&t.message.includes("Network Error")?(this.$message.error("网络连接失败，请检查网络"),!1):(this.$message.error(e),!1)},handleError:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"操作失败";return t.response?this.handleApiException(t,e):void 0!==t.success?this.handleApiError(t,e):this.handleApiException(t,e)},performLogout:function(){var t=u(i.a.mark((function t(){var e;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e=o["default"].ls.get(l["a"]),this.clearLocalUserData(),!e){t.next=14;break}return t.prev=5,t.next=8,Object(s["d"])(e);case 8:t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](5);case 14:if(!this.$store){t.next=17;break}return t.next=17,this.$store.dispatch("Logout");case 17:return t.abrupt("return",Promise.resolve());case 21:return t.prev=21,t.t1=t["catch"](0),this.clearLocalUserData(),t.abrupt("return",Promise.reject(t.t1));case 26:case"end":return t.stop()}}),t,this,[[0,21],[5,11]])})));function e(){return t.apply(this,arguments)}return e}(),clearLocalUserData:function(){try{o["default"].ls.remove(l["a"]),o["default"].ls.remove(l["u"]),o["default"].ls.remove(l["v"]),o["default"].ls.remove(l["s"]),o["default"].ls.remove(l["b"]),o["default"].ls.remove(l["r"]),localStorage.removeItem("Access-Token"),localStorage.removeItem("USER_INFO"),localStorage.removeItem("USER_NAME")}catch(t){}},handleTokenExpired:function(){var t=u(i.a.mark((function t(){var e;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,this.performLogout();case 4:this.$message.warning("登录已过期，请重新登录"),e=this.$route?this.$route.fullPath:window.location.pathname,e.startsWith("/isystem")||e.startsWith("/dashboard")?this.$router.push({path:"/user/login",query:{redirect:e}}):this.$router.push({path:"/login",query:{redirect:e}}),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](0),window.location.href="/login";case 14:case"end":return t.stop()}}),t,this,[[0,10]])})));function e(){return t.apply(this,arguments)}return e}()}}},3981:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-chart",{attrs:{forceFit:!0,height:t.height,data:t.data,padding:[20,20,95,20],scale:t.scale}},[n("v-tooltip"),n("v-axis",{attrs:{dataKey:t.axis1Opts.dataKey,line:t.axis1Opts.line,tickLine:t.axis1Opts.tickLine,grid:t.axis1Opts.grid}}),n("v-axis",{attrs:{dataKey:t.axis2Opts.dataKey,line:t.axis2Opts.line,tickLine:t.axis2Opts.tickLine,grid:t.axis2Opts.grid}}),n("v-legend",{attrs:{dataKey:"user",marker:"circle",offset:30}}),n("v-coord",{attrs:{type:"polar",radius:"0.8"}}),n("v-line",{attrs:{position:"item*score",color:"user",size:2}}),n("v-point",{attrs:{position:"item*score",color:"user",size:4,shape:"circle"}})],1)},i=[],r={dataKey:"item",line:null,tickLine:null,grid:{lineStyle:{lineDash:null},hideFirstLine:!1}},s={dataKey:"score",line:null,tickLine:null,grid:{type:"polygon",lineStyle:{lineDash:null}}},o=[{dataKey:"score",min:0,max:100},{dataKey:"user",alias:"类型"}],l=[{item:"示例一",score:40},{item:"示例二",score:20},{item:"示例三",score:67},{item:"示例四",score:43},{item:"示例五",score:90}],c={name:"Radar",props:{height:{type:Number,default:254},dataSource:{type:Array,default:function(){return[]}}},data:function(){return{axis1Opts:r,axis2Opts:s,scale:o,data:l}},watch:{dataSource:function(t){0===t.length?this.data=l:this.data=t}}},u=c,d=n("2877"),p=Object(d["a"])(u,a,i,!1,null,"f48462e2",null);e["default"]=p.exports},"3aca":function(t,e,n){"use strict";var a=n("1521"),i=n.n(a);i.a},"3c24f":function(t,e,n){"use strict";var a=n("a222"),i=n.n(a);i.a},"3dec":function(t,e,n){"use strict";var a=n("ab30"),i=n.n(a);i.a},4047:function(t,e,n){"use strict";var a=n("7195"),i=n.n(a);i.a},"44fe":function(t,e,n){},"4ba5":function(t,e,n){"use strict";var a=n("f8d8"),i=n.n(a),r=n("483f");e["a"]=function(t,e,n){if(!e)return n(),!0;var a=e.split(" ").filter((function(t){return!!t}));if(a.length>7)return n(new Error("Cron表达式最多7项！")),!1;var s=e;if(7===a.length){var o=Object(r["b"])(a[6]);if("*"!==o&&"?"!==o){var l=[];l=o.indexOf("-")>=0?o.split("-"):o.indexOf("/")?o.split("/"):[o];var c=l.some((function(t){return isNaN(t)}));if(c)return n(new Error("Cron表达式参数[年]错误："+o)),!1}s=a.slice(0,6).join(" ")}var u=!0;try{var d=i.a.parseExpression(s);d.next(),n()}catch(s){n(new Error("Cron表达式错误："+s)),u=!1}return u}},"4ced":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("v-chart",{attrs:{forceFit:!0,height:t.height,width:t.width,data:t.data,scale:t.scale,padding:0}},[n("v-tooltip"),n("v-interval",{attrs:{shape:["liquid-fill-gauge"],position:"transfer*value",color:"","v-style":{lineWidth:8,opacity:.75},tooltip:["transfer*value",function(t,e){return{name:t,value:e}}]}}),t._l(t.data,(function(t,e){return n("v-guide",{key:e,attrs:{type:"text",top:!0,position:{gender:t.transfer,value:45},content:t.value+"%","v-style":{fontSize:100,textAlign:"center",opacity:.75}}})}))],2)],1)},i=[],r=[{transfer:"一月",value:813},{transfer:"二月",value:233},{transfer:"三月",value:561}],s={name:"Liquid",props:{height:{type:Number,default:0},width:{type:Number,default:0}},data:function(){return{data:r,scale:[]}}},o=s,l=n("2877"),c=Object(l["a"])(o,a,i,!1,null,"383ba75c",null);e["default"]=c.exports},"4ec6":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 0 32px 32px"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{attrs:{"force-fit":!0,height:t.height,data:t.data,scale:t.scale,onClick:t.handleClick}},[n("v-tooltip"),n("v-axis"),n("v-legend"),n("v-line",{attrs:{position:"type*y",color:"x"}}),n("v-point",{attrs:{position:"type*y",color:"x",size:4,"v-style":t.style,shape:"circle"}})],1)],1)},i=[],r=n("7104"),s=n("c917");function o(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=l(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var a=0,i=function(){};return{s:i,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,r=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw r}}}}function l(t,e){if(t){if("string"===typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=t[n];return a}var u={name:"LineChartMultid",mixins:[s["a"]],props:{title:{type:String,default:""},dataSource:{type:Array,default:function(){return[{type:"Jan",jeecg:7,jeebt:3.9},{type:"Feb",jeecg:6.9,jeebt:4.2},{type:"Mar",jeecg:9.5,jeebt:5.7},{type:"Apr",jeecg:14.5,jeebt:8.5},{type:"May",jeecg:18.4,jeebt:11.9},{type:"Jun",jeecg:21.5,jeebt:15.2},{type:"Jul",jeecg:25.2,jeebt:17},{type:"Aug",jeecg:26.5,jeebt:16.6},{type:"Sep",jeecg:23.3,jeebt:14.2},{type:"Oct",jeecg:18.3,jeebt:10.3},{type:"Nov",jeecg:13.9,jeebt:6.6},{type:"Dec",jeecg:9.6,jeebt:4.8}]}},fields:{type:Array,default:function(){return["jeecg","jeebt"]}},aliases:{type:Array,default:function(){return[]}},height:{type:Number,default:254}},data:function(){return{scale:[{type:"cat",dataKey:"x",min:0,max:1}],style:{stroke:"#fff",lineWidth:1}}},computed:{data:function(){var t=this,e=(new r["DataSet"].View).source(this.dataSource);e.transform({type:"fold",fields:this.fields,key:"x",value:"y"});var n=e.rows;return n.forEach((function(e){var n,a=o(t.aliases);try{for(a.s();!(n=a.n()).done;){var i=n.value;if(i.field===e.x){e.x=i.alias;break}}}catch(r){a.e(r)}finally{a.f()}})),n}}},d=u,p=n("2877"),h=Object(p["a"])(d,a,i,!1,null,"527dc525",null);e["default"]=h.exports},5246:function(t,e,n){},"527e":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"chart-trend"},[t._v("\n  "+t._s(t.term)+"\n  "),n("span",[t._v(t._s(t.rate)+"%")]),n("span",{class:["trend-icon",t.trend]},[n("a-icon",{attrs:{type:"caret-"+t.trend}})],1)])},i=[],r={name:"Trend",props:{term:{type:String,default:"",required:!0},percentage:{type:Number,default:null},type:{type:Boolean,default:null},target:{type:Number,default:0},value:{type:Number,default:0},fixed:{type:Number,default:2}},data:function(){return{trend:this.type?"up":"down",rate:this.percentage}},created:function(){var t=null===this.type?this.value>=this.target:this.type;this.trend=t?"up":"down",this.rate=(null===this.percentage?100*Math.abs(this.value-this.target)/this.target:this.percentage).toFixed(this.fixed)}},s=r,o=(n("3dec"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"30501fa8",null);e["default"]=l.exports},"58f7":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return""!==t.tips?n("tooltip",[n("template",{slot:"title"},[t._v(t._s(t.tips))]),n("avatar",{attrs:{size:t.avatarSize,src:t.src}})],2):n("avatar",{attrs:{size:t.avatarSize,src:t.src}})},i=[],r=n("27fd"),s=n("f933"),o={name:"AvatarItem",components:{Avatar:r["a"],Tooltip:s["a"]},props:{tips:{type:String,default:"",required:!1},src:{type:String,default:""}},data:function(){return{size:this.$parent.size}},computed:{avatarSize:function(){return"mini"!==this.size&&this.size||20}},watch:{"$parent.size":function(t){this.size=t}}},l=o,c=n("2877"),u=Object(c["a"])(l,a,i,!1,null,null,null);e["default"]=u.exports},"611e":function(t,e,n){"use strict";var a=n("996b");e["a"]=a["default"]},6745:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"img"},[n("a-upload",{key:t.uploadKey,attrs:{name:"file",listType:"picture-card",multiple:t.isMultiple,action:t.dummyAction,fileList:t.fileList,beforeUpload:t.beforeUpload,disabled:t.disabled,customRequest:t.customRequest},on:{change:t.handleChange,preview:t.handlePreview,remove:t.handleRemove}},[t.fileList.length<t.maxCount?n("div",[n("a-icon",{attrs:{type:"plus"}}),n("div",{staticClass:"ant-upload-text"},[t._v(t._s(t.text))])],1):t._e()]),n("a-modal",{attrs:{visible:t.previewVisible,footer:null},on:{cancel:t.handleCancel}},[n("img",{staticStyle:{width:"100%"},attrs:{alt:"预览",src:t.previewImage}})])],1)},i=[],r=n("a34a"),s=n.n(r),o=n("9fb0");function l(t,e,n,a,i,r,s){try{var o=t[r](s),l=o.value}catch(c){return void n(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function c(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){l(r,a,i,s,o,"next",t)}function o(t){l(r,a,i,s,o,"throw",t)}s(void 0)}))}}function u(t){return f(t)||h(t)||p(t)||d()}function d(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"===typeof t)return m(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(t,e):void 0}}function h(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function f(t){if(Array.isArray(t))return m(t)}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=t[n];return a}var g={name:"JImageUploadDeferred",props:{value:{type:[String,Array],required:!1},text:{type:String,required:!1,default:"上传"},bizPath:{type:String,required:!1,default:"temp"},disabled:{type:Boolean,required:!1,default:!1},isMultiple:{type:Boolean,required:!1,default:!1},number:{type:Number,required:!1,default:0}},data:function(){return{fileList:[],previewVisible:!1,previewImage:"",pendingFiles:[],dummyAction:"dummy",maxCount:this.isMultiple?this.number||10:1,uploadKey:Date.now(),originalFiles:[],deletedFiles:[],isInitialized:!1}},watch:{value:{handler:function(t){this.initFileList(t)},immediate:!0}},methods:{initFileList:function(t){var e=this;if(!t)return this.fileList=[],void(this.originalFiles=[]);var n=Array.isArray(t)?t:t.split(",").filter((function(t){return t.trim()}));this.fileList=n.map((function(t,n){return{uid:"existing-".concat(n),name:e.getFileName(t),status:"done",url:e.getFullUrl(t),response:{message:t},isExisting:!0}})),this.isInitialized||(this.originalFiles=u(this.fileList),this.deletedFiles=[],this.isInitialized=!0)},getFileName:function(t){return t.split("/").pop()||"image.jpg"},getFullUrl:function(t){return t.startsWith("http")?t:window._CONFIG["staticDomainURL"]+"/"+t},customRequest:function(t){var e=t.file,n=t.onSuccess;this.pendingFiles.push(e);var a=new FileReader;a.onload=function(t){var a={success:!0,message:"pending",url:t.target.result};n(a,e)},a.readAsDataURL(e)},beforeUpload:function(t){var e=0===t.type.indexOf("image/");if(!e)return this.$message.error("只能上传图片文件!"),!1;var n=t.size/1024/1024<5;return n?!this.isMultiple&&this.fileList.length>=1?(this.$message.error("只能上传一张图片!"),!1):!(this.isMultiple&&this.fileList.length>=this.maxCount)||(this.$message.error("最多只能上传 ".concat(this.maxCount," 张图片!")),!1):(this.$message.error("图片大小不能超过 5MB!"),!1)},handleChange:function(t){if("done"===t.file.status){this.fileList=u(t.fileList);var e=this.fileList.find((function(e){return e.uid===t.file.uid}));e&&t.file.response&&"pending"===t.file.response.message&&(e.isPending=!0)}else"uploading"===t.file.status||"error"===t.file.status?this.fileList=u(t.fileList):"removed"===t.file.status&&(this.handleFileRemoval(t.file),this.fileList=u(t.fileList))},handleFileRemoval:function(t){var e=this.fileList.find((function(e){return e.uid===t.uid})),n=e&&e.isPending,a=e&&e.isExisting;if(n)this.pendingFiles=this.pendingFiles.filter((function(e){return e.uid!==t.uid}));else if(a){var i=e.response&&e.response.message;i&&"pending"!==i&&this.deletedFiles.push(i)}this.emitChange()},handleRemove:function(t){this.handleFileRemoval(t),this.fileList=this.fileList.filter((function(e){return e.uid!==t.uid}))},handlePreview:function(t){this.previewImage=t.url||t.thumbUrl,this.previewVisible=!0},handleCancel:function(){this.previewVisible=!1},emitChange:function(){this.fileList.forEach((function(t,e){}));var t=this.fileList.filter((function(t){var e=t.isExisting&&t.response&&"pending"!==t.response.message;return e})).map((function(t){return t.response.message})),e=this.isMultiple?t.join(","):t[0]||"";this.$emit("change",e)},performUpload:function(){var t=c(s.a.mark((function t(){var e,n,a=this;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==this.pendingFiles.length){t.next=3;break}return t.abrupt("return",[]);case 3:return e=this.pendingFiles.map((function(t){return a.uploadSingleFile(t)})),t.prev=5,t.next=8,Promise.all(e);case 8:return n=t.sent,this.fileList.forEach((function(t){if(t.isPending){var e=n.find((function(e){return e.originalUid===t.uid}));e&&e.success&&(t.response={message:e.filePath},t.isExisting=!0,t.isPending=!1,t.url=a.getFullUrl(e.filePath))}})),this.pendingFiles=[],this.emitChange(),t.abrupt("return",n.filter((function(t){return t.success})).map((function(t){return t.filePath})));case 16:throw t.prev=16,t.t0=t["catch"](5),this.$message.error("文件上传失败: "+t.t0.message),t.t0;case 21:case"end":return t.stop()}}),t,this,[[5,16]])})));function e(){return t.apply(this,arguments)}return e}(),uploadSingleFile:function(){var t=c(s.a.mark((function t(e){var n=this;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,a){var i=new FormData;i.append("file",e),i.append("biz",n.bizPath);var r=new XMLHttpRequest;r.upload.onprogress=function(t){if(t.lengthComputable)Math.round(t.loaded/t.total*100)},r.onload=function(){if(200===r.status)try{var n=JSON.parse(r.responseText);n.success?t({success:!0,filePath:n.message,originalUid:e.uid}):a(new Error(n.message||"上传失败"))}catch(i){a(new Error("响应解析失败"))}else a(new Error("HTTP ".concat(r.status,": ").concat(r.statusText)))},r.onerror=function(){a(new Error("网络错误"))},r.open("POST",window._CONFIG["domianURL"]+"/sys/common/upload"),r.setRequestHeader("X-Access-Token",n.$ls.get(o["a"])),r.send(i)})));case 1:case"end":return t.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}(),getCurrentValue:function(){var t=this.fileList.filter((function(t){return t.isExisting&&t.response&&"pending"!==t.response.message})).map((function(t){return t.response.message})),e=this.isMultiple?t.join(","):t[0]||"";return e},hasPendingFiles:function(){var t=this.pendingFiles.length>0;return t},hasChanges:function(){return this.pendingFiles.length>0||this.deletedFiles.length>0},rollbackChanges:function(){this.fileList=u(this.originalFiles),this.pendingFiles=[],this.deletedFiles=[],this.emitChange()},confirmDeleteOriginalFiles:function(){var t=c(s.a.mark((function t(){var e,n=this;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==this.deletedFiles.length){t.next=3;break}return t.abrupt("return");case 3:return t.prev=4,e=this.deletedFiles.map((function(t){return n.deleteServerFile(t)})),t.next=8,Promise.all(e);case 8:this.deletedFiles=[],t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](4);case 15:case"end":return t.stop()}}),t,this,[[4,12]])})));function e(){return t.apply(this,arguments)}return e}(),deleteServerFile:function(){var t=c(s.a.mark((function t(e){var n=this;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,a){var i=new XMLHttpRequest;i.onload=function(){if(200===i.status)try{var e=JSON.parse(i.responseText);e.success?t():a(new Error(e.message||"删除失败"))}catch(n){a(new Error("响应解析失败"))}else a(new Error("HTTP ".concat(i.status,": ").concat(i.statusText)))},i.onerror=function(){a(new Error("网络错误"))},i.open("DELETE",window._CONFIG["domianURL"]+"/sys/common/deleteFile?filePath="+encodeURIComponent(e)),i.setRequestHeader("X-Access-Token",n.$ls.get("Access-Token")),i.send()})));case 1:case"end":return t.stop()}}),t)})));function e(e){return t.apply(this,arguments)}return e}()},model:{prop:"value",event:"change"}},v=g,y=(n("0591"),n("2877")),b=Object(y["a"])(v,a,i,!1,null,"621efdb5",null);e["default"]=b.exports},"6a2a":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("span",[t._v("\n  "+t._s(t._f("format")(t.lastTime))+"\n")])},i=[];function r(t){return 1*t<10?"0".concat(t):t}var s={name:"CountDown",props:{format:{type:Function,default:void 0},target:{type:[Date,Number],required:!0},onEnd:{type:Function,default:function(){}}},data:function(){return{dateTime:"0",originTargetTime:0,lastTime:0,timer:0,interval:1e3}},filters:{format:function(t){var e=36e5,n=6e4,a=Math.floor(t/e),i=Math.floor((t-a*e)/n),s=Math.floor((t-a*e-i*n)/1e3);return"".concat(r(a),":").concat(r(i),":").concat(r(s))}},created:function(){this.initTime(),this.tick()},methods:{initTime:function(){var t=0,e=0;this.originTargetTime=this.target;try{e="[object Date]"===Object.prototype.toString.call(this.target)?this.target:new Date(this.target).getTime()}catch(n){throw new Error("invalid target prop")}t=e-(new Date).getTime(),this.lastTime=t<0?0:t},tick:function(){var t=this,e=this.onEnd;this.timer=setTimeout((function(){t.lastTime<t.interval?(clearTimeout(t.timer),t.lastTime=0,"function"===typeof e&&e()):(t.lastTime-=t.interval,t.tick())}),this.interval)}},beforeUpdate:function(){this.originTargetTime!==this.target&&this.initTime()},beforeDestroy:function(){clearTimeout(this.timer)}},o=s,l=n("2877"),c=Object(l["a"])(o,a,i,!1,null,"7b39227c",null);e["default"]=c.exports},"6cb2":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-chart",{attrs:{forceFit:!0,height:t.height,data:t.data,scale:t.scale,onClick:t.handleClick}},[n("v-tooltip",{attrs:{showTitle:!1,dataKey:"item*percent"}}),n("v-axis"),n("v-legend",{attrs:{dataKey:"item"}}),n("v-pie",{attrs:{position:"percent",color:"item","v-style":t.pieStyle,label:t.labelConfig}}),n("v-coord",{attrs:{type:"theta"}})],1)},i=[],r=n("c917"),s=n("7104"),o={name:"Pie",mixins:[r["a"]],props:{title:{type:String,default:""},height:{type:Number,default:254},dataSource:{type:Array,default:function(){return[{item:"示例一",count:40},{item:"示例二",count:21},{item:"示例三",count:17},{item:"示例四",count:13},{item:"示例五",count:9}]}}},data:function(){return{scale:[{dataKey:"percent",min:0,formatter:".0%"}],pieStyle:{stroke:"#fff",lineWidth:1},labelConfig:["percent",{formatter:function(t,e){return e.point.item+": "+t}}]}},computed:{data:function(){var t=(new s.View).source(this.dataSource);return t.transform({type:"percent",field:"count",dimension:"item",as:"percent"}),t.rows}}},l=o,c=n("2877"),u=Object(c["a"])(l,a,i,!1,null,null,null);e["default"]=u.exports},"6ded":function(t,e,n){},7195:function(t,e,n){},7227:function(t,e,n){"use strict";var a=n("5246"),i=n.n(a);i.a},7506:function(t,e,n){"use strict";var a=n("2dd4"),i=n.n(a);i.a},"7c57":function(t,e,n){"use strict";var a=n("44fe"),i=n.n(a);i.a},8191:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 0 32px 32px"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{attrs:{data:t.data,height:t.height,"force-fit":!0,scale:t.scale,onClick:t.handleClick}},[n("v-tooltip"),n("v-axis"),n("v-legend"),n("v-bar",{attrs:{position:"x*y",color:"type",adjust:t.adjust}})],1)],1)},i=[],r=n("7104"),s=n("c917");function o(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=l(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var a=0,i=function(){};return{s:i,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,r=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw r}}}}function l(t,e){if(t){if("string"===typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=t[n];return a}var u={name:"BarMultid",mixins:[s["a"]],props:{title:{type:String,default:""},dataSource:{type:Array,default:function(){return[{type:"Jeecg","Jan.":18.9,"Feb.":28.8,"Mar.":39.3,"Apr.":81.4,May:47,"Jun.":20.3,"Jul.":24,"Aug.":35.6},{type:"Jeebt","Jan.":12.4,"Feb.":23.2,"Mar.":34.5,"Apr.":99.7,May:52.6,"Jun.":35.5,"Jul.":37.4,"Aug.":42.4}]}},fields:{type:Array,default:function(){return["Jan.","Feb.","Mar.","Apr.","May","Jun.","Jul.","Aug."]}},aliases:{type:Array,default:function(){return[]}},height:{type:Number,default:254}},data:function(){return{adjust:[{type:"dodge",marginRatio:1/32}]}},computed:{data:function(){var t=this,e=(new r["DataSet"].View).source(this.dataSource);e.transform({type:"fold",fields:this.fields,key:"x",value:"y"});var n=e.rows.map((function(t){return"string"===typeof t.x&&(t.x=t.x.replace(/[-/]/g,"_")),t}));return n.forEach((function(e){var n,a=o(t.aliases);try{for(a.s();!(n=a.n()).done;){var i=n.value;if(i.field===e.type){e.type=i.alias;break}}}catch(r){a.e(r)}finally{a.f()}})),n},scale:function(){return[{type:"cat",dataKey:"x"}]}}},d=u,p=n("2877"),h=Object(p["a"])(d,a,i,!1,null,"04eb2e12",null);e["default"]=h.exports},"81d1":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"head-info",class:t.center&&"center"},[n("span",[t._v(t._s(t.title))]),n("p",[t._v(t._s(t.content))]),t.bordered?n("em"):t._e()])},i=[],r={name:"HeadInfo",props:{title:{type:String,default:""},content:{type:String,default:""},bordered:{type:Boolean,default:!1},center:{type:Boolean,default:!0}}},s=r,o=(n("c002"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"35671dcd",null);e["default"]=l.exports},"82be":function(t,e,n){"use strict";var a=n("f30a"),i=n.n(a);i.a},"831e":function(t,e,n){},84962:function(t,e,n){},"886b":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div")},i=[],r={name:"LogoutConfirmDialog",props:{visible:{type:Boolean,default:!1},title:{type:String,default:"确认退出登录"},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认退出"},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"退出中..."},dialogType:{type:String,default:"logout"},isFirstTimePassword:{type:Boolean,default:!1}},data:function(){return{portalElement:null,savedScrollY:null}},watch:{visible:{handler:function(t){t?this.createPortal():this.removePortal()},immediate:!0}},beforeDestroy:function(){this.removePortal()},methods:{createPortal:function(){if(this.removePortal(),!this.portalElement){this.savedScrollY=window.pageYOffset||document.documentElement.scrollTop,document.body.style.overflow="hidden",document.documentElement.style.overflow="hidden",this.portalElement=document.createElement("div"),this.portalElement.style.cssText="\n        position: fixed !important;\n        top: 0 !important;\n        left: 0 !important;\n        right: 0 !important;\n        bottom: 0 !important;\n        width: 100vw !important;\n        height: 100vh !important;\n        background: rgba(0, 0, 0, 0.85) !important;\n        backdrop-filter: blur(20px) !important;\n        -webkit-backdrop-filter: blur(20px) !important;\n        display: flex !important;\n        align-items: center !important;\n        justify-content: center !important;\n        z-index: 1000 !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        overflow: hidden !important;\n        pointer-events: all !important;\n      ",this.portalElement.innerHTML=this.generateDialogHTML(),this.portalElement.addEventListener("click",this.handleOverlayClick);var t=this.portalElement.querySelector(".cancel-btn"),e=this.portalElement.querySelector(".confirm-btn"),n=this.portalElement.querySelector(".confirm-dialog");t&&t.addEventListener("click",this.handleCancel),e&&e.addEventListener("click",this.handleConfirm),n&&n.addEventListener("click",(function(t){return t.stopPropagation()})),"changePassword"===this.dialogType&&this.setupPasswordValidation(),document.body.appendChild(this.portalElement)}},removePortal:function(){if(this.portalElement){var t=this.savedScrollY||0;document.body.style.overflow="",document.documentElement.style.overflow="",window.scrollTo(0,t),setTimeout((function(){window.scrollTo(0,t)}),10),requestAnimationFrame((function(){window.scrollTo(0,t)})),this.portalElement.removeEventListener("click",this.handleOverlayClick),this.portalElement.parentNode&&this.portalElement.parentNode.removeChild(this.portalElement),this.portalElement=null,this.savedScrollY=null}},generateDialogHTML:function(){return"changePassword"===this.dialogType?this.generatePasswordDialogHTML():this.generateLogoutDialogHTML()},generateLogoutDialogHTML:function(){return'\n        <div class="confirm-dialog-portal" style="\n          background: rgba(255, 255, 255, 0.98) !important;\n          border-radius: 24px !important;\n          padding: 4rem !important;\n          margin: 1rem !important;\n          max-width: 700px !important;\n          width: 98% !important;\n          max-height: 90vh !important;\n          overflow-y: auto !important;\n          box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;\n          backdrop-filter: blur(20px) !important;\n          -webkit-backdrop-filter: blur(20px) !important;\n          border: 1px solid rgba(124, 138, 237, 0.15) !important;\n          z-index: 1000 !important;\n          position: relative !important;\n        " onclick="event.stopPropagation()">\n          <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">\n            <div style="\n              width: 48px; height: 48px; border-radius: 50%; display: flex;\n              align-items: center; justify-content: center; color: white; font-size: 24px;\n              background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n            ">\n              <i class="anticon anticon-logout"></i>\n            </div>\n            <h3 style="font-size: 1.5rem; font-weight: 600; color: #334155; margin: 0;">\n              '.concat(this.title,'\n            </h3>\n          </div>\n\n          <div style="margin-bottom: 2rem;">\n            <div style="\n              display: flex; gap: 1rem; padding: 1.5rem; border-radius: 16px; border: 1px solid;\n              background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);\n              border-color: rgba(239, 68, 68, 0.2);\n            ">\n              <div style="flex-shrink: 0; width: 24px; height: 24px; font-size: 20px; color: #ef4444;">\n                <i class="anticon anticon-warning"></i>\n              </div>\n              <div style="flex: 1; color: #64748b; line-height: 1.6;">\n                <p style="margin: 0 0 0.75rem 0;"><strong style="color: #334155; font-weight: 600;">退出登录将清除您的登录状态和本地缓存数据。</strong></p>\n                <p style="margin: 0 0 0.75rem 0;">退出后您需要重新登录才能访问个人中心和其他需要登录的功能。</p>\n                <p style="color: #ef4444; font-weight: 600; margin: 1rem 0 0 0;">确定要退出登录吗？</p>\n              </div>\n            </div>\n          </div>\n\n          <div style="display: flex; gap: 1rem; justify-content: flex-end;">\n            <button class="cancel-btn" style="\n              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;\n              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;\n              border: 1px solid rgba(148, 163, 184, 0.2); font-size: 14px; min-width: 120px;\n              justify-content: center; background: rgba(148, 163, 184, 0.1); color: #64748b;\n            ">\n              <i class="anticon anticon-close"></i>\n              <span>').concat(this.cancelText,'</span>\n            </button>\n            <button class="confirm-btn" style="\n              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;\n              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;\n              border: none; font-size: 14px; min-width: 120px; justify-content: center;\n              background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;\n              box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);\n            " ').concat(this.loading?"disabled":"",'>\n              <i class="anticon ').concat(this.loading?"anticon-loading spinning":"anticon-logout",'"></i>\n              <span>').concat(this.loading?this.loadingText:this.confirmText,"</span>\n            </button>\n          </div>\n        </div>\n      ")},generatePasswordDialogHTML:function(){var t=this.isFirstTimePassword;return'\n        <div class="confirm-dialog-portal" style="\n          background: rgba(255, 255, 255, 0.98) !important;\n          border-radius: 24px !important;\n          padding: 4rem !important;\n          margin: 1rem !important;\n          max-width: 750px !important;\n          width: 98% !important;\n          max-height: 90vh !important;\n          overflow-y: auto !important;\n          box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;\n          backdrop-filter: blur(20px) !important;\n          -webkit-backdrop-filter: blur(20px) !important;\n          border: 1px solid rgba(124, 138, 237, 0.15) !important;\n          z-index: 1000 !important;\n          position: relative !important;\n        " onclick="event.stopPropagation()">\n          \x3c!-- 标题区域 --\x3e\n          <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">\n            <div style="\n              width: 48px; height: 48px; border-radius: 50%; display: flex;\n              align-items: center; justify-content: center; color: white; font-size: 24px;\n              background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n            ">\n              <i class="anticon anticon-lock"></i>\n            </div>\n            <h3 style="font-size: 1.5rem; font-weight: 600; color: #334155; margin: 0;">\n              '.concat(this.title,'\n            </h3>\n          </div>\n\n          \x3c!-- 提示信息 --\x3e\n          <div style="margin-bottom: 2rem;">\n            <div style="\n              display: flex; gap: 1rem; padding: 1.5rem; border-radius: 16px; border: 1px solid;\n              background: linear-gradient(135deg, rgba(124, 138, 237, 0.1) 0%, rgba(139, 95, 191, 0.05) 100%);\n              border-color: rgba(124, 138, 237, 0.2);\n            ">\n              <div style="flex-shrink: 0; width: 24px; height: 24px; font-size: 20px; color: #7c8aed;">\n                <i class="anticon anticon-info-circle"></i>\n              </div>\n              <div style="flex: 1; color: #64748b; line-height: 1.6;">\n                ').concat(t?'<p style="margin: 0;"><strong style="color: #334155;">首次设置密码</strong></p><p style="margin: 0.5rem 0 0 0;">为了您的账户安全，请设置一个强密码。</p>':'<p style="margin: 0;"><strong style="color: #334155;">修改登录密码</strong></p><p style="margin: 0.5rem 0 0 0;">定期更换密码可以提高账户安全性。</p>','\n              </div>\n            </div>\n          </div>\n\n          \x3c!-- 密码输入表单 --\x3e\n          <div style="margin-bottom: 2rem;">\n            ').concat(t?"":'\n              <div style="margin-bottom: 1rem;">\n                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #334155;">当前密码</label>\n                <input type="password" id="oldPassword" placeholder="请输入当前密码" style="\n                  width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px;\n                  font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;\n                " onclick="event.stopPropagation()" onmousedown="event.stopPropagation()" onfocus="event.stopPropagation()" />\n              </div>\n            ','\n\n            <div style="margin-bottom: 1rem;">\n              <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #334155;">新密码</label>\n              <input type="password" id="newPassword" placeholder="请输入新密码" style="\n                width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px;\n                font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;\n              " onclick="event.stopPropagation()" onmousedown="event.stopPropagation()" onfocus="event.stopPropagation()" />\n\n              \x3c!-- 密码强度指示器 --\x3e\n              <div id="passwordStrengthContainer" style="margin-top: 0.5rem; display: none;">\n                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">\n                  <span style="font-size: 12px; color: #64748b;">密码强度：</span>\n                  <span id="strengthText" style="font-size: 12px; font-weight: 600;"></span>\n                  <div style="flex: 1; height: 4px; background: #e5e7eb; border-radius: 2px; overflow: hidden;">\n                    <div id="strengthBar" style="height: 100%; transition: all 0.3s ease; border-radius: 2px; width: 0%;"></div>\n                  </div>\n                </div>\n                <div id="passwordErrors" style="font-size: 12px; color: #ef4444; display: none;"></div>\n              </div>\n            </div>\n\n            <div style="margin-bottom: 1rem;">\n              <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #334155;">确认密码</label>\n              <input type="password" id="confirmPassword" placeholder="请再次输入新密码" style="\n                width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px;\n                font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;\n              " onclick="event.stopPropagation()" onmousedown="event.stopPropagation()" onfocus="event.stopPropagation()" />\n              <div id="confirmPasswordError" style="margin-top: 0.5rem; font-size: 12px; color: #ef4444; display: none;"></div>\n            </div>\n\n            \x3c!-- 密码要求提示 --\x3e\n            <div style="\n              padding: 1rem; background: rgba(124, 138, 237, 0.05); border: 1px solid rgba(124, 138, 237, 0.1);\n              border-radius: 8px; font-size: 12px; color: #64748b; line-height: 1.5;\n            ">\n              <div style="font-weight: 600; color: #334155; margin-bottom: 0.5rem;">密码要求：</div>\n              <div>• 长度8-32位</div>\n              <div>• 至少包含3种字符类型（大写字母、小写字母、数字、特殊字符）</div>\n              ').concat(t?"":"<div>• 新密码不能与当前密码相同</div>",'\n              <div>• 不能过于简单（如连续相同字符等）</div>\n            </div>\n          </div>\n\n          \x3c!-- 按钮区域 --\x3e\n          <div style="display: flex; gap: 1rem; justify-content: flex-end;">\n            <button class="cancel-btn" style="\n              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;\n              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;\n              border: 1px solid rgba(148, 163, 184, 0.2); font-size: 14px; min-width: 120px;\n              justify-content: center; background: rgba(148, 163, 184, 0.1); color: #64748b;\n            ">\n              <i class="anticon anticon-close"></i>\n              <span>').concat(this.cancelText,'</span>\n            </button>\n            <button class="confirm-btn" id="confirmBtn" style="\n              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;\n              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;\n              border: none; font-size: 14px; min-width: 120px; justify-content: center;\n              background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%); color: white;\n              box-shadow: 0 4px 12px rgba(124, 138, 237, 0.3);\n            " ').concat(this.loading?"disabled":"",'>\n              <i class="anticon ').concat(this.loading?"anticon-loading spinning":"anticon-check",'"></i>\n              <span>').concat(this.loading?this.loadingText:this.confirmText,"</span>\n            </button>\n          </div>\n        </div>\n      ")},handleOverlayClick:function(t){var e=this.portalElement.querySelector(".confirm-dialog-portal");if(e){var n=e.getBoundingClientRect(),a=50,i={left:n.left-a,right:n.right+a,top:n.top-a,bottom:n.bottom+a},r=t.clientX,s=t.clientY,o=r<i.left||r>i.right||s<i.top||s>i.bottom;o&&this.handleCancel()}else this.handleCancel()},handleCancel:function(){this.$emit("cancel"),this.$emit("update:visible",!1)},handleConfirm:function(){this.$emit("confirm")},setupPasswordValidation:function(){var t=this,e=this.portalElement.querySelector("#newPassword"),n=this.portalElement.querySelector("#confirmPassword"),a=this.portalElement.querySelector("#oldPassword");e&&e.addEventListener("input",(function(){t.validatePasswordRealtime()})),n&&n.addEventListener("input",(function(){t.validatePasswordRealtime()})),a&&a.addEventListener("input",(function(){t.validatePasswordRealtime()}))},validatePasswordRealtime:function(){var t=this.portalElement.querySelector("#newPassword"),e=this.portalElement.querySelector("#confirmPassword"),n=this.portalElement.querySelector("#oldPassword"),a=t?t.value:"",i=e?e.value:"",r=n?n.value:"",s=this.validatePasswordStrength(a);this.updatePasswordStrengthDisplay(a,s),this.updateConfirmPasswordDisplay(a,i),this.updateConfirmButtonState(s,a,i,r)},validatePasswordStrength:function(t){var e=[];if(t.length>0&&t.length<8&&e.push("密码长度至少8位"),t.length>32&&e.push("密码长度不能超过32位"),t.length>0){var n=0;/[A-Z]/.test(t)&&n++,/[a-z]/.test(t)&&n++,/\d/.test(t)&&n++,/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(t)&&n++,n<3&&e.push("至少包含3种字符类型（大写字母、小写字母、数字、特殊字符）")}t.length>0&&(/(.)\1{3,}/.test(t)&&e.push("不能包含连续4个相同字符"),/^123456/.test(t)&&e.push("不能以123456开头"),/^password$/i.test(t)&&e.push("不能是password"));var a=this.calculatePasswordStrength(t);return{isValid:0===e.length&&t.length>=8,errors:e,strength:a}},calculatePasswordStrength:function(t){if(0===t.length)return{level:"none",text:"",color:"#d1d5db"};var e=0;return t.length>=8&&(e+=1),t.length>=12&&(e+=1),t.length>=16&&(e+=1),/[a-z]/.test(t)&&(e+=1),/[A-Z]/.test(t)&&(e+=1),/\d/.test(t)&&(e+=1),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(t)&&(e+=1),/(?=.*[a-zA-Z])(?=.*\d)/.test(t)&&(e+=1),e<=2?{level:"weak",text:"弱",color:"#ef4444"}:e<=4?{level:"medium",text:"中",color:"#f59e0b"}:e<=6?{level:"strong",text:"强",color:"#10b981"}:{level:"very-strong",text:"很强",color:"#059669"}},updatePasswordStrengthDisplay:function(t,e){var n=this.portalElement.querySelector("#passwordStrengthContainer");if(n)if(0!==t.length){n.style.display="block";var a=n.querySelector("#strengthText"),i=n.querySelector("#strengthBar"),r=n.querySelector("#passwordErrors");if(a&&i){a.textContent=e.strength.text,a.style.color=e.strength.color;var s={none:"0%",weak:"25%",medium:"50%",strong:"75%","very-strong":"100%"}[e.strength.level]||"0%";i.style.width=s,i.style.background=e.strength.color}r&&(e.errors.length>0?(r.textContent=e.errors.join("，"),r.style.display="block"):r.style.display="none")}else n.style.display="none"},updateConfirmPasswordDisplay:function(t,e){var n=this.portalElement.querySelector("#confirmPasswordError");n&&(0!==e.length&&e!==t?(n.textContent="两次输入的密码不一致",n.style.display="block"):n.style.display="none")},updateConfirmButtonState:function(t,e,n,a){var i=this.portalElement.querySelector("#confirmBtn");if(i){var r=t.isValid&&e.length>=8,s=n===e&&n.length>0,o=!this.portalElement.querySelector("#oldPassword")||a.length>0,l=r&&s&&o;i.disabled=!l,i.style.opacity=l?"1":"0.5",i.style.cursor=l?"pointer":"not-allowed"}}}},s=r,o=(n("1963"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"234fdcfb",null);e["default"]=l.exports},"8c9d":function(t,e,n){},"8d7f":function(t,e,n){"use strict";var a=n("f462"),i=n.n(a);i.a},"942d":function(t,e,n){"use strict";n.r(e);for(var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{width:null==t.width?"auto":t.width+"px"}},[n("v-chart",{attrs:{forceFit:null==t.width,height:t.height,data:t.data,padding:"0"}},[n("v-tooltip"),n("v-bar",{attrs:{position:"x*y"}})],1)],1)},i=[],r=n("5a0c"),s=n.n(r),o=[],l=(new Date).getTime(),c=0;c<10;c++)o.push({x:s()(new Date(l+864e5*c)).format("YYYY-MM-DD"),y:Math.round(10*Math.random())});var u=["x*y",function(t,e){return{name:t,value:e}}],d=[{dataKey:"x",min:2},{dataKey:"y",title:"时间",min:1,max:30}],p={name:"MiniBar",props:{dataSource:{type:Array,default:function(){return[]}},width:{type:Number,default:null},height:{type:Number,default:200}},created:function(){0===this.dataSource.length?this.data=o:this.data=this.dataSource},data:function(){return{tooltip:u,data:[],scale:d}}},h=p,f=(n("a8f1"),n("2877")),m=Object(f["a"])(h,a,i,!1,null,"ebc8c03c",null);e["default"]=m.exports},"972f":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 0 32px 32px"}},[n("v-chart",{attrs:{forceFit:!0,height:300,data:t.chartData,scale:t.scale}},[n("v-coord",{attrs:{type:"polar",startAngle:-202.5,endAngle:22.5,radius:.75}}),n("v-axis",{attrs:{dataKey:"value",zIndex:2,line:null,label:t.axisLabel,subTickCount:4,subTickLine:t.axisSubTickLine,tickLine:t.axisTickLine,grid:null}}),n("v-axis",{attrs:{dataKey:"1",show:!1}}),n("v-series",{attrs:{gemo:"point",position:"value*1",shape:"pointer",color:"#1890FF",active:!1}}),n("v-guide",{attrs:{type:"arc",zIndex:0,top:!1,start:t.arcGuide1Start,end:t.arcGuide1End,vStyle:t.arcGuide1Style}}),n("v-guide",{attrs:{type:"arc",zIndex:1,start:t.arcGuide2Start,end:t.getArcGuide2End,vStyle:t.arcGuide2Style}}),n("v-guide",{attrs:{type:"html",position:t.htmlGuidePosition,html:t.getHtmlGuideHtml()}})],1)],1)},i=[],r=n("3654");Object(r["b"])("point","pointer",{draw:function(t,e){var n=t.points[0];n=this.parsePoint(n);var a=this.parsePoint({x:0,y:0});return e.addShape("line",{attrs:{x1:a.x,y1:a.y,x2:n.x,y2:n.y+15,stroke:t.color,lineWidth:5,lineCap:"round"}}),e.addShape("circle",{attrs:{x:a.x,y:a.y,r:9.75,stroke:t.color,lineWidth:4.5,fill:"#fff"}})}});var s=[{dataKey:"value",min:0,max:9,tickInterval:1,nice:!1}],o=[{value:7}],l={name:"DashChartDemo",props:{datasource:{type:Number,default:7},title:{type:String,default:""}},created:function(){this.datasource?this.chartData=[{value:this.datasource}]:this.chartData=o,this.getChartData()},watch:{datasource:function(t){this.chartData=[{value:t}],this.getChartData()}},methods:{getChartData:function(){this.chartData&&this.chartData.length>0?this.abcd=10*this.chartData[0].value:this.abcd=70},getHtmlGuideHtml:function(){return'<div style="width: 300px;text-align: center;">\n<p style="font-size: 14px;color: #545454;margin: 0;">'+this.title+'</p>\n<p style="font-size: 36px;color: #545454;margin: 0;">'+this.abcd+"%</p>\n</div>"},getArcGuide2End:function(){return[this.chartData[0].value,.945]}},data:function(){return{chartData:[],height:400,scale:s,abcd:70,axisLabel:{offset:-16,textStyle:{fontSize:18,textAlign:"center",textBaseline:"middle"}},axisSubTickLine:{length:-8,stroke:"#fff",strokeOpacity:1},axisTickLine:{length:-17,stroke:"#fff",strokeOpacity:1},arcGuide1Start:[0,.945],arcGuide1End:[9,.945],arcGuide1Style:{stroke:"#CBCBCB",lineWidth:18},arcGuide2Start:[0,.945],arcGuide2Style:{stroke:"#1890FF",lineWidth:18},htmlGuidePosition:["50%","100%"],htmlGuideHtml:'\n      <div style="width: 300px;text-align: center;">\n        <p style="font-size: 14px;color: #545454;margin: 0;">'.concat(this.title,'</p>\n        <p style="font-size: 36px;color: #545454;margin: 0;">').concat(this.abcd,"%</p>\n      </div>\n    ")}}},c=l,u=n("2877"),d=Object(u["a"])(c,a,i,!1,null,null,null);e["default"]=d.exports},9815:function(t,e,n){"use strict";var a=n("2257"),i=n.n(a);i.a},"996b":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{class:[t.prefixCls,t.reverseColor&&"reverse-color"]},[n("span",[t._t("term"),n("span",{staticClass:"item-text"},[t._t("default")],2)],2),n("span",{class:[t.flag]},[n("a-icon",{attrs:{type:"caret-"+t.flag}})],1)])},i=[],r={name:"Trend",props:{prefixCls:{type:String,default:"ant-pro-trend"},flag:{type:String,required:!0},reverseColor:{type:Boolean,default:!1}}},s=r,o=(n("b6e2"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"a6292b02",null);e["default"]=l.exports},a222:function(t,e,n){},a22d:function(t,e,n){},a40c:function(t,e,n){"use strict";n.r(e);var a=n("27fd"),i=n("58f7");function r(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter((function(t){return t.tag||t.text&&""!==t.text.trim()}))}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var o,l,c={AvatarItem:i["default"],name:"AvatarList",components:{Avatar:a["a"],AvatarItem:i["default"]},props:{prefixCls:{type:String,default:"ant-pro-avatar-list"},size:{type:[String,Number],default:"default"},maxLength:{type:Number,default:0},excessItemsStyle:{type:Object,default:function(){return{color:"#f56a00",backgroundColor:"#fde3cf"}}}},data:function(){return{}},methods:{getItems:function(t){var e,n=this.$createElement,i=(e={},s(e,"".concat(this.prefixCls,"-item"),!0),s(e,"".concat(this.size),!0),e);this.maxLength>0&&(t=t.slice(0,this.maxLength),t.push(n(a["a"],{attrs:{size:this.size},style:this.excessItemsStyle},["+".concat(this.maxLength)])));var r=t.map((function(t){return n("li",{class:i},[t])}));return r}},render:function(){var t,e=arguments[0],n=this.$props,a=n.prefixCls,i=n.size,o=(t={},s(t,"".concat(a),!0),s(t,"".concat(i),!0),t),l=r(this.$slots.default),c=l&&l.length?e("ul",{class:"".concat(a,"-items")},[this.getItems(l)]):null;return e("div",{class:o},[c])}},u=c,d=n("2877"),p=Object(d["a"])(u,o,l,!1,null,null,null);e["default"]=p.exports},a545:function(t,e,n){"use strict";var a=n("a40c");n("84962");e["a"]=a["default"]},a8f1:function(t,e,n){"use strict";var a=n("ea2b"),i=n.n(a);i.a},ab30:function(t,e,n){},add5:function(t,e,n){},b6e2:function(t,e,n){"use strict";var a=n("e98a"),i=n.n(a);i.a},bd9e9:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{ref:"quantumContainer",staticClass:"quantum-jump-container",class:{visible:t.showButton}},[n("button",{ref:"quantumBtn",staticClass:"quantum-jump-btn",on:{click:t.scrollToTop}},[n("div",{staticClass:"btn-icon"},[n("a-icon",{attrs:{type:"rocket"}})],1),n("div",{staticClass:"btn-text"},[t._v("量子跳跃")]),n("div",{staticClass:"btn-glow"}),n("div",{ref:"particles",staticClass:"btn-particles"}),n("div",{staticClass:"quantum-waves"})])])},i=[],r=n("cffa"),s=n("9420");r["a"].registerPlugin(s["a"]);var o={name:"QuantumJump",props:{showAfterScroll:{type:Number,default:100},position:{type:Object,default:function(){return{bottom:"4rem",right:"2rem"}}},scrollDuration:{type:Number,default:1.5}},data:function(){return{showButton:!1}},mounted:function(){this.initQuantumJump(),this.applyPosition()},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll)},methods:{initQuantumJump:function(){window.addEventListener("scroll",this.handleScroll),this.initQuantumParticles()},applyPosition:function(){var t=this;if(this.$refs.quantumContainer){var e=this.$refs.quantumContainer;Object.keys(this.position).forEach((function(n){e.style[n]=t.position[n]}))}},handleScroll:function(){var t=window.pageYOffset||document.documentElement.scrollTop;this.showButton=t>this.showAfterScroll},scrollToTop:function(){this.triggerQuantumExplosion();var t=this;r["a"].to(window,{duration:this.scrollDuration,scrollTo:{y:0,autoKill:!1},ease:"power2.out",onComplete:function(){t.onQuantumJumpComplete()}})},initQuantumParticles:function(){this.$nextTick((function(){var t=this.$refs.particles;if(t)for(var e=0;e<15;e++){var n=document.createElement("div");n.className="quantum-particle",n.style.cssText="position: absolute; width: 4px; height: 4px; background: #3b82f6; border-radius: 50%; opacity: 0; box-shadow: 0 0 6px #3b82f6;",t.appendChild(n)}}))},triggerQuantumExplosion:function(){var t=this.$refs.particles;if(t){var e=t.querySelectorAll(".quantum-particle");e&&e.forEach((function(t,n){var a=n/e.length*Math.PI*2,i=80+60*Math.random(),s=Math.cos(a)*i,o=Math.sin(a)*i;r["a"].fromTo(t,{x:0,y:0,opacity:1,scale:.3},{duration:1.2,x:s,y:o,opacity:0,scale:2,ease:"power2.out",delay:.03*n})}))}},onQuantumJumpComplete:function(){r["a"].to(this.$refs.quantumBtn,{duration:.4,scale:1.15,yoyo:!0,repeat:1,ease:"power2.inOut"})}}},l=o,c=(n("1337"),n("2877")),u=Object(c["a"])(l,a,i,!1,null,"6d70bab2",null);e["default"]=u.exports},bdbd:function(t,e,n){"use strict";var a=n("8c9d"),i=n.n(a);i.a},bf13:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"chart-mini-progress"},[n("div",{staticClass:"target",style:{left:t.target+"%"}},[n("span",{style:{backgroundColor:t.color}}),n("span",{style:{backgroundColor:t.color}})]),n("div",{staticClass:"progress-wrapper"},[n("div",{staticClass:"progress",style:{backgroundColor:t.color,width:t.percentage+"%",height:t.height+"px"}})])])},i=[],r={name:"MiniProgress",props:{target:{type:Number,default:0},height:{type:Number,default:10},color:{type:String,default:"#13C2C2"},percentage:{type:Number,default:0}}},s=r,o=(n("3c24f"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"bc81620e",null);e["default"]=l.exports},c002:function(t,e,n){"use strict";var a=n("831e"),i=n.n(a);i.a},c4be:function(t,e,n){},c917:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var a={methods:{handleClick:function(t,e){this.handleEvent("click",t,e)},handleEvent:function(t,e,n){this.$emit(t,e,n)}}}},c984:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"toolbar"},[n("div",{staticStyle:{float:"left"}},[t._t("extra")],2),n("div",{staticStyle:{float:"right"}},[t._t("default")],2)])},i=[],r={name:"FooterToolBar"},s=r,o=(n("bdbd"),n("2877")),l=Object(o["a"])(s,a,i,!1,null,"1809f484",null);e["default"]=l.exports},ccb3:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("nav",{ref:"navbar",staticClass:"website-navbar",class:{scrolled:t.isScrolled,transparent:t.isTransparent}},[n("div",{staticClass:"nav-container"},[n("div",{ref:"navBrand",staticClass:"nav-brand",on:{click:t.goHome}},[n("LogoImage",{attrs:{size:"medium",hover:!0,"container-class":"brand-logo-container","image-class":"brand-logo-image","fallback-class":"brand-logo-fallback"}}),n("span",{staticClass:"brand-text"},[t._v("智界AIGC")])],1),n("div",{ref:"navMenu",staticClass:"nav-menu"},t._l(t.menuItems,(function(e){return n(e.path&&""!==e.path?"router-link":"span",{key:e.name,tag:"component",staticClass:"nav-link",class:{active:t.$route.path===e.path,"nav-link-disabled":!e.path||""===e.path},attrs:{to:e.path&&""!==e.path?e.path:void 0},on:{click:function(n){e.path&&""!==e.path||t.handleDevelopingClick(e.name)}}},[n("a-icon",{staticClass:"nav-icon",attrs:{type:e.icon}}),n("span",{staticClass:"nav-text"},[t._v(t._s(e.name))])],1)})),1),n("div",{ref:"navActions",staticClass:"nav-actions"},[t.isLoggedIn?t.isAdmin?n("button",{staticClass:"btn-admin",on:{click:t.goToAdmin}},[n("a-icon",{attrs:{type:"dashboard"}}),t._v("\n        后台管理\n      ")],1):t._e():n("button",{staticClass:"btn-secondary",on:{click:t.handleLogin}},[t._v("登录")])]),n("button",{ref:"mobileMenuBtn",staticClass:"mobile-menu-btn",on:{click:t.toggleMobileMenu}},[n("a-icon",{attrs:{type:t.mobileMenuOpen?"close":"menu"}})],1)]),n("div",{ref:"mobileMenu",staticClass:"mobile-menu",class:{open:t.mobileMenuOpen}},[t._l(t.menuItems,(function(e){return n(e.path&&""!==e.path?"router-link":"span",{key:e.name,tag:"component",staticClass:"mobile-nav-link",class:{"mobile-nav-link-disabled":!e.path||""===e.path},attrs:{to:e.path&&""!==e.path?e.path:void 0},on:{click:function(n){return t.handleMobileMenuClick(e)}}},[n("a-icon",{staticClass:"mobile-nav-icon",attrs:{type:e.icon}}),n("span",{staticClass:"mobile-nav-text"},[t._v(t._s(e.name))])],1)})),n("div",{staticClass:"mobile-actions"},[t.isLoggedIn?t.isAdmin?n("button",{staticClass:"mobile-btn-admin",on:{click:t.goToAdmin}},[n("a-icon",{attrs:{type:"dashboard"}}),t._v("\n        后台管理\n      ")],1):t._e():n("button",{staticClass:"mobile-btn-login",on:{click:t.handleLogin}},[t._v("登录")])])],2)])},i=[],r=n("a34a"),s=n.n(r),o=n("cffa"),l=n("9fb0"),c=n("cfda"),u=n("2b0e"),d=n("8bd7");function p(t,e,n,a,i,r,s){try{var o=t[r](s),l=o.value}catch(c){return void n(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function h(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){p(r,a,i,s,o,"next",t)}function o(t){p(r,a,i,s,o,"throw",t)}s(void 0)}))}}var f={name:"WebsiteHeader",components:{LogoImage:d["default"]},props:{transparent:{type:Boolean,default:!1}},data:function(){return{isScrolled:!1,mobileMenuOpen:!1,menuItems:[],userInfo:{},isLoggedIn:!1,isAdmin:!1}},computed:{isTransparent:function(){return this.transparent&&!this.isScrolled}},mounted:function(){var t=h(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadMenuData();case 2:return t.next=4,this.checkUserStatus();case 4:this.initScrollListener(),this.initNavbarAnimations();case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll)},methods:{loadMenuData:function(){var t=h(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{this.menuItems=[{name:"首页",path:"/home",icon:"home"},{name:"插件中心",path:"/market",icon:"shop"},{name:"客户案例",path:"",icon:"trophy"},{name:"教程中心",path:"",icon:"book"},{name:"签到奖励",path:"",icon:"gift"},{name:"订阅会员",path:"/membership",icon:"crown"},{name:"邀请奖励",path:"/affiliate",icon:"team"},{name:"个人中心",path:"/usercenter",icon:"user"}]}catch(e){this.menuItems=[{name:"首页",path:"/",icon:"home"},{name:"插件中心",path:"/market",icon:"shop"},{name:"个人中心",path:"/usercenter",icon:"user"}]}case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),initScrollListener:function(){window.addEventListener("scroll",this.handleScroll)},handleScroll:function(){this.isScrolled=window.scrollY>50},toggleMobileMenu:function(){this.mobileMenuOpen=!this.mobileMenuOpen},closeMobileMenu:function(){this.mobileMenuOpen=!1},handleDevelopingClick:function(t){this.$message.info("".concat(t,"功能正在开发中，敬请期待！"),3)},handleMobileMenuClick:function(t){t.path&&""!==t.path?this.$router.push(t.path):this.$message.info("".concat(t.name,"功能正在开发中，敬请期待！"),3),this.closeMobileMenu()},goHome:function(){this.$router.push("/")},handleLogin:function(){this.$router.push("/login")},checkUserStatus:function(){var t=h(s.a.mark((function t(){var e,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e=u["default"].ls.get(l["a"]),e){t.next=6;break}return this.isLoggedIn=!1,this.isAdmin=!1,t.abrupt("return");case 6:return this.isLoggedIn=!0,t.next=9,Object(c["b"])();case 9:return n=t.sent,t.next=12,Object(c["c"])();case 12:this.isAdmin=t.sent,this.userInfo={username:this.$store.getters.username||"用户",role:n},t.next=22;break;case 17:t.prev=17,t.t0=t["catch"](0),this.isLoggedIn=!1,this.isAdmin=!1;case 22:case"end":return t.stop()}}),t,this,[[0,17]])})));function e(){return t.apply(this,arguments)}return e}(),goToAdmin:function(){this.$router.push("/dashboard/analysis"),this.closeMobileMenu()},initNavbarAnimations:function(){var t=this;this.$nextTick((function(){var e=[t.$refs.navBrand,t.$refs.navMenu,t.$refs.navActions].filter((function(t){return t}));e.length>0&&o["a"].from(e,{duration:.4,y:-20,opacity:0,ease:"power2.out",stagger:.05})}))}}},m=f,g=(n("7c57"),n("2877")),v=Object(g["a"])(m,a,i,!1,null,"46809eaa",null);e["default"]=v.exports},cd1e:function(t,e,n){"use strict";var a=n("a22d"),i=n.n(a);i.a},d3b9:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"smart-not-found"},[t.loading?n("div",{staticClass:"loading-container"},[n("div",{staticClass:"loading-content"},[n("a-spin",{attrs:{size:"large"}}),n("p",{staticClass:"loading-text"},[t._v("正在检查页面权限...")])],1)]):n("div",[n("WebsiteNotFound")],1)])},i=[],r=n("a34a"),s=n.n(r),o=n("7862"),l=n("9fb0"),c=n("cfda"),u=n("ca00"),d=n("2b0e");function p(t,e,n,a,i,r,s){try{var o=t[r](s),l=o.value}catch(c){return void n(c)}o.done?e(l):Promise.resolve(l).then(a,i)}function h(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){p(r,a,i,s,o,"next",t)}function o(t){p(r,a,i,s,o,"throw",t)}s(void 0)}))}}var f={name:"SmartNotFound",components:{WebsiteNotFound:o["default"]},data:function(){return{loading:!0}},mounted:function(){var t=h(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=5,this.checkAndHandleRoute();case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{checkAndHandleRoute:function(){var t=h(s.a.mark((function t(){var e,n,a,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e=!!d["default"].ls.get(l["a"]),e){t.next=7;break}return this.loading=!1,t.abrupt("return");case 7:return t.next=9,Object(c["b"])();case 9:return t.sent,t.next=12,Object(c["c"])();case 12:if(n=t.sent,n){t.next=19;break}return this.loading=!1,t.abrupt("return");case 19:if(a=this.$route.path,i=this.isLikelyBackendPage(a),i){t.next=26;break}return this.loading=!1,t.abrupt("return");case 26:return t.next=29,this.loadDynamicRoutesAndRedirect(a);case 29:t.next=35;break;case 31:t.prev=31,t.t0=t["catch"](0),this.loading=!1;case 35:case"end":return t.stop()}}),t,this,[[0,31]])})));function e(){return t.apply(this,arguments)}return e}(),isLikelyBackendPage:function(t){var e=["/views/","/dashboard/","/system/","/aigcview/","/cjsc","/spjc","/sensitive-word","/usercenter/withdrawal","/online/","/jmreport/","/bigscreen/","/desform/","/act/","/plug-in/","/generic/","/eoa/","/joa/","/isystem","/modules/","/examples/","/jeecg/"];return e.some((function(e){return t.startsWith(e)}))},loadDynamicRoutesAndRedirect:function(){var t=h(s.a.mark((function t(e){var n,a,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!(this.$store.getters.permissionList.length>0)){t.next=6;break}return this.$router.replace(e),t.abrupt("return");case 6:return t.next=8,this.$store.dispatch("GetPermissionList");case 8:if(n=t.sent,a=n.result.menu,a&&0!==a.length){t.next=15;break}return this.loading=!1,t.abrupt("return");case 15:return i=Object(u["e"])(a),t.next=19,this.$store.dispatch("UpdateAppRouter",{constRoutes:i});case 19:this.$router.addRoutes(this.$store.getters.addRouters),this.$router.replace(e),t.next=28;break;case 24:t.prev=24,t.t0=t["catch"](0),this.loading=!1;case 28:case"end":return t.stop()}}),t,this,[[0,24]])})));function e(e){return t.apply(this,arguments)}return e}()}},m=f,g=(n("4047"),n("2877")),v=Object(g["a"])(m,a,i,!1,null,"063cf8f4",null);e["default"]=v.exports},df7c:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"website-page"},[n("WebsiteHeader"),n("main",{staticClass:"page-content"},[t._t("default")],2),n("WebsiteFooter"),n("QuantumJump")],1)},i=[],r=n("ccb3"),s=n("35b1"),o=n("bd9e9"),l={name:"WebsitePage",components:{WebsiteHeader:r["default"],WebsiteFooter:s["default"],QuantumJump:o["default"]}},c=l,u=(n("82be"),n("2877")),d=Object(u["a"])(c,a,i,!1,null,"e1cfea72",null);e["default"]=d.exports},e58a:function(t,e,n){"use strict";var a=n("1397"),i=n.n(a);i.a},e659:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 50px 32px 0"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{attrs:{forceFit:!0,height:t.height,data:t.data,scale:t.scale,padding:t.padding,onClick:t.handleClick}},[n("v-tooltip"),n("v-legend"),n("v-axis"),n("v-bar",{attrs:{position:"type*bar"}}),n("v-line",{attrs:{position:"type*line",color:"#2fc25b",size:3}})],1)],1)},i=[],r=n("c917"),s={name:"BarAndLine",mixins:[r["a"]],props:{title:{type:String,default:""},dataSource:{type:Array,default:function(){return[{type:"10:10",bar:200,line:1e3},{type:"10:15",bar:600,line:1e3},{type:"10:20",bar:200,line:1e3},{type:"10:25",bar:900,line:1e3},{type:"10:30",bar:200,line:1e3},{type:"10:35",bar:200,line:1e3},{type:"10:40",bar:100,line:1e3}]}},height:{type:Number,default:400}},data:function(){return{padding:{top:50,right:50,bottom:100,left:50},scale:[{dataKey:"bar",min:0},{dataKey:"line",min:0}]}},computed:{data:function(){return this.dataSource}}},o=s,l=n("2877"),c=Object(l["a"])(o,a,i,!1,null,null,null);e["default"]=c.exports},e8c4:function(t,e,n){"use strict";var a=n("372e"),i=n("c832"),r=n.n(i);function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}e["a"]={data:function(){return{needTotalList:[],selectedRows:[],selectedRowKeys:[],localLoading:!1,localDataSource:[],localPagination:Object.assign({},a["a"].props.pagination)}},props:Object.assign({},a["a"].props,{rowKey:{type:[String,Function],default:"id"},data:{type:Function,required:!0},pageNum:{type:Number,default:1},pageSize:{type:Number,default:10},showSizeChanger:{type:Boolean,default:!0},showAlertInfo:{type:Boolean,default:!1},showPagination:{default:"auto"}}),watch:{"localPagination.current":function(t){this.$router.push({name:this.$route.name,params:Object.assign({},this.$route.params,{pageNo:t})})},pageNum:function(t){Object.assign(this.localPagination,{current:t})},pageSize:function(t){Object.assign(this.localPagination,{pageSize:t})},showSizeChanger:function(t){Object.assign(this.localPagination,{showSizeChanger:t})}},created:function(){this.localPagination=["auto",!0].includes(this.showPagination)&&Object.assign({},this.localPagination,{current:this.pageNum,pageSize:this.pageSize,showSizeChanger:this.showSizeChanger}),this.needTotalList=this.initTotalList(this.columns),this.loadData()},methods:{refresh:function(){this.loadData()},loadData:function(t,e,n){var a=this;this.localLoading=!0;var i=this.data(Object.assign({pageNo:t&&t.current||this.localPagination.current,pageSize:t&&t.pageSize||this.localPagination.pageSize},n&&n.field&&{sortField:n.field}||{},n&&n.order&&{sortOrder:n.order}||{},o({},e)));i instanceof Promise&&i.then((function(e){a.localPagination=Object.assign({},a.localPagination,{current:e.pageNo,total:e.totalCount,showSizeChanger:a.showSizeChanger,pageSize:t&&t.pageSize||a.localPagination.pageSize}),(!a.showPagination||!e.totalCount&&"auto"===a.showPagination)&&(a.localPagination=!1),a.localDataSource=e.data,a.localLoading=!1}))},initTotalList:function(t){var e=[];return t&&t instanceof Array&&t.forEach((function(t){t.needTotal&&e.push(o(o({},t),{},{total:0}))})),e},updateSelect:function(t,e){this.selectedRowKeys=t,this.selectedRows=e;var n=this.needTotalList;this.needTotalList=n.map((function(t){return o(o({},t),{},{total:e.reduce((function(e,n){var a=e+r()(n,t.dataIndex);return isNaN(a)?0:a}),0)})}))},updateEdit:function(){this.selectedRows=[]},onClearSelected:function(){this.selectedRowKeys=[],this.selectedRows=[],this.updateSelect([],[]),this.$emit("clearAll")},renderMsg:function(t){var e=this,n=[];return n.push(t("span",{style:{marginRight:"12px"}},["已选择 ",t("a",{style:{fontWeight:600}},this.selectedRows.length)])),this.needTotalList.map((function(e){n.push(t("span",{style:{marginRight:"12px"}},["".concat(e.title,"总计 "),t("a",{style:{fontWeight:600}},"".concat(e.customRender?e.customRender(e.total):e.total))]))})),n.push(t("a",{style:{marginLeft:"24px"},on:{click:e.onClearSelected}},"清空")),n},renderAlert:function(t){return t("span",{slot:"message"},this.renderMsg(t))}},render:function(t){var e=this,n={},i=Object.keys(this.$data);return Object.keys(a["a"].props).forEach((function(t){var a="local".concat(t.substring(0,1).toUpperCase()).concat(t.substring(1));return i.includes(a)?n[t]=e[a]:n[t]=e[t]})),this.showAlertInfo?(n.rowSelection={selectedRowKeys:this.selectedRowKeys,onChange:function(t,n){e.updateSelect(t,n),e.$emit("onSelect",{selectedRowKeys:t,selectedRows:n})}},t("div",{},[t("a-alert",{style:{marginBottom:"16px"},props:{type:"info",showIcon:!0}},[e.renderAlert(t)]),t("a-table",{tag:"component",attrs:n,on:{change:e.loadData},scopedSlots:this.$scopedSlots},this.$slots.default)])):t("a-table",{tag:"component",attrs:n,on:{change:e.loadData},scopedSlots:this.$scopedSlots},this.$slots.default)}}},e98a:function(t,e,n){},ea2b:function(t,e,n){},edd9:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0 0 32px 32px"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{attrs:{forceFit:!0,height:t.height,data:t.dataSource,scale:t.scale,padding:t.padding}},[n("v-tooltip"),n("v-axis"),n("v-bar",{attrs:{position:"x*y"}})],1)],1)},i=[],r=n("ca00"),s={name:"Bar",props:{dataSource:{type:Array,required:!0},yaxisText:{type:String,default:"y"},title:{type:String,default:""},height:{type:Number,default:254}},data:function(){return{padding:["auto","auto","40","50"]}},computed:{scale:function(){return[{dataKey:"y",alias:this.yaxisText}]}},mounted:function(){Object(r["p"])()}},o=s,l=n("2877"),c=Object(l["a"])(o,a,i,!1,null,null,null);e["default"]=c.exports},ee3b:function(t,e,n){"use strict";var a=n("c4be"),i=n.n(a);i.a},f30a:function(t,e,n){},f462:function(t,e,n){},f552:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:{padding:"0"}},[n("h4",{style:{marginBottom:"20px"}},[t._v(t._s(t.title))]),n("v-chart",{ref:"chart",attrs:{forceFit:!0,height:t.height,data:t.dataSource,scale:t.scale}},[n("v-tooltip",{attrs:{shared:!1}}),n("v-axis"),n("v-line",{attrs:{position:"x*y",size:t.lineSize,color:t.lineColor}}),n("v-area",{attrs:{position:"x*y",color:t.color}})],1)],1)},i=[],r=n("ca00"),s={name:"AreaChartTy",props:{dataSource:{type:Array,required:!0},title:{type:String,default:""},x:{type:String,default:"x"},y:{type:String,default:"y"},min:{type:Number,default:0},max:{type:Number,default:null},height:{type:Number,default:254},lineSize:{type:Number,default:2},color:{type:String,default:""},lineColor:{type:String,default:""}},computed:{scale:function(){return[{dataKey:"x",title:this.x,alias:this.x},{dataKey:"y",title:this.y,alias:this.y,min:this.min,max:this.max}]}},mounted:function(){Object(r["p"])()}},o=s,l=(n("8d7f"),n("2877")),c=Object(l["a"])(o,a,i,!1,null,"6d58f736",null);e["default"]=c.exports},f8f4:function(t,e,n){},fe28:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.syncToApp&&t.syncToLocal?n("a-dropdown",[n("a-button",{attrs:{type:"primary",icon:"sync"}},[t._v("同步"+t._s(t.name))]),n("a-menu",{attrs:{slot:"overlay"},on:{click:t.handleMenuClick},slot:"overlay"},[t.syncToApp?n("a-menu-item",{key:"to-app"},[t._v("同步到"+t._s(t.name))]):t._e(),t.syncToLocal?n("a-menu-item",{key:"to-local"},[t._v("同步到本地")]):t._e()],1)],1):t.syncToApp?n("a-button",{attrs:{type:"primary",icon:"sync"},on:{click:function(e){return t.handleMenuClick({key:"to-app"})}}},[t._v("同步"+t._s(t.name))]):n("a-button",{attrs:{type:"primary",icon:"sync"},on:{click:function(e){return t.handleMenuClick({key:"to-local"})}}},[t._v("同步"+t._s(t.name)+"到本地")])},i=[],r={name:"JThirdAppDropdown",props:{type:String,name:String,syncToApp:Boolean,syncToLocal:Boolean},methods:{handleMenuClick:function(t){this.$emit(t.key,{type:this.type})}}},s=r,o=n("2877"),l=Object(o["a"])(s,a,i,!1,null,"1b7992e0",null);e["default"]=l.exports}}]);