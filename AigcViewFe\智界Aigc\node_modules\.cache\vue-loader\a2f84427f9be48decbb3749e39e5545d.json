{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue?vue&type=template&id=68052031&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\membership\\Membership.vue", "mtime": 1753812109664}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"WebsitePage\", [\n    _c(\"div\", { staticClass: \"membership-container\" }, [\n      _c(\"div\", { staticClass: \"simple-header\" }, [\n        _c(\"h1\", { staticClass: \"simple-title\" }, [_vm._v(\"订阅会员\")]),\n        _c(\"p\", { staticClass: \"simple-subtitle\" }, [\n          _vm._v(\"成为会员享受更多特权，解锁高级功能\")\n        ])\n      ]),\n      _c(\"section\", { staticClass: \"plans-section\" }, [\n        _c(\"div\", { staticClass: \"container\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"plans-grid\" },\n            _vm._l(_vm.plans, function(plan) {\n              return _c(\n                \"div\",\n                {\n                  key: plan.id,\n                  staticClass: \"plan-card\",\n                  class: { featured: plan.featured }\n                },\n                [\n                  plan.featured\n                    ? _c(\"div\", { staticClass: \"plan-badge\" }, [_vm._v(\"推荐\")])\n                    : _vm._e(),\n                  _c(\"div\", { staticClass: \"plan-header\" }, [\n                    _c(\"h3\", { staticClass: \"plan-name\" }, [\n                      _vm._v(_vm._s(plan.name))\n                    ]),\n                    _c(\"div\", { staticClass: \"plan-price\" }, [\n                      plan.originalPrice\n                        ? _c(\"div\", { staticClass: \"original-price\" }, [\n                            _c(\"span\", { staticClass: \"original-price-text\" }, [\n                              _vm._v(\"原价：¥\" + _vm._s(plan.originalPrice))\n                            ]),\n                            _c(\"span\", { staticClass: \"discount-badge\" }, [\n                              _vm._v(_vm._s(plan.discountText))\n                            ])\n                          ])\n                        : _vm._e(),\n                      _c(\"div\", { staticClass: \"current-price\" }, [\n                        _c(\"span\", { staticClass: \"price-symbol\" }, [\n                          _vm._v(\"¥\")\n                        ]),\n                        _c(\"span\", { staticClass: \"price-amount\" }, [\n                          _vm._v(_vm._s(plan.price))\n                        ]),\n                        _c(\"span\", { staticClass: \"price-period\" }, [\n                          _vm._v(\"/\" + _vm._s(plan.period))\n                        ])\n                      ]),\n                      plan.saveAmount\n                        ? _c(\"div\", { staticClass: \"save-amount\" }, [\n                            _vm._v(\n                              \"\\n                  立省¥\" +\n                                _vm._s(plan.saveAmount) +\n                                \"\\n                \"\n                            )\n                          ])\n                        : _vm._e()\n                    ]),\n                    _c(\"p\", { staticClass: \"plan-description\" }, [\n                      _vm._v(_vm._s(plan.description))\n                    ])\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"plan-features\" },\n                    _vm._l(plan.features, function(feature) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: feature.text || feature,\n                          staticClass: \"feature-item\",\n                          class: {\n                            \"feature-disabled\": feature.disabled,\n                            \"feature-exclusive\": feature.exclusive\n                          }\n                        },\n                        [\n                          _c(\"a-icon\", {\n                            class: {\n                              \"icon-disabled\": feature.disabled,\n                              \"icon-exclusive\": feature.exclusive\n                            },\n                            attrs: {\n                              type: feature.disabled ? \"close\" : \"check\"\n                            }\n                          }),\n                          _c(\"span\", {\n                            staticClass: \"feature-text\",\n                            domProps: {\n                              innerHTML: _vm._s(feature.text || feature)\n                            }\n                          }),\n                          feature.exclusive\n                            ? _c(\"span\", { staticClass: \"exclusive-badge\" }, [\n                                _vm._v(\"专属\")\n                              ])\n                            : _vm._e(),\n                          feature.disabled\n                            ? _c(\"span\", { staticClass: \"disabled-text\" }, [\n                                _vm._v(\"（SVIP专享）\")\n                              ])\n                            : _vm._e()\n                        ],\n                        1\n                      )\n                    }),\n                    0\n                  ),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"btn-subscribe\",\n                      class: { featured: plan.featured },\n                      on: {\n                        click: function($event) {\n                          return _vm.handleSubscribe(plan)\n                        }\n                      }\n                    },\n                    [\n                      _vm._v(\n                        \"\\n              \" +\n                          _vm._s(plan.buttonText) +\n                          \"\\n            \"\n                      )\n                    ]\n                  )\n                ]\n              )\n            }),\n            0\n          )\n        ])\n      ])\n    ])\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}