<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper">

    <!-- 根据用户ID查询用户扩展信息 -->
    <select id="getByUserId" parameterType="String" resultType="org.jeecg.modules.demo.userprofile.entity.AicgUserProfile">
        SELECT * FROM aicg_user_profile WHERE user_id = #{userId}
    </select>
    
    <!-- 更新用户余额 -->
    <update id="updateBalance">
        UPDATE aicg_user_profile 
        SET account_balance = #{newBalance}, 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE user_id = #{userId}
    </update>
    
    <!-- 增加用户余额 -->
    <update id="addBalance">
        UPDATE aicg_user_profile 
        SET account_balance = account_balance + #{amount}, 
            total_recharge = total_recharge + #{amount}, 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE user_id = #{userId}
    </update>
    
    <!-- 扣减用户余额 -->
    <update id="deductBalance">
        UPDATE aicg_user_profile 
        SET account_balance = account_balance - #{amount}, 
            total_consumption = total_consumption + #{amount}, 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE user_id = #{userId} 
        AND account_balance >= #{amount}
    </update>
    
    <!-- 重新生成API密钥 -->
    <update id="updateApiKey">
        UPDATE aicg_user_profile 
        SET api_key = #{apiKey}, 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE user_id = #{userId}
    </update>
    
    <!-- 查询用户余额统计信息 -->
    <select id="getUserBalanceStats" parameterType="String" resultType="java.util.Map">
        SELECT
            account_balance,
            total_consumption,
            total_recharge,
            member_expire_time
        FROM aicg_user_profile
        WHERE user_id = #{userId}
    </select>
    
    <!-- 批量初始化用户扩展信息 -->
    <insert id="batchInitUserProfiles" parameterType="java.util.List">
        INSERT INTO aicg_user_profile (
            id, user_id, nickname, account_balance, api_key,
            total_consumption, total_recharge, status,
            create_by, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.userId}, #{item.nickname}, #{item.accountBalance}, #{item.apiKey},
                #{item.totalConsumption}, #{item.totalRecharge}, #{item.status},
                #{item.createBy}, #{item.createTime}
            )
        </foreach>
    </insert>

</mapper>
