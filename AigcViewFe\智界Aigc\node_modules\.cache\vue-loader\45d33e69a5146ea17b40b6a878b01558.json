{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue?vue&type=template&id=7dc25498&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\QuickRecharge.vue", "mtime": 1753822761569}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"quick-recharge-card\" },\n    [\n      _c(\"div\", { staticClass: \"balance-section\" }, [\n        _c(\"div\", { staticClass: \"user-info\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"user-avatar\" },\n            [\n              _c(\n                \"a-avatar\",\n                {\n                  style: { backgroundColor: \"#87d068\" },\n                  attrs: { size: 60, src: _vm.avatarUrl, icon: \"user\" }\n                },\n                [\n                  _vm._v(\n                    \"\\n          \" +\n                      _vm._s(\n                        _vm.userNickname ? _vm.userNickname.charAt(0) : \"U\"\n                      ) +\n                      \"\\n        \"\n                  )\n                ]\n              )\n            ],\n            1\n          ),\n          _c(\"div\", { staticClass: \"user-details\" }, [\n            _c(\"div\", { staticClass: \"user-name\" }, [\n              _vm._v(\n                \"\\n          \" +\n                  _vm._s(_vm.userNickname || \"智界用户\") +\n                  \"\\n          \"\n              ),\n              _c(\"span\", { staticClass: \"user-role\" }, [\n                _vm._v(_vm._s(_vm.getUserRoleDisplayName()))\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"balance-info\" }, [\n              _c(\"span\", { staticClass: \"balance-label\" }, [\n                _vm._v(\"账户余额\")\n              ]),\n              _c(\"span\", { staticClass: \"balance-amount\" }, [\n                _vm._v(\"¥\" + _vm._s(_vm.formatBalance(_vm.userBalance)))\n              ])\n            ])\n          ])\n        ]),\n        _c(\"div\", { staticClass: \"balance-actions\" }, [\n          _c(\n            \"button\",\n            {\n              staticClass: \"recharge-btn\",\n              on: {\n                click: function($event) {\n                  _vm.showRechargeModal = true\n                }\n              }\n            },\n            [\n              _c(\"i\", { staticClass: \"anticon anticon-plus-circle\" }),\n              _vm._v(\"\\n        快速充值\\n      \")\n            ]\n          )\n        ])\n      ]),\n      _c(\n        \"a-modal\",\n        {\n          attrs: {\n            title: \"账户充值\",\n            visible: _vm.showRechargeModal,\n            footer: null,\n            width: \"500px\"\n          },\n          on: {\n            cancel: function($event) {\n              _vm.showRechargeModal = false\n            }\n          }\n        },\n        [\n          _c(\"div\", { staticClass: \"recharge-modal-content\" }, [\n            _c(\"div\", { staticClass: \"recharge-options\" }, [\n              _c(\"h4\", [_vm._v(\"选择充值金额\")]),\n              _c(\n                \"div\",\n                { staticClass: \"options-grid\" },\n                _vm._l(_vm.rechargeOptions, function(option) {\n                  return _c(\n                    \"div\",\n                    {\n                      key: option.amount,\n                      staticClass: \"recharge-option\",\n                      class: { selected: _vm.selectedAmount === option.amount },\n                      on: {\n                        click: function($event) {\n                          return _vm.selectRechargeAmount(option.amount)\n                        }\n                      }\n                    },\n                    [\n                      _c(\"div\", { staticClass: \"option-amount\" }, [\n                        _vm._v(\"¥\" + _vm._s(option.amount))\n                      ]),\n                      _c(\"div\", { staticClass: \"option-label\" }, [\n                        _vm._v(_vm._s(option.label))\n                      ])\n                    ]\n                  )\n                }),\n                0\n              )\n            ]),\n            _c(\"div\", { staticClass: \"custom-amount\" }, [\n              _c(\"h4\", [_vm._v(\"自定义金额\")]),\n              _c(\n                \"div\",\n                { staticClass: \"custom-input\" },\n                [\n                  _c(\"a-input-number\", {\n                    staticStyle: { width: \"200px\" },\n                    attrs: {\n                      min: 0.01,\n                      max: 10000,\n                      step: 0.01,\n                      placeholder: \"最低0.01元\",\n                      size: \"large\"\n                    },\n                    model: {\n                      value: _vm.customAmount,\n                      callback: function($$v) {\n                        _vm.customAmount = $$v\n                      },\n                      expression: \"customAmount\"\n                    }\n                  }),\n                  _c(\"span\", { staticClass: \"currency\" }, [_vm._v(\"元\")])\n                ],\n                1\n              )\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"payment-methods\" },\n              [\n                _c(\"h4\", [_vm._v(\"支付方式\")]),\n                _c(\n                  \"a-radio-group\",\n                  {\n                    attrs: { size: \"large\" },\n                    model: {\n                      value: _vm.selectedPaymentMethod,\n                      callback: function($$v) {\n                        _vm.selectedPaymentMethod = $$v\n                      },\n                      expression: \"selectedPaymentMethod\"\n                    }\n                  },\n                  [\n                    _c(\"a-radio-button\", { attrs: { value: \"alipay-qr\" } }, [\n                      _c(\"i\", { staticClass: \"anticon anticon-qrcode\" }),\n                      _vm._v(\"\\n            支付宝扫码\\n          \")\n                    ]),\n                    _c(\"a-radio-button\", { attrs: { value: \"alipay-page\" } }, [\n                      _c(\"i\", { staticClass: \"anticon anticon-alipay\" }),\n                      _vm._v(\"\\n            支付宝网页\\n          \")\n                    ])\n                  ],\n                  1\n                )\n              ],\n              1\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"recharge-actions\" },\n              [\n                _c(\"div\", { staticClass: \"amount-summary\" }, [\n                  _c(\"span\", [_vm._v(\"充值金额：\")]),\n                  _c(\"span\", { staticClass: \"final-amount\" }, [\n                    _vm._v(\"¥\" + _vm._s(_vm.finalRechargeAmount))\n                  ])\n                ]),\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: {\n                      type: \"primary\",\n                      size: \"large\",\n                      loading: _vm.rechargeLoading,\n                      disabled:\n                        !_vm.finalRechargeAmount ||\n                        _vm.finalRechargeAmount < 0.01\n                    },\n                    on: { click: _vm.handleRecharge }\n                  },\n                  [_vm._v(\"\\n          确认充值\\n        \")]\n                )\n              ],\n              1\n            )\n          ])\n        ]\n      ),\n      _c(\n        \"a-modal\",\n        {\n          attrs: {\n            title: \"扫码支付\",\n            visible: _vm.showQrModal,\n            footer: null,\n            width: \"400px\"\n          },\n          on: { cancel: _vm.closeQrModal }\n        },\n        [\n          _c(\"div\", { staticClass: \"qr-payment-content\" }, [\n            _c(\"div\", { staticClass: \"qr-code-container\" }, [\n              _vm.qrCodeUrl\n                ? _c(\"div\", { staticClass: \"qr-code\" }, [\n                    _c(\"img\", {\n                      attrs: { src: _vm.qrCodeUrl, alt: \"支付二维码\" }\n                    })\n                  ])\n                : _c(\n                    \"div\",\n                    { staticClass: \"qr-loading\" },\n                    [\n                      _c(\"a-spin\", { attrs: { size: \"large\" } }),\n                      _c(\"p\", [_vm._v(\"正在生成二维码...\")])\n                    ],\n                    1\n                  )\n            ]),\n            _c(\"div\", { staticClass: \"qr-info\" }, [\n              _c(\"p\", { staticClass: \"qr-amount\" }, [\n                _vm._v(\"支付金额：¥\" + _vm._s(_vm.currentOrderAmount))\n              ]),\n              _c(\"p\", { staticClass: \"qr-tip\" }, [\n                _vm._v(\"请使用支付宝扫码支付\")\n              ])\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"qr-status\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { loading: _vm.checkingStatus },\n                    on: { click: _vm.checkPaymentStatus }\n                  },\n                  [_vm._v(\"\\n          检查支付状态\\n        \")]\n                )\n              ],\n              1\n            )\n          ])\n        ]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}