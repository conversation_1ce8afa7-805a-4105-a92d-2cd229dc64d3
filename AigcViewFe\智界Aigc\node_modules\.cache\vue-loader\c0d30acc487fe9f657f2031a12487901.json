{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753808450477}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport Sidebar from './components/Sidebar.vue'\nimport FloatingNotifications from './components/FloatingNotifications.vue'\nimport Overview from './views/Overview.vue'\nimport Profile from './views/Profile.vue'\nimport Credits from './views/Credits.vue'\nimport Orders from './views/Orders.vue'\nimport Usage from './views/Usage.vue'\n// 🚫 临时注释掉会员服务和推荐奖励组件\n// import Membership from './views/Membership.vue'\n// import Referral from './views/Referral.vue'\nimport Notifications from './pages/Notifications.vue'\nimport { getUserFullInfo } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { usercenterAnimations } from '@/animations/gsap/pages/usercenterAnimations.js'\nimport Vue from 'vue'\n\nexport default {\n  name: 'UserCenter',\n  components: {\n    WebsitePage,\n    Sidebar,\n    FloatingNotifications,\n    Overview,\n    Profile,\n    Credits,\n    Orders,\n    Usage,\n    // 🚫 临时注释掉会员服务和推荐奖励组件\n    // Membership,\n    // Referral,\n    Notifications\n  },\n  data() {\n    return {\n      loading: true,\n      currentPage: 'overview',\n      userInfo: {\n        nickname: '',\n        email: '',\n        avatar: '',\n        phone: '',\n        accountBalance: 0,\n        currentRole: '普通用户',\n        totalConsumption: 0,\n        totalRecharge: 0,\n        memberExpireTime: null,\n        apiKey: '',\n        createTime: null,\n        // 🔑 关键：添加密码修改状态字段\n        passwordChanged: 0\n      },\n      pageMap: {\n        overview: '概览',\n        profile: '账户设置',\n        credits: '账户管理',\n        orders: '订单记录',\n        usage: '使用记录',\n        notifications: '系统通知'\n        // 🚫 临时注释掉会员服务和推荐奖励页面\n        // membership: '会员服务',\n        // referral: '推荐奖励'\n      }\n    }\n  },\n  computed: {\n    currentPageTitle() {\n      return this.pageMap[this.currentPage] || ''\n    }\n  },\n  async mounted() {\n    // 检查登录状态\n    if (!this.checkLoginStatus()) {\n      return\n    }\n\n    await this.loadUserInfo()\n    this.initAnimations()\n  },\n  methods: {\n    /**\n     * 检查登录状态 - 使用与路由守卫相同的TOKEN检查方法\n     */\n    checkLoginStatus() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        console.log('🔍 UserCenter: 未登录，重定向到登录页')\n        this.$message.warning('请先登录')\n        this.$router.push({\n          path: '/login',\n          query: { redirect: this.$route.fullPath }\n        })\n        return false\n      }\n      console.log('🔍 UserCenter: 已登录，TOKEN存在')\n      return true\n    },\n\n    async loadUserInfo() {\n      try {\n        this.loading = true\n\n        const response = await getUserFullInfo()\n        console.log('🔍 UserCenter: 完整的响应对象:', response)\n        console.log('🔍 UserCenter: response.success:', response.success)\n        console.log('🔍 UserCenter: response.data:', response.data)\n        console.log('🔍 UserCenter: response.result:', response.result)\n        console.log('🔍 UserCenter: response.message:', response.message)\n\n        if (response.success) {\n          // 使用正确的字段：response.result 而不是 response.data\n          const rawData = response.result || response.data || {}\n          console.log('🔍 UserCenter: 后端返回的原始数据:', rawData)\n\n          // 字段名映射：后端下划线 -> 前端驼峰命名\n          const mappedData = {\n            nickname: rawData.nickname || '',\n            email: rawData.email || '',\n            avatar: rawData.avatar || '',\n            phone: rawData.phone || '',\n            accountBalance: rawData.account_balance || 0,\n            totalConsumption: rawData.total_consumption || 0,\n            totalRecharge: rawData.total_recharge || 0,\n            currentRole: rawData.current_role || '普通用户',\n            apiKey: rawData.api_key || '',\n            createTime: rawData.user_create_time || null,\n            username: rawData.username || '',\n            realname: rawData.realname || '',\n            // 🔑 关键：添加密码修改状态字段\n            passwordChanged: rawData.password_changed || 0\n          }\n\n          console.log('🔍 UserCenter: 映射后的数据:', mappedData)\n\n          // 使用Object.assign确保响应式更新\n          Object.assign(this.userInfo, mappedData)\n\n          console.log('🔍 UserCenter: 最终的userInfo:', this.userInfo)\n\n          // 强制触发视图更新\n          this.$forceUpdate()\n        } else {\n          console.error('🔍 UserCenter: 获取用户信息失败')\n          console.error('🔍 UserCenter: response.code:', response.code)\n          console.error('🔍 UserCenter: response.message:', response.message)\n          console.error('🔍 UserCenter: 完整响应:', response)\n\n          // 检查是否是认证失败\n          if (response.code === 401 || (response.message && response.message.includes('Token')) || (response.message && response.message.includes('登录'))) {\n            this.$message.warning('登录已过期，请重新登录')\n            this.$router.push({\n              path: '/login',\n              query: { redirect: this.$route.fullPath }\n            })\n            return\n          }\n          this.$message.error(`获取用户信息失败: ${response.message || '未知错误'}`)\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        // 检查是否是认证相关错误\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n          this.$router.push({\n            path: '/login',\n            query: { redirect: this.$route.fullPath }\n          })\n          return\n        }\n        this.$message.error('加载用户信息失败，请刷新重试')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 刷新用户信息的方法（供子组件调用）\n    async getUserInfo() {\n      console.log('🔍 UserCenter: 收到刷新用户信息请求')\n      await this.loadUserInfo()\n    },\n\n    initAnimations() {\n      this.$nextTick(() => {\n        // 初始化GSAP动画\n        if (this.$animationManager) {\n          usercenterAnimations.init(this.$animationManager)\n        }\n      })\n    },\n\n    handleMenuChange(page) {\n      if (this.currentPage !== page) {\n        // 页面切换动画\n        const fromPage = `.page-${this.currentPage}`\n        const toPage = `.page-${page}`\n\n        if (this.$animationManager) {\n          usercenterAnimations.switchPage(fromPage, toPage)\n        }\n\n        this.currentPage = page\n\n        // 更新URL（可选）\n        this.$router.replace({\n          path: '/usercenter',\n          query: { page }\n        })\n      }\n    },\n\n    handleNavigate(page) {\n      this.handleMenuChange(page)\n    },\n\n    handleSidebarAction(action) {\n      switch (action) {\n        case 'recharge':\n          this.handleMenuChange('credits')\n          break\n        // 🚫 临时注释掉升级会员功能\n        // case 'upgrade':\n        //   this.handleMenuChange('membership')\n        //   break\n        default:\n          console.log('未知操作:', action)\n      }\n    },\n\n    handleNavigateToNotifications() {\n      // 导航到系统通知页面\n      this.handleMenuChange('notifications')\n    },\n\n    handleNotificationUpdated() {\n      // 通知更新时，刷新悬浮通知组件的数据\n      if (this.$refs.floatingNotifications) {\n        this.$refs.floatingNotifications.loadNotifications()\n      }\n\n      // 同时通知Sidebar组件更新未读通知数量\n      if (this.$refs.sidebar) {\n        this.$refs.sidebar.loadUnreadNotificationCount()\n      }\n    },\n\n    handleAvatarUpdated(newAvatar) {\n      // 头像更新时，更新用户信息\n      console.log('🔍 UserCenter: 头像更新事件，新头像:', newAvatar)\n      this.userInfo.avatar = newAvatar\n      this.$forceUpdate()\n    },\n\n    handleInfoUpdated(updatedInfo) {\n      // 基本信息更新时，更新用户信息\n      console.log('🔍 UserCenter: 基本信息更新事件:', updatedInfo)\n      Object.assign(this.userInfo, updatedInfo)\n      this.$forceUpdate()\n    },\n\n    handleApiKeyUpdated(newApiKey) {\n      // API Key更新时，保存当前滚动位置\n      const currentScrollY = window.pageYOffset || document.documentElement.scrollTop\n      console.log('🔍 UserCenter: API Key更新事件，当前滚动位置:', currentScrollY)\n\n      // 使用Vue.set确保响应式更新\n      this.$set(this.userInfo, 'apiKey', newApiKey)\n\n      // 在下一个tick恢复滚动位置\n      this.$nextTick(() => {\n        window.scrollTo(0, currentScrollY)\n        console.log('🔍 UserCenter: 已恢复滚动位置到:', currentScrollY)\n      })\n    },\n\n    // 🔑 新增：密码修改处理\n    handlePasswordChanged() {\n      console.log('🔍 UserCenter: 收到密码修改事件')\n      if (this.userInfo) {\n        this.userInfo.passwordChanged = 1\n        console.log('🔍 UserCenter: passwordChanged已更新为1')\n      }\n    }\n  },\n\n  // 路由守卫：根据URL参数设置当前页面\n  beforeRouteEnter(to, _from, next) {\n    next(vm => {\n      const page = to.query.page\n      if (page && vm.pageMap[page]) {\n        vm.currentPage = page\n      }\n    })\n  },\n\n  beforeRouteUpdate(to, _from, next) {\n    const page = to.query.page\n    if (page && this.pageMap[page]) {\n      this.currentPage = page\n    }\n    next()\n  }\n}\n", null]}