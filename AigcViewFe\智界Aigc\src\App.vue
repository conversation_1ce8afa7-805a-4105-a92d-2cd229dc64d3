<template>
  <a-config-provider :locale="locale">
    <div id="app">
      <router-view/>
    </div>
  </a-config-provider>
</template>
<script>
  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
  import enquireScreen from '@/utils/device'
  import { handleReferralFromUrl } from '@/utils/referralUtils'

  export default {
    data () {
      return {
        locale: zhCN,
      }
    },
    created () {
      // 处理邀请链接参数
      this.handleReferralCode()
    },
    mounted () {
      let that = this
      enquireScreen(deviceType => {
        // tablet
        if (deviceType === 0) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile')
          that.$store.dispatch('setSidebar', false)
        }
        // mobile
        else if (deviceType === 1) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile')
          that.$store.dispatch('setSidebar', false)
        }
        else {
          that.$store.commit('TOGGLE_DEVICE', 'desktop')
          that.$store.dispatch('setSidebar', true)
        }

      })
    },
    watch: {
      // 监听路由变化，处理邀请链接
      '$route'(to, from) {
        this.handleReferralCode()
      }
    },
    methods: {
      // 处理邀请码
      handleReferralCode() {
        try {
          const currentUrl = window.location.href
          handleReferralFromUrl(currentUrl)
        } catch (error) {
          console.warn('🔗 处理邀请码失败:', error)
        }
      }
    }
  }
</script>
<style>
  #app {
    height: 100%;
  }
</style>