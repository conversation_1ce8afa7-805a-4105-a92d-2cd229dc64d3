{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=style&index=0&id=4569dbde&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753808450477}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.usercenter-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n}\n\n\n\n.container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* 主要内容区域 */\n.usercenter-main {\n  padding: 2rem 0;\n}\n\n.usercenter-layout {\n  display: grid;\n  grid-template-columns: 260px 1fr;\n  gap: 1.5rem;\n  align-items: flex-start;\n  /* 取消左边距，改为正常的两列布局 */\n  /* margin-left: 320px; */\n}\n\n.usercenter-content {\n  min-height: 600px;\n  position: relative;\n}\n\n/* 页面切换动画 */\n.usercenter-content > * {\n  opacity: 0.85;\n  animation: fadeInUp 0.6s ease-out forwards;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .usercenter-layout {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .usercenter-main {\n    padding: 1rem 0;\n  }\n\n  .usercenter-layout {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n\n\n", {"version": 3, "sources": ["UserCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkZA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "UserCenter.vue", "sourceRoot": "src/views/website/usercenter", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"usercenter-container\">\n\n\n      <!-- 主要内容区域 -->\n      <div class=\"usercenter-main\">\n        <div class=\"container\">\n          <div class=\"usercenter-layout\">\n            <!-- 侧边栏 -->\n            <Sidebar\n              ref=\"sidebar\"\n              :current-page=\"currentPage\"\n              :user-info=\"userInfo\"\n              @menu-change=\"handleMenuChange\"\n              @action=\"handleSidebarAction\"\n            />\n\n            <!-- 内容区域 -->\n            <div class=\"usercenter-content\">\n              <!-- 概览页面 -->\n              <Overview\n                v-if=\"currentPage === 'overview'\"\n                :key=\"'overview'\"\n                :user-info=\"userInfo\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 账户设置页面 -->\n              <Profile\n                v-else-if=\"currentPage === 'profile'\"\n                :key=\"'profile'\"\n                :user-info=\"userInfo\"\n                @navigate=\"handleNavigate\"\n                @avatar-updated=\"handleAvatarUpdated\"\n                @info-updated=\"handleInfoUpdated\"\n                @api-key-updated=\"handleApiKeyUpdated\"\n                @password-changed=\"handlePasswordChanged\"\n                @refresh-user-info=\"getUserInfo\"\n              />\n\n              <!-- 账户管理页面 -->\n              <Credits\n                v-else-if=\"currentPage === 'credits'\"\n                :key=\"'credits'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 订单记录页面 -->\n              <Orders\n                v-else-if=\"currentPage === 'orders'\"\n                :key=\"'orders'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 使用记录页面 -->\n              <Usage\n                v-else-if=\"currentPage === 'usage'\"\n                :key=\"'usage'\"\n                @navigate=\"handleNavigate\"\n              />\n\n              <!-- 🚫 临时注释掉会员服务和推荐奖励页面 -->\n              <!-- <Membership\n                v-else-if=\"currentPage === 'membership'\"\n                :key=\"'membership'\"\n                @navigate=\"handleNavigate\"\n              /> -->\n\n              <!-- <Referral\n                v-else-if=\"currentPage === 'referral'\"\n                :key=\"'referral'\"\n                @navigate=\"handleNavigate\"\n              /> -->\n\n              <!-- 系统通知页面 -->\n              <Notifications\n                v-else-if=\"currentPage === 'notifications'\"\n                :key=\"'notifications'\"\n                @navigate=\"handleNavigate\"\n                @notification-updated=\"handleNotificationUpdated\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 悬浮系统通知 -->\n    <FloatingNotifications\n      ref=\"floatingNotifications\"\n      @navigate-to-notifications=\"handleNavigateToNotifications\"\n    />\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport Sidebar from './components/Sidebar.vue'\nimport FloatingNotifications from './components/FloatingNotifications.vue'\nimport Overview from './views/Overview.vue'\nimport Profile from './views/Profile.vue'\nimport Credits from './views/Credits.vue'\nimport Orders from './views/Orders.vue'\nimport Usage from './views/Usage.vue'\n// 🚫 临时注释掉会员服务和推荐奖励组件\n// import Membership from './views/Membership.vue'\n// import Referral from './views/Referral.vue'\nimport Notifications from './pages/Notifications.vue'\nimport { getUserFullInfo } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { usercenterAnimations } from '@/animations/gsap/pages/usercenterAnimations.js'\nimport Vue from 'vue'\n\nexport default {\n  name: 'UserCenter',\n  components: {\n    WebsitePage,\n    Sidebar,\n    FloatingNotifications,\n    Overview,\n    Profile,\n    Credits,\n    Orders,\n    Usage,\n    // 🚫 临时注释掉会员服务和推荐奖励组件\n    // Membership,\n    // Referral,\n    Notifications\n  },\n  data() {\n    return {\n      loading: true,\n      currentPage: 'overview',\n      userInfo: {\n        nickname: '',\n        email: '',\n        avatar: '',\n        phone: '',\n        accountBalance: 0,\n        currentRole: '普通用户',\n        totalConsumption: 0,\n        totalRecharge: 0,\n        memberExpireTime: null,\n        apiKey: '',\n        createTime: null,\n        // 🔑 关键：添加密码修改状态字段\n        passwordChanged: 0\n      },\n      pageMap: {\n        overview: '概览',\n        profile: '账户设置',\n        credits: '账户管理',\n        orders: '订单记录',\n        usage: '使用记录',\n        notifications: '系统通知'\n        // 🚫 临时注释掉会员服务和推荐奖励页面\n        // membership: '会员服务',\n        // referral: '推荐奖励'\n      }\n    }\n  },\n  computed: {\n    currentPageTitle() {\n      return this.pageMap[this.currentPage] || ''\n    }\n  },\n  async mounted() {\n    // 检查登录状态\n    if (!this.checkLoginStatus()) {\n      return\n    }\n\n    await this.loadUserInfo()\n    this.initAnimations()\n  },\n  methods: {\n    /**\n     * 检查登录状态 - 使用与路由守卫相同的TOKEN检查方法\n     */\n    checkLoginStatus() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        console.log('🔍 UserCenter: 未登录，重定向到登录页')\n        this.$message.warning('请先登录')\n        this.$router.push({\n          path: '/login',\n          query: { redirect: this.$route.fullPath }\n        })\n        return false\n      }\n      console.log('🔍 UserCenter: 已登录，TOKEN存在')\n      return true\n    },\n\n    async loadUserInfo() {\n      try {\n        this.loading = true\n\n        const response = await getUserFullInfo()\n        console.log('🔍 UserCenter: 完整的响应对象:', response)\n        console.log('🔍 UserCenter: response.success:', response.success)\n        console.log('🔍 UserCenter: response.data:', response.data)\n        console.log('🔍 UserCenter: response.result:', response.result)\n        console.log('🔍 UserCenter: response.message:', response.message)\n\n        if (response.success) {\n          // 使用正确的字段：response.result 而不是 response.data\n          const rawData = response.result || response.data || {}\n          console.log('🔍 UserCenter: 后端返回的原始数据:', rawData)\n\n          // 字段名映射：后端下划线 -> 前端驼峰命名\n          const mappedData = {\n            nickname: rawData.nickname || '',\n            email: rawData.email || '',\n            avatar: rawData.avatar || '',\n            phone: rawData.phone || '',\n            accountBalance: rawData.account_balance || 0,\n            totalConsumption: rawData.total_consumption || 0,\n            totalRecharge: rawData.total_recharge || 0,\n            currentRole: rawData.current_role || '普通用户',\n            apiKey: rawData.api_key || '',\n            createTime: rawData.user_create_time || null,\n            username: rawData.username || '',\n            realname: rawData.realname || '',\n            // 🔑 关键：添加密码修改状态字段\n            passwordChanged: rawData.password_changed || 0\n          }\n\n          console.log('🔍 UserCenter: 映射后的数据:', mappedData)\n\n          // 使用Object.assign确保响应式更新\n          Object.assign(this.userInfo, mappedData)\n\n          console.log('🔍 UserCenter: 最终的userInfo:', this.userInfo)\n\n          // 强制触发视图更新\n          this.$forceUpdate()\n        } else {\n          console.error('🔍 UserCenter: 获取用户信息失败')\n          console.error('🔍 UserCenter: response.code:', response.code)\n          console.error('🔍 UserCenter: response.message:', response.message)\n          console.error('🔍 UserCenter: 完整响应:', response)\n\n          // 检查是否是认证失败\n          if (response.code === 401 || (response.message && response.message.includes('Token')) || (response.message && response.message.includes('登录'))) {\n            this.$message.warning('登录已过期，请重新登录')\n            this.$router.push({\n              path: '/login',\n              query: { redirect: this.$route.fullPath }\n            })\n            return\n          }\n          this.$message.error(`获取用户信息失败: ${response.message || '未知错误'}`)\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        // 检查是否是认证相关错误\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n          this.$router.push({\n            path: '/login',\n            query: { redirect: this.$route.fullPath }\n          })\n          return\n        }\n        this.$message.error('加载用户信息失败，请刷新重试')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 刷新用户信息的方法（供子组件调用）\n    async getUserInfo() {\n      console.log('🔍 UserCenter: 收到刷新用户信息请求')\n      await this.loadUserInfo()\n    },\n\n    initAnimations() {\n      this.$nextTick(() => {\n        // 初始化GSAP动画\n        if (this.$animationManager) {\n          usercenterAnimations.init(this.$animationManager)\n        }\n      })\n    },\n\n    handleMenuChange(page) {\n      if (this.currentPage !== page) {\n        // 页面切换动画\n        const fromPage = `.page-${this.currentPage}`\n        const toPage = `.page-${page}`\n\n        if (this.$animationManager) {\n          usercenterAnimations.switchPage(fromPage, toPage)\n        }\n\n        this.currentPage = page\n\n        // 更新URL（可选）\n        this.$router.replace({\n          path: '/usercenter',\n          query: { page }\n        })\n      }\n    },\n\n    handleNavigate(page) {\n      this.handleMenuChange(page)\n    },\n\n    handleSidebarAction(action) {\n      switch (action) {\n        case 'recharge':\n          this.handleMenuChange('credits')\n          break\n        // 🚫 临时注释掉升级会员功能\n        // case 'upgrade':\n        //   this.handleMenuChange('membership')\n        //   break\n        default:\n          console.log('未知操作:', action)\n      }\n    },\n\n    handleNavigateToNotifications() {\n      // 导航到系统通知页面\n      this.handleMenuChange('notifications')\n    },\n\n    handleNotificationUpdated() {\n      // 通知更新时，刷新悬浮通知组件的数据\n      if (this.$refs.floatingNotifications) {\n        this.$refs.floatingNotifications.loadNotifications()\n      }\n\n      // 同时通知Sidebar组件更新未读通知数量\n      if (this.$refs.sidebar) {\n        this.$refs.sidebar.loadUnreadNotificationCount()\n      }\n    },\n\n    handleAvatarUpdated(newAvatar) {\n      // 头像更新时，更新用户信息\n      console.log('🔍 UserCenter: 头像更新事件，新头像:', newAvatar)\n      this.userInfo.avatar = newAvatar\n      this.$forceUpdate()\n    },\n\n    handleInfoUpdated(updatedInfo) {\n      // 基本信息更新时，更新用户信息\n      console.log('🔍 UserCenter: 基本信息更新事件:', updatedInfo)\n      Object.assign(this.userInfo, updatedInfo)\n      this.$forceUpdate()\n    },\n\n    handleApiKeyUpdated(newApiKey) {\n      // API Key更新时，保存当前滚动位置\n      const currentScrollY = window.pageYOffset || document.documentElement.scrollTop\n      console.log('🔍 UserCenter: API Key更新事件，当前滚动位置:', currentScrollY)\n\n      // 使用Vue.set确保响应式更新\n      this.$set(this.userInfo, 'apiKey', newApiKey)\n\n      // 在下一个tick恢复滚动位置\n      this.$nextTick(() => {\n        window.scrollTo(0, currentScrollY)\n        console.log('🔍 UserCenter: 已恢复滚动位置到:', currentScrollY)\n      })\n    },\n\n    // 🔑 新增：密码修改处理\n    handlePasswordChanged() {\n      console.log('🔍 UserCenter: 收到密码修改事件')\n      if (this.userInfo) {\n        this.userInfo.passwordChanged = 1\n        console.log('🔍 UserCenter: passwordChanged已更新为1')\n      }\n    }\n  },\n\n  // 路由守卫：根据URL参数设置当前页面\n  beforeRouteEnter(to, _from, next) {\n    next(vm => {\n      const page = to.query.page\n      if (page && vm.pageMap[page]) {\n        vm.currentPage = page\n      }\n    })\n  },\n\n  beforeRouteUpdate(to, _from, next) {\n    const page = to.query.page\n    if (page && this.pageMap[page]) {\n      this.currentPage = page\n    }\n    next()\n  }\n}\n</script>\n\n<style scoped>\n.usercenter-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n}\n\n\n\n.container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n/* 主要内容区域 */\n.usercenter-main {\n  padding: 2rem 0;\n}\n\n.usercenter-layout {\n  display: grid;\n  grid-template-columns: 260px 1fr;\n  gap: 1.5rem;\n  align-items: flex-start;\n  /* 取消左边距，改为正常的两列布局 */\n  /* margin-left: 320px; */\n}\n\n.usercenter-content {\n  min-height: 600px;\n  position: relative;\n}\n\n/* 页面切换动画 */\n.usercenter-content > * {\n  opacity: 0.85;\n  animation: fadeInUp 0.6s ease-out forwards;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .usercenter-layout {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .usercenter-main {\n    padding: 1rem 0;\n  }\n\n  .usercenter-layout {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n\n\n</style>\n"]}]}