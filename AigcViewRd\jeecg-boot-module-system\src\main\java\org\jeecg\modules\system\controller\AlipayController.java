package org.jeecg.modules.system.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.system.service.AlipayService;
import org.jeecg.modules.system.config.AlipayConfig;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.reward.service.ReferralRewardTriggerService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.Calendar;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付宝支付控制器
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Api(tags = "支付宝支付")
@RestController
@RequestMapping("/api/alipay")
@Slf4j
public class AlipayController {

    @Autowired
    private AlipayService alipayService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ReferralRewardTriggerService rewardTriggerService;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private AlipayConfig alipayConfig;

    /**
     * 创建支付订单
     */
    @AutoLog(value = "创建支付宝支付订单")
    @ApiOperation(value = "创建支付宝支付订单", notes = "创建支付宝支付订单")
    @PostMapping("/createOrder")
    public Result<?> createPayOrder(@RequestBody Map<String, Object> requestData) {
        try {
            String orderId = (String) requestData.get("orderId");
            BigDecimal amount = new BigDecimal(requestData.get("amount").toString());
            String subject = (String) requestData.getOrDefault("subject", "智界Aigc充值");
            String body = (String) requestData.getOrDefault("body", "智界Aigc账户充值");

            log.info("💰 创建支付宝支付订单请求 - 订单号: {}, 金额: {}", orderId, amount);

            String payForm = alipayService.createPayOrder(orderId, amount, subject, body);

            log.info("🔍 支付表单内容长度: {}", payForm != null ? payForm.length() : 0);
            log.info("🔍 支付表单前100字符: {}", payForm != null && payForm.length() > 100 ? payForm.substring(0, 100) : payForm);

            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("amount", amount);
            result.put("payForm", payForm);

            log.info("🔍 返回结果: orderId={}, amount={}, payForm长度={}", orderId, amount, payForm != null ? payForm.length() : 0);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 创建支付宝支付订单失败", e);
            return Result.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建支付宝扫码支付订单
     */
    @AutoLog(value = "创建支付宝扫码支付订单")
    @ApiOperation(value = "创建支付宝扫码支付订单", notes = "创建支付宝扫码支付订单")
    @PostMapping("/createQrOrder")
    public Result<?> createQrPayOrder(@RequestBody Map<String, Object> params) {
        try {
            String orderId = (String) params.get("orderId");
            BigDecimal amount = new BigDecimal(params.get("amount").toString());
            String subject = (String) params.get("subject");
            String body = (String) params.get("body");

            log.info("💰 创建支付宝扫码支付订单请求 - 订单号: {}, 金额: {}", orderId, amount);

            String qrCode = alipayService.createQrPayOrder(orderId, amount, subject, body);

            log.info("🔍 扫码支付二维码URL: {}", qrCode);

            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("amount", amount);
            result.put("qrCode", qrCode);

            log.info("🔍 返回扫码支付结果: orderId={}, amount={}, qrCode={}", orderId, amount, qrCode != null ? "已生成" : "为空");
            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 创建支付宝扫码支付订单失败", e);
            return Result.error("创建扫码支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     */
    @AutoLog(value = "查询支付宝订单状态")
    @ApiOperation(value = "查询支付宝订单状态", notes = "查询支付宝订单状态")
    @GetMapping("/queryOrder")
    public Result<?> queryPayOrder(@RequestParam String orderId) {
        try {
            log.info("💰 查询支付宝订单状态请求 - 订单号: {}", orderId);

            Map<String, Object> orderInfo = alipayService.queryPayOrder(orderId);
            return Result.OK(orderInfo);

        } catch (Exception e) {
            log.error("💰 查询支付宝订单状态失败 - 订单号: {}", orderId, e);
            return Result.error("查询订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 支付宝异步通知处理
     */
    @AutoLog(value = "支付宝异步通知")
    @ApiOperation(value = "支付宝异步通知", notes = "处理支付宝异步通知")
    @PostMapping("/notify")
    public String handleNotify(HttpServletRequest request) {
        try {
            log.info("💰 收到支付宝异步通知");

            // 验证签名
            boolean signVerified = alipayService.verifyNotify(request);
            if (!signVerified) {
                log.error("💰 支付宝异步通知签名验证失败");
                return "failure";
            }
            log.info("💰 支付宝签名验证通过");

            // 获取通知参数
            String tradeStatus = request.getParameter("trade_status");
            String outTradeNo = request.getParameter("out_trade_no");
            String tradeNo = request.getParameter("trade_no");
            String totalAmount = request.getParameter("total_amount");
            String buyerEmail = request.getParameter("buyer_email");

            log.info("💰 支付宝异步通知参数 - 订单号: {}, 交易号: {}, 状态: {}, 金额: {}, 买家: {}",
                outTradeNo, tradeNo, tradeStatus, totalAmount, buyerEmail);

            // 验证订单是否存在且状态正确
            if (!isValidOrder(outTradeNo)) {
                log.error("💰 无效订单或订单已处理 - 订单号: {}", outTradeNo);
                return "failure";
            }

            // 处理支付成功通知
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                log.info("💰 支付成功 - 订单号: {}, 交易号: {}", outTradeNo, tradeNo);

                // 处理支付成功业务逻辑
                boolean processResult = processPaymentSuccess(outTradeNo, tradeNo, totalAmount);
                if (processResult) {
                    log.info("💰 支付成功处理完成 - 订单号: {}", outTradeNo);
                } else {
                    log.error("💰 支付成功处理失败 - 订单号: {}", outTradeNo);
                }

                // 返回success给支付宝，表示处理成功
                return "success";
            }

            log.warn("💰 支付状态异常 - 订单号: {}, 状态: {}", outTradeNo, tradeStatus);
            return "success"; // 即使状态异常也返回success，避免支付宝重复通知

        } catch (Exception e) {
            log.error("💰 处理支付宝异步通知异常", e);
            return "failure";
        }
    }

    /**
     * 支付宝同步返回处理
     */
    @AutoLog(value = "支付宝同步返回")
    @ApiOperation(value = "支付宝同步返回", notes = "处理支付宝同步返回")
    @GetMapping("/return")
    public void handleReturn(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("💰 收到支付宝同步返回");

            // 验证签名
            boolean signVerified = alipayService.verifyNotify(request);
            if (!signVerified) {
                log.error("💰 支付宝同步返回签名验证失败");
                response.sendRedirect("/payment/failure");
                return;
            }

            String outTradeNo = request.getParameter("out_trade_no");
            String tradeNo = request.getParameter("trade_no");
            String totalAmount = request.getParameter("total_amount");

            log.info("💰 支付宝同步返回参数 - 订单号: {}, 交易号: {}, 金额: {}",
                outTradeNo, tradeNo, totalAmount);

            // 动态获取当前请求的域名，确保重定向到正确的地址
            String scheme = request.getScheme(); // http 或 https
            String serverName = request.getServerName(); // 域名或IP
            int serverPort = request.getServerPort(); // 端口号

            String frontendUrl = scheme + "://" + serverName;
            // 只有非标准端口才需要添加端口号
            if ((scheme.equals("http") && serverPort != 80) ||
                (scheme.equals("https") && serverPort != 443)) {
                frontendUrl += ":" + serverPort;
            }

            log.info("💰 动态获取前端地址: {} (scheme: {}, serverName: {}, port: {})",
                frontendUrl, scheme, serverName, serverPort);

            // 统一根据来源页面决定重定向（不区分订单类型）
            String redirectUrl;

            // 查询订单来源信息
            try {
                String sql = "SELECT product_info FROM aicg_user_transaction WHERE related_order_id = ?";
                List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, outTradeNo);

                String source = "usercenter"; // 默认值
                if (!result.isEmpty()) {
                    String productInfo = (String) result.get(0).get("product_info");
                    if (productInfo != null && !productInfo.isEmpty()) {
                        try {
                            Map<String, Object> sourceInfo = JSON.parseObject(productInfo, Map.class);
                            source = (String) sourceInfo.getOrDefault("source", "usercenter");
                        } catch (Exception e) {
                            log.warn("解析来源信息失败，使用默认值", e);
                        }
                    }
                }

                // 根据来源页面决定重定向
                if ("membership".equals(source)) {
                    // 来自membership页面：重定向到membership页面
                    redirectUrl = frontendUrl + "/membership?paymentSuccess=true&orderId=" + outTradeNo;
                } else {
                    // 来自其他页面：重定向到个人中心
                    redirectUrl = frontendUrl + "/usercenter?page=credits&paymentSuccess=true&orderId=" + outTradeNo;
                }

                log.info("💰 重定向决策 - 订单号: {}, 来源: {}, 重定向: {}", outTradeNo, source, redirectUrl);

            } catch (Exception e) {
                log.error("查询订单信息失败，使用默认重定向", e);
                redirectUrl = frontendUrl + "/usercenter?page=credits&paymentSuccess=true&orderId=" + outTradeNo;
            }

            log.info("💰 重定向到前端页面: {}", redirectUrl);
            response.sendRedirect(redirectUrl);

        } catch (IOException e) {
            log.error("💰 处理支付宝同步返回异常", e);
        }
    }

    /**
     * 支付宝授权回调处理
     */
    @AutoLog(value = "支付宝授权回调")
    @ApiOperation(value = "支付宝授权回调", notes = "处理支付宝授权回调")
    @GetMapping("/auth/callback")
    public Result<?> handleAuthCallback(@RequestParam(required = false) String code,
                                       @RequestParam(required = false) String state) {
        try {
            log.info("💰 收到支付宝授权回调 - code: {}, state: {}", code, state);

            if (code == null || code.isEmpty()) {
                return Result.error("授权失败：未获取到授权码");
            }

            // TODO: 这里可以实现支付宝登录功能
            // 使用授权码换取用户信息等

            Map<String, Object> result = new HashMap<>();
            result.put("code", code);
            result.put("state", state);
            result.put("message", "授权回调处理成功");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 处理支付宝授权回调异常", e);
            return Result.error("授权回调处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证订单是否有效
     */
    private boolean isValidOrder(String outTradeNo) {
        try {
            String sql = "SELECT order_status, create_time FROM aicg_user_transaction WHERE related_order_id = ?";
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, outTradeNo);

            if (result.isEmpty()) {
                log.warn("💰 订单不存在 - 订单号: {}", outTradeNo);
                return false;
            }

            Map<String, Object> order = result.get(0);
            Integer orderStatus = (Integer) order.get("order_status");

            // 只有待支付状态(1)的订单才能处理
            if (orderStatus != 1) {
                log.warn("💰 订单状态异常 - 订单号: {}, 状态: {}", outTradeNo, orderStatus);
                return false;
            }

            // 检查订单是否超时（15分钟）
            java.sql.Timestamp createTime = (java.sql.Timestamp) order.get("create_time");
            long diffMinutes = (System.currentTimeMillis() - createTime.getTime()) / (1000 * 60);
            if (diffMinutes > 15) {
                log.warn("💰 订单已超时 - 订单号: {}, 创建时间: {}, 超时: {}分钟", outTradeNo, createTime, diffMinutes);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("💰 验证订单异常 - 订单号: {}", outTradeNo, e);
            return false;
        }
    }

    /**
     * 处理支付成功业务逻辑
     */
    private boolean processPaymentSuccess(String outTradeNo, String tradeNo, String totalAmount) {
        try {
            log.info("💰 开始处理支付成功业务逻辑 - 订单号: {}, 交易号: {}, 金额: {}",
                outTradeNo, tradeNo, totalAmount);

            // 查询订单信息
            String querySql = "SELECT * FROM aicg_user_transaction WHERE related_order_id = ? AND order_status = 1";
            List<Map<String, Object>> orderResult = jdbcTemplate.queryForList(querySql, outTradeNo);

            if (orderResult.isEmpty()) {
                log.warn("💰 未找到待支付订单 - 订单号: {}", outTradeNo);
                return false;
            }

            Map<String, Object> order = orderResult.get(0);
            String userId = (String) order.get("user_id");
            String orderType = (String) order.get("order_type");
            BigDecimal orderAmount = (BigDecimal) order.get("amount");
            BigDecimal payAmount = new BigDecimal(totalAmount);

            // 验证金额是否一致
            if (orderAmount.compareTo(payAmount) != 0) {
                log.error("💰 订单金额不一致 - 订单号: {}, 订单金额: {}, 支付金额: {}",
                    outTradeNo, orderAmount, payAmount);
                return false;
            }

            // 获取用户信息
            String userSql = "SELECT * FROM aicg_user_profile WHERE user_id = ?";
            List<Map<String, Object>> userResult = jdbcTemplate.queryForList(userSql, userId);

            if (userResult.isEmpty()) {
                log.error("💰 用户不存在 - 用户ID: {}", userId);
                return false;
            }

            Map<String, Object> user = userResult.get(0);
            BigDecimal currentBalance = (BigDecimal) user.get("account_balance");
            Date now = new Date();

            // 更新订单状态（根据订单类型设置不同的余额信息）
            String updateOrderSql = "UPDATE aicg_user_transaction SET " +
                "order_status = 3, " +  // 已完成
                "balance_before = ?, " +
                "balance_after = ?, " +
                "transaction_time = ?, " +
                "update_time = ?, " +
                "description = ? " +
                "WHERE related_order_id = ? AND user_id = ?";

            // 根据订单类型确定如何处理余额
            BigDecimal balanceAfter;
            String description;

            if ("recharge".equals(orderType)) {
                // 充值订单：余额会增加
                balanceAfter = currentBalance.add(orderAmount);
                description = "支付宝付款：账户充值";
            } else {
                // 会员订单：余额不变，根据会员类型和时长生成描述
                balanceAfter = currentBalance;
                description = generateMembershipDescription(order);
            }

            jdbcTemplate.update(updateOrderSql,
                currentBalance,
                balanceAfter,
                now,
                now,
                description,
                outTradeNo,
                userId
            );

            // 根据订单类型处理不同的业务逻辑
            if ("recharge".equals(orderType)) {
                // 更新用户余额（仅充值订单）
                String updateUserSql = "UPDATE aicg_user_profile SET " +
                    "account_balance = ?, " +
                    "total_recharge = total_recharge + ?, " +
                    "update_time = ? " +
                    "WHERE user_id = ?";

                jdbcTemplate.update(updateUserSql,
                    balanceAfter,
                    orderAmount,
                    now,
                    userId
                );

                log.info("💰 充值成功 - 用户: {}, 充值金额: {}, 新余额: {}", userId, orderAmount, balanceAfter);

            } else if ("membership".equals(orderType)) {
                try {
                    // 解析产品信息获取会员等级和时长
                    String productInfoStr = (String) order.get("product_info");
                    Integer membershipLevel = 2; // 默认VIP等级
                    Integer duration = 12; // 默认12个月

                    if (productInfoStr != null && !productInfoStr.isEmpty()) {
                        try {
                            JSONObject productInfo = JSON.parseObject(productInfoStr);
                            membershipLevel = productInfo.getInteger("membershipLevel");
                            duration = productInfo.getInteger("duration");
                            log.info("📋 解析会员订单信息 - 等级: {}, 时长: {}个月", membershipLevel, duration);
                        } catch (Exception e) {
                            log.warn("📋 解析产品信息失败，使用默认值 - {}", e.getMessage());
                        }
                    }

                    // 1. 更新用户会员状态
                    updateUserMembershipStatus(userId, membershipLevel, duration);

                    // 2. 更新用户角色
                    updateUserRole(userId, membershipLevel);

                    // 3. 触发邀请奖励
                    boolean rewardSuccess = rewardTriggerService.triggerMembershipSubscriptionReward(
                        userId, orderAmount, membershipLevel, outTradeNo
                    );

                    if (rewardSuccess) {
                        log.info("🎁 邀请奖励触发成功 - 订单号: {}, 用户: {}, 金额: {}",
                                outTradeNo, userId, orderAmount);
                    }

                    log.info("🎯 会员购买处理完成 - 用户: {}, 等级: {}, 时长: {}个月",
                            userId, membershipLevel, duration);

                } catch (Exception e) {
                    log.error("🎁 会员订阅处理失败 - 订单号: {}, 用户: {}", outTradeNo, userId, e);
                    // 会员处理失败不影响主业务流程
                }
            }

            log.info("💰 支付成功处理完成 - 订单号: {}, 订单类型: {}, 用户余额: {} -> {}",
                outTradeNo, orderType, currentBalance, balanceAfter);

            return true;

        } catch (Exception e) {
            log.error("💰 处理支付成功业务逻辑异常 - 订单号: {}", outTradeNo, e);
            return false;
        }
    }

    /**
     * 更新用户会员状态（支持复杂的续费和升级逻辑）
     */
    private void updateUserMembershipStatus(String userId, Integer membershipLevel, Integer duration) {
        try {
            // 获取用户当前会员信息
            MembershipInfo currentInfo = getCurrentMembershipInfo(userId);
            Integer currentLevel = currentInfo.getCurrentRoleLevel();

            if (currentLevel == 2 && membershipLevel == 3) {
                // VIP升级到SVIP的特殊处理
                handleVipToSvipUpgrade(userId, currentInfo, duration);
            } else if (currentLevel.equals(membershipLevel)) {
                // 相同等级续费
                handleMembershipRenewal(userId, currentInfo, duration);
            } else {
                // 其他情况（新购买或降级）
                handleRegularMembershipUpdate(userId, membershipLevel, duration);
            }

        } catch (Exception e) {
            log.error("👑 更新用户会员状态异常 - 用户: {}", userId, e);
            throw e;
        }
    }

    /**
     * 更新用户角色
     */
    private void updateUserRole(String userId, Integer membershipLevel) {
        try {
            // 根据会员等级确定角色代码
            String roleCode = getRoleCodeByMembershipLevel(membershipLevel);

            // 删除用户现有的会员角色（保留admin等其他角色）
            String deleteRoleSql = "DELETE FROM sys_user_role WHERE user_id = ? AND role_id IN " +
                "(SELECT id FROM sys_role WHERE role_code IN ('user', 'VIP', 'SVIP'))";
            jdbcTemplate.update(deleteRoleSql, userId);

            // 添加新的会员角色
            sysUserRoleService.addUserRole(userId, roleCode);

            log.info("🎭 更新用户角色成功 - 用户: {}, 会员等级: {}, 角色: {}",
                    userId, membershipLevel, roleCode);

        } catch (Exception e) {
            log.error("🎭 更新用户角色异常 - 用户: {}, 会员等级: {}", userId, membershipLevel, e);
            throw e;
        }
    }

    /**
     * 根据会员等级获取角色代码
     */
    private String getRoleCodeByMembershipLevel(Integer membershipLevel) {
        switch (membershipLevel) {
            case 3:
                return "SVIP";
            case 2:
                return "VIP";
            case 1:
            default:
                return "user";
        }
    }

    /**
     * 获取用户当前会员信息（基于角色机制）
     */
    private MembershipInfo getCurrentMembershipInfo(String userId) {
        try {
            // 获取用户角色和到期时间
            String sql = "SELECT p.member_expire_time, r.role_code " +
                        "FROM aicg_user_profile p " +
                        "LEFT JOIN sys_user_role ur ON p.user_id = ur.user_id " +
                        "LEFT JOIN sys_role r ON ur.role_id = r.id " +
                        "WHERE p.user_id = ? AND r.role_code IN ('user', 'VIP', 'SVIP') " +
                        "ORDER BY CASE r.role_code WHEN 'SVIP' THEN 3 WHEN 'VIP' THEN 2 ELSE 1 END DESC " +
                        "LIMIT 1";

            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, userId);

            if (results.isEmpty()) {
                // 用户不存在或没有角色，返回默认信息
                return new MembershipInfo("user", null);
            }

            Map<String, Object> result = results.get(0);
            String roleCode = (String) result.get("role_code");
            Date expireTime = (Date) result.get("member_expire_time");

            return new MembershipInfo(roleCode != null ? roleCode : "user", expireTime);

        } catch (Exception e) {
            log.error("获取用户会员信息失败 - 用户: {}", userId, e);
            return new MembershipInfo("user", null);
        }
    }

    /**
     * 计算会员到期时间（基于角色级别）
     */
    private Date calculateMembershipExpireTime(MembershipInfo currentInfo, Integer newMembershipLevel, Integer duration) {
        Calendar calendar = Calendar.getInstance();
        Date baseTime;

        Integer currentLevel = currentInfo.getCurrentRoleLevel();

        // 判断续费还是新购买
        if (currentInfo.isExpired() || currentLevel < newMembershipLevel) {
            // 已过期或升级：从当前时间开始
            baseTime = new Date();
            log.info("📅 会员购买类型: {} - 从当前时间开始计算",
                    currentInfo.isExpired() ? "过期续费" : "升级购买");
        } else if (currentLevel.equals(newMembershipLevel)) {
            // 相同等级续费：从现有到期时间开始
            baseTime = currentInfo.getExpireTime();
            log.info("📅 会员购买类型: 续费 - 从现有到期时间开始计算");
        } else {
            // 降级情况：从当前时间开始（不推荐，但支持）
            baseTime = new Date();
            log.warn("📅 会员购买类型: 降级 - 从当前时间开始计算");
        }

        calendar.setTime(baseTime);
        calendar.add(Calendar.MONTH, duration);

        Date expireTime = calendar.getTime();
        log.info("📅 会员到期时间计算 - 基准时间: {}, 延长: {}个月, 到期时间: {}",
                baseTime, duration, expireTime);

        return expireTime;
    }

    /**
     * VIP升级到SVIP的特殊处理（冻结VIP剩余天数）
     */
    private void handleVipToSvipUpgrade(String userId, MembershipInfo currentInfo, Integer duration) {
        try {
            // 计算VIP剩余天数
            int remainingDays = calculateRemainingDays(currentInfo.getExpireTime());

            // 计算SVIP到期时间（从今天开始）
            Date svipExpireTime = calculateExpireTime(new Date(), duration);

            if (remainingDays > 0) {
                // 冻结VIP剩余天数
                String updateSql = "UPDATE aicg_user_profile SET " +
                                  "frozen_vip_days = ?, " +
                                  "frozen_vip_expire_time = ?, " +
                                  "member_expire_time = ?, " +
                                  "update_time = ? " +
                                  "WHERE user_id = ?";

                jdbcTemplate.update(updateSql, remainingDays, currentInfo.getExpireTime(),
                                   svipExpireTime, new Date(), userId);

                log.info("🔄 VIP升级SVIP - 用户: {}, 冻结VIP天数: {}, SVIP到期: {}",
                        userId, remainingDays, svipExpireTime);
            } else {
                // 没有剩余天数，直接升级
                String updateSql = "UPDATE aicg_user_profile SET " +
                                  "member_expire_time = ?, " +
                                  "update_time = ? " +
                                  "WHERE user_id = ?";

                jdbcTemplate.update(updateSql, svipExpireTime, new Date(), userId);

                log.info("🔄 VIP升级SVIP - 用户: {}, 无剩余天数, SVIP到期: {}", userId, svipExpireTime);
            }

        } catch (Exception e) {
            log.error("VIP升级SVIP处理失败 - 用户: {}", userId, e);
            throw e;
        }
    }

    /**
     * 相同等级续费处理
     */
    private void handleMembershipRenewal(String userId, MembershipInfo currentInfo, Integer duration) {
        try {
            // 在现有到期时间基础上延长
            Date baseTime = currentInfo.isExpired() ? new Date() : currentInfo.getExpireTime();
            Date newExpireTime = calculateExpireTime(baseTime, duration);

            String updateSql = "UPDATE aicg_user_profile SET " +
                              "member_expire_time = ?, " +
                              "update_time = ? " +
                              "WHERE user_id = ?";

            jdbcTemplate.update(updateSql, newExpireTime, new Date(), userId);

            log.info("🔄 会员续费 - 用户: {}, 角色: {}, 新到期时间: {}",
                    userId, currentInfo.getRoleCode(), newExpireTime);

        } catch (Exception e) {
            log.error("会员续费处理失败 - 用户: {}", userId, e);
            throw e;
        }
    }

    /**
     * 常规会员更新处理（新购买或降级）
     */
    private void handleRegularMembershipUpdate(String userId, Integer membershipLevel, Integer duration) {
        try {
            // 从当前时间开始计算
            Date expireTime = calculateExpireTime(new Date(), duration);

            String updateSql = "UPDATE aicg_user_profile SET " +
                              "member_expire_time = ?, " +
                              "update_time = ? " +
                              "WHERE user_id = ?";

            jdbcTemplate.update(updateSql, expireTime, new Date(), userId);

            log.info("🔄 会员更新 - 用户: {}, 等级: {}, 到期时间: {}",
                    userId, membershipLevel, expireTime);

        } catch (Exception e) {
            log.error("会员更新处理失败 - 用户: {}", userId, e);
            throw e;
        }
    }

    /**
     * 计算剩余天数
     */
    private int calculateRemainingDays(Date expireTime) {
        if (expireTime == null) return 0;

        long diffInMillies = expireTime.getTime() - System.currentTimeMillis();
        if (diffInMillies <= 0) return 0;

        return (int) (diffInMillies / (24 * 60 * 60 * 1000));
    }

    /**
     * 计算到期时间
     */
    private Date calculateExpireTime(Date baseTime, Integer duration) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(baseTime);
        calendar.add(Calendar.MONTH, duration);
        return calendar.getTime();
    }

    /**
     * 记录会员变更日志
     */
    private void logMembershipChange(String userId, MembershipInfo oldInfo, Integer newLevel, Date newExpireTime, String changeType) {
        try {
            log.info("📋 会员变更记录 - 用户: {}, 变更类型: {}, 角色: {} → {}, 到期时间: {} → {}",
                    userId, changeType, oldInfo.getRoleCode(), getRoleCodeByMembershipLevel(newLevel),
                    oldInfo.getExpireTime(), newExpireTime);
        } catch (Exception e) {
            log.error("记录会员变更日志失败", e);
        }
    }

    /**
     * 生成会员购买描述
     */
    private String generateMembershipDescription(Map<String, Object> order) {
        try {
            String productInfo = (String) order.get("product_info");
            if (productInfo != null && !productInfo.isEmpty()) {
                Map<String, Object> info = JSON.parseObject(productInfo, Map.class);
                Integer membershipLevel = (Integer) info.get("membershipLevel");
                Integer duration = (Integer) info.get("duration");

                // 根据等级确定会员类型
                String memberType = "VIP会员";
                if (membershipLevel != null && membershipLevel == 3) {
                    memberType = "SVIP会员";
                }

                // 根据时长确定套餐类型
                String planType = "月卡";
                if (duration != null && duration >= 12) {
                    planType = "年卡";
                }

                return String.format("支付宝付款：%s购买%s", memberType, planType);
            }
        } catch (Exception e) {
            log.warn("解析会员订单信息失败，使用默认描述", e);
        }

        // 默认描述
        return "支付宝付款：会员购买";
    }

    /**
     * 会员信息内部类（基于角色机制）
     */
    private static class MembershipInfo {
        private String roleCode;
        private Date expireTime;

        public MembershipInfo(String roleCode, Date expireTime) {
            this.roleCode = roleCode;
            this.expireTime = expireTime;
        }

        public String getRoleCode() {
            return roleCode != null ? roleCode : "user";
        }

        public Integer getCurrentRoleLevel() {
            String role = getRoleCode();
            switch (role) {
                case "SVIP": return 3;
                case "VIP": return 2;
                case "user":
                default: return 1;
            }
        }

        public Date getExpireTime() {
            return expireTime;
        }

        public boolean isExpired() {
            return expireTime == null || expireTime.before(new Date());
        }
    }
}
