<template>
  <div class="quick-recharge-card">
    <!-- 余额显示区域 -->
    <div class="balance-section">
      <div class="user-info">
        <div class="user-avatar">
          <a-avatar
            :size="60"
            :src="avatarUrl"
            icon="user"
            :style="{ backgroundColor: '#87d068' }"
          >
            {{ userNickname ? userNickname.charAt(0) : 'U' }}
          </a-avatar>
        </div>
        <div class="user-details">
          <div class="user-name">
            {{ userNickname || '智界用户' }}
            <span class="user-role">{{ getUserRoleDisplayName() }}</span>
          </div>
          <div class="balance-info">
            <span class="balance-label">账户余额</span>
            <span class="balance-amount">¥{{ formatBalance(userBalance) }}</span>
          </div>
        </div>
      </div>
      <div class="balance-actions">
        <button class="recharge-btn" @click="showRechargeModal = true">
          <i class="anticon anticon-plus-circle"></i>
          快速充值
        </button>
      </div>
    </div>
    
    <!-- 充值弹窗 -->
    <a-modal
      title="账户充值"
      :visible="showRechargeModal"
      @cancel="showRechargeModal = false"
      :footer="null"
      width="500px"
    >
      <div class="recharge-modal-content">
        <!-- 充值选项 -->
        <div class="recharge-options">
          <h4>选择充值金额</h4>
          <div class="options-grid">
            <div 
              v-for="option in rechargeOptions" 
              :key="option.amount"
              class="recharge-option"
              :class="{ selected: selectedAmount === option.amount }"
              @click="selectRechargeAmount(option.amount)"
            >
              <div class="option-amount">¥{{ option.amount }}</div>
              <div class="option-label">{{ option.label }}</div>
            </div>
          </div>
        </div>
        
        <!-- 自定义金额 -->
        <div class="custom-amount">
          <h4>自定义金额</h4>
          <div class="custom-input">
            <a-input-number
              v-model="customAmount"
              :min="0.01"
              :max="10000"
              :step="0.01"
              placeholder="最低0.01元"
              size="large"
              style="width: 200px"
            />
            <span class="currency">元</span>
          </div>
        </div>
        
        <!-- 支付方式 -->
        <div class="payment-methods">
          <h4>支付方式</h4>
          <a-radio-group v-model="selectedPaymentMethod" size="large">
            <a-radio-button value="alipay-qr">
              <i class="anticon anticon-qrcode"></i>
              支付宝扫码
            </a-radio-button>
            <a-radio-button value="alipay-page">
              <i class="anticon anticon-alipay"></i>
              支付宝网页
            </a-radio-button>
          </a-radio-group>
        </div>
        
        <!-- 充值按钮 -->
        <div class="recharge-actions">
          <div class="amount-summary">
            <span>充值金额：</span>
            <span class="final-amount">¥{{ finalRechargeAmount }}</span>
          </div>
          <a-button 
            type="primary" 
            size="large"
            :loading="rechargeLoading"
            @click="handleRecharge"
            :disabled="!finalRechargeAmount || finalRechargeAmount < 0.01"
          >
            确认充值
          </a-button>
        </div>
      </div>
    </a-modal>
    
    <!-- 支付二维码弹窗 -->
    <a-modal
      title="扫码支付"
      :visible="showQrModal"
      @cancel="closeQrModal"
      :footer="null"
      width="400px"
    >
      <div class="qr-payment-content">
        <div class="qr-code-container">
          <div v-if="qrCodeUrl" class="qr-code">
            <img :src="qrCodeUrl" alt="支付二维码" />
          </div>
          <div v-else class="qr-loading">
            <a-spin size="large" />
            <p>正在生成二维码...</p>
          </div>
        </div>
        <div class="qr-info">
          <p class="qr-amount">支付金额：¥{{ currentOrderAmount }}</p>
          <p class="qr-tip">请使用支付宝扫码支付</p>
        </div>
        <div class="qr-status">
          <a-button @click="checkPaymentStatus" :loading="checkingStatus">
            检查支付状态
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  getTransactionStats,
  createRechargeOrder,
  getUserProfile
} from '@/api/usercenter'
import { getFileAccessHttpUrl } from '@/utils/util'
import { getCurrentUserRole } from '@/utils/roleUtils'

export default {
  name: 'QuickRecharge',
  data() {
    return {
      loading: false,
      rechargeLoading: false,
      checkingStatus: false,
      
      // 用户信息
      userBalance: 0,
      userNickname: '',
      userAvatar: '',
      defaultAvatar: '/default-avatar.png', // 本地默认头像作为降级方案
      
      // 充值弹窗
      showRechargeModal: false,
      
      // 充值选项
      rechargeOptions: [
        { amount: 50, label: '体验套餐' },
        { amount: 100, label: '基础套餐' },
        { amount: 300, label: '进阶套餐' },
        { amount: 500, label: '专业套餐' },
        { amount: 1000, label: '企业套餐' }
      ],
      selectedAmount: 0,
      customAmount: null,
      
      // 支付方式
      selectedPaymentMethod: 'alipay-qr',
      
      // 二维码支付
      showQrModal: false,
      qrCodeUrl: '',
      currentOrderId: '',
      currentOrderAmount: 0,
      paymentCheckTimer: null
    }
  },
  
  computed: {
    finalRechargeAmount() {
      return this.customAmount || this.selectedAmount || 0
    },

    // 头像URL处理（与个人中心逻辑一致）
    avatarUrl() {
      const avatar = this.userAvatar
      if (!avatar) {
        return this.defaultAvatar
      }

      // 如果是完整的URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar
      }

      // 如果是相对路径，使用getFileAccessHttpUrl转换
      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar
    }
  },
  
  mounted() {
    this.loadUserInfo()
    this.loadDefaultAvatar()
  },
  
  beforeDestroy() {
    if (this.paymentCheckTimer) {
      clearInterval(this.paymentCheckTimer)
    }
  },
  
  methods: {
    // 加载用户信息（包含余额）
    async loadUserInfo() {
      try {
        this.loading = true

        // 加载余额信息
        const statsResponse = await getTransactionStats()
        if (statsResponse.success) {
          const stats = statsResponse.result || {}
          this.userBalance = stats.accountBalance || 0
        }

        // 加载用户基本信息
        const profileResponse = await getUserProfile()
        if (profileResponse.success) {
          const profile = profileResponse.result || {}
          this.userNickname = profile.nickname || profile.realname || ''
          this.userAvatar = profile.avatar || ''
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载TOS默认头像URL
    async loadDefaultAvatar() {
      try {
        const response = await this.$http.get('/sys/common/default-avatar-url')
        if (response && response.success && response.result) {
          this.defaultAvatar = response.result
          console.log('🎯 QuickRecharge: 已加载TOS默认头像:', this.defaultAvatar)
        }
      } catch (error) {
        console.warn('⚠️ QuickRecharge: 获取TOS默认头像失败，使用本地降级:', error)
        // 保持本地默认头像作为降级方案
      }
    },

    // 头像URL处理方法（与个人中心逻辑一致）
    getFileAccessHttpUrl(avatar) {
      if (!avatar) return ''

      // 如果已经是完整URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar
      }

      // 如果是TOS文件，使用全局方法
      if (avatar.startsWith('uploads/')) {
        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar
      }

      // 本地文件，使用静态域名
      return this.$store.state.app.staticDomainURL + '/' + avatar
    },
    
    // 格式化余额显示
    formatBalance(balance) {
      return parseFloat(balance || 0).toFixed(2)
    },
    
    // 选择充值金额
    selectRechargeAmount(amount) {
      this.selectedAmount = amount
      this.customAmount = null
    },
    
    // 处理充值
    async handleRecharge() {
      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {
        this.$message.warning('请选择或输入充值金额，最低0.01元')
        return
      }

      try {
        this.rechargeLoading = true

        const orderData = {
          amount: this.finalRechargeAmount,
          paymentMethod: this.selectedPaymentMethod
        }

        const response = await createRechargeOrder(orderData)
        if (response.success) {
          const result = response.result

          if (this.selectedPaymentMethod === 'alipay-page') {
            await this.handleAlipayPagePayment(result.orderId, result.amount)
          } else if (this.selectedPaymentMethod === 'alipay-qr') {
            await this.handleAlipayQrPayment(result.orderId, result.amount)
          }

          this.showRechargeModal = false
        } else {
          this.$message.error(response.message || '创建充值订单失败')
        }
      } catch (error) {
        console.error('创建充值订单失败:', error)
        this.$message.error('充值失败，请重试')
      } finally {
        this.rechargeLoading = false
      }
    },
    
    // 获取用户角色显示名称
    getUserRoleDisplayName() {
      try {
        const role = getCurrentUserRole()

        // 角色显示名称映射
        const roleDisplayMap = {
          // 中文角色名（直接显示）
          '普通用户': '普通用户',
          'VIP会员': 'VIP会员',
          'SVIP会员': 'SVIP会员',
          '管理员': '管理员',
          // 英文角色代码（转换为中文）
          'user': '普通用户',
          'VIP': 'VIP会员',
          'SVIP': 'SVIP会员',
          'admin': '管理员',
          // 大小写兼容
          'USER': '普通用户',
          'vip': 'VIP会员',
          'svip': 'SVIP会员',
          'ADMIN': '管理员'
        }

        return roleDisplayMap[role] || '普通用户'
      } catch (error) {
        console.warn('获取用户角色失败:', error)
        return '普通用户'
      }
    },

    // 处理支付宝网页支付
    async handleAlipayPagePayment(orderId, amount) {
      try {
        console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount)
        this.$message.loading('正在跳转到支付宝支付...', 0)

        // 调用支付宝支付接口
        const paymentData = {
          orderId: orderId,
          amount: amount,
          subject: '智界Aigc账户充值',
          body: `充值金额：¥${amount}`
        }

        console.log('🔍 发送支付请求数据:', paymentData)
        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)
        console.log('🔍 支付响应:', payResponse)
        this.$message.destroy()

        if (payResponse.success) {
          const payForm = payResponse.result.payForm
          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')

          if (!payForm) {
            this.$message.error('支付表单为空')
            return
          }

          // 创建表单并提交到支付宝
          const div = document.createElement('div')
          div.innerHTML = payForm
          document.body.appendChild(div)

          const form = div.querySelector('form')
          if (form) {
            console.log('🔍 找到支付表单，准备提交')
            form.submit()
          } else {
            console.error('🔍 未找到支付表单')
            this.$message.error('支付表单创建失败')
          }

          // 清理DOM
          setTimeout(() => {
            if (document.body.contains(div)) {
              document.body.removeChild(div)
            }
          }, 1000)

        } else {
          console.error('🔍 支付请求失败:', payResponse.message)
          this.$message.error(payResponse.message || '创建支付订单失败')
        }

      } catch (error) {
        this.$message.destroy()
        console.error('支付宝支付失败:', error)
        this.$message.error('支付宝支付失败，请重试')
      }
    },
    
    // 处理支付宝扫码支付
    async handleAlipayQrPayment(orderId, amount) {
      try {
        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount)
        this.$message.loading('正在生成支付二维码...', 0)

        // 调用支付宝扫码支付接口
        const paymentData = {
          orderId: orderId,
          amount: amount,
          subject: '智界Aigc账户充值',
          body: `充值金额：¥${amount}`
        }

        console.log('🔍 发送扫码支付请求数据:', paymentData)
        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)
        console.log('🔍 扫码支付响应:', payResponse)
        this.$message.destroy()

        if (payResponse.success) {
          const qrCode = payResponse.result.qrCode
          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')

          if (!qrCode) {
            this.$message.error('支付二维码生成失败')
            return
          }

          // 显示二维码支付弹窗
          this.showQrCodeModal(qrCode, orderId, amount)

        } else {
          console.error('🔍 扫码支付请求失败:', payResponse.message)
          this.$message.error(payResponse.message || '创建扫码支付订单失败')
        }

      } catch (error) {
        this.$message.destroy()
        console.error('支付宝扫码支付失败:', error)
        this.$message.error('支付宝扫码支付失败，请重试')
      }
    },
    
    // 显示二维码弹窗
    showQrCodeModal(qrCode, orderId, amount) {
      // 生成二维码图片URL
      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`
      this.currentOrderId = orderId
      this.currentOrderAmount = amount
      this.showQrModal = true

      console.log('🔍 显示二维码弹窗 - 订单号:', orderId, '金额:', amount)
      console.log('🔍 二维码URL:', this.qrCodeUrl)

      // 开始轮询支付状态
      this.startPaymentStatusCheck()
    },
    
    // 关闭二维码弹窗
    closeQrModal() {
      this.showQrModal = false
      this.qrCodeUrl = ''
      this.currentOrderId = ''
      this.currentOrderAmount = 0
      
      if (this.paymentCheckTimer) {
        clearInterval(this.paymentCheckTimer)
        this.paymentCheckTimer = null
      }
    },
    
    // 开始支付状态检查
    startPaymentStatusCheck() {
      if (this.paymentCheckTimer) {
        clearInterval(this.paymentCheckTimer)
      }
      
      this.paymentCheckTimer = setInterval(() => {
        this.checkPaymentStatus()
      }, 3000) // 每3秒检查一次
    },
    
    // 检查支付状态
    async checkPaymentStatus() {
      if (!this.currentOrderId) return

      try {
        this.checkingStatus = true
        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)
        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)
        console.log('🔍 支付状态查询响应:', response)

        if (response.success && response.result.status === 'TRADE_SUCCESS') {
          console.log('🔍 支付成功！')
          this.$message.success('支付成功！')
          this.closeQrModal()
          this.loadUserInfo() // 刷新用户信息和余额
          this.$emit('recharge-success') // 通知父组件
        } else {
          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')
        }
      } catch (error) {
        console.error('检查支付状态失败:', error)
      } finally {
        this.checkingStatus = false
      }
    }
  }
}
</script>

<style scoped>
.quick-recharge-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.quick-recharge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.balance-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  position: relative;
}

.user-avatar .ant-avatar {
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-role {
  font-size: 0.75rem;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
  border: 1px solid rgba(255, 255, 255, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
  font-weight: 600;
}

.balance-info {
  display: flex;
  flex-direction: column;
}

.balance-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  margin-bottom: 0.1rem;
}

.balance-amount {
  color: white;
  font-size: 1.6rem;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.balance-actions {
  position: relative;
  z-index: 1;
}

.recharge-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
}

.recharge-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.recharge-btn i {
  font-size: 1.1rem;
}

/* 充值弹窗样式 */
.recharge-modal-content {
  padding: 1rem 0;
}

.recharge-modal-content h4 {
  margin-bottom: 1rem;
  color: #1f2937;
  font-weight: 600;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.recharge-option {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;
}

.recharge-option:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.recharge-option.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.option-amount {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.option-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

.custom-amount {
  margin-bottom: 2rem;
}

.custom-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.currency {
  color: #6b7280;
  font-weight: 500;
}

.payment-methods {
  margin-bottom: 2rem;
}

.recharge-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.amount-summary {
  font-size: 1rem;
  color: #374151;
}

.final-amount {
  font-size: 1.2rem;
  font-weight: bold;
  color: #3b82f6;
  margin-left: 0.5rem;
}

/* 二维码支付样式 */
.qr-payment-content {
  text-align: center;
  padding: 1rem 0;
}

.qr-code-container {
  margin-bottom: 1.5rem;
}

.qr-code img {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.qr-loading {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.qr-info {
  margin-bottom: 1.5rem;
}

.qr-amount {
  font-size: 1.2rem;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.qr-tip {
  color: #6b7280;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-recharge-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .balance-section {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .user-info {
    flex-direction: column;
    gap: 1rem;
  }

  .user-details {
    align-items: center;
  }

  .balance-amount {
    font-size: 1.4rem;
  }

  .options-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .recharge-actions {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
