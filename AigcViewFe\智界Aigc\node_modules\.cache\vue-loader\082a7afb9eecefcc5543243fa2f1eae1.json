{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=style&index=0&id=46809eaa&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753808171915}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 官网导航栏样式 - 支持透明和普通两种模式 */\n.website-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 1000;\n  padding: 1.75rem 0;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(25px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.15);\n  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);\n  /* 添加微妙的渐变边框 */\n  border-image: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent) 1;\n}\n\n/* 透明模式（首页专用） */\n.website-navbar.transparent {\n  background: transparent;\n  backdrop-filter: none;\n  border-bottom: none;\n  box-shadow: none;\n}\n\n.website-navbar.scrolled {\n  background: rgba(255, 255, 255, 0.99);\n  backdrop-filter: blur(30px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 12px 50px rgba(59, 130, 246, 0.12), 0 4px 16px rgba(0, 0, 0, 0.06);\n  padding: 1.5rem 0;\n  /* 滚动后增强效果 */\n  border-image: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent) 1;\n}\n\n.nav-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 品牌Logo区域 */\n.nav-brand {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1e293b;\n  text-decoration: none;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  margin-right: 1rem;\n  flex-shrink: 0;\n  position: relative;\n}\n\n/* 透明模式下的品牌样式 */\n.website-navbar.transparent .nav-brand {\n  color: #ffffff;\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n.nav-brand:hover {\n  transform: translateY(-2px);\n}\n\n/* Logo容器样式 */\n.brand-logo-container {\n  width: 48px;\n  height: 48px;\n  border-radius: 14px;\n  overflow: hidden;\n  position: relative;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25), 0 2px 8px rgba(139, 92, 246, 0.15);\n}\n\n.brand-logo-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.6s ease;\n  z-index: 1;\n}\n\n.brand-logo-image {\n  width: 100% !important;\n  height: 100% !important;\n  object-fit: cover;\n  border-radius: 14px;\n}\n\n/* Fallback样式（当logo图片加载失败时） */\n.brand-logo-fallback {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 14px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.4rem;\n}\n\n/* 悬停效果 */\n.nav-brand:hover .brand-logo-container {\n  transform: scale(1.08) rotate(8deg);\n  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.35), 0 4px 12px rgba(139, 92, 246, 0.2);\n}\n\n.nav-brand:hover .brand-logo-container::before {\n  left: 100%;\n}\n\n.brand-text {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n}\n\n.nav-brand:hover .brand-text {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  transform: translateY(-1px);\n}\n\n/* 透明模式下的品牌文字 */\n.website-navbar.transparent .brand-text {\n  color: #ffffff;\n  background: none;\n  -webkit-text-fill-color: #ffffff;\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n/* 导航菜单 */\n.nav-menu {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  flex-wrap: nowrap;\n  flex: 1;\n  justify-content: center;\n}\n\n.nav-link {\n  color: rgba(30, 41, 59, 0.8);\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  padding: 0.875rem 1.125rem;\n  border-radius: 12px;\n  white-space: nowrap;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  overflow: hidden;\n}\n\n/* 透明模式下的导航链接 */\n.website-navbar.transparent .nav-link {\n  color: rgba(255, 255, 255, 0.9);\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);\n  transition: left 0.6s ease;\n  z-index: -1;\n}\n\n.nav-link:hover,\n.nav-link.active {\n  color: #3b82f6;\n  background: rgba(59, 130, 246, 0.1);\n  transform: translateY(-3px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.nav-link:hover::before {\n  left: 100%;\n}\n\n/* 透明模式下的导航链接悬停 */\n.website-navbar.transparent .nav-link:hover {\n  color: #ffffff;\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n}\n\n.nav-link::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  width: 0;\n  height: 3px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  transform: translateX(-50%);\n  border-radius: 2px;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n}\n\n.nav-link:hover::after,\n.nav-link.active::after {\n  width: 85%;\n}\n\n/* 导航图标和文字 */\n.nav-icon {\n  font-size: 0.9rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-text {\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-link:hover .nav-icon {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.nav-link:hover .nav-text {\n  transform: translateX(2px);\n}\n\n.nav-link:hover .nav-icon,\n.nav-link.active .nav-icon {\n  color: #3b82f6;\n  transform: scale(1.1);\n}\n\n.nav-link:hover .nav-text,\n.nav-link.active .nav-text {\n  color: #3b82f6;\n}\n\n/* 🔥 开发中菜单项样式 */\n.nav-link-disabled {\n  color: rgba(30, 41, 59, 0.6) !important;\n  cursor: pointer;\n}\n\n.nav-link-disabled:hover {\n  color: rgba(30, 41, 59, 0.8) !important;\n  background: rgba(249, 115, 22, 0.1);\n}\n\n.website-navbar.transparent .nav-link-disabled {\n  color: rgba(255, 255, 255, 0.7) !important;\n}\n\n.website-navbar.transparent .nav-link-disabled:hover {\n  color: rgba(255, 255, 255, 0.9) !important;\n  background: rgba(249, 115, 22, 0.2);\n}\n\n/* 透明模式下的图标和文字悬停 */\n.website-navbar.transparent .nav-link:hover .nav-icon,\n.website-navbar.transparent .nav-link:hover .nav-text {\n  color: #ffffff;\n}\n\n/* 右侧操作区 */\n.nav-actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.btn-secondary,\n.btn-admin {\n  padding: 0.875rem 1.75rem;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.btn-secondary {\n  background: transparent;\n  border: 2px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n}\n\n.btn-secondary::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);\n  transition: left 0.6s ease;\n}\n\n.btn-admin {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);\n}\n\n/* 透明模式下的按钮 */\n.website-navbar.transparent .btn-secondary {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: #ffffff;\n  backdrop-filter: blur(10px);\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.website-navbar.transparent .btn-admin {\n  background: rgba(16, 185, 129, 0.9);\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n}\n\n.btn-secondary:hover {\n  background: rgba(59, 130, 246, 0.12);\n  border-color: rgba(59, 130, 246, 0.6);\n  transform: translateY(-3px);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);\n}\n\n.btn-secondary:hover::before {\n  left: 100%;\n}\n\n.btn-admin:hover {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.35);\n}\n\n/* 透明模式下的按钮悬停 */\n.website-navbar.transparent .btn-secondary:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n.website-navbar.transparent .btn-admin:hover {\n  background: rgba(5, 150, 105, 0.95);\n  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);\n}\n\n/* 移动端菜单按钮 */\n.mobile-menu-btn {\n  display: none;\n  background: none;\n  border: none;\n  color: #1e293b;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n/* 透明模式下的移动端按钮 */\n.website-navbar.transparent .mobile-menu-btn {\n  color: #ffffff;\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.mobile-menu-btn:hover {\n  background: rgba(59, 130, 246, 0.1);\n  color: #3b82f6;\n}\n\n/* 透明模式下的移动端按钮悬停 */\n.website-navbar.transparent .mobile-menu-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  color: #ffffff;\n}\n\n/* 移动端菜单 */\n.mobile-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\n  box-shadow: 0 4px 30px rgba(59, 130, 246, 0.12);\n  transform: translateY(-100%);\n  opacity: 0;\n  visibility: hidden;\n  transition: all 0.3s ease;\n  padding: 1rem 0;\n}\n\n.mobile-menu.open {\n  transform: translateY(0);\n  opacity: 1;\n  visibility: visible;\n}\n\n.mobile-nav-link {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem 2rem;\n  color: rgba(30, 41, 59, 0.8);\n  text-decoration: none;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.mobile-nav-link:hover {\n  background: rgba(59, 130, 246, 0.08);\n  color: #3b82f6;\n}\n\n.mobile-nav-icon {\n  font-size: 1.1rem;\n}\n\n/* 🔥 移动端开发中菜单项样式 */\n.mobile-nav-link-disabled {\n  color: rgba(30, 41, 59, 0.6) !important;\n  cursor: pointer;\n}\n\n.mobile-nav-link-disabled:hover {\n  background: rgba(249, 115, 22, 0.1) !important;\n  color: rgba(30, 41, 59, 0.8) !important;\n}\n\n.mobile-actions {\n  padding: 1rem 2rem;\n  border-top: 1px solid rgba(59, 130, 246, 0.1);\n  margin-top: 1rem;\n}\n\n.mobile-btn-login,\n.mobile-btn-admin {\n  width: 100%;\n  padding: 0.875rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n\n.mobile-btn-login {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n}\n\n.mobile-btn-admin {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.mobile-btn-login:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\n}\n\n.mobile-btn-admin:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n}\n\n\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .nav-container {\n    padding: 0 1.5rem;\n  }\n  \n  .nav-menu {\n    gap: 0.3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .nav-menu,\n  .nav-actions {\n    display: none;\n  }\n  \n  .mobile-menu-btn {\n    display: flex;\n  }\n  \n  .nav-container {\n    padding: 0 1rem;\n  }\n}\n", null]}