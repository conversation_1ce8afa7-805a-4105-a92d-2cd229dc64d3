<template>
  <WebsitePage>
    <div class="membership-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">订阅会员</h1>
        <p class="simple-subtitle">成为会员享受更多特权，解锁高级功能</p>
      </div>

      <!-- 会员套餐区域 -->
      <section class="plans-section">
        <div class="container">
          <div class="plans-grid">
            <div 
              v-for="plan in plans" 
              :key="plan.id"
              class="plan-card"
              :class="{ 'featured': plan.featured }"
            >
              <div v-if="plan.featured" class="plan-badge">推荐</div>
              <div class="plan-header">
                <h3 class="plan-name">{{ plan.name }}</h3>
                <div class="plan-price">
                  <span class="price-symbol">¥</span>
                  <span class="price-amount">{{ plan.price }}</span>
                  <span class="price-period">/{{ plan.period }}</span>
                </div>
                <p class="plan-description">{{ plan.description }}</p>
              </div>
              
              <div class="plan-features">
                <div 
                  v-for="feature in plan.features" 
                  :key="feature"
                  class="feature-item"
                >
                  <a-icon type="check" />
                  <span>{{ feature }}</span>
                </div>
              </div>
              
              <button 
                class="btn-subscribe"
                :class="{ 'featured': plan.featured }"
                @click="handleSubscribe(plan)"
              >
                {{ plan.buttonText }}
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'

export default {
  name: 'Membership',
  components: {
    WebsitePage
  },
  data() {
    return {
      plans: [
        {
          id: 1,
          name: '基础版',
          price: '29',
          period: '月',
          description: '适合个人用户的基础功能',
          features: [
            '每月100次AI生成',
            '基础模板库',
            '标准客服支持',
            '基础数据分析'
          ],
          buttonText: '选择基础版',
          featured: false
        },
        {
          id: 2,
          name: '专业版',
          price: '99',
          period: '月',
          description: '适合内容创作者的专业功能',
          features: [
            '每月500次AI生成',
            '高级模板库',
            '优先客服支持',
            '详细数据分析',
            '多平台发布',
            '自定义品牌'
          ],
          buttonText: '选择专业版',
          featured: true
        },
        {
          id: 3,
          name: '企业版',
          price: '299',
          period: '月',
          description: '适合团队和企业的高级功能',
          features: [
            '无限次AI生成',
            '全部模板库',
            '专属客服支持',
            '高级数据分析',
            '团队协作功能',
            'API接口访问',
            '定制化服务'
          ],
          buttonText: '联系销售',
          featured: false
        }
      ]
    }
  },
  methods: {
    handleSubscribe(plan) {
      console.log('选择订阅套餐:', plan)

      // 检查用户登录状态
      const token = this.$ls.get('ACCESS_TOKEN')
      if (!token) {
        this.$message.warning('请先登录后再订阅会员')
        this.$router.push('/user/login')
        return
      }

      // 跳转到用户中心的会员管理页面
      this.$router.push({
        path: '/usercenter',
        query: {
          page: 'membership',
          planId: plan.id,
          planName: plan.name,
          price: plan.price
        }
      })
    }
  }
}
</script>

<style scoped>
.membership-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* 会员套餐区域 */
.plans-section {
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.plan-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
}

.plan-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.featured {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.plan-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 1rem;
}

.price-symbol {
  font-size: 1.2rem;
  color: #3b82f6;
  font-weight: 600;
}

.price-amount {
  font-size: 3rem;
  font-weight: 700;
  color: #3b82f6;
  margin: 0 0.25rem;
}

.price-period {
  font-size: 1rem;
  color: #64748b;
}

.plan-description {
  color: #64748b;
  margin: 0;
}

.plan-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.feature-item .anticon {
  color: #10b981;
  font-weight: bold;
}

.btn-subscribe {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: 2px solid #3b82f6;
  color: #3b82f6;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-subscribe:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

.btn-subscribe.featured {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
}

.btn-subscribe.featured:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-card.featured {
    transform: none;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }
}
</style>
