<template>
  <WebsitePage>
    <div class="membership-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">订阅会员</h1>
        <p class="simple-subtitle">成为会员享受更多特权，解锁高级功能</p>
      </div>

      <!-- 会员套餐区域 -->
      <section class="plans-section">
        <div class="container">
          <div class="plans-grid">
            <div 
              v-for="plan in plans" 
              :key="plan.id"
              class="plan-card"
              :class="{ 'featured': plan.featured }"
            >
              <div v-if="plan.featured" class="plan-badge">推荐</div>
              <div class="plan-header">
                <h3 class="plan-name">{{ plan.name }}</h3>
                <div class="plan-price">
                  <!-- 原价显示 -->
                  <div v-if="plan.originalPrice" class="original-price">
                    <span class="original-price-text">原价：¥{{ plan.originalPrice }}</span>
                    <span class="discount-badge">{{ plan.discountText }}</span>
                  </div>
                  <!-- 现价显示 -->
                  <div class="current-price">
                    <span class="price-symbol">¥</span>
                    <span class="price-amount">{{ plan.price }}</span>
                    <span class="price-period">/{{ plan.period }}</span>
                  </div>
                  <!-- 立省金额 -->
                  <div v-if="plan.saveAmount" class="save-amount">
                    立省¥{{ plan.saveAmount }}
                  </div>
                </div>
                <p class="plan-description">{{ plan.description }}</p>
              </div>
              
              <div class="plan-features">
                <div
                  v-for="feature in plan.features"
                  :key="feature.text || feature"
                  class="feature-item"
                  :class="{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }"
                >
                  <a-icon
                    :type="feature.disabled ? 'close' : 'check'"
                    :class="{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }"
                  />
                  <span class="feature-text" v-html="feature.text || feature"></span>
                  <span v-if="feature.exclusive" class="exclusive-badge">专属</span>
                  <span v-if="feature.disabled" class="disabled-text">（SVIP专享）</span>
                </div>
              </div>
              
              <button 
                class="btn-subscribe"
                :class="{ 'featured': plan.featured }"
                @click="handleSubscribe(plan)"
              >
                {{ plan.buttonText }}
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'

export default {
  name: 'Membership',
  components: {
    WebsitePage
  },
  data() {
    return {
      plans: [
        {
          id: 1,
          name: 'VIP月卡',
          price: '29',
          originalPrice: '39',
          discountText: '限时7.4折',
          saveAmount: '10',
          period: '月',
          description: '适合体验用户的基础功能',
          features: [
            { text: '解锁VIP课程', disabled: false },
            { text: '插件基础折扣', disabled: false },
            { text: '邀请奖励35%基础比例', disabled: false },
            { text: '调用工作流基础折扣', disabled: false },
            { text: '复制所有工作流', disabled: true },
            { text: '解锁流媒体转换', disabled: true },
            { text: '部分插件免费', disabled: true }
          ],
          buttonText: '立即订阅',
          featured: false
        },
        {
          id: 2,
          name: 'VIP年卡',
          price: '298',
          originalPrice: '468',
          discountText: '限时6.4折',
          saveAmount: '170',
          period: '年',
          description: '适合长期使用的优惠套餐',
          features: [
            { text: '解锁VIP课程', disabled: false },
            { text: '插件基础折扣', disabled: false },
            { text: '邀请奖励35%基础比例', disabled: false },
            { text: '调用工作流基础折扣', disabled: false },
            { text: '复制所有工作流', disabled: true },
            { text: '解锁流媒体转换', disabled: true },
            { text: '部分插件免费', disabled: true }
          ],
          buttonText: '立即订阅',
          featured: false
        },
        {
          id: 3,
          name: 'SVIP年卡',
          price: '489',
          originalPrice: '999',
          discountText: '限时4.9折',
          saveAmount: '510',
          period: '年',
          description: '适合专业用户的全功能套餐',
          features: [
            { text: '解锁<strong class="highlight">全部课程</strong>', disabled: false, exclusive: true },
            { text: '插件<strong class="highlight">最高折扣</strong>', disabled: false, exclusive: true },
            { text: '邀请<strong class="highlight">奖励50%</strong>', disabled: false, exclusive: true },
            { text: '调用工作流<strong class="highlight">最高折扣</strong>', disabled: false, exclusive: true },
            { text: '复制<strong class="highlight">所有工作流</strong>', disabled: false, exclusive: true },
            { text: '<strong class="highlight">解锁流媒体转换</strong>', disabled: false, exclusive: true },
            { text: '<strong class="highlight">插件免费</strong>', disabled: false, exclusive: true }
          ],
          buttonText: '立即订阅',
          featured: true
        }
      ]
    }
  },
  methods: {
    handleSubscribe(plan) {
      console.log('选择订阅套餐:', plan)

      // 检查用户登录状态
      const token = this.$ls.get('ACCESS_TOKEN')
      if (!token) {
        this.$message.warning('请先登录后再订阅会员')
        this.$router.push('/user/login')
        return
      }

      // 显示订阅功能提示
      this.$message.info(`您选择了 ${plan.name}，会员订阅功能即将上线，敬请期待！`)
    }
  }
}
</script>

<style>
.membership-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* 会员套餐区域 */
.plans-section {
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.plan-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
}

.plan-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.featured {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.plan-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.plan-price {
  text-align: center;
  margin-bottom: 1rem;
}

/* 原价显示 */
.original-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.original-price-text {
  font-size: 0.9rem;
  color: #999;
  text-decoration: line-through;
}

.discount-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

/* 现价显示 */
.current-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin: 0.5rem 0;
}

/* 立省金额 */
.save-amount {
  font-size: 0.9rem;
  color: #27ae60;
  font-weight: bold;
  margin-top: 0.3rem;
}

.price-symbol {
  font-size: 1.2rem;
  color: #3b82f6;
  font-weight: 600;
}

.price-amount {
  font-size: 3rem;
  font-weight: 700;
  color: #3b82f6;
  margin: 0 0.25rem;
}

.price-period {
  font-size: 1rem;
  color: #64748b;
}

.plan-description {
  color: #64748b;
  margin: 0;
}

.plan-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #1e293b;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
}

.feature-item .anticon {
  color: #10b981;
  font-weight: bold;
  flex-shrink: 0;
}

/* 禁用功能样式 */
.feature-item.feature-disabled {
  color: #94a3b8;
  opacity: 0.6;
}

.feature-item.feature-disabled .anticon {
  color: #ef4444;
}

.feature-item.feature-disabled .icon-disabled {
  color: #ef4444;
}

.disabled-text {
  font-size: 0.8rem;
  color: #ef4444;
  margin-left: auto;
}

/* 专属功能样式 */
.feature-item.feature-exclusive {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  padding: 0.5rem;
  border-radius: 8px;
  border-left: 3px solid #0ea5e9;
}

.feature-item.feature-exclusive .anticon {
  color: #0ea5e9;
}

.feature-item.feature-exclusive .icon-exclusive {
  color: #0ea5e9;
}

.exclusive-badge {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  padding: 0.1rem 0.4rem;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: auto;
  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

.feature-text {
  flex: 1;
  font-size: 0.95rem;
  line-height: 1.5;
  font-weight: 500;
  color: inherit;
}

/* 确保highlight样式不被覆盖 */
.feature-text .highlight {
  color: transparent !important;
}

/* 重点亮点样式 */
.highlight {
  color: #ff6b35 !important;
  font-weight: bold !important;
  font-size: 1em !important;
  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  display: inline !important;
}

/* 兼容性备用方案 */
@supports not (-webkit-background-clip: text) {
  .highlight {
    color: #ff6b35 !important;
    text-shadow: 0 2px 4px rgba(255, 107, 53, 0.4) !important;
    -webkit-text-fill-color: initial !important;
  }
}

.btn-subscribe {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: 2px solid #3b82f6;
  color: #3b82f6;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 扫光特效 */
.btn-subscribe::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.8s ease;
  z-index: 1;
}

.btn-subscribe:hover::after {
  left: 100%;
}

.btn-subscribe:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

.btn-subscribe.featured {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
}

/* 推荐套餐的自动扫光特效 */
.btn-subscribe.featured::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  animation: autoSweep 2.5s ease-in-out infinite;
  z-index: 1;
}

.btn-subscribe.featured:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

@keyframes autoSweep {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: -100%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-card.featured {
    transform: none;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }
}
</style>
