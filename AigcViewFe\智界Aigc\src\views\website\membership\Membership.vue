<template>
  <WebsitePage>
    <div class="membership-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">订阅会员</h1>
        <p class="simple-subtitle">成为会员享受更多特权，解锁高级功能</p>
      </div>

      <!-- 用户角色调试信息 -->
      <section class="debug-section" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;">
        <div class="container">
          <p style="margin: 0; font-size: 14px; color: #666;">
            🔍 调试信息 - 当前用户角色: <strong style="color: #e74c3c;">{{ getUserRole() }}</strong>
            | userInfo.currentRole: <strong style="color: #3498db;">{{ userInfo && userInfo.currentRole ? userInfo.currentRole : '未获取到' }}</strong>
            | localStorage.userRole: <strong style="color: #27ae60;">{{ getLocalStorageRole() }}</strong>
          </p>
        </div>
      </section>

      <!-- 快速充值模块 -->
      <section class="recharge-section">
        <div class="container">
          <QuickRecharge @recharge-success="handleRechargeSuccess" />
        </div>
      </section>

      <!-- 会员套餐区域 -->
      <section class="plans-section">
        <div class="container">
          <div class="plans-grid">
            <div 
              v-for="plan in plans" 
              :key="plan.id"
              class="plan-card"
              :class="{ 'featured': plan.featured }"
            >
              <div v-if="plan.featured" class="plan-badge">推荐</div>
              <div class="plan-header">
                <h3 class="plan-name">{{ plan.name }}</h3>
                <div class="plan-price">
                  <!-- 原价显示 -->
                  <div v-if="plan.originalPrice" class="original-price">
                    <span class="original-price-text">原价：¥{{ plan.originalPrice }}</span>
                    <span class="discount-badge">{{ plan.discountText }}</span>
                  </div>
                  <!-- 现价显示 -->
                  <div class="current-price">
                    <span class="price-symbol">¥</span>
                    <span class="price-amount">{{ plan.price }}</span>
                    <span class="price-period">/{{ plan.period }}</span>
                  </div>
                  <!-- 立省金额 -->
                  <div v-if="plan.saveAmount" class="save-amount">
                    立省¥{{ plan.saveAmount }}
                  </div>
                </div>
                <p class="plan-description">{{ plan.description }}</p>
              </div>
              
              <div class="plan-features">
                <div
                  v-for="feature in plan.features"
                  :key="feature.text || feature"
                  class="feature-item"
                  :class="{ 'feature-disabled': feature.disabled, 'feature-exclusive': feature.exclusive }"
                >
                  <a-icon
                    :type="feature.disabled ? 'close' : 'check'"
                    :class="{ 'icon-disabled': feature.disabled, 'icon-exclusive': feature.exclusive }"
                  />
                  <span class="feature-text" v-html="feature.text || feature"></span>
                  <span v-if="feature.exclusive" class="exclusive-badge">专属</span>
                  <span v-if="feature.disabled" class="disabled-text">（SVIP专享）</span>
                </div>
              </div>
              
              <button
                class="btn-subscribe"
                :class="{ 'featured': plan.featured }"
                @click="handleSubscribe(plan)"
              >
                {{ getButtonText(plan) }}
              </button>

              <!-- VIP升级SVIP的特殊提示 -->
              <div v-if="showUpgradeNotice(plan)" class="upgrade-notice">
                <i class="icon-info"></i>
                <span>{{ getUpgradeNoticeText(plan) }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 支付方式选择弹窗 -->
    <a-modal
      title="选择支付方式"
      :visible="showPaymentModal"
      @cancel="showPaymentModal = false"
      :footer="null"
      width="500px"
    >
      <div class="payment-modal-content">
        <div class="order-info">
          <h4>订单信息</h4>
          <div class="order-details">
            <div class="order-item">
              <span>套餐名称：</span>
              <span>{{ selectedPlan ? selectedPlan.name : '' }}</span>
            </div>
            <div class="order-item">
              <span>支付金额：</span>
              <span class="amount">¥{{ currentOrderAmount }}</span>
            </div>
          </div>
        </div>

        <div class="payment-methods">
          <h4>支付方式</h4>
          <a-radio-group v-model="selectedPaymentMethod" size="large">
            <a-radio-button value="alipay-qr">
              <i class="anticon anticon-qrcode"></i>
              支付宝扫码
            </a-radio-button>
            <a-radio-button value="alipay-page">
              <i class="anticon anticon-alipay"></i>
              支付宝网页
            </a-radio-button>
          </a-radio-group>
        </div>

        <div class="payment-actions">
          <a-button
            type="primary"
            size="large"
            :loading="paymentLoading"
            @click="handlePayment"
            block
          >
            确认支付
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 支付二维码弹窗 -->
    <a-modal
      title="扫码支付"
      :visible="showQrModal"
      @cancel="closeQrModal"
      :footer="null"
      width="400px"
    >
      <div class="qr-payment-content">
        <div class="qr-code-container">
          <div v-if="qrCodeUrl" class="qr-code">
            <img :src="qrCodeUrl" alt="支付二维码" />
          </div>
          <div v-else class="qr-loading">
            <a-spin size="large" />
            <p>正在生成二维码...</p>
          </div>
        </div>
        <div class="qr-info">
          <p class="qr-amount">支付金额：¥{{ currentOrderAmount }}</p>
          <p class="qr-tip">请使用支付宝扫码支付</p>
        </div>
        <div class="qr-status">
          <a-button @click="checkPaymentStatus" :loading="checkingStatus">
            检查支付状态
          </a-button>
        </div>
      </div>
    </a-modal>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import QuickRecharge from '@/components/QuickRecharge.vue'
import { createMembershipOrder, payOrder } from '@/api/usercenter'
import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
  name: 'Membership',
  components: {
    WebsitePage,
    QuickRecharge
  },
  data() {
    return {
      // 支付相关
      paymentLoading: false,
      showPaymentModal: false,
      selectedPlan: null,
      selectedPaymentMethod: 'alipay-qr',

      // 二维码支付
      showQrModal: false,
      qrCodeUrl: '',
      currentOrderId: '',
      currentOrderAmount: 0,
      paymentCheckTimer: null,
      checkingStatus: false,

      plans: [
        {
          id: 1,
          name: 'VIP月卡',
          price: '29',
          originalPrice: '39',
          discountText: '限时7.4折',
          saveAmount: '10',
          period: '月',
          description: '适合体验用户的基础功能',
          features: [
            { text: '解锁VIP课程', disabled: false },
            { text: '插件基础折扣', disabled: false },
            { text: '邀请奖励35%基础比例', disabled: false },
            { text: '调用工作流基础折扣', disabled: false },
            { text: '复制所有工作流', disabled: true },
            { text: '解锁流媒体转换', disabled: true },
            { text: '部分插件免费', disabled: true }
          ],
          buttonText: '立即购买',
          featured: false
        },
        {
          id: 2,
          name: 'VIP年卡',
          price: '298',
          originalPrice: '468',
          discountText: '限时6.4折',
          saveAmount: '170',
          period: '年',
          description: '适合长期使用的优惠套餐',
          features: [
            { text: '解锁VIP课程', disabled: false },
            { text: '插件基础折扣', disabled: false },
            { text: '邀请奖励35%基础比例', disabled: false },
            { text: '调用工作流基础折扣', disabled: false },
            { text: '复制所有工作流', disabled: true },
            { text: '解锁流媒体转换', disabled: true },
            { text: '部分插件免费', disabled: true }
          ],
          buttonText: '立即购买',
          featured: false
        },
        {
          id: 3,
          name: 'SVIP年卡',
          price: '489',
          originalPrice: '999',
          discountText: '限时4.9折',
          saveAmount: '510',
          period: '年',
          description: '适合专业用户的全功能套餐',
          features: [
            { text: '解锁<strong class="highlight">全部课程</strong>', disabled: false, exclusive: true },
            { text: '插件<strong class="highlight">最高折扣</strong>', disabled: false, exclusive: true },
            { text: '邀请<strong class="highlight">奖励50%</strong>', disabled: false, exclusive: true },
            { text: '调用工作流<strong class="highlight">最高折扣</strong>', disabled: false, exclusive: true },
            { text: '复制<strong class="highlight">所有工作流</strong>', disabled: false, exclusive: true },
            { text: '<strong class="highlight">解锁流媒体转换</strong>', disabled: false, exclusive: true },
            { text: '部分<strong class="highlight">插件免费</strong>', disabled: false, exclusive: true }
          ],
          buttonText: '立即购买',
          featured: true
        }
      ]
    }
  },

  beforeDestroy() {
    if (this.paymentCheckTimer) {
      clearInterval(this.paymentCheckTimer)
    }
  },

  methods: {
    // 获取按钮文本（续费/升级/购买）
    getButtonText(plan) {
      const userRole = this.getUserRole() // 获取用户当前角色
      const planRole = this.getPlanRole(plan.id) // 获取套餐对应角色

      // 管理员显示"立即购买"（管理员不需要购买会员，但可以测试）
      if (userRole === 'admin') {
        return '立即购买'
      }

      // 相同等级显示"续费会员"
      if (userRole === planRole) {
        return '续费会员'
      }
      // 普通用户购买任何会员都显示"立即购买"
      else if (userRole === 'user') {
        return '立即购买'
      }
      // 会员用户购买更高等级显示"升级会员"
      else if (this.isUpgrade(userRole, planRole)) {
        return '升级会员'
      }
      // 其他情况显示"立即购买"
      else {
        return '立即购买'
      }
    },

    // 获取套餐对应的角色
    getPlanRole(planId) {
      const roleMap = {
        1: 'VIP',     // VIP月卡
        2: 'VIP',     // VIP年卡
        3: 'SVIP'     // SVIP年卡
      }
      return roleMap[planId] || 'user'
    },

    // 判断是否为升级
    isUpgrade(currentRole, targetRole) {
      const roleLevel = {
        'user': 1,
        'VIP': 2,
        'SVIP': 3,
        'admin': 999  // 管理员级别最高
      }
      return roleLevel[targetRole] > (roleLevel[currentRole] || 0)
    },

    // 是否显示升级提示
    showUpgradeNotice(plan) {
      const userRole = this.getUserRole()
      const planRole = this.getPlanRole(plan.id)

      // 只有VIP升级到SVIP时显示特殊提示
      return userRole === 'VIP' && planRole === 'SVIP'
    },

    // 获取升级提示文本
    getUpgradeNoticeText(plan) {
      const userRole = this.getUserRole()
      const planRole = this.getPlanRole(plan.id)

      if (userRole === 'VIP' && planRole === 'SVIP') {
        return '当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~'
      }

      return ''
    },

    // 获取用户当前角色
    getUserRole() {
      // 优先从userInfo中获取角色信息
      if (this.userInfo && this.userInfo.currentRole) {
        console.log('🔍 Membership: getUserRole - 从userInfo获取角色:', this.userInfo.currentRole)
        return this.userInfo.currentRole
      }

      // 从localStorage获取用户角色
      try {
        // 尝试从多个可能的存储位置获取角色信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
        const userRole = localStorage.getItem('userRole')
        const roleCode = localStorage.getItem('roleCode')

        console.log('🔍 Membership: getUserRole - userInfo from localStorage:', userInfo)
        console.log('🔍 Membership: getUserRole - userRole from localStorage:', userRole)
        console.log('🔍 Membership: getUserRole - roleCode from localStorage:', roleCode)

        // 尝试多种可能的角色字段
        let role = userInfo.role || userInfo.userRole || userInfo.roleCode || userRole || roleCode

        // 如果还是没有找到角色信息，默认为普通用户
        if (!role) {
          role = 'user'  // 默认为普通用户角色
        }

        console.log('🔍 Membership: getUserRole - 最终获取到的角色:', role)
        return role
      } catch (error) {
        console.warn('🔍 Membership: 获取用户角色失败:', error)
        return 'user'  // 出错时默认为普通用户
      }
    },

    async handleSubscribe(plan) {
      console.log('选择购买套餐:', plan)

      // 检查用户登录状态
      const token = this.$ls.get(ACCESS_TOKEN)
      if (!token) {
        this.$message.warning('请先登录后再购买会员')
        this.$router.push('/user/login')
        return
      }

      try {
        this.paymentLoading = true

        // 准备会员订单数据（与充值订单结构保持一致）
        const orderData = {
          membershipLevel: plan.id,
          duration: plan.name.includes('月') ? 1 : 12, // 根据套餐名称判断时长
          amount: parseFloat(plan.price),
          planName: plan.name,
          paymentMethod: 'alipay' // 默认支付宝，与充值保持一致
        }

        console.log('🎯 创建会员订单:', orderData)
        this.$message.loading('正在创建订单...', 0)

        // 创建会员订单（使用与充值相同的交易记录系统）
        const response = await createMembershipOrder(orderData)
        this.$message.destroy()

        if (response.success) {
          const orderResult = response.result
          console.log('🎯 会员订单创建成功:', orderResult)

          // 显示支付选择弹窗
          this.selectedPlan = plan
          this.currentOrderId = orderResult.orderId
          this.currentOrderAmount = orderResult.amount
          this.showPaymentModal = true

        } else {
          this.$message.error(response.message || '创建订单失败')
        }
      } catch (error) {
        this.$message.destroy()
        console.error('创建会员订单失败:', error)
        this.$message.error('创建订单失败，请重试')
      } finally {
        this.paymentLoading = false
      }
    },

    // 处理支付
    async handlePayment() {
      if (!this.currentOrderId) {
        this.$message.error('订单信息错误')
        return
      }

      try {
        this.paymentLoading = true

        if (this.selectedPaymentMethod === 'alipay-page') {
          await this.handleAlipayPagePayment()
        } else if (this.selectedPaymentMethod === 'alipay-qr') {
          await this.handleAlipayQrPayment()
        }

        this.showPaymentModal = false
      } catch (error) {
        console.error('支付处理失败:', error)
        this.$message.error('支付失败，请重试')
      } finally {
        this.paymentLoading = false
      }
    },

    // 处理支付宝网页支付（与充值功能完全一致）
    async handleAlipayPagePayment() {
      try {
        console.log('🔍 开始处理支付宝网页支付 - 订单号:', this.currentOrderId)
        this.$message.loading('正在跳转到支付宝支付...', 0)

        const paymentData = {
          orderId: this.currentOrderId,
          amount: this.currentOrderAmount,
          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,
          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`
        }

        console.log('🔍 发送支付请求数据:', paymentData)
        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)
        console.log('🔍 支付响应:', payResponse)
        this.$message.destroy()

        if (payResponse.success) {
          const payForm = payResponse.result.payForm
          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')

          if (!payForm) {
            this.$message.error('支付表单为空')
            return
          }

          // 创建表单并提交到支付宝
          const div = document.createElement('div')
          div.innerHTML = payForm
          document.body.appendChild(div)

          const form = div.querySelector('form')
          if (form) {
            console.log('🔍 找到支付表单，准备提交')
            form.submit()
          } else {
            console.error('🔍 未找到支付表单')
            this.$message.error('支付表单创建失败')
          }

          // 清理DOM
          setTimeout(() => {
            if (document.body.contains(div)) {
              document.body.removeChild(div)
            }
          }, 1000)
        } else {
          console.error('🔍 支付请求失败:', payResponse.message)
          this.$message.error(payResponse.message || '创建支付订单失败')
        }
      } catch (error) {
        this.$message.destroy()
        console.error('支付宝网页支付失败:', error)
        this.$message.error('支付宝支付失败，请重试')
      }
    },

    // 处理支付宝扫码支付（与充值功能完全一致）
    async handleAlipayQrPayment() {
      try {
        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', this.currentOrderId)
        this.$message.loading('正在生成支付二维码...', 0)

        const paymentData = {
          orderId: this.currentOrderId,
          amount: this.currentOrderAmount,
          subject: `智界Aigc会员 - ${this.selectedPlan.name}`,
          body: `购买${this.selectedPlan.name}，金额：¥${this.currentOrderAmount}`
        }

        console.log('🔍 发送扫码支付请求数据:', paymentData)
        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)
        console.log('🔍 扫码支付响应:', payResponse)
        this.$message.destroy()

        if (payResponse.success) {
          const qrCode = payResponse.result.qrCode
          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')

          if (!qrCode) {
            this.$message.error('支付二维码生成失败')
            return
          }

          // 显示二维码支付弹窗
          this.showQrCodeModal(qrCode)
        } else {
          console.error('🔍 扫码支付请求失败:', payResponse.message)
          this.$message.error(payResponse.message || '创建扫码支付订单失败')
        }
      } catch (error) {
        this.$message.destroy()
        console.error('支付宝扫码支付失败:', error)
        this.$message.error('支付宝扫码支付失败，请重试')
      }
    },

    // 显示二维码弹窗
    showQrCodeModal(qrCode) {
      // 生成二维码图片URL
      this.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`
      this.showQrModal = true

      console.log('🔍 显示二维码弹窗 - 订单号:', this.currentOrderId, '金额:', this.currentOrderAmount)
      console.log('🔍 二维码URL:', this.qrCodeUrl)

      // 开始轮询支付状态
      this.startPaymentStatusCheck()
    },

    // 关闭二维码弹窗
    closeQrModal() {
      this.showQrModal = false
      this.qrCodeUrl = ''

      if (this.paymentCheckTimer) {
        clearInterval(this.paymentCheckTimer)
        this.paymentCheckTimer = null
      }
    },

    // 开始支付状态检查
    startPaymentStatusCheck() {
      if (this.paymentCheckTimer) {
        clearInterval(this.paymentCheckTimer)
      }

      this.paymentCheckTimer = setInterval(() => {
        this.checkPaymentStatus()
      }, 3000) // 每3秒检查一次
    },

    // 检查支付状态（与充值功能完全一致）
    async checkPaymentStatus() {
      if (!this.currentOrderId) return

      try {
        this.checkingStatus = true
        console.log('🔍 检查支付状态 - 订单号:', this.currentOrderId)
        const response = await this.$http.get(`/api/alipay/queryOrder/${this.currentOrderId}`)
        console.log('🔍 支付状态查询响应:', response)

        if (response.success && response.result.status === 'TRADE_SUCCESS') {
          console.log('🔍 会员购买支付成功！')
          this.$message.success('支付成功！会员权益已生效')
          this.closeQrModal()

          // 留在当前membership页面，刷新用户状态
          setTimeout(() => {
            this.$message.success('会员购买成功！页面即将刷新以更新会员状态')
            // 刷新当前页面以更新会员状态和按钮显示
            window.location.reload()
          }, 2000)
        } else {
          console.log('🔍 支付状态:', (response.result && response.result.status) || '未知')
        }
      } catch (error) {
        console.error('检查支付状态失败:', error)
      } finally {
        this.checkingStatus = false
      }
    },

    // 处理充值成功事件
    handleRechargeSuccess() {
      this.$message.success('充值成功！您可以继续选择套餐')
      // 可以在这里添加其他逻辑，比如刷新用户信息等
    }
  }
}
</script>

<style>
.membership-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 充值模块样式 */
.recharge-section {
  margin-bottom: 3rem;
}

.recharge-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* 会员套餐区域 */
.plans-section {
  padding: 1rem 0 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.plan-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
}

.plan-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.featured {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.plan-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.plan-price {
  text-align: center;
  margin-bottom: 1rem;
}

/* 原价显示 */
.original-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.original-price-text {
  font-size: 0.9rem;
  color: #999;
  text-decoration: line-through;
}

.discount-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

/* 现价显示 */
.current-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin: 0.5rem 0;
}

/* 立省金额 */
.save-amount {
  font-size: 0.9rem;
  color: #27ae60;
  font-weight: bold;
  margin-top: 0.3rem;
}

.price-symbol {
  font-size: 1.2rem;
  color: #3b82f6;
  font-weight: 600;
}

.price-amount {
  font-size: 3rem;
  font-weight: 700;
  color: #3b82f6;
  margin: 0 0.25rem;
}

.price-period {
  font-size: 1rem;
  color: #64748b;
}

.plan-description {
  color: #64748b;
  margin: 0;
}

.plan-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #1e293b;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
}

.feature-item .anticon {
  color: #10b981;
  font-weight: bold;
  flex-shrink: 0;
}

/* 禁用功能样式 */
.feature-item.feature-disabled {
  color: #94a3b8;
  opacity: 0.6;
}

.feature-item.feature-disabled .anticon {
  color: #ef4444;
}

.feature-item.feature-disabled .icon-disabled {
  color: #ef4444;
}

.disabled-text {
  font-size: 0.8rem;
  color: #ef4444;
  margin-left: auto;
}

/* 专属功能样式 */
.feature-item.feature-exclusive {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  padding: 0.5rem;
  border-radius: 8px;
  border-left: 3px solid #0ea5e9;
}

.feature-item.feature-exclusive .anticon {
  color: #0ea5e9;
}

.feature-item.feature-exclusive .icon-exclusive {
  color: #0ea5e9;
}

.exclusive-badge {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: white;
  padding: 0.1rem 0.4rem;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: auto;
  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

.feature-text {
  flex: 1;
  font-size: 0.95rem;
  line-height: 1.5;
  font-weight: 500;
  color: inherit;
}

/* 确保highlight样式不被覆盖 */
.feature-text .highlight {
  color: transparent !important;
}

/* 重点亮点样式 */
.highlight {
  color: #ff6b35 !important;
  font-weight: bold !important;
  font-size: 1em !important;
  background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  display: inline !important;
}

/* 兼容性备用方案 */
@supports not (-webkit-background-clip: text) {
  .highlight {
    color: #ff6b35 !important;
    text-shadow: 0 2px 4px rgba(255, 107, 53, 0.4) !important;
    -webkit-text-fill-color: initial !important;
  }
}

.btn-subscribe {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: 2px solid #3b82f6;
  color: #3b82f6;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 扫光特效 */
.btn-subscribe::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.8s ease;
  z-index: 1;
}

.btn-subscribe:hover::after {
  left: 100%;
}

.btn-subscribe:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

.btn-subscribe.featured {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  animation: borderGlow 3s ease-in-out infinite;
}

/* 推荐套餐的斜向扫光特效 */
.btn-subscribe.featured::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(240, 248, 255, 0.4) 45%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(240, 248, 255, 0.4) 55%,
    transparent 70%
  );
  animation: diagonalSweep 3.5s ease-in-out infinite;
  z-index: 1;
  transform: rotate(-10deg);
}

/* 鼠标悬停时的扫光效果 */
.btn-subscribe.featured:hover::after {
  animation: hoverSweepIn 0.6s ease-out forwards;
}

/* 鼠标离开时先扫回来，然后延迟恢复自动动画 */
.btn-subscribe.featured::after {
  animation: diagonalSweep 3.5s ease-in-out infinite;
}

.btn-subscribe.featured:not(:hover)::after {
  animation: hoverSweepOut 0.6s ease-in forwards, diagonalSweep 3.5s ease-in-out 2s infinite;
}

.btn-subscribe.featured:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), 0 0 15px rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}

/* 升级提示样式 */
.upgrade-notice {
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 6px;
  font-size: 12px;
  color: #856404;
  display: flex;
  align-items: center;
  gap: 6px;
}

.upgrade-notice .icon-info {
  width: 14px;
  height: 14px;
  background: #ffc107;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
}

.upgrade-notice .icon-info::before {
  content: 'i';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
  font-style: normal;
}

.upgrade-notice span {
  line-height: 1.4;
}

/* 边框微光动画 */
@keyframes borderGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);
  }
}

/* 斜向来回扫光动画 */
@keyframes diagonalSweep {
  0% {
    left: -150%;
  }
  25% {
    left: 100%;
  }
  50% {
    left: 100%;
  }
  75% {
    left: -150%;
  }
  100% {
    left: -150%;
  }
}

/* 鼠标悬停时扫光进入动画 */
@keyframes hoverSweepIn {
  0% {
    left: -150%;
  }
  100% {
    left: 100%;
  }
}

/* 鼠标离开时扫光退出动画 */
@keyframes hoverSweepOut {
  0% {
    left: 100%;
  }
  100% {
    left: -150%;
  }
}

/* 支付弹窗样式 */
.payment-modal-content {
  padding: 1rem 0;
}

.payment-modal-content h4 {
  margin-bottom: 1rem;
  color: #1f2937;
  font-weight: 600;
}

.order-info {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-item .amount {
  font-size: 1.2rem;
  font-weight: bold;
  color: #3b82f6;
}

.payment-methods {
  margin-bottom: 2rem;
}

.payment-actions {
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* 二维码支付样式 */
.qr-payment-content {
  text-align: center;
  padding: 1rem 0;
}

.qr-code-container {
  margin-bottom: 1.5rem;
}

.qr-code img {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.qr-loading {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.qr-info {
  margin-bottom: 1.5rem;
}

.qr-amount {
  font-size: 1.2rem;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.qr-tip {
  color: #6b7280;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-card.featured {
    transform: none;
  }

  .container {
    padding: 0 1rem;
  }

  .order-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }
}
</style>
